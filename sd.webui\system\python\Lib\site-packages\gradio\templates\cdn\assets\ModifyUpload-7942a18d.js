import{S as _,e as b,s as w,f,g as o,K as i,h as k,j as d,n as m,k as v,m as j,o as z,F as C,N as $,G as M,w as p,r as B,u as g,v as E,H as I,a0 as S,a1 as q,C as D}from"./index-afe51b5b.js";import"./Button-b4eb936e.js";import{I as L}from"./IconButton-12dccad1.js";function F(r){let e,s,t,l;return{c(){e=f("svg"),s=f("g"),t=f("path"),l=f("path"),o(t,"d","M18,6L6.087,17.913"),i(t,"fill","none"),i(t,"fill-rule","nonzero"),i(t,"stroke-width","2px"),o(s,"transform","matrix(1.14096,-0.140958,-0.140958,1.14096,-0.0559523,0.0559523)"),o(l,"d","M4.364,4.364L19.636,19.636"),i(l,"fill","none"),i(l,"fill-rule","nonzero"),i(l,"stroke-width","2px"),o(e,"width","100%"),o(e,"height","100%"),o(e,"viewBox","0 0 24 24"),o(e,"version","1.1"),o(e,"xmlns","http://www.w3.org/2000/svg"),o(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),o(e,"xml:space","preserve"),o(e,"stroke","currentColor"),i(e,"fill-rule","evenodd"),i(e,"clip-rule","evenodd"),i(e,"stroke-linecap","round"),i(e,"stroke-linejoin","round")},m(n,a){k(n,e,a),d(e,s),d(s,t),d(e,l)},p:m,i:m,o:m,d(n){n&&v(e)}}}class G extends _{constructor(e){super(),b(this,e,null,F,w,{})}}function H(r){let e,s;return{c(){e=f("svg"),s=f("path"),o(s,"d","M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"),o(e,"xmlns","http://www.w3.org/2000/svg"),o(e,"width","100%"),o(e,"height","100%"),o(e,"viewBox","0 0 24 24"),o(e,"fill","none"),o(e,"stroke","currentColor"),o(e,"stroke-width","1.5"),o(e,"stroke-linecap","round"),o(e,"stroke-linejoin","round"),o(e,"class","feather feather-edit-2")},m(t,l){k(t,e,l),d(e,s)},p:m,i:m,o:m,d(t){t&&v(e)}}}class K extends _{constructor(e){super(),b(this,e,null,H,w,{})}}function x(r){let e,s;return e=new L({props:{Icon:K,label:r[2]("common.edit")}}),e.$on("click",r[4]),{c(){C(e.$$.fragment)},m(t,l){M(e,t,l),s=!0},p(t,l){const n={};l&4&&(n.label=t[2]("common.edit")),e.$set(n)},i(t){s||(p(e.$$.fragment,t),s=!0)},o(t){g(e.$$.fragment,t),s=!1},d(t){I(e,t)}}}function N(r){let e,s,t,l,n=r[0]&&x(r);return t=new L({props:{Icon:G,label:r[2]("common.clear")}}),t.$on("click",r[5]),{c(){e=j("div"),n&&n.c(),s=z(),C(t.$$.fragment),o(e,"class","svelte-19sk1im"),$(e,"not-absolute",!r[1]),i(e,"position",r[1]?"absolute":"static")},m(a,c){k(a,e,c),n&&n.m(e,null),d(e,s),M(t,e,null),l=!0},p(a,[c]){a[0]?n?(n.p(a,c),c&1&&p(n,1)):(n=x(a),n.c(),p(n,1),n.m(e,s)):n&&(B(),g(n,1,1,()=>{n=null}),E());const h={};c&4&&(h.label=a[2]("common.clear")),t.$set(h),(!l||c&2)&&$(e,"not-absolute",!a[1]),c&2&&i(e,"position",a[1]?"absolute":"static")},i(a){l||(p(n),p(t.$$.fragment,a),l=!0)},o(a){g(n),g(t.$$.fragment,a),l=!1},d(a){a&&v(e),n&&n.d(),I(t)}}}function P(r,e,s){let t;S(r,q,u=>s(2,t=u));let{editable:l=!1}=e,{absolute:n=!0}=e;const a=D(),c=()=>a("edit"),h=u=>{a("clear"),u.stopPropagation()};return r.$$set=u=>{"editable"in u&&s(0,l=u.editable),"absolute"in u&&s(1,n=u.absolute)},[l,n,t,a,c,h]}class J extends _{constructor(e){super(),b(this,e,P,N,w,{editable:0,absolute:1})}}export{G as C,J as M};
//# sourceMappingURL=ModifyUpload-7942a18d.js.map
