{"version": 3, "file": "index-d5ef9846.js", "sources": ["../../../../js/checkboxgroup/static/StaticCheckboxgroup.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport CheckboxGroup from \"../shared\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { _ } from \"svelte-i18n\";\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: (string | number)[] = [];\n\texport let value_is_output = false;\n\texport let choices: [string, number][];\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let label = $_(\"checkbox.checkbox_group\");\n\texport let info: string | undefined = undefined;\n\texport let show_label: boolean;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tselect: SelectData;\n\t\tinput: never;\n\t}>;\n\n\texport let loading_status: LoadingStatus;\n</script>\n\n<Block\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\ttype=\"fieldset\"\n\t{container}\n\t{scale}\n\t{min_width}\n>\n\t<StatusTracker {...loading_status} />\n\n\t<CheckboxGroup\n\t\tbind:value\n\t\tbind:value_is_output\n\t\t{choices}\n\t\t{label}\n\t\t{info}\n\t\t{show_label}\n\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\ton:input={() => gradio.dispatch(\"input\")}\n\t\tdisabled\n\t/>\n</Block>\n"], "names": ["ctx", "elem_id", "$$props", "elem_classes", "visible", "value", "value_is_output", "choices", "container", "scale", "min_width", "label", "$_", "info", "show_label", "gradio", "loading_status"], "mappings": "4WAqCoBA,EAAc,EAAA,CAAA,ghBAAdA,EAAc,EAAA,CAAA,CAAA,CAAA,g6BA9BtB,GAAA,CAAA,QAAAC,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,GACd,MAAAG,EAAK,EAAA,EAAAH,EACL,CAAA,gBAAAI,EAAkB,EAAK,EAAAJ,GACvB,QAAAK,CAA2B,EAAAL,EAC3B,CAAA,UAAAM,EAAY,EAAI,EAAAN,EAChB,CAAA,MAAAO,EAAuB,IAAI,EAAAP,EAC3B,CAAA,UAAAQ,EAAgC,MAAS,EAAAR,GACzC,MAAAS,EAAQC,EAAG,yBAAyB,CAAA,EAAAV,EACpC,CAAA,KAAAW,EAA2B,MAAS,EAAAX,GACpC,WAAAY,CAAmB,EAAAZ,GACnB,OAAAa,CAIT,EAAAb,GAES,eAAAc,CAA6B,EAAAd,4DAqB3B,GAAMa,EAAO,SAAS,SAAU,EAAE,MAAM,QACnCA,EAAO,SAAS,QAAQ,QACzBA,EAAO,SAAS,OAAO"}