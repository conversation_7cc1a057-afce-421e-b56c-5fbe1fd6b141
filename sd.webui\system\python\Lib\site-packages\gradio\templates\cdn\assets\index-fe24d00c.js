import{S as J,e as K,s as L,af as f,F as w,G as v,w as g,u as m,H as k,R as N,U as R,o as M,h as O,r as P,v as Q,X as U,k as Y,Z as y,ad as p,V as $,W as ee}from"./index-afe51b5b.js";import{T as te}from"./Textbox-3a8fafc1.js";import{B as se}from"./Button-b4eb936e.js";import"./BlockTitle-5b84032d.js";import"./Info-1e8b7dd5.js";import"./Copy-df6acdb9.js";function V(i){let e,t;const n=[i[17]];let o={};for(let a=0;a<n.length;a+=1)o=y(o,n[a]);return e=new p({props:o}),{c(){w(e.$$.fragment)},m(a,u){v(e,a,u),t=!0},p(a,u){const r=u&131072?$(n,[ee(a[17])]):{};e.$set(r)},i(a){t||(g(e.$$.fragment,a),t=!0)},o(a){m(e.$$.fragment,a),t=!1},d(a){k(e,a)}}}function ie(i){let e,t,n,o,a,u=i[17]&&V(i);function r(l){i[21](l)}function b(l){i[22](l)}let c={label:i[3],info:i[4],show_label:i[10],lines:i[8],type:i[12],rtl:i[18],text_align:i[19],max_lines:i[11]?i[11]:i[8]+1,placeholder:i[9],show_copy_button:i[16],autofocus:i[20],container:i[13],disabled:!0};return i[0]!==void 0&&(c.value=i[0]),i[1]!==void 0&&(c.value_is_output=i[1]),t=new te({props:c}),N.push(()=>R(t,"value",r)),N.push(()=>R(t,"value_is_output",b)),t.$on("change",i[23]),t.$on("input",i[24]),t.$on("submit",i[25]),t.$on("blur",i[26]),t.$on("select",i[27]),t.$on("focus",i[28]),{c(){u&&u.c(),e=M(),w(t.$$.fragment)},m(l,_){u&&u.m(l,_),O(l,e,_),v(t,l,_),a=!0},p(l,_){l[17]?u?(u.p(l,_),_&131072&&g(u,1)):(u=V(l),u.c(),g(u,1),u.m(e.parentNode,e)):u&&(P(),m(u,1,1,()=>{u=null}),Q());const h={};_&8&&(h.label=l[3]),_&16&&(h.info=l[4]),_&1024&&(h.show_label=l[10]),_&256&&(h.lines=l[8]),_&4096&&(h.type=l[12]),_&262144&&(h.rtl=l[18]),_&524288&&(h.text_align=l[19]),_&2304&&(h.max_lines=l[11]?l[11]:l[8]+1),_&512&&(h.placeholder=l[9]),_&65536&&(h.show_copy_button=l[16]),_&1048576&&(h.autofocus=l[20]),_&8192&&(h.container=l[13]),!n&&_&1&&(n=!0,h.value=l[0],U(()=>n=!1)),!o&&_&2&&(o=!0,h.value_is_output=l[1],U(()=>o=!1)),t.$set(h)},i(l){a||(g(u),g(t.$$.fragment,l),a=!0)},o(l){m(u),m(t.$$.fragment,l),a=!1},d(l){l&&Y(e),u&&u.d(l),k(t,l)}}}function le(i){let e,t;return e=new se({props:{visible:i[7],elem_id:i[5],elem_classes:i[6],scale:i[14],min_width:i[15],allow_overflow:!1,padding:i[13],$$slots:{default:[ie]},$$scope:{ctx:i}}}),{c(){w(e.$$.fragment)},m(n,o){v(e,n,o),t=!0},p(n,[o]){const a={};o&128&&(a.visible=n[7]),o&32&&(a.elem_id=n[5]),o&64&&(a.elem_classes=n[6]),o&16384&&(a.scale=n[14]),o&32768&&(a.min_width=n[15]),o&8192&&(a.padding=n[13]),o&538918687&&(a.$$scope={dirty:o,ctx:n}),e.$set(a)},i(n){t||(g(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function ne(i,e,t){let{gradio:n}=e,{label:o="Textbox"}=e,{info:a=void 0}=e,{elem_id:u=""}=e,{elem_classes:r=[]}=e,{visible:b=!0}=e,{value:c=""}=e,{lines:l}=e,{placeholder:_=""}=e,{show_label:h}=e,{max_lines:x}=e,{type:S="text"}=e,{container:T=!0}=e,{scale:B=null}=e,{min_width:j=void 0}=e,{show_copy_button:q=!1}=e,{loading_status:C=void 0}=e,{value_is_output:d=!1}=e,{rtl:F=!1}=e,{text_align:G=void 0}=e,{autofocus:H=!1}=e;function W(s){c=s,t(0,c)}function X(s){d=s,t(1,d)}const Z=()=>n.dispatch("change",c),z=()=>n.dispatch("input"),A=()=>n.dispatch("submit"),D=()=>n.dispatch("blur"),E=s=>n.dispatch("select",s.detail),I=()=>n.dispatch("focus");return i.$$set=s=>{"gradio"in s&&t(2,n=s.gradio),"label"in s&&t(3,o=s.label),"info"in s&&t(4,a=s.info),"elem_id"in s&&t(5,u=s.elem_id),"elem_classes"in s&&t(6,r=s.elem_classes),"visible"in s&&t(7,b=s.visible),"value"in s&&t(0,c=s.value),"lines"in s&&t(8,l=s.lines),"placeholder"in s&&t(9,_=s.placeholder),"show_label"in s&&t(10,h=s.show_label),"max_lines"in s&&t(11,x=s.max_lines),"type"in s&&t(12,S=s.type),"container"in s&&t(13,T=s.container),"scale"in s&&t(14,B=s.scale),"min_width"in s&&t(15,j=s.min_width),"show_copy_button"in s&&t(16,q=s.show_copy_button),"loading_status"in s&&t(17,C=s.loading_status),"value_is_output"in s&&t(1,d=s.value_is_output),"rtl"in s&&t(18,F=s.rtl),"text_align"in s&&t(19,G=s.text_align),"autofocus"in s&&t(20,H=s.autofocus)},[c,d,n,o,a,u,r,b,l,_,h,x,S,T,B,j,q,C,F,G,H,W,X,Z,z,A,D,E,I]}class ae extends J{constructor(e){super(),K(this,e,ne,le,L,{gradio:2,label:3,info:4,elem_id:5,elem_classes:6,visible:7,value:0,lines:8,placeholder:9,show_label:10,max_lines:11,type:12,container:13,scale:14,min_width:15,show_copy_button:16,loading_status:17,value_is_output:1,rtl:18,text_align:19,autofocus:20})}get gradio(){return this.$$.ctx[2]}set gradio(e){this.$$set({gradio:e}),f()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),f()}get info(){return this.$$.ctx[4]}set info(e){this.$$set({info:e}),f()}get elem_id(){return this.$$.ctx[5]}set elem_id(e){this.$$set({elem_id:e}),f()}get elem_classes(){return this.$$.ctx[6]}set elem_classes(e){this.$$set({elem_classes:e}),f()}get visible(){return this.$$.ctx[7]}set visible(e){this.$$set({visible:e}),f()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),f()}get lines(){return this.$$.ctx[8]}set lines(e){this.$$set({lines:e}),f()}get placeholder(){return this.$$.ctx[9]}set placeholder(e){this.$$set({placeholder:e}),f()}get show_label(){return this.$$.ctx[10]}set show_label(e){this.$$set({show_label:e}),f()}get max_lines(){return this.$$.ctx[11]}set max_lines(e){this.$$set({max_lines:e}),f()}get type(){return this.$$.ctx[12]}set type(e){this.$$set({type:e}),f()}get container(){return this.$$.ctx[13]}set container(e){this.$$set({container:e}),f()}get scale(){return this.$$.ctx[14]}set scale(e){this.$$set({scale:e}),f()}get min_width(){return this.$$.ctx[15]}set min_width(e){this.$$set({min_width:e}),f()}get show_copy_button(){return this.$$.ctx[16]}set show_copy_button(e){this.$$set({show_copy_button:e}),f()}get loading_status(){return this.$$.ctx[17]}set loading_status(e){this.$$set({loading_status:e}),f()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(e){this.$$set({value_is_output:e}),f()}get rtl(){return this.$$.ctx[18]}set rtl(e){this.$$set({rtl:e}),f()}get text_align(){return this.$$.ctx[19]}set text_align(e){this.$$set({text_align:e}),f()}get autofocus(){return this.$$.ctx[20]}set autofocus(e){this.$$set({autofocus:e}),f()}}const re=ae;export{re as default};
//# sourceMappingURL=index-fe24d00c.js.map
