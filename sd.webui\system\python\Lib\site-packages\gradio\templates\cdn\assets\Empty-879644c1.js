import{S as h,e as b,s as k,a8 as z,m as f,g,N as u,h as v,j as C,aa as E,ab as R,ac as B,w as S,u as j,k as q,R as w}from"./index-afe51b5b.js";import"./Button-b4eb936e.js";function N(a){let e,o,n;const _=a[5].default,s=z(_,a,a[4],null);return{c(){e=f("div"),o=f("div"),s&&s.c(),g(o,"class","icon svelte-lk9eg8"),g(e,"class","empty svelte-lk9eg8"),u(e,"small",a[0]==="small"),u(e,"large",a[0]==="large"),u(e,"unpadded_box",a[1]),u(e,"small_parent",a[3])},m(t,i){v(t,e,i),C(e,o),s&&s.m(o,null),a[6](e),n=!0},p(t,[i]){s&&s.p&&(!n||i&16)&&E(s,_,t,t[4],n?B(_,t[4],i,null):R(t[4]),null),(!n||i&1)&&u(e,"small",t[0]==="small"),(!n||i&1)&&u(e,"large",t[0]==="large"),(!n||i&2)&&u(e,"unpadded_box",t[1]),(!n||i&8)&&u(e,"small_parent",t[3])},i(t){n||(S(s,t),n=!0)},o(t){j(s,t),n=!1},d(t){t&&q(e),s&&s.d(t),a[6](null)}}}function A(a,e,o){let n,{$$slots:_={},$$scope:s}=e,{size:t="small"}=e,{unpadded_box:i=!1}=e,r;function m(l){if(!l)return!1;const{height:d}=l.getBoundingClientRect(),{height:c}=l.parentElement?.getBoundingClientRect()||{height:d};return d>c+2}function p(l){w[l?"unshift":"push"](()=>{r=l,o(2,r)})}return a.$$set=l=>{"size"in l&&o(0,t=l.size),"unpadded_box"in l&&o(1,i=l.unpadded_box),"$$scope"in l&&o(4,s=l.$$scope)},a.$$.update=()=>{a.$$.dirty&4&&o(3,n=m(r))},[t,i,r,n,s,_,p]}class G extends h{constructor(e){super(),b(this,e,A,N,k,{size:0,unpadded_box:1})}}export{G as E};
//# sourceMappingURL=Empty-879644c1.js.map
