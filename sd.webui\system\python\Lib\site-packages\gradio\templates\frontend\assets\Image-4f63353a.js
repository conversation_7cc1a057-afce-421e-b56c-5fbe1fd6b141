import{S as p,e as g,s as d,f as n,g as e,h as f,j as i,n as l,k as m}from"./index-2519a27e.js";function u(c){let t,r,s,o;return{c(){t=n("svg"),r=n("rect"),s=n("circle"),o=n("polyline"),e(r,"x","3"),e(r,"y","3"),e(r,"width","18"),e(r,"height","18"),e(r,"rx","2"),e(r,"ry","2"),e(s,"cx","8.5"),e(s,"cy","8.5"),e(s,"r","1.5"),e(o,"points","21 15 16 10 5 21"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","100%"),e(t,"height","100%"),e(t,"viewBox","0 0 24 24"),e(t,"fill","none"),e(t,"stroke","currentColor"),e(t,"stroke-width","1.5"),e(t,"stroke-linecap","round"),e(t,"stroke-linejoin","round"),e(t,"class","feather feather-image")},m(a,h){f(a,t,h),i(t,r),i(t,s),i(t,o)},p:l,i:l,o:l,d(a){a&&m(t)}}}class x extends p{constructor(t){super(),g(this,t,null,u,d,{})}}export{x as I};
//# sourceMappingURL=Image-4f63353a.js.map
