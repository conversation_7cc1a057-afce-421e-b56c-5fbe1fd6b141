{"version": 3, "file": "index-0261d910.js", "sources": ["../../../../js/timeseries/static/StaticTimeseries.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { Block, BlockLabel, Empty } from \"@gradio/atoms\";\n\timport Chart from \"../shared\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { _ } from \"svelte-i18n\";\n\n\timport { Chart as ChartIcon } from \"@gradio/icons\";\n\n\tfunction format_value(val: StaticData): any {\n\t\treturn val.data.map((r) =>\n\t\t\tr.reduce((acc, next, i) => ({ ...acc, [val.headers[i]]: next }), {})\n\t\t);\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tclear: undefined;\n\t}>();\n\n\tinterface StaticData {\n\t\tdata: number[][];\n\t\theaders: string[];\n\t}\n\tinterface Data {\n\t\tdata: number[][] | string;\n\t\theaders?: string[];\n\t}\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: null | Data;\n\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let colors: string[];\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\n\t$: static_data = value && format_value(value as StaticData);\n\n\t$: value, dispatch(\"change\");\n</script>\n\n<Block\n\t{visible}\n\tvariant={\"solid\"}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n>\n\t<BlockLabel {show_label} Icon={ChartIcon} label={label || \"TimeSeries\"} />\n\t<StatusTracker {...loading_status} />\n\n\t{#if static_data}\n\t\t<Chart value={static_data} {colors} />\n\t{:else}\n\t\t<Empty unpadded_box={true} size=\"large\">\n\t\t\t<ChartIcon />\n\t\t</Empty>\n\t{/if}\n</Block>\n"], "names": ["ctx", "ChartIcon", "dirty", "blocklabel_changes", "format_value", "val", "r", "acc", "next", "i", "dispatch", "createEventDispatcher", "elem_id", "$$props", "elem_classes", "visible", "value", "label", "show_label", "colors", "container", "scale", "min_width", "loading_status", "static_data"], "mappings": "2fAgEuB,ySAFPA,EAAW,EAAA,iGAAXA,EAAW,EAAA,+VAJKC,EAAkB,MAAAD,MAAS,yBACvCA,EAAc,CAAA,CAAA,gHAE5BA,EAAW,EAAA,EAAA,mNAHiCE,EAAA,IAAAC,EAAA,MAAAH,MAAS,6CACvCA,EAAc,CAAA,CAAA,CAAA,CAAA,iYATxB,gBACA,mcAzCA,SAAAI,EAAaC,EAAe,QAC7BA,EAAI,KAAK,IAAKC,GACpBA,EAAE,OAAQ,CAAAC,EAAKC,EAAMC,KAAC,CAAA,GAAWF,EAAG,CAAGF,EAAI,QAAQI,CAAC,CAAA,EAAID,CAAI,GAAA,CAAA,CAAA,CAAA,0BAIxD,MAAAE,EAAWC,IAcN,GAAA,CAAA,QAAAC,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,GACd,MAAAG,CAAkB,EAAAH,GAElB,MAAAI,CAAa,EAAAJ,GACb,WAAAK,CAAmB,EAAAL,GACnB,OAAAM,CAAgB,EAAAN,EAChB,CAAA,UAAAO,EAAY,EAAI,EAAAP,EAChB,CAAA,MAAAQ,EAAuB,IAAI,EAAAR,EAC3B,CAAA,UAAAS,EAAgC,MAAS,EAAAT,GACzC,eAAAU,CAA6B,EAAAV,sbAErCW,EAAcR,GAASZ,EAAaY,CAAmB,CAAA,mBAEhDN,EAAS,QAAQ"}