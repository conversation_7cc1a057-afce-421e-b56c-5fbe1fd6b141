{"version": 3, "file": "Player-abcfefb3.js", "sources": ["../../../../js/icons/src/Maximise.svelte", "../../../../js/icons/src/Pause.svelte", "../../../../js/icons/src/Play.svelte", "../../../../js/icons/src/Video.svelte", "../../../../js/video/shared/utils.ts", "../../../../js/video/shared/Player.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n>\n\t<path\n\t\td=\"M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3\"\n\t/>\n</svg>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n>\n\t<rect x=\"6\" y=\"4\" width=\"4\" height=\"16\" />\n\t<rect x=\"14\" y=\"4\" width=\"4\" height=\"16\" />\n</svg>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n>\n\t<polygon points=\"5 3 19 12 5 21 5 3\" />\n</svg>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-video\"\n>\n\t<polygon points=\"23 7 16 12 23 17 23 7\" />\n\t<rect x=\"1\" y=\"5\" width=\"15\" height=\"14\" rx=\"2\" ry=\"2\" />\n</svg>\n", "import type { ActionReturn } from \"svelte/action\";\n\nexport const prettyBytes = (bytes: number): string => {\n\tlet units = [\"B\", \"KB\", \"MB\", \"GB\", \"PB\"];\n\tlet i = 0;\n\twhile (bytes > 1024) {\n\t\tbytes /= 1024;\n\t\ti++;\n\t}\n\tlet unit = units[i];\n\treturn bytes.toFixed(1) + \" \" + unit;\n};\n\nexport const playable = (): boolean => {\n\t// TODO: Fix this\n\t// let video_element = document.createElement(\"video\");\n\t// let mime_type = mime.lookup(filename);\n\t// return video_element.canPlayType(mime_type) != \"\";\n\treturn true; // FIX BEFORE COMMIT - mime import causing issues\n};\n\nexport function loaded(\n\tnode: HTMLVideoElement,\n\t{ autoplay }: { autoplay: boolean }\n): ActionReturn {\n\tasync function handle_playback(): Promise<void> {\n\t\tif (!autoplay) return;\n\t\tawait node.play();\n\t}\n\n\tnode.addEventListener(\"loadeddata\", handle_playback);\n\n\treturn {\n\t\tdestroy(): void {\n\t\t\tnode.removeEventListener(\"loadeddata\", handle_playback);\n\t\t}\n\t};\n}\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { Play, Pause, <PERSON>ise, Undo } from \"@gradio/icons\";\n\timport { loaded } from \"./utils\";\n\n\texport let src: string;\n\texport let subtitle: string | null = null;\n\texport let mirror: boolean;\n\texport let autoplay: boolean;\n\texport let label = \"test\";\n\n\tconst dispatch = createEventDispatcher<{\n\t\tplay: undefined;\n\t\tpause: undefined;\n\t\tstop: undefined;\n\t\tend: undefined;\n\t}>();\n\n\tlet time = 0;\n\tlet duration: number;\n\tlet paused = true;\n\tlet video: HTMLVideoElement;\n\n\tfunction handleMove(e: TouchEvent | MouseEvent): void {\n\t\tif (!duration) return;\n\n\t\tif (e.type === \"click\") {\n\t\t\thandle_click(e as MouseEvent);\n\t\t\treturn;\n\t\t}\n\n\t\tif (e.type !== \"touchmove\" && !((e as MouseEvent).buttons & 1)) return;\n\n\t\tconst clientX =\n\t\t\te.type === \"touchmove\"\n\t\t\t\t? (e as TouchEvent).touches[0].clientX\n\t\t\t\t: (e as MouseEvent).clientX;\n\t\tconst { left, right } = (\n\t\t\te.currentTarget as HTMLProgressElement\n\t\t).getBoundingClientRect();\n\t\ttime = (duration * (clientX - left)) / (right - left);\n\t}\n\n\tasync function play_pause(): Promise<void> {\n\t\tif (document.fullscreenElement != video) {\n\t\t\tconst isPlaying =\n\t\t\t\tvideo.currentTime > 0 &&\n\t\t\t\t!video.paused &&\n\t\t\t\t!video.ended &&\n\t\t\t\tvideo.readyState > video.HAVE_CURRENT_DATA;\n\n\t\t\tif (!isPlaying) {\n\t\t\t\tawait video.play();\n\t\t\t} else video.pause();\n\t\t}\n\t}\n\n\tfunction handle_click(e: MouseEvent): void {\n\t\tconst { left, right } = (\n\t\t\te.currentTarget as HTMLProgressElement\n\t\t).getBoundingClientRect();\n\t\ttime = (duration * (e.clientX - left)) / (right - left);\n\t}\n\n\tfunction format(seconds: number): string {\n\t\tif (isNaN(seconds) || !isFinite(seconds)) return \"...\";\n\n\t\tconst minutes = Math.floor(seconds / 60);\n\t\tlet _seconds: number | string = Math.floor(seconds % 60);\n\t\tif (_seconds < 10) _seconds = `0${_seconds}`;\n\n\t\treturn `${minutes}:${_seconds}`;\n\t}\n\n\tfunction handle_end(): void {\n\t\tdispatch(\"stop\");\n\t\tdispatch(\"end\");\n\t}\n\n\tfunction open_full_screen(): void {\n\t\tvideo.requestFullscreen();\n\t}\n</script>\n\n<div class=\"wrap\">\n\t<video\n\t\t{src}\n\t\tpreload=\"auto\"\n\t\ton:click={play_pause}\n\t\ton:play\n\t\ton:pause\n\t\ton:ended={handle_end}\n\t\tbind:currentTime={time}\n\t\tbind:duration\n\t\tbind:paused\n\t\tbind:this={video}\n\t\tclass:mirror\n\t\tuse:loaded={{ autoplay }}\n\t\tdata-testid={`${label}-player`}\n\t>\n\t\t<track kind=\"captions\" src={subtitle} default />\n\t</video>\n\n\t<div class=\"controls\">\n\t\t<div class=\"inner\">\n\t\t\t<span\n\t\t\t\trole=\"button\"\n\t\t\t\ttabindex=\"0\"\n\t\t\t\tclass=\"icon\"\n\t\t\t\taria-label=\"play-pause-replay-button\"\n\t\t\t\ton:click={play_pause}\n\t\t\t\ton:keydown={play_pause}\n\t\t\t>\n\t\t\t\t{#if time === duration}\n\t\t\t\t\t<Undo />\n\t\t\t\t{:else if paused}\n\t\t\t\t\t<Play />\n\t\t\t\t{:else}\n\t\t\t\t\t<Pause />\n\t\t\t\t{/if}\n\t\t\t</span>\n\n\t\t\t<span class=\"time\">{format(time)} / {format(duration)}</span>\n\n\t\t\t<!-- TODO: implement accessible video timeline for 4.0 -->\n\t\t\t<!-- svelte-ignore a11y-click-events-have-key-events -->\n\t\t\t<!-- svelte-ignore a11y-no-noninteractive-element-interactions -->\n\t\t\t<progress\n\t\t\t\tvalue={time / duration || 0}\n\t\t\t\ton:mousemove={handleMove}\n\t\t\t\ton:touchmove|preventDefault={handleMove}\n\t\t\t\ton:click|stopPropagation|preventDefault={handle_click}\n\t\t\t/>\n\n\t\t\t<div\n\t\t\t\trole=\"button\"\n\t\t\t\ttabindex=\"0\"\n\t\t\t\tclass=\"icon\"\n\t\t\t\taria-label=\"full-screen\"\n\t\t\t\ton:click={open_full_screen}\n\t\t\t\ton:keypress={open_full_screen}\n\t\t\t>\n\t\t\t\t<Maximise />\n\t\t\t</div>\n\t\t</div>\n\t</div>\n</div>\n\n<style lang=\"postcss\">\n\tspan {\n\t\ttext-shadow: 0 0 8px rgba(0, 0, 0, 0.5);\n\t}\n\n\tprogress {\n\t\tmargin-right: var(--size-3);\n\t\tborder-radius: var(--radius-sm);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-2);\n\t}\n\n\tprogress::-webkit-progress-bar {\n\t\tborder-radius: 2px;\n\t\tbackground-color: rgba(255, 255, 255, 0.2);\n\t\toverflow: hidden;\n\t}\n\n\tprogress::-webkit-progress-value {\n\t\tbackground-color: rgba(255, 255, 255, 0.9);\n\t}\n\n\tvideo {\n\t\tposition: inherit;\n\t\tbackground-color: black;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: contain;\n\t}\n\n\t.mirror {\n\t\ttransform: scaleX(-1);\n\t}\n\n\t.controls {\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\topacity: 0;\n\t\ttransition: 500ms;\n\t\tmargin: var(--size-2);\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--color-grey-800);\n\t\tpadding: var(--size-2) var(--size-1);\n\t\twidth: calc(100% - 0.375rem * 2);\n\t\twidth: calc(100% - var(--size-2) * 2);\n\t}\n\t.wrap:hover .controls {\n\t\topacity: 1;\n\t}\n\n\t.inner {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding-right: var(--size-2);\n\t\tpadding-left: var(--size-2);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t.icon {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tcursor: pointer;\n\t\twidth: var(--size-6);\n\t\tcolor: white;\n\t}\n\n\t.time {\n\t\tflex-shrink: 0;\n\t\tmargin-right: var(--size-3);\n\t\tmargin-left: var(--size-3);\n\t\tcolor: white;\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t}\n\t.wrap {\n\t\tposition: relative;\n\t\tbackground-color: var(--background-fill-secondary);\n\t\theight: var(--size-full);\n\t\twidth: var(--size-full);\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "rect0", "rect1", "polygon", "rect", "prettyBytes", "bytes", "units", "i", "unit", "playable", "loaded", "node", "autoplay", "handle_playback", "t2_value", "format", "ctx", "t4_value", "attr", "track", "track_src_value", "div3", "video_1", "div2", "div1", "span0", "span1", "progress", "div0", "action_destroyer", "loaded_action", "is_function", "dirty", "current", "set_data", "t2", "t4", "seconds", "minutes", "_seconds", "src", "$$props", "subtitle", "mirror", "label", "dispatch", "createEventDispatcher", "time", "duration", "paused", "video", "handleMove", "e", "handle_click", "clientX", "left", "right", "play_pause", "handle_end", "open_full_screen", "$$value"], "mappings": "qyBAAAA,EAcKC,EAAAC,EAAAC,CAAA,EAHJC,EAECF,EAAAG,CAAA,iiBCbFL,EAaKC,EAAAC,EAAAC,CAAA,EAFJC,EAAyCF,EAAAI,CAAA,EACzCF,EAA0CF,EAAAK,CAAA,4bCZ3CP,EAYKC,EAAAC,EAAAC,CAAA,EADJC,EAAsCF,EAAAM,CAAA,6kBCXvCR,EAcKC,EAAAC,EAAAC,CAAA,EAFJC,EAAyCF,EAAAM,CAAA,EACzCJ,EAAwDF,EAAAO,CAAA,gGCX5C,MAAAC,GAAeC,GAA0B,CACrD,IAAIC,EAAQ,CAAC,IAAK,KAAM,KAAM,KAAM,IAAI,EACpCC,EAAI,EACR,KAAOF,EAAQ,MACLA,GAAA,KACTE,IAEG,IAAAC,EAAOF,EAAMC,CAAC,EAClB,OAAOF,EAAM,QAAQ,CAAC,EAAI,IAAMG,CACjC,EAEaC,GAAW,IAKhB,GAGD,SAASC,GACfC,EACA,CAAE,SAAAC,GACa,CACf,eAAeC,GAAiC,CAC1CD,GACL,MAAMD,EAAK,MACZ,CAEK,OAAAA,EAAA,iBAAiB,aAAcE,CAAe,EAE5C,CACN,SAAgB,CACVF,EAAA,oBAAoB,aAAcE,CAAe,CACvD,CAAA,CAEF,skBCqFuBC,EAAAC,EAAOC,EAAI,CAAA,CAAA,EAAA,OAAMC,EAAAF,EAAOC,EAAQ,CAAA,CAAA,EAAA,gJAT9C,OAAAA,OAASA,EAAQ,CAAA,EAAA,EAEZA,EAAM,CAAA,EAAA,uKAOgB,KAAG,mGAtBTA,EAAQ,CAAA,CAAA,GAAAE,EAAAC,EAAA,MAAAC,CAAA,0FAFpBJ,EAAK,CAAA,UAAA,4PA8BZA,EAAI,CAAA,EAAGA,EAAQ,CAAA,GAAI,6PA5C9BtB,EA8DKC,EAAA0B,EAAAxB,CAAA,EA7DJC,EAgBOuB,EAAAC,CAAA,EADNxB,EAA+CwB,EAAAH,CAAA,kBAGhDrB,EA0CKuB,EAAAE,CAAA,EAzCJzB,EAwCKyB,EAAAC,CAAA,EAvCJ1B,EAeM0B,EAAAC,CAAA,wBAEN3B,EAA4D0B,EAAAE,CAAA,8BAK5D5B,EAKC0B,EAAAG,CAAA,SAED7B,EASK0B,EAAAI,CAAA,uCAvDIZ,EAAU,EAAA,CAAA,mDAGVA,EAAU,EAAA,CAAA,wFAMNa,GAAAC,EAAApB,GAAA,KAAA,KAAAY,EAAA,CAAA,SAAAN,EAAQ,CAAA,CAAA,CAAA,CAAA,cAaVA,EAAU,EAAA,CAAA,gBACRA,EAAU,EAAA,CAAA,kBAkBRA,EAAU,CAAA,CAAA,qBACKA,EAAU,CAAA,CAAA,CAAA,oBACEA,EAAY,EAAA,CAAA,CAAA,CAAA,cAQ3CA,EAAgB,EAAA,CAAA,iBACbA,EAAgB,EAAA,CAAA,wCAxCHA,EAAQ,CAAA,CAAA,gFAFpBA,EAAK,CAAA,iDANHA,EAAI,CAAA,CAAA,kBAAJA,EAAI,CAAA,mDAKRc,GAAAC,GAAAD,EAAA,MAAA,GAAAE,EAAA,GAAAF,EAAA,OAAA,KAAA,KAAA,CAAA,SAAAd,EAAQ,CAAA,CAAA,CAAA,0JAyBD,CAAAiB,GAAAD,EAAA,KAAAlB,KAAAA,EAAAC,EAAOC,EAAI,CAAA,CAAA,EAAA,KAAAkB,GAAAC,EAAArB,CAAA,GAAM,CAAAmB,GAAAD,EAAA,KAAAf,KAAAA,EAAAF,EAAOC,EAAQ,CAAA,CAAA,EAAA,KAAAkB,GAAAE,EAAAnB,CAAA,oBAM5CD,EAAI,CAAA,EAAGA,EAAQ,CAAA,GAAI,kJAhEpB,SAAAD,EAAOsB,EAAe,CAC1B,GAAA,MAAMA,CAAO,GAAA,CAAM,SAASA,CAAO,QAAU,MAE3C,MAAAC,EAAU,KAAK,MAAMD,EAAU,EAAE,EACnC,IAAAE,EAA4B,KAAK,MAAMF,EAAU,EAAE,EACnD,OAAAE,EAAW,KAAIA,MAAeA,KAExB,GAAAD,KAAWC,2BAlEX,IAAAC,CAAW,EAAAC,EACX,CAAA,SAAAC,EAA0B,IAAI,EAAAD,GAC9B,OAAAE,CAAe,EAAAF,GACf,SAAA7B,CAAiB,EAAA6B,EACjB,CAAA,MAAAG,EAAQ,MAAM,EAAAH,EAEnB,MAAAI,EAAWC,KAOb,IAAAC,EAAO,EACPC,EACAC,EAAS,GACTC,EAEK,SAAAC,EAAWC,EAA0B,KACxCJ,EAAQ,UAETI,EAAE,OAAS,QAAO,CACrBC,EAAaD,CAAe,YAIzBA,EAAE,OAAS,eAAkBA,EAAiB,QAAU,GAAC,OAEvD,MAAAE,EACLF,EAAE,OAAS,YACPA,EAAiB,QAAQ,CAAC,EAAE,QAC5BA,EAAiB,QACd,CAAA,KAAAG,EAAM,MAAAC,CAAK,EAClBJ,EAAE,cACD,4BACFL,EAAQC,GAAYM,EAAUC,IAAUC,EAAQD,EAAI,iBAGtCE,GAAU,CACpB,SAAS,mBAAqBP,IAEhCA,EAAM,YAAc,GAAC,CACpBA,EAAM,SACNA,EAAM,OACPA,EAAM,WAAaA,EAAM,kBAInBA,EAAM,QADN,MAAAA,EAAM,QAKN,SAAAG,EAAaD,EAAa,CAC1B,KAAA,CAAA,KAAAG,EAAM,MAAAC,CAAK,EAClBJ,EAAE,cACD,4BACFL,EAAQC,GAAYI,EAAE,QAAUG,IAAUC,EAAQD,EAAI,WAa9CG,GAAU,CAClBb,EAAS,MAAM,EACfA,EAAS,KAAK,WAGNc,GAAgB,CACxBT,EAAM,kBAAiB,+EAYLH,EAAI,KAAA,mIAGXG,EAAKU"}