#!/usr/bin/env python3
"""
簡単なStable Diffusion画像生成スクリプト
"""

import torch
from diffusers import StableDiffusionPipeline
import os
from datetime import datetime

def generate_image(prompt, output_dir="../outputs", model_id="runwayml/stable-diffusion-v1-5"):
    """
    テキストプロンプトから画像を生成します
    
    Args:
        prompt (str): 画像生成のためのテキストプロンプト
        output_dir (str): 出力ディレクトリ
        model_id (str): 使用するStable Diffusionモデル
    """
    
    print(f"プロンプト: {prompt}")
    print(f"モデル: {model_id}")
    
    # 出力ディレクトリの作成
    os.makedirs(output_dir, exist_ok=True)
    
    # デバイスの設定
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"使用デバイス: {device}")
    
    try:
        # パイプラインの読み込み
        print("モデルを読み込み中...")
        pipe = StableDiffusionPipeline.from_pretrained(
            model_id,
            torch_dtype=torch.float16 if device == "cuda" else torch.float32,
            safety_checker=None,  # 安全性チェッカーを無効化（オプション）
            requires_safety_checker=False
        )
        pipe = pipe.to(device)
        
        # メモリ効率化（CPUの場合）
        if device == "cpu":
            pipe.enable_attention_slicing()
        
        print("画像を生成中...")
        
        # 画像生成
        with torch.autocast(device):
            image = pipe(
                prompt,
                num_inference_steps=20,  # 推論ステップ数（少なくして高速化）
                guidance_scale=7.5,      # ガイダンススケール
                height=512,              # 画像の高さ
                width=512                # 画像の幅
            ).images[0]
        
        # ファイル名の生成
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"generated_{timestamp}.png"
        filepath = os.path.join(output_dir, filename)
        
        # 画像の保存
        image.save(filepath)
        print(f"画像を保存しました: {filepath}")
        
        return filepath
        
    except Exception as e:
        print(f"エラーが発生しました: {e}")
        return None

def main():
    """メイン関数"""
    print("=== Simple Stable Diffusion Generator ===\n")
    
    # デフォルトのプロンプト
    default_prompt = "a beautiful young woman with long flowing hair, portrait, soft lighting, detailed face, photorealistic, high quality"
    
    print("画像生成を開始します...")
    print("注意: 初回実行時はモデルのダウンロードに時間がかかります。")
    print()
    
    # プロンプトの入力
    prompt = input(f"プロンプトを入力してください (Enter でデフォルト使用): ").strip()
    if not prompt:
        prompt = default_prompt
    
    # 画像生成
    result = generate_image(prompt)
    
    if result:
        print(f"\n✅ 画像生成が完了しました!")
        print(f"ファイル: {result}")
    else:
        print("\n❌ 画像生成に失敗しました。")

if __name__ == "__main__":
    main()
