{"txt2img/Prompt/visible": true, "txt2img/Prompt/value": "", "txt2img/Negative prompt/visible": true, "txt2img/Negative prompt/value": "", "txt2img/Interrupt/visible": true, "txt2img/Skip/visible": true, "txt2img/Interrupting.../visible": true, "txt2img/Generate/visible": true, "txt2img/↙️/visible": true, "txt2img/🗑️/visible": true, "txt2img/📋/visible": true, "txt2img/Styles/visible": true, "txt2img/Styles/value": [], "txt2img/🖌️/visible": true, "txt2img/🔄/visible": true, "txt2img/📝/visible": true, "txt2img/Close/visible": true, "txt2img/Tabs@txt2img_extra_tabs/selected": null, "customscript/sampler.py/txt2img/Sampling method/visible": true, "customscript/sampler.py/txt2img/Sampling method/value": "DPM++ 2M", "customscript/sampler.py/txt2img/Schedule type/visible": true, "customscript/sampler.py/txt2img/Schedule type/value": "Automatic", "customscript/sampler.py/txt2img/Sampling steps/visible": true, "customscript/sampler.py/txt2img/Sampling steps/value": 20, "customscript/sampler.py/txt2img/Sampling steps/minimum": 1, "customscript/sampler.py/txt2img/Sampling steps/maximum": 150, "customscript/sampler.py/txt2img/Sampling steps/step": 1, "txt2img/Hires. fix/visible": true, "txt2img/Hires. fix/value": false, "txt2img/Upscaler/visible": true, "txt2img/Upscaler/value": "Latent", "txt2img/Hires steps/visible": true, "txt2img/Hires steps/value": 0, "txt2img/Hires steps/minimum": 0, "txt2img/Hires steps/maximum": 150, "txt2img/Hires steps/step": 1, "txt2img/Denoising strength/visible": true, "txt2img/Denoising strength/value": 0.7, "txt2img/Denoising strength/minimum": 0.0, "txt2img/Denoising strength/maximum": 1.0, "txt2img/Denoising strength/step": 0.01, "txt2img/Upscale by/visible": true, "txt2img/Upscale by/value": 2.0, "txt2img/Upscale by/minimum": 1.0, "txt2img/Upscale by/maximum": 4.0, "txt2img/Upscale by/step": 0.05, "txt2img/Resize width to/visible": true, "txt2img/Resize width to/value": 0, "txt2img/Resize width to/minimum": 0, "txt2img/Resize width to/maximum": 2048, "txt2img/Resize width to/step": 8, "txt2img/Resize height to/visible": true, "txt2img/Resize height to/value": 0, "txt2img/Resize height to/minimum": 0, "txt2img/Resize height to/maximum": 2048, "txt2img/Resize height to/step": 8, "txt2img/Checkpoint/visible": true, "txt2img/Checkpoint/value": "Use same checkpoint", "txt2img/Hires sampling method/visible": true, "txt2img/Hires sampling method/value": "Use same sampler", "txt2img/Hires schedule type/visible": true, "txt2img/Hires schedule type/value": "Use same scheduler", "txt2img/Hires prompt/visible": true, "txt2img/Hires prompt/value": "", "txt2img/Hires negative prompt/visible": true, "txt2img/Hires negative prompt/value": "", "customscript/refiner.py/txt2img/Refiner/visible": true, "customscript/refiner.py/txt2img/Refiner/value": false, "customscript/refiner.py/txt2img/Checkpoint/visible": true, "customscript/refiner.py/txt2img/Checkpoint/value": "", "customscript/refiner.py/txt2img/Switch at/visible": true, "customscript/refiner.py/txt2img/Switch at/value": 0.8, "customscript/refiner.py/txt2img/Switch at/minimum": 0.01, "customscript/refiner.py/txt2img/Switch at/maximum": 1.0, "customscript/refiner.py/txt2img/Switch at/step": 0.01, "txt2img/Width/visible": true, "txt2img/Width/value": 512, "txt2img/Width/minimum": 64, "txt2img/Width/maximum": 2048, "txt2img/Width/step": 8, "txt2img/Height/visible": true, "txt2img/Height/value": 512, "txt2img/Height/minimum": 64, "txt2img/Height/maximum": 2048, "txt2img/Height/step": 8, "txt2img/⇅/visible": true, "txt2img/Batch count/visible": true, "txt2img/Batch count/value": 1, "txt2img/Batch count/minimum": 1, "txt2img/Batch count/maximum": 100, "txt2img/Batch count/step": 1, "txt2img/Batch size/visible": true, "txt2img/Batch size/value": 1, "txt2img/Batch size/minimum": 1, "txt2img/Batch size/maximum": 8, "txt2img/Batch size/step": 1, "txt2img/CFG Scale/visible": true, "txt2img/CFG Scale/value": 7.0, "txt2img/CFG Scale/minimum": 1.0, "txt2img/CFG Scale/maximum": 30.0, "txt2img/CFG Scale/step": 0.5, "customscript/seed.py/txt2img/Seed/visible": true, "customscript/seed.py/txt2img/Seed/value": -1, "txt2img/🎲️/visible": true, "txt2img/♻️/visible": true, "customscript/seed.py/txt2img/Extra/visible": true, "customscript/seed.py/txt2img/Extra/value": false, "customscript/seed.py/txt2img/Variation seed/visible": true, "customscript/seed.py/txt2img/Variation seed/value": -1, "customscript/seed.py/txt2img/Variation strength/visible": true, "customscript/seed.py/txt2img/Variation strength/value": 0.0, "customscript/seed.py/txt2img/Variation strength/minimum": 0, "customscript/seed.py/txt2img/Variation strength/maximum": 1, "customscript/seed.py/txt2img/Variation strength/step": 0.01, "customscript/seed.py/txt2img/Resize seed from width/visible": true, "customscript/seed.py/txt2img/Resize seed from width/value": 0, "customscript/seed.py/txt2img/Resize seed from width/minimum": 0, "customscript/seed.py/txt2img/Resize seed from width/maximum": 2048, "customscript/seed.py/txt2img/Resize seed from width/step": 8, "customscript/seed.py/txt2img/Resize seed from height/visible": true, "customscript/seed.py/txt2img/Resize seed from height/value": 0, "customscript/seed.py/txt2img/Resize seed from height/minimum": 0, "customscript/seed.py/txt2img/Resize seed from height/maximum": 2048, "customscript/seed.py/txt2img/Resize seed from height/step": 8, "txt2img/Override settings/value": null, "txt2img/Script/visible": true, "txt2img/Script/value": "None", "customscript/prompt_matrix.py/txt2img/Put variable parts at start of prompt/visible": true, "customscript/prompt_matrix.py/txt2img/Put variable parts at start of prompt/value": false, "customscript/prompt_matrix.py/txt2img/Use different seed for each picture/visible": true, "customscript/prompt_matrix.py/txt2img/Use different seed for each picture/value": false, "customscript/prompt_matrix.py/txt2img/Select prompt/visible": true, "customscript/prompt_matrix.py/txt2img/Select prompt/value": "positive", "customscript/prompt_matrix.py/txt2img/Select joining char/visible": true, "customscript/prompt_matrix.py/txt2img/Select joining char/value": "comma", "customscript/prompt_matrix.py/txt2img/Grid margins (px)/visible": true, "customscript/prompt_matrix.py/txt2img/Grid margins (px)/value": 0, "customscript/prompt_matrix.py/txt2img/Grid margins (px)/minimum": 0, "customscript/prompt_matrix.py/txt2img/Grid margins (px)/maximum": 500, "customscript/prompt_matrix.py/txt2img/Grid margins (px)/step": 2, "customscript/prompts_from_file.py/txt2img/Iterate seed every line/visible": true, "customscript/prompts_from_file.py/txt2img/Iterate seed every line/value": false, "customscript/prompts_from_file.py/txt2img/Use same random seed for all lines/visible": true, "customscript/prompts_from_file.py/txt2img/Use same random seed for all lines/value": false, "customscript/prompts_from_file.py/txt2img/Insert prompts at the/visible": true, "customscript/prompts_from_file.py/txt2img/Insert prompts at the/value": "start", "customscript/prompts_from_file.py/txt2img/List of prompt inputs/visible": true, "customscript/prompts_from_file.py/txt2img/List of prompt inputs/value": "", "customscript/xyz_grid.py/txt2img/X type/visible": true, "customscript/xyz_grid.py/txt2img/X type/value": "Seed", "customscript/xyz_grid.py/txt2img/X values/visible": true, "customscript/xyz_grid.py/txt2img/X values/value": "", "customscript/xyz_grid.py/txt2img/Y type/visible": true, "customscript/xyz_grid.py/txt2img/Y type/value": "Nothing", "customscript/xyz_grid.py/txt2img/Y values/visible": true, "customscript/xyz_grid.py/txt2img/Y values/value": "", "customscript/xyz_grid.py/txt2img/Z type/visible": true, "customscript/xyz_grid.py/txt2img/Z type/value": "Nothing", "customscript/xyz_grid.py/txt2img/Z values/visible": true, "customscript/xyz_grid.py/txt2img/Z values/value": "", "customscript/xyz_grid.py/txt2img/Draw legend/visible": true, "customscript/xyz_grid.py/txt2img/Draw legend/value": true, "customscript/xyz_grid.py/txt2img/Keep -1 for seeds/visible": true, "customscript/xyz_grid.py/txt2img/Keep -1 for seeds/value": false, "customscript/xyz_grid.py/txt2img/Vary seeds for X/visible": true, "customscript/xyz_grid.py/txt2img/Vary seeds for X/value": false, "customscript/xyz_grid.py/txt2img/Vary seeds for Y/visible": true, "customscript/xyz_grid.py/txt2img/Vary seeds for Y/value": false, "customscript/xyz_grid.py/txt2img/Vary seeds for Z/visible": true, "customscript/xyz_grid.py/txt2img/Vary seeds for Z/value": false, "customscript/xyz_grid.py/txt2img/Include Sub Images/visible": true, "customscript/xyz_grid.py/txt2img/Include Sub Images/value": false, "customscript/xyz_grid.py/txt2img/Include Sub Grids/visible": true, "customscript/xyz_grid.py/txt2img/Include Sub Grids/value": false, "customscript/xyz_grid.py/txt2img/Use text inputs instead of dropdowns/visible": true, "customscript/xyz_grid.py/txt2img/Use text inputs instead of dropdowns/value": false, "customscript/xyz_grid.py/txt2img/Grid margins (px)/visible": true, "customscript/xyz_grid.py/txt2img/Grid margins (px)/value": 0, "customscript/xyz_grid.py/txt2img/Grid margins (px)/minimum": 0, "customscript/xyz_grid.py/txt2img/Grid margins (px)/maximum": 500, "customscript/xyz_grid.py/txt2img/Grid margins (px)/step": 2, "txt2img/Swap X/Y axes/visible": true, "txt2img/Swap Y/Z axes/visible": true, "txt2img/Swap X/Z axes/visible": true, "txt2img/📂/visible": true, "txt2img/💾/visible": true, "txt2img/🗃️/visible": true, "txt2img/🖼️/visible": true, "txt2img/🎨️/visible": true, "txt2img/📐/visible": true, "txt2img/✨/visible": true, "txt2img/Description/visible": true, "txt2img/Description/value": "", "txt2img/Cancel/visible": true, "txt2img/Replace preview/visible": true, "txt2img/Save/visible": true, "txt2img/Preferred VAE/visible": true, "txt2img/Preferred VAE/value": "None", "txt2img/Stable Diffusion version/visible": true, "txt2img/Stable Diffusion version/value": "Unknown", "txt2img/Activation text/visible": true, "txt2img/Activation text/value": "", "txt2img/Preferred weight/visible": true, "txt2img/Preferred weight/value": 0.0, "txt2img/Preferred weight/minimum": 0.0, "txt2img/Preferred weight/maximum": 2.0, "txt2img/Preferred weight/step": 0.01, "txt2img/Random prompt/visible": true, "txt2img/Random prompt/value": "", "img2img/Prompt/visible": true, "img2img/Prompt/value": "", "img2img/Negative prompt/visible": true, "img2img/Negative prompt/value": "", "img2img/Interrupt/visible": true, "img2img/Skip/visible": true, "img2img/Interrupting.../visible": true, "img2img/Generate/visible": true, "img2img/↙️/visible": true, "img2img/🗑️/visible": true, "img2img/📋/visible": true, "img2img/📎/visible": true, "img2img/📦/visible": true, "img2img/Styles/visible": true, "img2img/Styles/value": [], "img2img/🖌️/visible": true, "img2img/🔄/visible": true, "img2img/📝/visible": true, "img2img/Close/visible": true, "img2img/Tabs@img2img_extra_tabs/selected": null, "img2img/Tabs@mode_img2img/selected": null, "img2img/img2img/visible": true, "img2img/sketch/visible": true, "img2img/inpaint/visible": true, "img2img/inpaint sketch/visible": true, "img2img/Tabs@img2img_batch_source/selected": null, "img2img/Input directory/visible": true, "img2img/Input directory/value": "", "img2img/Output directory/visible": true, "img2img/Output directory/value": "", "img2img/Inpaint batch mask directory (required for inpaint batch processing only)/visible": true, "img2img/Inpaint batch mask directory (required for inpaint batch processing only)/value": "", "img2img/Append png info to prompts/visible": true, "img2img/Append png info to prompts/value": false, "img2img/PNG info directory/visible": true, "img2img/PNG info directory/value": "", "img2img/Resize mode/visible": true, "img2img/Resize mode/value": "Just resize", "img2img/Mask blur/visible": true, "img2img/Mask blur/value": 4, "img2img/Mask blur/minimum": 0, "img2img/Mask blur/maximum": 64, "img2img/Mask blur/step": 1, "img2img/Mask transparency/value": 0, "img2img/Mask transparency/minimum": 0, "img2img/Mask transparency/maximum": 100, "img2img/Mask transparency/step": 1, "img2img/Mask mode/visible": true, "img2img/Mask mode/value": "Inpaint masked", "img2img/Masked content/visible": true, "img2img/Masked content/value": "original", "img2img/Inpaint area/visible": true, "img2img/Inpaint area/value": "Whole picture", "img2img/Only masked padding, pixels/visible": true, "img2img/Only masked padding, pixels/value": 32, "img2img/Only masked padding, pixels/minimum": 0, "img2img/Only masked padding, pixels/maximum": 256, "img2img/Only masked padding, pixels/step": 4, "customscript/soft_inpainting.py/img2img/Soft inpainting/visible": true, "customscript/soft_inpainting.py/img2img/Soft inpainting/value": false, "customscript/soft_inpainting.py/img2img/Schedule bias/visible": true, "customscript/soft_inpainting.py/img2img/Schedule bias/value": 1, "customscript/soft_inpainting.py/img2img/Schedule bias/minimum": 0, "customscript/soft_inpainting.py/img2img/Schedule bias/maximum": 8, "customscript/soft_inpainting.py/img2img/Schedule bias/step": 0.1, "customscript/soft_inpainting.py/img2img/Preservation strength/visible": true, "customscript/soft_inpainting.py/img2img/Preservation strength/value": 0.5, "customscript/soft_inpainting.py/img2img/Preservation strength/minimum": 0, "customscript/soft_inpainting.py/img2img/Preservation strength/maximum": 8, "customscript/soft_inpainting.py/img2img/Preservation strength/step": 0.05, "customscript/soft_inpainting.py/img2img/Transition contrast boost/visible": true, "customscript/soft_inpainting.py/img2img/Transition contrast boost/value": 4, "customscript/soft_inpainting.py/img2img/Transition contrast boost/minimum": 1, "customscript/soft_inpainting.py/img2img/Transition contrast boost/maximum": 32, "customscript/soft_inpainting.py/img2img/Transition contrast boost/step": 0.5, "customscript/soft_inpainting.py/img2img/Mask influence/visible": true, "customscript/soft_inpainting.py/img2img/Mask influence/value": 0, "customscript/soft_inpainting.py/img2img/Mask influence/minimum": 0, "customscript/soft_inpainting.py/img2img/Mask influence/maximum": 1, "customscript/soft_inpainting.py/img2img/Mask influence/step": 0.05, "customscript/soft_inpainting.py/img2img/Difference threshold/visible": true, "customscript/soft_inpainting.py/img2img/Difference threshold/value": 0.5, "customscript/soft_inpainting.py/img2img/Difference threshold/minimum": 0, "customscript/soft_inpainting.py/img2img/Difference threshold/maximum": 8, "customscript/soft_inpainting.py/img2img/Difference threshold/step": 0.25, "customscript/soft_inpainting.py/img2img/Difference contrast/visible": true, "customscript/soft_inpainting.py/img2img/Difference contrast/value": 2, "customscript/soft_inpainting.py/img2img/Difference contrast/minimum": 0, "customscript/soft_inpainting.py/img2img/Difference contrast/maximum": 8, "customscript/soft_inpainting.py/img2img/Difference contrast/step": 0.25, "customscript/sampler.py/img2img/Sampling method/visible": true, "customscript/sampler.py/img2img/Sampling method/value": "DPM++ 2M", "customscript/sampler.py/img2img/Schedule type/visible": true, "customscript/sampler.py/img2img/Schedule type/value": "Automatic", "customscript/sampler.py/img2img/Sampling steps/visible": true, "customscript/sampler.py/img2img/Sampling steps/value": 20, "customscript/sampler.py/img2img/Sampling steps/minimum": 1, "customscript/sampler.py/img2img/Sampling steps/maximum": 150, "customscript/sampler.py/img2img/Sampling steps/step": 1, "customscript/refiner.py/img2img/Refiner/visible": true, "customscript/refiner.py/img2img/Refiner/value": false, "customscript/refiner.py/img2img/Checkpoint/visible": true, "customscript/refiner.py/img2img/Checkpoint/value": "", "customscript/refiner.py/img2img/Switch at/visible": true, "customscript/refiner.py/img2img/Switch at/value": 0.8, "customscript/refiner.py/img2img/Switch at/minimum": 0.01, "customscript/refiner.py/img2img/Switch at/maximum": 1.0, "customscript/refiner.py/img2img/Switch at/step": 0.01, "img2img/Tabs@img2img_tabs_resize/selected": null, "img2img/Width/visible": true, "img2img/Width/value": 512, "img2img/Width/minimum": 64, "img2img/Width/maximum": 2048, "img2img/Width/step": 8, "img2img/Height/visible": true, "img2img/Height/value": 512, "img2img/Height/minimum": 64, "img2img/Height/maximum": 2048, "img2img/Height/step": 8, "img2img/⇅/visible": true, "img2img/📐/visible": true, "img2img/Scale/visible": true, "img2img/Scale/value": 1.0, "img2img/Scale/minimum": 0.05, "img2img/Scale/maximum": 4.0, "img2img/Scale/step": 0.05, "img2img/Unused/visible": true, "img2img/Unused/value": 0, "img2img/Unused/minimum": 0, "img2img/Unused/maximum": 100, "img2img/Unused/step": 1, "img2img/Batch count/visible": true, "img2img/Batch count/value": 1, "img2img/Batch count/minimum": 1, "img2img/Batch count/maximum": 100, "img2img/Batch count/step": 1, "img2img/Batch size/visible": true, "img2img/Batch size/value": 1, "img2img/Batch size/minimum": 1, "img2img/Batch size/maximum": 8, "img2img/Batch size/step": 1, "img2img/CFG Scale/visible": true, "img2img/CFG Scale/value": 7.0, "img2img/CFG Scale/minimum": 1.0, "img2img/CFG Scale/maximum": 30.0, "img2img/CFG Scale/step": 0.5, "img2img/Image CFG Scale/value": 1.5, "img2img/Image CFG Scale/minimum": 0, "img2img/Image CFG Scale/maximum": 3.0, "img2img/Image CFG Scale/step": 0.05, "img2img/Denoising strength/visible": true, "img2img/Denoising strength/value": 0.75, "img2img/Denoising strength/minimum": 0.0, "img2img/Denoising strength/maximum": 1.0, "img2img/Denoising strength/step": 0.01, "customscript/seed.py/img2img/Seed/visible": true, "customscript/seed.py/img2img/Seed/value": -1, "img2img/🎲️/visible": true, "img2img/♻️/visible": true, "customscript/seed.py/img2img/Extra/visible": true, "customscript/seed.py/img2img/Extra/value": false, "customscript/seed.py/img2img/Variation seed/visible": true, "customscript/seed.py/img2img/Variation seed/value": -1, "customscript/seed.py/img2img/Variation strength/visible": true, "customscript/seed.py/img2img/Variation strength/value": 0.0, "customscript/seed.py/img2img/Variation strength/minimum": 0, "customscript/seed.py/img2img/Variation strength/maximum": 1, "customscript/seed.py/img2img/Variation strength/step": 0.01, "customscript/seed.py/img2img/Resize seed from width/visible": true, "customscript/seed.py/img2img/Resize seed from width/value": 0, "customscript/seed.py/img2img/Resize seed from width/minimum": 0, "customscript/seed.py/img2img/Resize seed from width/maximum": 2048, "customscript/seed.py/img2img/Resize seed from width/step": 8, "customscript/seed.py/img2img/Resize seed from height/visible": true, "customscript/seed.py/img2img/Resize seed from height/value": 0, "customscript/seed.py/img2img/Resize seed from height/minimum": 0, "customscript/seed.py/img2img/Resize seed from height/maximum": 2048, "customscript/seed.py/img2img/Resize seed from height/step": 8, "img2img/Override settings/value": null, "img2img/Script/visible": true, "img2img/Script/value": "None", "customscript/img2imgalt.py/img2img/Override `Sampling method` to Euler?(this method is built for it)/visible": true, "customscript/img2imgalt.py/img2img/Override `Sampling method` to Euler?(this method is built for it)/value": true, "customscript/img2imgalt.py/img2img/Override `prompt` to the same value as `original prompt`?(and `negative prompt`)/visible": true, "customscript/img2imgalt.py/img2img/Override `prompt` to the same value as `original prompt`?(and `negative prompt`)/value": true, "customscript/img2imgalt.py/img2img/Original prompt/visible": true, "customscript/img2imgalt.py/img2img/Original prompt/value": "", "customscript/img2imgalt.py/img2img/Original negative prompt/visible": true, "customscript/img2imgalt.py/img2img/Original negative prompt/value": "", "customscript/img2imgalt.py/img2img/Override `Sampling Steps` to the same value as `Decode steps`?/visible": true, "customscript/img2imgalt.py/img2img/Override `Sampling Steps` to the same value as `Decode steps`?/value": true, "customscript/img2imgalt.py/img2img/Decode steps/visible": true, "customscript/img2imgalt.py/img2img/Decode steps/value": 50, "customscript/img2imgalt.py/img2img/Decode steps/minimum": 1, "customscript/img2imgalt.py/img2img/Decode steps/maximum": 150, "customscript/img2imgalt.py/img2img/Decode steps/step": 1, "customscript/img2imgalt.py/img2img/Override `Denoising strength` to 1?/visible": true, "customscript/img2imgalt.py/img2img/Override `Denoising strength` to 1?/value": true, "customscript/img2imgalt.py/img2img/Decode CFG scale/visible": true, "customscript/img2imgalt.py/img2img/Decode CFG scale/value": 1.0, "customscript/img2imgalt.py/img2img/Decode CFG scale/minimum": 0.0, "customscript/img2imgalt.py/img2img/Decode CFG scale/maximum": 15.0, "customscript/img2imgalt.py/img2img/Decode CFG scale/step": 0.1, "customscript/img2imgalt.py/img2img/Randomness/visible": true, "customscript/img2imgalt.py/img2img/Randomness/value": 0.0, "customscript/img2imgalt.py/img2img/Randomness/minimum": 0.0, "customscript/img2imgalt.py/img2img/Randomness/maximum": 1.0, "customscript/img2imgalt.py/img2img/Randomness/step": 0.01, "customscript/img2imgalt.py/img2img/Sigma adjustment for finding noise for image/visible": true, "customscript/img2imgalt.py/img2img/Sigma adjustment for finding noise for image/value": false, "customscript/loopback.py/img2img/Loops/visible": true, "customscript/loopback.py/img2img/Loops/value": 4, "customscript/loopback.py/img2img/Loops/minimum": 1, "customscript/loopback.py/img2img/Loops/maximum": 32, "customscript/loopback.py/img2img/Loops/step": 1, "customscript/loopback.py/img2img/Final denoising strength/visible": true, "customscript/loopback.py/img2img/Final denoising strength/value": 0.5, "customscript/loopback.py/img2img/Final denoising strength/minimum": 0, "customscript/loopback.py/img2img/Final denoising strength/maximum": 1, "customscript/loopback.py/img2img/Final denoising strength/step": 0.01, "customscript/loopback.py/img2img/Denoising strength curve/visible": true, "customscript/loopback.py/img2img/Denoising strength curve/value": "Linear", "customscript/loopback.py/img2img/Append interrogated prompt at each iteration/visible": true, "customscript/loopback.py/img2img/Append interrogated prompt at each iteration/value": "None", "customscript/outpainting_mk_2.py/img2img/Pixels to expand/visible": true, "customscript/outpainting_mk_2.py/img2img/Pixels to expand/value": 128, "customscript/outpainting_mk_2.py/img2img/Pixels to expand/minimum": 8, "customscript/outpainting_mk_2.py/img2img/Pixels to expand/maximum": 256, "customscript/outpainting_mk_2.py/img2img/Pixels to expand/step": 8, "customscript/outpainting_mk_2.py/img2img/Mask blur/visible": true, "customscript/outpainting_mk_2.py/img2img/Mask blur/value": 8, "customscript/outpainting_mk_2.py/img2img/Mask blur/minimum": 0, "customscript/outpainting_mk_2.py/img2img/Mask blur/maximum": 64, "customscript/outpainting_mk_2.py/img2img/Mask blur/step": 1, "customscript/outpainting_mk_2.py/img2img/Fall-off exponent (lower=higher detail)/visible": true, "customscript/outpainting_mk_2.py/img2img/Fall-off exponent (lower=higher detail)/value": 1.0, "customscript/outpainting_mk_2.py/img2img/Fall-off exponent (lower=higher detail)/minimum": 0.0, "customscript/outpainting_mk_2.py/img2img/Fall-off exponent (lower=higher detail)/maximum": 4.0, "customscript/outpainting_mk_2.py/img2img/Fall-off exponent (lower=higher detail)/step": 0.01, "customscript/outpainting_mk_2.py/img2img/Color variation/visible": true, "customscript/outpainting_mk_2.py/img2img/Color variation/value": 0.05, "customscript/outpainting_mk_2.py/img2img/Color variation/minimum": 0.0, "customscript/outpainting_mk_2.py/img2img/Color variation/maximum": 1.0, "customscript/outpainting_mk_2.py/img2img/Color variation/step": 0.01, "customscript/poor_mans_outpainting.py/img2img/Pixels to expand/visible": true, "customscript/poor_mans_outpainting.py/img2img/Pixels to expand/value": 128, "customscript/poor_mans_outpainting.py/img2img/Pixels to expand/minimum": 8, "customscript/poor_mans_outpainting.py/img2img/Pixels to expand/maximum": 256, "customscript/poor_mans_outpainting.py/img2img/Pixels to expand/step": 8, "customscript/poor_mans_outpainting.py/img2img/Mask blur/visible": true, "customscript/poor_mans_outpainting.py/img2img/Mask blur/value": 4, "customscript/poor_mans_outpainting.py/img2img/Mask blur/minimum": 0, "customscript/poor_mans_outpainting.py/img2img/Mask blur/maximum": 64, "customscript/poor_mans_outpainting.py/img2img/Mask blur/step": 1, "customscript/poor_mans_outpainting.py/img2img/Masked content/visible": true, "customscript/poor_mans_outpainting.py/img2img/Masked content/value": "fill", "customscript/prompt_matrix.py/img2img/Put variable parts at start of prompt/visible": true, "customscript/prompt_matrix.py/img2img/Put variable parts at start of prompt/value": false, "customscript/prompt_matrix.py/img2img/Use different seed for each picture/visible": true, "customscript/prompt_matrix.py/img2img/Use different seed for each picture/value": false, "customscript/prompt_matrix.py/img2img/Select prompt/visible": true, "customscript/prompt_matrix.py/img2img/Select prompt/value": "positive", "customscript/prompt_matrix.py/img2img/Select joining char/visible": true, "customscript/prompt_matrix.py/img2img/Select joining char/value": "comma", "customscript/prompt_matrix.py/img2img/Grid margins (px)/visible": true, "customscript/prompt_matrix.py/img2img/Grid margins (px)/value": 0, "customscript/prompt_matrix.py/img2img/Grid margins (px)/minimum": 0, "customscript/prompt_matrix.py/img2img/Grid margins (px)/maximum": 500, "customscript/prompt_matrix.py/img2img/Grid margins (px)/step": 2, "customscript/prompts_from_file.py/img2img/Iterate seed every line/visible": true, "customscript/prompts_from_file.py/img2img/Iterate seed every line/value": false, "customscript/prompts_from_file.py/img2img/Use same random seed for all lines/visible": true, "customscript/prompts_from_file.py/img2img/Use same random seed for all lines/value": false, "customscript/prompts_from_file.py/img2img/Insert prompts at the/visible": true, "customscript/prompts_from_file.py/img2img/Insert prompts at the/value": "start", "customscript/prompts_from_file.py/img2img/List of prompt inputs/visible": true, "customscript/prompts_from_file.py/img2img/List of prompt inputs/value": "", "customscript/sd_upscale.py/img2img/Tile overlap/visible": true, "customscript/sd_upscale.py/img2img/Tile overlap/value": 64, "customscript/sd_upscale.py/img2img/Tile overlap/minimum": 0, "customscript/sd_upscale.py/img2img/Tile overlap/maximum": 256, "customscript/sd_upscale.py/img2img/Tile overlap/step": 16, "customscript/sd_upscale.py/img2img/Scale Factor/visible": true, "customscript/sd_upscale.py/img2img/Scale Factor/value": 2.0, "customscript/sd_upscale.py/img2img/Scale Factor/minimum": 1.0, "customscript/sd_upscale.py/img2img/Scale Factor/maximum": 4.0, "customscript/sd_upscale.py/img2img/Scale Factor/step": 0.05, "customscript/sd_upscale.py/img2img/Upscaler/visible": true, "customscript/sd_upscale.py/img2img/Upscaler/value": "None", "customscript/xyz_grid.py/img2img/X type/visible": true, "customscript/xyz_grid.py/img2img/X type/value": "Seed", "customscript/xyz_grid.py/img2img/X values/visible": true, "customscript/xyz_grid.py/img2img/X values/value": "", "customscript/xyz_grid.py/img2img/Y type/visible": true, "customscript/xyz_grid.py/img2img/Y type/value": "Nothing", "customscript/xyz_grid.py/img2img/Y values/visible": true, "customscript/xyz_grid.py/img2img/Y values/value": "", "customscript/xyz_grid.py/img2img/Z type/visible": true, "customscript/xyz_grid.py/img2img/Z type/value": "Nothing", "customscript/xyz_grid.py/img2img/Z values/visible": true, "customscript/xyz_grid.py/img2img/Z values/value": "", "customscript/xyz_grid.py/img2img/Draw legend/visible": true, "customscript/xyz_grid.py/img2img/Draw legend/value": true, "customscript/xyz_grid.py/img2img/Keep -1 for seeds/visible": true, "customscript/xyz_grid.py/img2img/Keep -1 for seeds/value": false, "customscript/xyz_grid.py/img2img/Vary seeds for X/visible": true, "customscript/xyz_grid.py/img2img/Vary seeds for X/value": false, "customscript/xyz_grid.py/img2img/Vary seeds for Y/visible": true, "customscript/xyz_grid.py/img2img/Vary seeds for Y/value": false, "customscript/xyz_grid.py/img2img/Vary seeds for Z/visible": true, "customscript/xyz_grid.py/img2img/Vary seeds for Z/value": false, "customscript/xyz_grid.py/img2img/Include Sub Images/visible": true, "customscript/xyz_grid.py/img2img/Include Sub Images/value": false, "customscript/xyz_grid.py/img2img/Include Sub Grids/visible": true, "customscript/xyz_grid.py/img2img/Include Sub Grids/value": false, "customscript/xyz_grid.py/img2img/Use text inputs instead of dropdowns/visible": true, "customscript/xyz_grid.py/img2img/Use text inputs instead of dropdowns/value": false, "customscript/xyz_grid.py/img2img/Grid margins (px)/visible": true, "customscript/xyz_grid.py/img2img/Grid margins (px)/value": 0, "customscript/xyz_grid.py/img2img/Grid margins (px)/minimum": 0, "customscript/xyz_grid.py/img2img/Grid margins (px)/maximum": 500, "customscript/xyz_grid.py/img2img/Grid margins (px)/step": 2, "img2img/Swap X/Y axes/visible": true, "img2img/Swap Y/Z axes/visible": true, "img2img/Swap X/Z axes/visible": true, "img2img/📂/visible": true, "img2img/💾/visible": true, "img2img/🗃️/visible": true, "img2img/🖼️/visible": true, "img2img/🎨️/visible": true, "img2img/Description/visible": true, "img2img/Description/value": "", "img2img/Cancel/visible": true, "img2img/Replace preview/visible": true, "img2img/Save/visible": true, "img2img/Preferred VAE/visible": true, "img2img/Preferred VAE/value": "None", "img2img/Stable Diffusion version/visible": true, "img2img/Stable Diffusion version/value": "Unknown", "img2img/Activation text/visible": true, "img2img/Activation text/value": "", "img2img/Preferred weight/visible": true, "img2img/Preferred weight/value": 0.0, "img2img/Preferred weight/minimum": 0.0, "img2img/Preferred weight/maximum": 2.0, "img2img/Preferred weight/step": 0.01, "img2img/Random prompt/visible": true, "img2img/Random prompt/value": "", "extras/Tabs@mode_extras/selected": null, "extras/Input directory/visible": true, "extras/Input directory/value": "", "extras/Output directory/visible": true, "extras/Output directory/value": "", "extras/Show result images/visible": true, "extras/Show result images/value": true, "customscript/postprocessing_upscale.py/extras/Upscale/visible": true, "customscript/postprocessing_upscale.py/extras/Upscale/value": true, "customscript/postprocessing_upscale.py/extras/Upscaler 1/visible": true, "customscript/postprocessing_upscale.py/extras/Upscaler 1/value": "None", "customscript/postprocessing_upscale.py/extras/Upscaler 2/visible": true, "customscript/postprocessing_upscale.py/extras/Upscaler 2/value": "None", "customscript/postprocessing_upscale.py/extras/Upscaler 2 visibility/visible": true, "customscript/postprocessing_upscale.py/extras/Upscaler 2 visibility/value": 0.0, "customscript/postprocessing_upscale.py/extras/Upscaler 2 visibility/minimum": 0.0, "customscript/postprocessing_upscale.py/extras/Upscaler 2 visibility/maximum": 1.0, "customscript/postprocessing_upscale.py/extras/Upscaler 2 visibility/step": 0.001, "extras/Tabs@extras_resize_mode/selected": null, "customscript/postprocessing_upscale.py/extras/Resize/visible": true, "customscript/postprocessing_upscale.py/extras/Resize/value": 4, "customscript/postprocessing_upscale.py/extras/Resize/minimum": 1.0, "customscript/postprocessing_upscale.py/extras/Resize/maximum": 8.0, "customscript/postprocessing_upscale.py/extras/Resize/step": 0.05, "customscript/postprocessing_upscale.py/extras/Max side length/visible": true, "customscript/postprocessing_upscale.py/extras/Max side length/value": 0.0, "customscript/postprocessing_upscale.py/extras/Width/visible": true, "customscript/postprocessing_upscale.py/extras/Width/value": 512, "customscript/postprocessing_upscale.py/extras/Width/minimum": 64, "customscript/postprocessing_upscale.py/extras/Width/maximum": 8192, "customscript/postprocessing_upscale.py/extras/Width/step": 8, "customscript/postprocessing_upscale.py/extras/Height/visible": true, "customscript/postprocessing_upscale.py/extras/Height/value": 512, "customscript/postprocessing_upscale.py/extras/Height/minimum": 64, "customscript/postprocessing_upscale.py/extras/Height/maximum": 8192, "customscript/postprocessing_upscale.py/extras/Height/step": 8, "extras/⇅/visible": true, "customscript/postprocessing_upscale.py/extras/Crop to fit/visible": true, "customscript/postprocessing_upscale.py/extras/Crop to fit/value": true, "customscript/postprocessing_gfpgan.py/extras/GFPGAN/visible": true, "customscript/postprocessing_gfpgan.py/extras/GFPGAN/value": false, "customscript/postprocessing_gfpgan.py/extras/Visibility/visible": true, "customscript/postprocessing_gfpgan.py/extras/Visibility/value": 1.0, "customscript/postprocessing_gfpgan.py/extras/Visibility/minimum": 0.0, "customscript/postprocessing_gfpgan.py/extras/Visibility/maximum": 1.0, "customscript/postprocessing_gfpgan.py/extras/Visibility/step": 0.001, "customscript/postprocessing_codeformer.py/extras/CodeFormer/visible": true, "customscript/postprocessing_codeformer.py/extras/CodeFormer/value": false, "customscript/postprocessing_codeformer.py/extras/Visibility/visible": true, "customscript/postprocessing_codeformer.py/extras/Visibility/value": 1.0, "customscript/postprocessing_codeformer.py/extras/Visibility/minimum": 0.0, "customscript/postprocessing_codeformer.py/extras/Visibility/maximum": 1.0, "customscript/postprocessing_codeformer.py/extras/Visibility/step": 0.001, "customscript/postprocessing_codeformer.py/extras/Weight (0 = maximum effect, 1 = minimum effect)/visible": true, "customscript/postprocessing_codeformer.py/extras/Weight (0 = maximum effect, 1 = minimum effect)/value": 0, "customscript/postprocessing_codeformer.py/extras/Weight (0 = maximum effect, 1 = minimum effect)/minimum": 0.0, "customscript/postprocessing_codeformer.py/extras/Weight (0 = maximum effect, 1 = minimum effect)/maximum": 1.0, "customscript/postprocessing_codeformer.py/extras/Weight (0 = maximum effect, 1 = minimum effect)/step": 0.001, "customscript/postprocessing_split_oversized.py/extras/Split oversized images/visible": true, "customscript/postprocessing_split_oversized.py/extras/Split oversized images/value": false, "customscript/postprocessing_split_oversized.py/extras/Threshold/visible": true, "customscript/postprocessing_split_oversized.py/extras/Threshold/value": 0.5, "customscript/postprocessing_split_oversized.py/extras/Threshold/minimum": 0.0, "customscript/postprocessing_split_oversized.py/extras/Threshold/maximum": 1.0, "customscript/postprocessing_split_oversized.py/extras/Threshold/step": 0.05, "customscript/postprocessing_split_oversized.py/extras/Overlap ratio/visible": true, "customscript/postprocessing_split_oversized.py/extras/Overlap ratio/value": 0.2, "customscript/postprocessing_split_oversized.py/extras/Overlap ratio/minimum": 0.0, "customscript/postprocessing_split_oversized.py/extras/Overlap ratio/maximum": 0.9, "customscript/postprocessing_split_oversized.py/extras/Overlap ratio/step": 0.05, "customscript/postprocessing_focal_crop.py/extras/Auto focal point crop/visible": true, "customscript/postprocessing_focal_crop.py/extras/Auto focal point crop/value": false, "customscript/postprocessing_focal_crop.py/extras/Focal point face weight/visible": true, "customscript/postprocessing_focal_crop.py/extras/Focal point face weight/value": 0.9, "customscript/postprocessing_focal_crop.py/extras/Focal point face weight/minimum": 0.0, "customscript/postprocessing_focal_crop.py/extras/Focal point face weight/maximum": 1.0, "customscript/postprocessing_focal_crop.py/extras/Focal point face weight/step": 0.05, "customscript/postprocessing_focal_crop.py/extras/Focal point entropy weight/visible": true, "customscript/postprocessing_focal_crop.py/extras/Focal point entropy weight/value": 0.15, "customscript/postprocessing_focal_crop.py/extras/Focal point entropy weight/minimum": 0.0, "customscript/postprocessing_focal_crop.py/extras/Focal point entropy weight/maximum": 1.0, "customscript/postprocessing_focal_crop.py/extras/Focal point entropy weight/step": 0.05, "customscript/postprocessing_focal_crop.py/extras/Focal point edges weight/visible": true, "customscript/postprocessing_focal_crop.py/extras/Focal point edges weight/value": 0.5, "customscript/postprocessing_focal_crop.py/extras/Focal point edges weight/minimum": 0.0, "customscript/postprocessing_focal_crop.py/extras/Focal point edges weight/maximum": 1.0, "customscript/postprocessing_focal_crop.py/extras/Focal point edges weight/step": 0.05, "customscript/postprocessing_focal_crop.py/extras/Create debug image/visible": true, "customscript/postprocessing_focal_crop.py/extras/Create debug image/value": false, "customscript/postprocessing_autosized_crop.py/extras/Auto-sized crop/visible": true, "customscript/postprocessing_autosized_crop.py/extras/Auto-sized crop/value": false, "customscript/postprocessing_autosized_crop.py/extras/Dimension lower bound/visible": true, "customscript/postprocessing_autosized_crop.py/extras/Dimension lower bound/value": 384, "customscript/postprocessing_autosized_crop.py/extras/Dimension lower bound/minimum": 64, "customscript/postprocessing_autosized_crop.py/extras/Dimension lower bound/maximum": 2048, "customscript/postprocessing_autosized_crop.py/extras/Dimension lower bound/step": 8, "customscript/postprocessing_autosized_crop.py/extras/Dimension upper bound/visible": true, "customscript/postprocessing_autosized_crop.py/extras/Dimension upper bound/value": 768, "customscript/postprocessing_autosized_crop.py/extras/Dimension upper bound/minimum": 64, "customscript/postprocessing_autosized_crop.py/extras/Dimension upper bound/maximum": 2048, "customscript/postprocessing_autosized_crop.py/extras/Dimension upper bound/step": 8, "customscript/postprocessing_autosized_crop.py/extras/Area lower bound/visible": true, "customscript/postprocessing_autosized_crop.py/extras/Area lower bound/value": 4096, "customscript/postprocessing_autosized_crop.py/extras/Area lower bound/minimum": 4096, "customscript/postprocessing_autosized_crop.py/extras/Area lower bound/maximum": 4194304, "customscript/postprocessing_autosized_crop.py/extras/Area lower bound/step": 1, "customscript/postprocessing_autosized_crop.py/extras/Area upper bound/visible": true, "customscript/postprocessing_autosized_crop.py/extras/Area upper bound/value": 409600, "customscript/postprocessing_autosized_crop.py/extras/Area upper bound/minimum": 4096, "customscript/postprocessing_autosized_crop.py/extras/Area upper bound/maximum": 4194304, "customscript/postprocessing_autosized_crop.py/extras/Area upper bound/step": 1, "customscript/postprocessing_autosized_crop.py/extras/Resizing objective/visible": true, "customscript/postprocessing_autosized_crop.py/extras/Resizing objective/value": "Maximize area", "customscript/postprocessing_autosized_crop.py/extras/Error threshold/visible": true, "customscript/postprocessing_autosized_crop.py/extras/Error threshold/value": 0.1, "customscript/postprocessing_autosized_crop.py/extras/Error threshold/minimum": 0, "customscript/postprocessing_autosized_crop.py/extras/Error threshold/maximum": 1, "customscript/postprocessing_autosized_crop.py/extras/Error threshold/step": 0.01, "customscript/postprocessing_create_flipped_copies.py/extras/Create flipped copies/visible": true, "customscript/postprocessing_create_flipped_copies.py/extras/Create flipped copies/value": false, "customscript/postprocessing_caption.py/extras/Caption/visible": true, "customscript/postprocessing_caption.py/extras/Caption/value": false, "extras/Interrupt/visible": true, "extras/Skip/visible": true, "extras/Interrupting.../visible": true, "extras/Generate/visible": true, "extras/📂/visible": true, "extras/🖼️/visible": true, "extras/🎨️/visible": true, "extras/📐/visible": true, "pnginfo/Send to txt2img/visible": true, "pnginfo/Send to img2img/visible": true, "pnginfo/Send to inpaint/visible": true, "pnginfo/Send to extras/visible": true, "modelmerger/Primary model (A)/visible": true, "modelmerger/Primary model (A)/value": null, "modelmerger/🔄/visible": true, "modelmerger/Secondary model (B)/visible": true, "modelmerger/Secondary model (B)/value": null, "modelmerger/Tertiary model (C)/visible": true, "modelmerger/Tertiary model (C)/value": null, "modelmerger/Custom Name (Optional)/visible": true, "modelmerger/Custom Name (Optional)/value": "", "modelmerger/Multiplier (M) - set to 0 to get model A/visible": true, "modelmerger/Multiplier (M) - set to 0 to get model A/value": 0.3, "modelmerger/Multiplier (M) - set to 0 to get model A/minimum": 0.0, "modelmerger/Multiplier (M) - set to 0 to get model A/maximum": 1.0, "modelmerger/Multiplier (M) - set to 0 to get model A/step": 0.05, "modelmerger/Interpolation Method/visible": true, "modelmerger/Interpolation Method/value": "Weighted sum", "modelmerger/Checkpoint format/visible": true, "modelmerger/Checkpoint format/value": "safetensors", "modelmerger/Save as float16/visible": true, "modelmerger/Save as float16/value": false, "modelmerger/Copy config from/visible": true, "modelmerger/Copy config from/value": "A, B or C", "modelmerger/Bake in VAE/visible": true, "modelmerger/Bake in VAE/value": "None", "modelmerger/Discard weights with matching name/visible": true, "modelmerger/Discard weights with matching name/value": "", "modelmerger/Save metadata/visible": true, "modelmerger/Save metadata/value": true, "modelmerger/Add merge recipe metadata/visible": true, "modelmerger/Add merge recipe metadata/value": true, "modelmerger/Copy metadata from merged models/visible": true, "modelmerger/Copy metadata from merged models/value": true, "modelmerger/Read metadata from selected checkpoints/visible": true, "modelmerger/Merge/visible": true, "train/Tabs@train_tabs/selected": null, "train/Name/visible": true, "train/Name/value": "", "train/Initialization text/visible": true, "train/Initialization text/value": "*", "train/Number of vectors per token/visible": true, "train/Number of vectors per token/value": 1, "train/Number of vectors per token/minimum": 1, "train/Number of vectors per token/maximum": 75, "train/Number of vectors per token/step": 1, "train/Overwrite Old Embedding/visible": true, "train/Overwrite Old Embedding/value": false, "train/Create embedding/visible": true, "train/Enter hypernetwork layer structure/visible": true, "train/Enter hypernetwork layer structure/value": "1, 2, 1", "train/Select activation function of hypernetwork. Recommended : Swish / Linear(none)/visible": true, "train/Select activation function of hypernetwork. Recommended : Swish / Linear(none)/value": "linear", "train/Select Layer weights initialization. Recommended: Kaiming for relu-like, Xavier for sigmoid-like, Normal otherwise/visible": true, "train/Select Layer weights initialization. Recommended: Kaiming for relu-like, Xavier for sigmoid-like, Normal otherwise/value": "Normal", "train/Add layer normalization/visible": true, "train/Add layer normalization/value": false, "train/Use dropout/visible": true, "train/Use dropout/value": false, "train/Enter hypernetwork Dropout structure (or empty). Recommended : 0~0.35 incrementing sequence: 0, 0.05, 0.15/visible": true, "train/Enter hypernetwork Dropout structure (or empty). Recommended : 0~0.35 incrementing sequence: 0, 0.05, 0.15/value": "0, 0, 0", "train/Overwrite Old Hypernetwork/visible": true, "train/Overwrite Old Hypernetwork/value": false, "train/Create hypernetwork/visible": true, "train/Embedding/visible": true, "train/Embedding/value": null, "train/🔄/visible": true, "train/Hypernetwork/visible": true, "train/Hypernetwork/value": null, "train/Embedding Learning rate/visible": true, "train/Embedding Learning rate/value": "0.005", "train/Hypernetwork Learning rate/visible": true, "train/Hypernetwork Learning rate/value": "0.00001", "train/Gradient Clipping/visible": true, "train/Gradient Clipping/value": "disabled", "train/Batch size/visible": true, "train/Batch size/value": 1, "train/Gradient accumulation steps/visible": true, "train/Gradient accumulation steps/value": 1, "train/Dataset directory/visible": true, "train/Dataset directory/value": "", "train/Log directory/visible": true, "train/Log directory/value": "textual_inversion", "train/Prompt template/visible": true, "train/Prompt template/value": "style_filewords.txt", "train/Width/visible": true, "train/Width/value": 512, "train/Width/minimum": 64, "train/Width/maximum": 2048, "train/Width/step": 8, "train/Height/visible": true, "train/Height/value": 512, "train/Height/minimum": 64, "train/Height/maximum": 2048, "train/Height/step": 8, "train/Do not resize images/visible": true, "train/Do not resize images/value": false, "train/Max steps/visible": true, "train/Max steps/value": 100000, "train/Save an image to log directory every N steps, 0 to disable/visible": true, "train/Save an image to log directory every N steps, 0 to disable/value": 500, "train/Save a copy of embedding to log directory every N steps, 0 to disable/visible": true, "train/Save a copy of embedding to log directory every N steps, 0 to disable/value": 500, "train/Use PNG alpha channel as loss weight/visible": true, "train/Use PNG alpha channel as loss weight/value": false, "train/Save images with embedding in PNG chunks/visible": true, "train/Save images with embedding in PNG chunks/value": true, "train/Read parameters (prompt, etc...) from txt2img tab when making previews/visible": true, "train/Read parameters (prompt, etc...) from txt2img tab when making previews/value": false, "train/Shuffle tags by ',' when creating prompts./visible": true, "train/Shuffle tags by ',' when creating prompts./value": false, "train/Drop out tags when creating prompts./visible": true, "train/Drop out tags when creating prompts./value": 0, "train/Drop out tags when creating prompts./minimum": 0, "train/Drop out tags when creating prompts./maximum": 1, "train/Drop out tags when creating prompts./step": 0.1, "train/Choose latent sampling method/visible": true, "train/Choose latent sampling method/value": "once", "train/Train Embedding/visible": true, "train/Interrupt/visible": true, "train/Train Hypernetwork/visible": true, "webui/Tabs@tabs/selected": null}