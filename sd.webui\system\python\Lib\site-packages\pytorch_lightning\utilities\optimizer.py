# Copyright The PyTorch Lightning team.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from typing import Any

from lightning_fabric.utilities.optimizer import _optimizer_to_device as new_optimizer_to_device
from lightning_fabric.utilities.optimizer import _optimizers_to_device as new_optimizers_to_device
from pytorch_lightning.utilities.rank_zero import rank_zero_deprecation


def optimizers_to_device(*args: Any, **kwargs: Any) -> None:
    rank_zero_deprecation(
        "`pytorch_lightning.utilities.optimizer.optimizers_to_device` has been deprecated in v1.8.0 and will be"
        " removed in v2.0.0. This function is internal but you can copy over its implementation."
    )
    return new_optimizers_to_device(*args, **kwargs)


def optimizer_to_device(*args: Any, **kwargs: Any) -> None:
    rank_zero_deprecation(
        "`pytorch_lightning.utilities.optimizer.optimizer_to_device` has been deprecated in v1.8.0 and will be"
        " removed in v2.0.0. This function is internal but you can copy over its implementation."
    )
    return new_optimizer_to_device(*args, **kwargs)
