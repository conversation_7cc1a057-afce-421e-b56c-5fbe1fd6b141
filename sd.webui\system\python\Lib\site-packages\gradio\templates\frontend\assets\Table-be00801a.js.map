{"version": 3, "file": "Table-be00801a.js", "sources": ["../../../../node_modules/.pnpm/dequal@2.0.2/node_modules/dequal/lite/index.mjs", "../../../../js/dataframe/shared/EditableCell.svelte", "../../../../js/dataframe/shared/Table.svelte"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nexport function dequal(foo, bar) {\n\tvar ctor, len;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n", "<script lang=\"ts\">\n\timport { MarkdownCode } from \"@gradio/markdown\";\n\n\texport let edit: boolean;\n\texport let value: string | number = \"\";\n\texport let header = false;\n\texport let datatype:\n\t\t| \"str\"\n\t\t| \"markdown\"\n\t\t| \"html\"\n\t\t| \"number\"\n\t\t| \"bool\"\n\t\t| \"date\" = \"str\";\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\n\texport let el: HTMLInputElement | null;\n</script>\n\n{#if edit}\n\t<input\n\t\tbind:this={el}\n\t\tclass:header\n\t\ttabindex=\"-1\"\n\t\t{value}\n\t\ton:keydown\n\t\ton:blur={({ currentTarget }) => {\n\t\t\tvalue = currentTarget.value;\n\t\t\tcurrentTarget.setAttribute(\"tabindex\", \"-1\");\n\t\t}}\n\t/>\n{/if}\n\n<span on:dblclick tabindex=\"-1\" role=\"button\" class:edit>\n\t{#if datatype === \"html\"}\n\t\t{@html value}\n\t{:else if datatype === \"markdown\"}\n\t\t<MarkdownCode\n\t\t\tmessage={value.toLocaleString()}\n\t\t\t{latex_delimiters}\n\t\t\tchatbot={false}\n\t\t/>\n\t{:else}\n\t\t{value}\n\t{/if}\n</span>\n\n<style>\n\tinput {\n\t\tposition: absolute;\n\t\ttop: var(--size-2);\n\t\tright: var(--size-2);\n\t\tbottom: var(--size-2);\n\t\tleft: var(--size-2);\n\t\tflex: 1 1 0%;\n\t\ttransform: translateX(-0.1px);\n\t\toutline: none;\n\t\tborder: none;\n\t\tbackground: transparent;\n\t}\n\n\tspan {\n\t\tflex: 1 1 0%;\n\t\toutline: none;\n\t\tpadding: var(--size-2);\n\t}\n\n\t.header {\n\t\ttransform: translateX(0);\n\t\tfont: var(--weight-bold);\n\t}\n\n\t.edit {\n\t\topacity: 0;\n\t\tpointer-events: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher, tick } from \"svelte\";\n\timport { dsvFormat } from \"d3-dsv\";\n\timport { dequal } from \"dequal/lite\";\n\timport { copy } from \"@gradio/utils\";\n\timport { Upload } from \"@gradio/upload\";\n\timport { BaseButton } from \"@gradio/button/static\";\n\timport EditableCell from \"./EditableCell.svelte\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { _ } from \"svelte-i18n\";\n\n\ttype Datatype = \"str\" | \"markdown\" | \"html\" | \"number\" | \"bool\" | \"date\";\n\n\texport let datatype: Datatype | Datatype[];\n\texport let label: string | null = null;\n\texport let headers: string[] = [];\n\texport let values:\n\t\t| (string | number)[][]\n\t\t| { data: (string | number)[][]; headers: string[] } = [[]];\n\texport let col_count: [number, \"fixed\" | \"dynamic\"];\n\texport let row_count: [number, \"fixed\" | \"dynamic\"];\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\n\texport let editable = true;\n\texport let wrap = false;\n\texport let height: number | undefined = undefined;\n\n\tlet selected: false | string = false;\n\n\t$: {\n\t\tif (values && !Array.isArray(values)) {\n\t\t\theaders = values.headers;\n\t\t\tvalues = values.data;\n\t\t\tselected = false;\n\t\t} else if (values === null) {\n\t\t\tvalues = [];\n\t\t\tselected = false;\n\t\t}\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: { data: (string | number)[][]; headers: string[] };\n\t\tselect: SelectData;\n\t}>();\n\n\tlet editing: false | string = false;\n\n\tconst get_data_at = (row: number, col: number): string | number =>\n\t\tdata[row][col].value;\n\t$: {\n\t\tif (selected !== false) {\n\t\t\tconst loc = selected.split(\"-\");\n\t\t\tconst row = parseInt(loc[0]);\n\t\t\tconst col = parseInt(loc[1]);\n\t\t\tif (!isNaN(row) && !isNaN(col)) {\n\t\t\t\tdispatch(\"select\", { index: [row, col], value: get_data_at(row, col) });\n\t\t\t}\n\t\t}\n\t}\n\tlet els: Record<\n\t\tstring,\n\t\t{ cell: null | HTMLTableCellElement; input: null | HTMLInputElement }\n\t> = {};\n\n\ttype Headers = { value: string; id: string }[];\n\n\tfunction make_headers(_head: string[]): Headers {\n\t\tlet _h = _head || [];\n\t\tif (col_count[1] === \"fixed\" && _h.length < col_count[0]) {\n\t\t\tconst fill = Array(col_count[0] - _h.length)\n\t\t\t\t.fill(\"\")\n\t\t\t\t.map((_, i) => `${i + _h.length}`);\n\t\t\t_h = _h.concat(fill);\n\t\t}\n\n\t\tif (!_h || _h.length === 0) {\n\t\t\treturn Array(col_count[0])\n\t\t\t\t.fill(0)\n\t\t\t\t.map((_, i) => {\n\t\t\t\t\tconst _id = `h-${i}`;\n\t\t\t\t\tels[_id] = { cell: null, input: null };\n\t\t\t\t\treturn { id: _id, value: JSON.stringify(i + 1) };\n\t\t\t\t});\n\t\t}\n\t\treturn _h.map((h, i) => {\n\t\t\tconst _id = `h-${i}`;\n\t\t\tels[_id] = { cell: null, input: null };\n\t\t\treturn { id: _id, value: h ?? \"\" };\n\t\t});\n\t}\n\n\tfunction process_data(_values: (string | number)[][]): {\n\t\tvalue: string | number;\n\t\tid: string;\n\t}[][] {\n\t\tconst data_row_length = _values.length;\n\t\treturn Array(\n\t\t\trow_count[1] === \"fixed\"\n\t\t\t\t? row_count[0]\n\t\t\t\t: data_row_length < row_count[0]\n\t\t\t\t? row_count[0]\n\t\t\t\t: data_row_length\n\t\t)\n\t\t\t.fill(0)\n\t\t\t.map((_, i) =>\n\t\t\t\tArray(\n\t\t\t\t\tcol_count[1] === \"fixed\"\n\t\t\t\t\t\t? col_count[0]\n\t\t\t\t\t\t: data_row_length > 0\n\t\t\t\t\t\t? _values[0].length\n\t\t\t\t\t\t: headers.length\n\t\t\t\t)\n\t\t\t\t\t.fill(0)\n\t\t\t\t\t.map((_, j) => {\n\t\t\t\t\t\tconst id = `${i}-${j}`;\n\t\t\t\t\t\tels[id] = { input: null, cell: null };\n\t\t\t\t\t\treturn { value: _values?.[i]?.[j] ?? \"\", id };\n\t\t\t\t\t})\n\t\t\t);\n\t}\n\n\tlet _headers = make_headers(headers);\n\tlet old_headers: string[] | undefined;\n\n\t$: {\n\t\tif (!dequal(headers, old_headers)) {\n\t\t\t_headers = make_headers(headers);\n\n\t\t\told_headers = headers;\n\t\t\trefresh_focus();\n\t\t}\n\t}\n\t$: if (!dequal(values, old_val)) {\n\t\tdata = process_data(values as (string | number)[][]);\n\t\told_val = values as (string | number)[][];\n\n\t\trefresh_focus();\n\t}\n\n\tasync function refresh_focus(): Promise<void> {\n\t\tif (typeof editing === \"string\") {\n\t\t\tawait tick();\n\t\t\tels[editing as string]?.input?.focus();\n\t\t} else if (typeof selected === \"string\") {\n\t\t\tawait tick();\n\t\t\tels[selected as string]?.input?.focus();\n\t\t}\n\t}\n\n\tlet data: { id: string; value: string | number }[][] = [[]];\n\n\tlet old_val: undefined | (string | number)[][] = undefined;\n\n\t$: _headers &&\n\t\tdispatch(\"change\", {\n\t\t\tdata: data.map((r) => r.map(({ value }) => value)),\n\t\t\theaders: _headers.map((h) => h.value)\n\t\t});\n\n\tfunction get_sort_status(\n\t\tname: string,\n\t\t_sort: number,\n\t\tdirection?: SortDirection\n\t): \"none\" | \"ascending\" | \"descending\" {\n\t\tif (!_sort) return \"none\";\n\t\tif (headers[_sort] === name) {\n\t\t\tif (direction === \"asc\") return \"ascending\";\n\t\t\tif (direction === \"des\") return \"descending\";\n\t\t}\n\n\t\treturn \"none\";\n\t}\n\n\tfunction get_current_indices(id: string): [number, number] {\n\t\treturn data.reduce(\n\t\t\t(acc, arr, i) => {\n\t\t\t\tconst j = arr.reduce(\n\t\t\t\t\t(_acc, _data, k) => (id === _data.id ? k : _acc),\n\t\t\t\t\t-1\n\t\t\t\t);\n\n\t\t\t\treturn j === -1 ? acc : [i, j];\n\t\t\t},\n\t\t\t[-1, -1]\n\t\t);\n\t}\n\n\tasync function start_edit(id: string, clear?: boolean): Promise<void> {\n\t\tif (!editable || editing === id) return;\n\n\t\tif (clear) {\n\t\t\tconst [i, j] = get_current_indices(id);\n\n\t\t\tdata[i][j].value = \"\";\n\t\t}\n\t\tediting = id;\n\t\tawait tick();\n\t\tconst { input } = els[id];\n\t\tinput?.focus();\n\t}\n\n\t// eslint-disable-next-line complexity\n\tasync function handle_keydown(\n\t\tevent: KeyboardEvent,\n\t\ti: number,\n\t\tj: number,\n\t\tid: string\n\t): Promise<void> {\n\t\tlet is_data;\n\n\t\tswitch (event.key) {\n\t\t\tcase \"ArrowRight\":\n\t\t\t\tif (editing) break;\n\t\t\t\tevent.preventDefault();\n\t\t\t\tis_data = data[i][j + 1];\n\t\t\t\tselected = is_data ? is_data.id : selected;\n\t\t\t\tbreak;\n\t\t\tcase \"ArrowLeft\":\n\t\t\t\tif (editing) break;\n\t\t\t\tevent.preventDefault();\n\t\t\t\tis_data = data[i][j - 1];\n\t\t\t\tselected = is_data ? is_data.id : selected;\n\t\t\t\tbreak;\n\t\t\tcase \"ArrowDown\":\n\t\t\t\tif (editing) break;\n\t\t\t\tevent.preventDefault();\n\t\t\t\tis_data = data[i + 1];\n\t\t\t\tselected = is_data ? is_data[j].id : selected;\n\t\t\t\tbreak;\n\t\t\tcase \"ArrowUp\":\n\t\t\t\tif (editing) break;\n\t\t\t\tevent.preventDefault();\n\t\t\t\tis_data = data[i - 1];\n\t\t\t\tselected = is_data ? is_data[j].id : selected;\n\t\t\t\tbreak;\n\t\t\tcase \"Escape\":\n\t\t\t\tif (!editable) break;\n\t\t\t\tevent.preventDefault();\n\t\t\t\tselected = editing;\n\t\t\t\tediting = false;\n\t\t\t\tbreak;\n\t\t\tcase \"Enter\":\n\t\t\t\tif (!editable) break;\n\t\t\t\tevent.preventDefault();\n\n\t\t\t\tif (event.shiftKey) {\n\t\t\t\t\tadd_row(i);\n\t\t\t\t\tawait tick();\n\t\t\t\t\tconst [pos] = get_current_indices(id);\n\t\t\t\t\tselected = data[pos + 1][j].id;\n\t\t\t\t} else {\n\t\t\t\t\tif (editing === id) {\n\t\t\t\t\t\tediting = false;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tstart_edit(id);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\t\t\tcase \"Backspace\":\n\t\t\t\tif (!editable) break;\n\t\t\t\tif (!editing) {\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tdata[i][j].value = \"\";\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase \"Delete\":\n\t\t\t\tif (!editable) break;\n\t\t\t\tif (!editing) {\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tdata[i][j].value = \"\";\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase \"Tab\":\n\t\t\t\tlet direction = event.shiftKey ? -1 : 1;\n\n\t\t\t\tlet is_data_x = data[i][j + direction];\n\t\t\t\tlet is_data_y =\n\t\t\t\t\tdata?.[i + direction]?.[direction > 0 ? 0 : _headers.length - 1];\n\t\t\t\tlet _selected = is_data_x || is_data_y;\n\t\t\t\tif (_selected) {\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tselected = _selected ? _selected.id : selected;\n\t\t\t\t}\n\t\t\t\tediting = false;\n\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tif (\n\t\t\t\t\t(!editing || (editing && editing !== id)) &&\n\t\t\t\t\tevent.key.length === 1\n\t\t\t\t) {\n\t\t\t\t\tstart_edit(id, true);\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\t\t}\n\t}\n\n\tasync function handle_cell_click(id: string): Promise<void> {\n\t\tif (editing === id) return;\n\t\tif (selected === id) return;\n\t\tediting = false;\n\t\tselected = id;\n\t}\n\n\tasync function set_focus(\n\t\tid: string | boolean,\n\t\ttype: \"edit\" | \"select\"\n\t): Promise<void> {\n\t\tif (type === \"edit\" && typeof id == \"string\") {\n\t\t\tawait tick();\n\t\t\tels[id].input?.focus();\n\t\t}\n\n\t\tif (\n\t\t\ttype === \"edit\" &&\n\t\t\ttypeof id == \"boolean\" &&\n\t\t\ttypeof selected === \"string\"\n\t\t) {\n\t\t\tlet cell = els[selected]?.cell;\n\t\t\tawait tick();\n\t\t\tcell?.focus();\n\t\t}\n\n\t\tif (type === \"select\" && typeof id == \"string\") {\n\t\t\tconst { cell } = els[id];\n\t\t\tawait tick();\n\t\t\tcell?.focus();\n\t\t}\n\t}\n\n\t$: set_focus(editing, \"edit\");\n\t$: set_focus(selected, \"select\");\n\n\ttype SortDirection = \"asc\" | \"des\";\n\tlet sort_direction: SortDirection;\n\tlet sort_by: number;\n\n\tfunction sort(col: number, dir: SortDirection): void {\n\t\tif (dir === \"asc\") {\n\t\t\tdata = data.sort((a, b) => (a[col].value < b[col].value ? -1 : 1));\n\t\t} else if (dir === \"des\") {\n\t\t\tdata = data.sort((a, b) => (a[col].value > b[col].value ? -1 : 1));\n\t\t}\n\t}\n\n\tfunction handle_sort(col: number): void {\n\t\tif (typeof sort_by !== \"number\" || sort_by !== col) {\n\t\t\tsort_direction = \"asc\";\n\t\t\tsort_by = col;\n\t\t} else {\n\t\t\tif (sort_direction === \"asc\") {\n\t\t\t\tsort_direction = \"des\";\n\t\t\t} else if (sort_direction === \"des\") {\n\t\t\t\tsort_direction = \"asc\";\n\t\t\t}\n\t\t}\n\n\t\tsort(col, sort_direction);\n\t}\n\n\tlet header_edit: string | false;\n\n\tfunction update_headers_data(): void {\n\t\tif (typeof selected === \"string\") {\n\t\t\tconst new_header = els[selected].input?.value;\n\t\t\tif (_headers.find((i) => i.id === selected)) {\n\t\t\t\tlet obj = _headers.find((i) => i.id === selected);\n\t\t\t\tif (new_header) obj![\"value\"] = new_header;\n\t\t\t} else {\n\t\t\t\tif (new_header) _headers.push({ id: selected, value: new_header });\n\t\t\t}\n\t\t}\n\t}\n\n\tasync function edit_header(_id: string, select?: boolean): Promise<void> {\n\t\tif (!editable || col_count[1] !== \"dynamic\" || editing === _id) return;\n\t\theader_edit = _id;\n\t\tawait tick();\n\t\tels[_id].input?.focus();\n\t\tif (select) els[_id].input?.select();\n\t}\n\n\tfunction end_header_edit(event: KeyboardEvent): void {\n\t\tif (!editable) return;\n\n\t\tswitch (event.key) {\n\t\t\tcase \"Escape\":\n\t\t\tcase \"Enter\":\n\t\t\tcase \"Tab\":\n\t\t\t\tevent.preventDefault();\n\t\t\t\tselected = header_edit;\n\t\t\t\theader_edit = false;\n\t\t\t\tupdate_headers_data();\n\t\t\t\tbreak;\n\t\t}\n\t}\n\n\tfunction add_row(index?: number): void {\n\t\tif (row_count[1] !== \"dynamic\") return;\n\t\tif (data.length === 0) {\n\t\t\tvalues = [Array(headers.length).fill(\"\")];\n\t\t\treturn;\n\t\t}\n\t\tdata.splice(\n\t\t\tindex ? index + 1 : data.length,\n\t\t\t0,\n\t\t\tArray(data[0].length)\n\t\t\t\t.fill(0)\n\t\t\t\t.map((_, i) => {\n\t\t\t\t\tconst _id = `${data.length}-${i}`;\n\t\t\t\t\tels[_id] = { cell: null, input: null };\n\t\t\t\t\treturn { id: _id, value: \"\" };\n\t\t\t\t})\n\t\t);\n\n\t\tdata = data;\n\t}\n\n\tasync function add_col(): Promise<void> {\n\t\tif (col_count[1] !== \"dynamic\") return;\n\t\tfor (let i = 0; i < data.length; i++) {\n\t\t\tconst _id = `${i}-${data[i].length}`;\n\t\t\tels[_id] = { cell: null, input: null };\n\t\t\tdata[i].push({ id: _id, value: \"\" });\n\t\t}\n\n\t\tconst _id = `h-${_headers.length}`;\n\t\tels[_id] = { cell: null, input: null };\n\t\t_headers.push({ id: _id, value: `Header ${_headers.length + 1}` });\n\n\t\tdata = data;\n\t\t_headers = _headers;\n\n\t\tawait tick();\n\n\t\tedit_header(_id, true);\n\t}\n\n\tfunction handle_click_outside(event: Event): void {\n\t\tif (typeof editing === \"string\" && els[editing]) {\n\t\t\tif (\n\t\t\t\tels[editing].cell !== event.target &&\n\t\t\t\t!els[editing].cell?.contains(event?.target as Node | null)\n\t\t\t) {\n\t\t\t\tediting = false;\n\t\t\t}\n\t\t}\n\n\t\tif (typeof header_edit === \"string\" && els[header_edit]) {\n\t\t\tif (\n\t\t\t\tels[header_edit].cell !== event.target &&\n\t\t\t\t!els[header_edit].cell?.contains(event.target as Node | null)\n\t\t\t) {\n\t\t\t\tselected = header_edit;\n\t\t\t\theader_edit = false;\n\t\t\t\tupdate_headers_data();\n\t\t\t\theader_edit = false;\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction guess_delimitaor(\n\t\ttext: string,\n\t\tpossibleDelimiters: string[]\n\t): string[] {\n\t\treturn possibleDelimiters.filter(weedOut);\n\n\t\tfunction weedOut(delimiter: string): boolean {\n\t\t\tvar cache = -1;\n\t\t\treturn text.split(\"\\n\").every(checkLength);\n\n\t\t\tfunction checkLength(line: string): boolean {\n\t\t\t\tif (!line) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\n\t\t\t\tvar length = line.split(delimiter).length;\n\t\t\t\tif (cache < 0) {\n\t\t\t\t\tcache = length;\n\t\t\t\t}\n\t\t\t\treturn cache === length && length > 1;\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction data_uri_to_blob(data_uri: string): Blob {\n\t\tconst byte_str = atob(data_uri.split(\",\")[1]);\n\t\tconst mime_str = data_uri.split(\",\")[0].split(\":\")[1].split(\";\")[0];\n\n\t\tconst ab = new ArrayBuffer(byte_str.length);\n\t\tconst ia = new Uint8Array(ab);\n\n\t\tfor (let i = 0; i < byte_str.length; i++) {\n\t\t\tia[i] = byte_str.charCodeAt(i);\n\t\t}\n\n\t\treturn new Blob([ab], { type: mime_str });\n\t}\n\n\tfunction blob_to_string(blob: Blob): void {\n\t\tconst reader = new FileReader();\n\n\t\tfunction handle_read(e: ProgressEvent<FileReader>): void {\n\t\t\tif (!e?.target?.result || typeof e.target.result !== \"string\") return;\n\n\t\t\tconst [delimiter] = guess_delimitaor(e.target.result, [\",\", \"\\t\"]);\n\n\t\t\tconst [head, ...rest] = dsvFormat(delimiter).parseRows(e.target.result);\n\n\t\t\t_headers = make_headers(\n\t\t\t\tcol_count[1] === \"fixed\" ? head.slice(0, col_count[0]) : head\n\t\t\t);\n\n\t\t\tvalues = rest;\n\t\t\treader.removeEventListener(\"loadend\", handle_read);\n\t\t}\n\n\t\treader.addEventListener(\"loadend\", handle_read);\n\n\t\treader.readAsText(blob);\n\t}\n\n\tlet dragging = false;\n</script>\n\n<svelte:window\n\ton:click={handle_click_outside}\n\ton:touchstart={handle_click_outside}\n/>\n\n<div class:label={label && label.length !== 0} use:copy>\n\t{#if label && label.length !== 0}\n\t\t<p>\n\t\t\t{label}\n\t\t</p>\n\t{/if}\n\t<div\n\t\tclass=\"table-wrap\"\n\t\tclass:dragging\n\t\tclass:no-wrap={!wrap}\n\t\tstyle=\"max-height: {typeof height === undefined ? 'auto' : height + 'px'};\"\n\t>\n\t\t<Upload\n\t\t\tflex={false}\n\t\t\tcenter={false}\n\t\t\tboundedheight={false}\n\t\t\tdisable_click={true}\n\t\t\ton:load={(e) => blob_to_string(data_uri_to_blob(e.detail.data))}\n\t\t\tbind:dragging\n\t\t>\n\t\t\t<table class:dragging>\n\t\t\t\t{#if label && label.length !== 0}\n\t\t\t\t\t<caption class=\"sr-only\">{label}</caption>\n\t\t\t\t{/if}\n\t\t\t\t<thead>\n\t\t\t\t\t<tr>\n\t\t\t\t\t\t{#each _headers as { value, id }, i (id)}\n\t\t\t\t\t\t\t<th\n\t\t\t\t\t\t\t\tbind:this={els[id].cell}\n\t\t\t\t\t\t\t\tclass:editing={header_edit === id}\n\t\t\t\t\t\t\t\taria-sort={get_sort_status(value, sort_by, sort_direction)}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<div class=\"cell-wrap\">\n\t\t\t\t\t\t\t\t\t<EditableCell\n\t\t\t\t\t\t\t\t\t\t{value}\n\t\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t\tbind:el={els[id].input}\n\t\t\t\t\t\t\t\t\t\tedit={header_edit === id}\n\t\t\t\t\t\t\t\t\t\ton:keydown={end_header_edit}\n\t\t\t\t\t\t\t\t\t\ton:dblclick={() => edit_header(id)}\n\t\t\t\t\t\t\t\t\t\theader\n\t\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t\t<!-- TODO: fix -->\n\t\t\t\t\t\t\t\t\t<!-- svelte-ignore a11y-click-events-have-key-events -->\n\t\t\t\t\t\t\t\t\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\t\tclass:sorted={sort_by === i}\n\t\t\t\t\t\t\t\t\t\tclass:des={sort_by === i && sort_direction === \"des\"}\n\t\t\t\t\t\t\t\t\t\tclass=\"sort-button {sort_direction} \"\n\t\t\t\t\t\t\t\t\t\ton:click={() => handle_sort(i)}\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<svg\n\t\t\t\t\t\t\t\t\t\t\twidth=\"1em\"\n\t\t\t\t\t\t\t\t\t\t\theight=\"1em\"\n\t\t\t\t\t\t\t\t\t\t\tviewBox=\"0 0 9 7\"\n\t\t\t\t\t\t\t\t\t\t\tfill=\"none\"\n\t\t\t\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<path d=\"M4.49999 0L8.3971 6.75H0.602875L4.49999 0Z\" />\n\t\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</th>\n\t\t\t\t\t\t{/each}\n\t\t\t\t\t</tr>\n\t\t\t\t</thead>\n\n\t\t\t\t<tbody>\n\t\t\t\t\t{#each data as row, i (row)}\n\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t{#each row as { value, id }, j (id)}\n\t\t\t\t\t\t\t\t<td\n\t\t\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\t\t\tbind:this={els[id].cell}\n\t\t\t\t\t\t\t\t\ton:touchstart={() => start_edit(id)}\n\t\t\t\t\t\t\t\t\ton:click={() => handle_cell_click(id)}\n\t\t\t\t\t\t\t\t\ton:dblclick={() => start_edit(id)}\n\t\t\t\t\t\t\t\t\ton:keydown={(e) => handle_keydown(e, i, j, id)}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\t\tclass:border-transparent={selected !== id}\n\t\t\t\t\t\t\t\t\t\tclass=\"cell-wrap\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<EditableCell\n\t\t\t\t\t\t\t\t\t\t\tbind:value\n\t\t\t\t\t\t\t\t\t\t\tbind:el={els[id].input}\n\t\t\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t\t\tedit={editing === id}\n\t\t\t\t\t\t\t\t\t\t\tdatatype={Array.isArray(datatype)\n\t\t\t\t\t\t\t\t\t\t\t\t? datatype[j]\n\t\t\t\t\t\t\t\t\t\t\t\t: datatype}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t{/each}\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t{/each}\n\t\t\t\t</tbody>\n\t\t\t</table>\n\t\t</Upload>\n\t</div>\n\t{#if editable}\n\t\t<div class=\"controls-wrap\">\n\t\t\t{#if row_count[1] === \"dynamic\"}\n\t\t\t\t<span class=\"button-wrap\">\n\t\t\t\t\t<BaseButton variant=\"secondary\" size=\"sm\" on:click={() => add_row()}>\n\t\t\t\t\t\t<svg\n\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\t\t\t\t\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\trole=\"img\"\n\t\t\t\t\t\t\twidth=\"1em\"\n\t\t\t\t\t\t\theight=\"1em\"\n\t\t\t\t\t\t\tpreserveAspectRatio=\"xMidYMid meet\"\n\t\t\t\t\t\t\tviewBox=\"0 0 32 32\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<path\n\t\t\t\t\t\t\t\tfill=\"currentColor\"\n\t\t\t\t\t\t\t\td=\"M24.59 16.59L17 24.17V4h-2v20.17l-7.59-7.58L6 18l10 10l10-10l-1.41-1.41z\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t{$_(\"dataframe.new_row\")}\n\t\t\t\t\t</BaseButton>\n\t\t\t\t</span>\n\t\t\t{/if}\n\t\t\t{#if col_count[1] === \"dynamic\"}\n\t\t\t\t<span class=\"button-wrap\">\n\t\t\t\t\t<BaseButton variant=\"secondary\" size=\"sm\" on:click={add_col}>\n\t\t\t\t\t\t<svg\n\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\t\t\t\t\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\trole=\"img\"\n\t\t\t\t\t\t\twidth=\"1em\"\n\t\t\t\t\t\t\theight=\"1em\"\n\t\t\t\t\t\t\tpreserveAspectRatio=\"xMidYMid meet\"\n\t\t\t\t\t\t\tviewBox=\"0 0 32 32\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<path\n\t\t\t\t\t\t\t\tfill=\"currentColor\"\n\t\t\t\t\t\t\t\td=\"m18 6l-1.43 1.393L24.15 15H4v2h20.15l-7.58 7.573L18 26l10-10L18 6z\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t{$_(\"dataframe.new_column\")}\n\t\t\t\t\t</BaseButton>\n\t\t\t\t</span>\n\t\t\t{/if}\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.button-wrap:hover svg {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.button-wrap svg {\n\t\tmargin-right: var(--size-1);\n\t\tmargin-left: -5px;\n\t}\n\n\t.label p {\n\t\tposition: relative;\n\t\tz-index: var(--layer-4);\n\t\tmargin-bottom: var(--size-2);\n\t\tcolor: var(--block-label-text-color);\n\t\tfont-size: var(--block-label-text-size);\n\t}\n\n\t.table-wrap {\n\t\tposition: relative;\n\t\ttransition: 150ms;\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--table-radius);\n\t\toverflow-x: auto;\n\t\toverflow-y: auto;\n\t}\n\n\t.dragging {\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.no-wrap {\n\t\twhite-space: nowrap;\n\t}\n\n\ttable {\n\t\ttransition: 150ms;\n\t\twidth: var(--size-full);\n\t\ttable-layout: auto;\n\t\toverflow: hidden;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-md);\n\t\tfont-family: var(--font-mono);\n\t}\n\n\ttable.dragging {\n\t\topacity: 0.4;\n\t}\n\n\tthead {\n\t\tposition: sticky;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tz-index: var(--layer-1);\n\t\tbox-shadow: var(--shadow-drop);\n\t}\n\n\ttr {\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t\ttext-align: left;\n\t}\n\n\ttr > * + * {\n\t\tborder-right-width: 0px;\n\t\tborder-left-width: 1px;\n\t\tborder-style: solid;\n\t\tborder-color: var(--border-color-primary);\n\t}\n\n\tth,\n\ttd {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\toutline: none;\n\t\tbox-shadow: inset 0 0 0 1px var(--ring-color);\n\t\tpadding: 0;\n\t}\n\n\tth:first-child {\n\t\tborder-top-left-radius: var(--table-radius);\n\t}\n\n\tth:last-child {\n\t\tborder-top-right-radius: var(--table-radius);\n\t}\n\n\tth:focus-within,\n\ttd:focus-within {\n\t\t--ring-color: var(--color-accent);\n\t}\n\n\ttr:last-child td:first-child {\n\t\tborder-bottom-left-radius: var(--table-radius);\n\t}\n\n\ttr:last-child td:last-child {\n\t\tborder-bottom-right-radius: var(--table-radius);\n\t}\n\n\ttr th {\n\t\tbackground: var(--table-even-background-fill);\n\t}\n\n\tth svg {\n\t\tfill: currentColor;\n\t\tfont-size: 10px;\n\t}\n\n\t.sort-button {\n\t\tdisplay: flex;\n\t\tflex: none;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\ttransition: 150ms;\n\t\tcursor: pointer;\n\t\tpadding: var(--size-2);\n\t\tcolor: var(--body-text-color-subdued);\n\t\tline-height: var(--text-sm);\n\t}\n\n\t.sort-button:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.des {\n\t\ttransform: scaleY(-1);\n\t}\n\n\t.sort-button.sorted {\n\t\tcolor: var(--color-accent);\n\t}\n\n\ttbody {\n\t\toverflow-y: scroll;\n\t}\n\n\ttbody > tr:last-child {\n\t\tborder: none;\n\t}\n\n\ttbody > tr:nth-child(even) {\n\t\tbackground: var(--table-even-background-fill);\n\t}\n\n\ttbody > tr:nth-child(odd) {\n\t\tbackground: var(--table-odd-background-fill);\n\t}\n\n\ttbody > tr:nth-child(odd):focus {\n\t\tbackground: var(--background-fill-primary);\n\t}\n\n\t.editing {\n\t\tbackground: var(--table-editing);\n\t}\n\n\t.cell-wrap {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\toutline: none;\n\t\theight: var(--size-full);\n\t\tmin-height: var(--size-9);\n\t}\n\n\t.controls-wrap {\n\t\tdisplay: flex;\n\t\tjustify-content: flex-end;\n\t\tpadding-top: var(--size-2);\n\t}\n\n\t.controls-wrap > * + * {\n\t\tmargin-left: var(--size-1);\n\t}\n</style>\n"], "names": ["has", "dequal", "foo", "bar", "ctor", "len", "insert", "target", "input", "anchor", "ctx", "dirty", "markdowncode_changes", "create_if_block_2", "span", "edit", "$$props", "value", "header", "datatype", "latex_delimiters", "el", "$$value", "currentTarget", "p", "caption", "toggle_class", "div0", "attr", "th", "th_aria_sort_value", "append", "div1", "svg", "path", "editablecell_changes", "current", "div", "td", "i", "tr", "if_block", "create_if_block_3", "table", "thead", "tbody", "create_if_block_1", "t1_value", "set_data", "t1", "if_block0", "create_if_block_4", "create_if_block", "set_style", "guess_delimitaor", "text", "possibleDelimiters", "weedOut", "delimiter", "cache", "checkLength", "line", "length", "data_uri_to_blob", "data_uri", "byte_str", "mime_str", "ab", "ia", "label", "headers", "values", "col_count", "row_count", "editable", "wrap", "height", "selected", "dispatch", "createEventDispatcher", "editing", "get_data_at", "row", "col", "data", "els", "make_headers", "_head", "_h", "fill", "_", "_id", "h", "process_data", "_values", "data_row_length", "j", "id", "_headers", "old_headers", "refresh_focus", "tick", "old_val", "get_sort_status", "name", "_sort", "direction", "get_current_indices", "acc", "arr", "_acc", "_data", "k", "start_edit", "clear", "$$invalidate", "handle_keydown", "event", "is_data", "add_row", "pos", "is_data_x", "is_data_y", "_selected", "handle_cell_click", "set_focus", "type", "cell", "sort_direction", "sort_by", "sort", "dir", "a", "b", "handle_sort", "header_edit", "update_headers_data", "new_header", "obj", "edit_header", "select", "end_header_edit", "index", "add_col", "handle_click_outside", "blob_to_string", "blob", "reader", "handle_read", "e", "head", "rest", "dsvFormat", "dragging", "dblclick_handler", "click_handler", "touchstart_handler", "click_handler_1", "dblclick_handler_1", "loc"], "mappings": "yhBAAA,IAAIA,GAAM,OAAO,UAAU,eAEpB,SAASC,GAAOC,EAAKC,EAAK,CAChC,IAAIC,EAAMC,EACV,GAAIH,IAAQC,EAAK,MAAO,GAExB,GAAID,GAAOC,IAAQC,EAAKF,EAAI,eAAiBC,EAAI,YAAa,CAC7D,GAAIC,IAAS,KAAM,OAAOF,EAAI,YAAcC,EAAI,UAChD,GAAIC,IAAS,OAAQ,OAAOF,EAAI,aAAeC,EAAI,WAEnD,GAAIC,IAAS,MAAO,CACnB,IAAKC,EAAIH,EAAI,UAAYC,EAAI,OAC5B,KAAOE,KAASJ,GAAOC,EAAIG,CAAG,EAAGF,EAAIE,CAAG,CAAC,GAAE,CAE5C,OAAOA,IAAQ,GAGhB,GAAI,CAACD,GAAQ,OAAOF,GAAQ,SAAU,CACrCG,EAAM,EACN,IAAKD,KAAQF,EAEZ,GADIF,GAAI,KAAKE,EAAKE,CAAI,GAAK,EAAEC,GAAO,CAACL,GAAI,KAAKG,EAAKC,CAAI,GACnD,EAAEA,KAAQD,IAAQ,CAACF,GAAOC,EAAIE,CAAI,EAAGD,EAAIC,CAAI,CAAC,EAAG,MAAO,GAE7D,OAAO,OAAO,KAAKD,CAAG,EAAE,SAAWE,GAIrC,OAAOH,IAAQA,GAAOC,IAAQA,CAC/B,4ICLCG,EAUCC,EAAAC,EAAAC,CAAA,4MAaCC,EAAK,CAAA,CAAA,qCAALA,EAAK,CAAA,CAAA,2EALI,QAAAA,KAAM,eAAc,gCAEpB,qEAFAC,EAAA,IAAAC,EAAA,QAAAF,KAAM,oNAHTA,EAAK,CAAA,EAAAH,EAAAE,CAAA,4BAALC,EAAK,CAAA,CAAA,uEAhBTA,EAAI,CAAA,GAAAG,GAAAH,CAAA,0CAeH,OAAAA,OAAa,OAAM,EAEdA,OAAa,WAAU,4LAHlCJ,EAYMC,EAAAO,EAAAL,CAAA,iEA1BDC,EAAI,CAAA,kVAnBG,KAAAK,CAAa,EAAAC,EACb,CAAA,MAAAC,EAAyB,EAAE,EAAAD,EAC3B,CAAA,OAAAE,EAAS,EAAK,EAAAF,EACd,CAAA,SAAAG,EAMC,KAAK,EAAAH,GACN,iBAAAI,CAIR,EAAAJ,GAEQ,GAAAK,CAA2B,EAAAL,2GAK1BK,EAAEC,qBAKD,cAAAC,KAAa,KACxBN,EAAQM,EAAc,KAAK,EAC3BA,EAAc,aAAa,WAAY,IAAI,mpBC4f1Cb,EAAK,CAAA,CAAA,wCADPJ,EAEGC,EAAAiB,EAAAf,CAAA,8BADDC,EAAK,CAAA,CAAA,wEAmBsBA,EAAK,CAAA,CAAA,iCAA/BJ,EAAyCC,EAAAkB,EAAAhB,CAAA,8BAAfC,EAAK,CAAA,CAAA,kLAepB,KAAAA,QAAgBA,EAAE,EAAA,aADfA,EAAG,EAAA,EAACA,EAAE,EAAA,CAAA,EAAE,QAAK,cAAbA,EAAG,EAAA,EAACA,EAAE,EAAA,CAAA,EAAE,oEAELA,EAAe,EAAA,CAAA,gcAWPA,EAAc,EAAA,EAAA,iBAAA,EAFpBgB,EAAAC,EAAA,SAAAjB,QAAYA,EAAC,EAAA,CAAA,EAChBgB,EAAAC,EAAA,MAAAjB,EAAY,EAAA,IAAAA,EAAK,EAAA,GAAAA,QAAmB,KAAK,0CAlB3CkB,EAAAC,EAAA,YAAAC,EAAApB,EAAgB,EAAA,EAAAA,EAAO,EAAA,EAAAA,MAASA,EAAc,EAAA,CAAA,CAAA,gCAD1CgB,EAAAG,EAAA,UAAAnB,QAAgBA,EAAE,EAAA,CAAA,uBAFlCJ,EAoCIC,EAAAsB,EAAApB,CAAA,EA/BHsB,EA8BKF,EAAAG,CAAA,qBAhBJD,EAeKC,EAAAL,CAAA,EATJI,EAQKJ,EAAAM,CAAA,EADJF,EAAsDE,EAAAC,CAAA,kIAtBjDvB,EAAA,CAAA,EAAA,QAAAwB,EAAA,KAAAzB,QAAgBA,EAAE,EAAA,6BADfA,EAAG,EAAA,EAACA,EAAE,EAAA,CAAA,EAAE,oEAaGA,EAAc,EAAA,EAAA,sDAFpBgB,EAAAC,EAAA,SAAAjB,QAAYA,EAAC,EAAA,CAAA,oBAChBgB,EAAAC,EAAA,MAAAjB,EAAY,EAAA,IAAAA,EAAK,EAAA,GAAAA,QAAmB,KAAK,GAlB3C,CAAA0B,GAAAzB,EAAA,CAAA,EAAA,OAAAmB,KAAAA,EAAApB,EAAgB,EAAA,EAAAA,EAAO,EAAA,EAAAA,MAASA,EAAc,EAAA,CAAA,uEAD1CgB,EAAAG,EAAA,UAAAnB,QAAgBA,EAAE,EAAA,CAAA,qPA2DxB,KAAAA,OAAYA,EAAE,EAAA,WACV,MAAM,QAAQA,EAAQ,CAAA,CAAA,EAC7BA,KAASA,EAAC,EAAA,CAAA,EACVA,EAAQ,CAAA,mCALFA,EAAG,EAAA,EAACA,EAAE,EAAA,CAAA,EAAE,QAAK,cAAbA,EAAG,EAAA,EAACA,EAAE,EAAA,CAAA,EAAE,8XALQgB,EAAAW,EAAA,qBAAA3B,OAAaA,EAAE,EAAA,CAAA,yEAT3CJ,EAsBIC,EAAA+B,EAAA7B,CAAA,EAdHsB,EAaKO,EAAAD,CAAA,kLALG1B,EAAA,CAAA,EAAA,OAAAwB,EAAA,KAAAzB,OAAYA,EAAE,EAAA,0BACV,MAAM,QAAQA,EAAQ,CAAA,CAAA,EAC7BA,KAASA,EAAC,EAAA,CAAA,EACVA,EAAQ,CAAA,8EALFA,EAAG,EAAA,EAACA,EAAE,EAAA,CAAA,EAAE,+CALQgB,EAAAW,EAAA,qBAAA3B,OAAaA,EAAE,EAAA,CAAA,kLAVrCA,EAAG,EAAA,CAAA,aAAsBA,EAAE,EAAA,kBAAhC,OAAI6B,GAAA,EAAA,kLADPjC,EA0BIC,EAAAiC,EAAA/B,CAAA,8FAzBIC,EAAG,EAAA,CAAA,yEAAR,OAAI6B,GAAA,mLAlDJE,EAAA/B,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,GAACgC,GAAAhC,CAAA,MAKvBA,EAAQ,EAAA,CAAA,aAAsBA,EAAE,EAAA,kBAArC,OAAI6B,GAAA,EAAA,sDA2CA7B,EAAI,EAAA,CAAA,aAAYA,EAAG,EAAA,kBAAxB,OAAI6B,GAAA,EAAA,+VAjDRjC,EA+EOC,EAAAoC,EAAAlC,CAAA,wBA3ENsB,EA0COY,EAAAC,CAAA,EAzCNb,EAwCIa,EAAAJ,CAAA,0DAGLT,EA8BOY,EAAAE,CAAA,gEA7EFnC,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,6EAKtBA,EAAQ,EAAA,CAAA,kEA2CTA,EAAI,EAAA,CAAA,oHA3CR,OAAI6B,GAAA,0BA2CL,OAAIA,GAAA,iOAmCH7B,EAAS,CAAA,EAAC,CAAC,IAAM,WAASG,GAAAH,CAAA,IAsB1BA,EAAS,CAAA,EAAC,CAAC,IAAM,WAASoC,GAAApC,CAAA,oGAvBhCJ,EA6CKC,EAAA8B,EAAA5B,CAAA,oDA5CCC,EAAS,CAAA,EAAC,CAAC,IAAM,yGAsBjBA,EAAS,CAAA,EAAC,CAAC,IAAM,+ZArBrBJ,EAmBMC,EAAAO,EAAAL,CAAA,+MAFHsC,EAAArC,MAAG,mBAAmB,EAAA,+bAfvBJ,EAcKC,EAAA0B,EAAAxB,CAAA,EAJJsB,EAGCE,EAAAC,CAAA,4BAEDvB,EAAA,CAAA,EAAA,QAAAoC,KAAAA,EAAArC,MAAG,mBAAmB,EAAA,KAAAsC,GAAAC,EAAAF,CAAA,qKAM4BrC,EAAO,EAAA,CAAA,sFAD5DJ,EAmBMC,EAAAO,EAAAL,CAAA,+MAFHsC,EAAArC,MAAG,sBAAsB,EAAA,ybAf1BJ,EAcKC,EAAA0B,EAAAxB,CAAA,EAJJsB,EAGCE,EAAAC,CAAA,4BAEDvB,EAAA,CAAA,EAAA,QAAAoC,KAAAA,EAAArC,MAAG,sBAAsB,EAAA,KAAAsC,GAAAC,EAAAF,CAAA,mEA/I1BG,EAAAxC,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,GAACyC,GAAAzC,CAAA,qCAYxB,UACE,iBACO,iBACA,2JAsFZA,EAAQ,CAAA,GAAA0C,GAAA1C,CAAA,2HA5Fe2C,GAAA1B,EAAA,aAAA,OAAAjB,OAAW,OAAY,OAASA,KAAS,IAAI,uCADxDA,EAAI,CAAA,CAAA,gCATJgB,EAAAM,EAAA,QAAAtB,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,CAAC,UAA7CJ,EAsJKC,EAAAyB,EAAAvB,CAAA,wBAhJJsB,EA+FKC,EAAAL,CAAA,iEAzGKjB,EAAoB,EAAA,CAAA,wBACfA,EAAoB,EAAA,CAAA,qCAI9BA,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,mMASH2C,GAAA1B,EAAA,aAAA,OAAAjB,OAAW,OAAY,OAASA,KAAS,IAAI,wEADxDA,EAAI,CAAA,CAAA,EA6FhBA,EAAQ,CAAA,mHAtGIgB,EAAAM,EAAA,QAAAtB,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,CAAC,wIArEnC4C,GACRC,EACAC,EAA4B,QAErBA,EAAmB,OAAOC,CAAO,EAE/B,SAAAA,EAAQC,EAAiB,CAC7B,IAAAC,KACG,OAAAJ,EAAK,MAAM;AAAA,CAAI,EAAE,MAAMK,CAAW,EAEhC,SAAAA,EAAYC,EAAY,KAC3BA,QACG,GAGJ,IAAAC,EAASD,EAAK,MAAMH,CAAS,EAAE,OAC/B,OAAAC,EAAQ,IACXA,EAAQG,GAEFH,IAAUG,GAAUA,EAAS,IAK9B,SAAAC,GAAiBC,EAAgB,OACnCC,EAAW,KAAKD,EAAS,MAAM,GAAG,EAAE,CAAC,CAAA,EACrCE,EAAWF,EAAS,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAE5DG,EAAS,IAAA,YAAYF,EAAS,MAAM,EACpCG,EAAE,IAAO,WAAWD,CAAE,UAEnB,EAAI,EAAG,EAAIF,EAAS,OAAQ,IACpCG,EAAG,CAAC,EAAIH,EAAS,WAAW,CAAC,EAGnB,OAAA,IAAA,KAAM,CAAAE,CAAE,EAAK,CAAA,KAAMD,CAAQ,CAAA,qDAze5B,SAAA/C,CAA+B,EAAAH,EAC/B,CAAA,MAAAqD,EAAuB,IAAI,EAAArD,GAC3B,QAAAsD,EAAO,EAAA,EAAAtD,GACP,OAAAuD,EAAM,CAAA,EAAA,CAAA,EAAAvD,GAGN,UAAAwD,CAAwC,EAAAxD,GACxC,UAAAyD,CAAwC,EAAAzD,GACxC,iBAAAI,CAIR,EAAAJ,EAEQ,CAAA,SAAA0D,EAAW,EAAI,EAAA1D,EACf,CAAA,KAAA2D,EAAO,EAAK,EAAA3D,EACZ,CAAA,OAAA4D,EAA6B,MAAS,EAAA5D,EAE7C6D,EAA2B,GAazB,MAAAC,EAAWC,KAKb,IAAAC,EAA0B,SAExBC,EAAW,CAAIC,EAAaC,IACjCC,EAAKF,CAAG,EAAEC,CAAG,EAAE,UAWZE,EAAG,CAAA,EAOE,SAAAC,EAAaC,EAAe,CAChC,IAAAC,EAAKD,GAAK,MACVf,EAAU,CAAC,IAAM,SAAWgB,EAAG,OAAShB,EAAU,CAAC,EAAA,OAChDiB,EAAO,MAAMjB,EAAU,CAAC,EAAIgB,EAAG,MAAM,EACzC,KAAK,EAAE,EACP,IAAG,CAAEE,EAAGnD,IAAC,GAAQA,EAAIiD,EAAG,QAAM,EAChCA,EAAKA,EAAG,OAAOC,CAAI,EAGf,MAAA,CAAAD,GAAMA,EAAG,SAAW,EACjB,MAAMhB,EAAU,CAAC,CACtB,EAAA,KAAK,CAAC,EACN,IAAK,CAAAkB,EAAGnD,IAAC,CACH,MAAAoD,OAAWpD,gBACjB8C,EAAIM,CAAG,EAAM,CAAA,KAAM,KAAM,MAAO,IAAI,EAAAN,CAAA,GAC3B,GAAIM,EAAK,MAAO,KAAK,UAAUpD,EAAI,CAAC,KAGzCiD,EAAG,IAAK,CAAAI,EAAGrD,IAAC,CACZ,MAAAoD,OAAWpD,gBACjB8C,EAAIM,CAAG,EAAM,CAAA,KAAM,KAAM,MAAO,IAAI,EAAAN,CAAA,EAC3B,CAAA,GAAIM,EAAK,MAAOC,GAAK,EAAE,IAIzB,SAAAC,EAAaC,EAA8B,OAI7CC,EAAkBD,EAAQ,OACzB,OAAA,MACNrB,EAAU,CAAC,IAAM,SAEdsB,EAAkBtB,EAAU,CAAC,EAD7BA,EAAU,CAAC,EAGXsB,CAAe,EAEjB,KAAK,CAAC,EACN,IAAK,CAAAL,EAAGnD,IACR,MACCiC,EAAU,CAAC,IAAM,QACdA,EAAU,CAAC,EACXuB,EAAkB,EAClBD,EAAQ,CAAC,EAAE,OACXxB,EAAQ,MAAM,EAEhB,KAAK,CAAC,EACN,IAAK,CAAAoB,EAAGM,IAAC,OACHC,EAAE,GAAM1D,KAAKyD,gBACnBX,EAAIY,CAAE,EAAM,CAAA,MAAO,KAAM,KAAM,IAAI,EAAAZ,CAAA,GAC1B,MAAOS,IAAUvD,CAAC,IAAIyD,CAAC,GAAK,GAAI,GAAAC,UAK1CC,EAAWZ,EAAahB,CAAO,EAC/B6B,iBAiBWC,GAAa,CAChB,OAAApB,GAAY,gBAChBqB,EAAI,EACVhB,EAAIL,CAAiB,GAAG,OAAO,MAAK,GACnB,OAAAH,GAAa,iBACxBwB,EAAI,EACVhB,EAAIR,CAAkB,GAAG,OAAO,MAAK,OAInCO,EAAI,CAAA,CAAA,CAAA,EAEJkB,EAQK,SAAAC,GACRC,EACAC,EACAC,EAAyB,CAEpB,GAAA,CAAAD,QAAc,UACfnC,EAAQmC,CAAK,IAAMD,EAAI,IACtBE,IAAc,MAAK,MAAS,eAC5BA,IAAc,MAAK,MAAS,mBAG1B,OAGC,SAAAC,GAAoBV,EAAU,CAC/B,OAAAb,EAAK,QACVwB,EAAKC,EAAKtE,IAAC,OACLyD,EAAIa,EAAI,QACZC,EAAMC,EAAOC,KAAOf,IAAOc,EAAM,GAAKC,GAAIF,EAAI,EAC7C,EAGI,OAAAd,OAAWY,EAAO,CAAArE,EAAGyD,CAAC,GAE5B,CAAA,KAAK,kBAIMiB,GAAWhB,EAAYiB,EAAe,KAC/CxC,GAAYM,IAAYiB,EAAE,UAE3BiB,EAAK,CACD,KAAA,CAAA3E,EAAGyD,CAAC,EAAIW,GAAoBV,CAAE,EAErCkB,EAAA,GAAA/B,EAAK7C,CAAC,EAAEyD,CAAC,EAAE,MAAQ,GAAEZ,CAAA,EAEtB+B,EAAA,EAAAnC,EAAUiB,CAAE,QACNI,EAAI,QACF,MAAA7F,CAAK,EAAK6E,EAAIY,CAAE,EACxBzF,GAAO,MAAK,EAIE,eAAA4G,GACdC,EACA9E,EACAyD,EACAC,EAAU,KAENqB,EAEI,OAAAD,EAAM,IAAG,KACX,gBACArC,EAAO,MACXqC,EAAM,eAAc,EACpBC,EAAUlC,EAAK7C,CAAC,EAAEyD,EAAI,CAAC,EACvBmB,EAAA,EAAAtC,EAAWyC,EAAUA,EAAQ,GAAKzC,CAAQ,YAEtC,eACAG,EAAO,MACXqC,EAAM,eAAc,EACpBC,EAAUlC,EAAK7C,CAAC,EAAEyD,EAAI,CAAC,EACvBmB,EAAA,EAAAtC,EAAWyC,EAAUA,EAAQ,GAAKzC,CAAQ,YAEtC,eACAG,EAAO,MACXqC,EAAM,eAAc,EACpBC,EAAUlC,EAAK7C,EAAI,CAAC,MACpBsC,EAAWyC,EAAUA,EAAQtB,CAAC,EAAE,GAAKnB,CAAQ,YAEzC,aACAG,EAAO,MACXqC,EAAM,eAAc,EACpBC,EAAUlC,EAAK7C,EAAI,CAAC,MACpBsC,EAAWyC,EAAUA,EAAQtB,CAAC,EAAE,GAAKnB,CAAQ,YAEzC,aACCH,EAAQ,MACb2C,EAAM,eAAc,EACpBF,EAAA,EAAAtC,EAAWG,CAAO,EAClBmC,EAAA,EAAAnC,EAAU,EAAK,YAEX,YACCN,EAAQ,MAGT,GAFJ2C,EAAM,eAAc,EAEhBA,EAAM,SAAQ,CACjBE,GAAQhF,CAAC,QACH8D,EAAI,QACHmB,EAAG,EAAIb,GAAoBV,CAAE,MACpCpB,EAAWO,EAAKoC,GAAM,CAAC,EAAExB,CAAC,EAAE,EAAE,OAE1BhB,IAAYiB,EACfkB,EAAA,EAAAnC,EAAU,EAAK,EAEfiC,GAAWhB,CAAE,YAKX,gBACCvB,EAAQ,MACRM,IACJqC,EAAM,eAAc,EACpBF,EAAA,GAAA/B,EAAK7C,CAAC,EAAEyD,CAAC,EAAE,MAAQ,GAAEZ,CAAA,aAGlB,aACCV,EAAQ,MACRM,IACJqC,EAAM,eAAc,EACpBF,EAAA,GAAA/B,EAAK7C,CAAC,EAAEyD,CAAC,EAAE,MAAQ,GAAEZ,CAAA,aAGlB,MACA,IAAAsB,EAAYW,EAAM,SAAY,GAAI,EAElCI,EAAYrC,EAAK7C,CAAC,EAAEyD,EAAIU,CAAS,EACjCgB,GACHtC,IAAO7C,EAAImE,CAAS,IAAIA,EAAY,EAAI,EAAIR,EAAS,OAAS,CAAC,EAC5DyB,GAAYF,GAAaC,GACzBC,KACHN,EAAM,eAAc,EACpBF,EAAA,EAAAtC,EAAW8C,GAAYA,GAAU,GAAK9C,CAAQ,GAE/CsC,EAAA,EAAAnC,EAAU,EAAK,iBAKZ,CAAAA,GAAYA,GAAWA,IAAYiB,IACrCoB,EAAM,IAAI,SAAW,GAErBJ,GAAWhB,EAAI,EAAI,SAOR,eAAA2B,GAAkB3B,EAAU,CACtCjB,IAAYiB,GACZpB,IAAaoB,IACjBkB,EAAA,EAAAnC,EAAU,EAAK,EACfmC,EAAA,EAAAtC,EAAWoB,CAAE,kBAGC4B,GACd5B,EACA6B,EAAuB,IAEnBA,IAAS,QAAiB,OAAA7B,GAAM,iBAC7BI,EAAI,EACVhB,EAAIY,CAAE,EAAE,OAAO,MAAK,GAIpB6B,IAAS,QACF,OAAA7B,GAAM,WAAS,OACfpB,GAAa,SAAQ,CAExB,IAAAkD,EAAO1C,EAAIR,CAAQ,GAAG,WACpBwB,EAAI,EACV0B,GAAM,MAAK,EAGR,GAAAD,IAAS,UAAmB,OAAA7B,GAAM,SAAQ,OACrC,KAAA8B,CAAI,EAAK1C,EAAIY,CAAE,QACjBI,EAAI,EACV0B,GAAM,MAAK,OAQTC,EACAC,YAEKC,GAAK/C,EAAagD,EAAkB,CACxCA,IAAQ,WACX/C,EAAOA,EAAK,MAAMgD,EAAGC,IAAOD,EAAEjD,CAAG,EAAE,MAAQkD,EAAElD,CAAG,EAAE,MAAK,GAAQ,CAAC,CAAA,EACtDgD,IAAQ,YAClB/C,EAAOA,EAAK,MAAMgD,EAAGC,IAAOD,EAAEjD,CAAG,EAAE,MAAQkD,EAAElD,CAAG,EAAE,MAAK,GAAQ,CAAC,CAAA,EAIzD,SAAAmD,GAAYnD,EAAW,CACpB,OAAA8C,IAAY,UAAYA,KAAY9C,GAC9CgC,EAAA,GAAAa,EAAiB,KAAK,EACtBb,EAAA,GAAAc,GAAU9C,CAAG,GAET6C,IAAmB,MACtBb,EAAA,GAAAa,EAAiB,KAAK,EACZA,IAAmB,OAC7Bb,EAAA,GAAAa,EAAiB,KAAK,EAIxBE,GAAK/C,EAAK6C,CAAc,MAGrBO,WAEKC,IAAmB,CAChB,GAAA,OAAA3D,GAAa,SAAQ,CACzB,MAAA4D,EAAapD,EAAIR,CAAQ,EAAE,OAAO,SACpCqB,EAAS,KAAM3D,GAAMA,EAAE,KAAOsC,CAAQ,EAAA,KACrC6D,EAAMxC,EAAS,KAAM3D,GAAMA,EAAE,KAAOsC,CAAQ,EAC5C4D,IAAYC,EAAK,MAAWD,QAE5BA,GAAYvC,EAAS,KAAI,CAAG,GAAIrB,EAAU,MAAO4D,CAAU,CAAA,kBAKnDE,GAAYhD,EAAaiD,EAAgB,EAClDlE,GAAYF,EAAU,CAAC,IAAM,WAAaQ,IAAYW,IAC3DwB,EAAA,GAAAoB,EAAc5C,CAAG,QACXU,EAAI,EACVhB,EAAIM,CAAG,EAAE,OAAO,MAAK,EACjBiD,GAAQvD,EAAIM,CAAG,EAAE,OAAO,OAAM,GAG1B,SAAAkD,GAAgBxB,EAAoB,IACvC3C,EAEG,OAAA2C,EAAM,IAAG,KACX,aACA,YACA,MACJA,EAAM,eAAc,EACpBF,EAAA,EAAAtC,EAAW0D,CAAW,EACtBpB,EAAA,GAAAoB,EAAc,EAAK,EACnBC,YAKM,SAAAjB,GAAQuB,EAAc,IAC1BrE,EAAU,CAAC,IAAM,cACjBW,EAAK,SAAW,EAAC,MACpBb,EAAM,CAAI,MAAMD,EAAQ,MAAM,EAAE,KAAK,EAAE,CAAA,CAAA,SAGxCc,EAAK,OACJ0D,EAAQA,EAAQ,EAAI1D,EAAK,OACzB,EACA,MAAMA,EAAK,CAAC,EAAE,MAAM,EAClB,KAAK,CAAC,EACN,IAAK,CAAAM,EAAGnD,IAAC,CACH,MAAAoD,EAAS,GAAAP,EAAK,UAAU7C,gBAC9B8C,EAAIM,CAAG,EAAM,CAAA,KAAM,KAAM,MAAO,IAAI,EAAAN,CAAA,EAC3B,CAAA,GAAIM,EAAK,MAAO,EAAE,6CAOhBoD,IAAO,IACjBvE,EAAU,CAAC,IAAM,UAAS,eACrBjC,EAAI,EAAGA,EAAI6C,EAAK,OAAQ7C,IAAC,CAC3B,MAAAoD,KAASpD,KAAK6C,EAAK7C,CAAC,EAAE,cAC5B8C,EAAIM,CAAG,EAAM,CAAA,KAAM,KAAM,MAAO,IAAI,EAAAN,CAAA,EACpCD,EAAK7C,CAAC,EAAE,KAAI,CAAG,GAAIoD,EAAK,MAAO,EAAE,CAAA,QAG5BA,EAAG,KAAQO,EAAS,cAC1Bb,EAAIM,CAAG,EAAM,CAAA,KAAM,KAAM,MAAO,IAAI,EAAAN,CAAA,EACpCa,EAAS,KAAI,CAAG,GAAIP,EAAK,MAAiB,UAAAO,EAAS,OAAS,oEAKtDG,EAAI,EAEVsC,GAAYhD,EAAK,EAAI,EAGb,SAAAqD,GAAqB3B,EAAY,CAC9B,OAAArC,GAAY,UAAYK,EAAIL,CAAO,GAE5CK,EAAIL,CAAO,EAAE,OAASqC,EAAM,QAAM,CACjChC,EAAIL,CAAO,EAAE,MAAM,SAASqC,GAAO,MAAqB,GAEzDF,EAAA,EAAAnC,EAAU,EAAK,EAIN,OAAAuD,GAAgB,UAAYlD,EAAIkD,CAAW,GAEpDlD,EAAIkD,CAAW,EAAE,OAASlB,EAAM,QAAM,CACrChC,EAAIkD,CAAW,EAAE,MAAM,SAASlB,EAAM,MAAqB,IAE5DF,EAAA,EAAAtC,EAAW0D,CAAW,EACtBpB,EAAA,GAAAoB,EAAc,EAAK,EACnBC,KACArB,EAAA,GAAAoB,EAAc,EAAK,GA2Cb,SAAAU,GAAeC,EAAU,CAC3B,MAAAC,MAAa,WAEV,SAAAC,EAAYC,EAA4B,KAC3CA,GAAG,QAAQ,QAAM,OAAWA,EAAE,OAAO,QAAW,SAAQ,aAEtD3F,CAAS,EAAIJ,GAAiB+F,EAAE,OAAO,OAAM,CAAG,IAAK,GAAI,CAAA,EAEzD,CAAAC,EAAS,GAAAC,CAAI,EAAIC,GAAU9F,CAAS,EAAE,UAAU2F,EAAE,OAAO,MAAM,EAEtElC,EAAA,GAAAjB,EAAWZ,EACVd,EAAU,CAAC,IAAM,QAAU8E,EAAK,MAAM,EAAG9E,EAAU,CAAC,CAAA,EAAK8E,CAAI,CAAA,EAG9DnC,EAAA,GAAA5C,EAASgF,CAAI,EACbJ,EAAO,oBAAoB,UAAWC,CAAW,EAGlDD,EAAO,iBAAiB,UAAWC,CAAW,EAE9CD,EAAO,WAAWD,CAAI,EAGnB,IAAAO,GAAW,mCA4CGpE,EAAIY,CAAE,EAAE,MAAKhF,CAAA,IAAboE,EAAIY,CAAE,EAAE,MAAKhF,WAGH,MAAAyI,GAAAzD,GAAA0C,GAAY1C,CAAE,EAWjB0D,GAAApH,GAAA+F,GAAY/F,CAAC,8CAtBpB8C,EAAIY,CAAE,EAAE,KAAI3E,sGA0DX+D,EAAIY,CAAE,EAAE,MAAKhF,CAAA,IAAboE,EAAIY,CAAE,EAAE,MAAKhF,uDAZboE,EAAIY,CAAE,EAAE,KAAI3E,YACF,MAAAsI,GAAA3D,GAAAgB,GAAWhB,CAAE,EAClB4D,GAAA5D,GAAA2B,GAAkB3B,CAAE,EACjB6D,GAAA7D,GAAAgB,GAAWhB,CAAE,YACnBoD,IAAMjC,GAAeiC,EAAG9G,EAAGyD,EAAGC,CAAE,wCA7DzCoD,GAAMJ,GAAelF,GAAiBsF,EAAE,OAAO,IAAI,CAAA,SAyFD9B,0ZAhmBzDhD,GAAW,CAAA,MAAM,QAAQA,CAAM,QAClCD,EAAUC,EAAO,OAAO,OACxBA,EAASA,EAAO,IAAI,EACpB4C,EAAA,EAAAtC,EAAW,EAAK,GACNN,IAAW,YACrBA,EAAM,CAAA,CAAA,EACN4C,EAAA,EAAAtC,EAAW,EAAK,uBAcbA,IAAa,GAAK,CACf,MAAAkF,EAAMlF,EAAS,MAAM,GAAG,EACxBK,EAAM,SAAS6E,EAAI,CAAC,CAAA,EACpB5E,EAAM,SAAS4E,EAAI,CAAC,CAAA,EACrB,CAAA,MAAM7E,CAAG,GAAM,CAAA,MAAMC,CAAG,GAC5BL,EAAS,SAAQ,CAAI,MAAK,CAAGI,EAAKC,CAAG,EAAG,MAAOF,EAAYC,EAAKC,CAAG,8CAsEhElF,GAAOqE,EAAS6B,CAAW,SAC/BD,EAAWZ,EAAahB,CAAO,CAAA,EAE/B6C,EAAA,GAAAhB,EAAc7B,CAAO,EACrB8B,iDAGMnG,GAAOsE,EAAQ+B,CAAO,SAC7BlB,EAAOS,EAAatB,CAA+B,CAAA,EACnD4C,EAAA,GAAAb,EAAU/B,CAA+B,EAEzC6B,0BAiBEF,GACFpB,EAAS,SAAQ,CAChB,KAAMM,EAAK,IAAK,GAAM,EAAE,IAAO,CAAA,CAAA,MAAAnE,KAAYA,CAAK,CAAA,EAChD,QAASiF,EAAS,IAAKN,GAAMA,EAAE,KAAK,uBAgLnCiC,GAAU7C,EAAS,MAAM,qBACzB6C,GAAUhD,EAAU,QAAQ", "x_google_ignoreList": [0]}