{"version": 3, "file": "index-a1ea7e02.js", "sources": ["../../../../js/annotatedimage/static/AnnotatedImage.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\n\timport { Block, BlockLabel, Empty } from \"@gradio/atoms\";\n\timport { Image } from \"@gradio/icons\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { type FileData, normalise_file } from \"@gradio/upload\";\n\timport { _ } from \"svelte-i18n\";\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: [FileData, [FileData, string][]] | null;\n\tlet old_value: [FileData, [FileData, string][]] | null;\n\tlet _value: [FileData, [FileData, string][]] | null;\n\texport let label = $_(\"annotated_image.annotated_image\");\n\texport let show_label = true;\n\texport let show_legend = true;\n\texport let height: number | undefined;\n\texport let width: number | undefined;\n\texport let color_map: Record<string, string>;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let root: string;\n\texport let root_url: string;\n\tlet active: string | null = null;\n\texport let loading_status: LoadingStatus;\n\texport let gradio: Gradio<{\n\t\tchange: undefined;\n\t\tselect: SelectData;\n\t}>;\n\n\t$: {\n\t\tif (value !== old_value) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}\n\t\tif (value) {\n\t\t\t_value = [\n\t\t\t\tnormalise_file(value[0], root, root_url) as FileData,\n\t\t\t\tvalue[1].map(([file, _label]) => [\n\t\t\t\t\tnormalise_file(file, root, root_url) as FileData,\n\t\t\t\t\t_label\n\t\t\t\t])\n\t\t\t];\n\t\t} else {\n\t\t\t_value = null;\n\t\t}\n\t}\n\tfunction handle_mouseover(_label: string): void {\n\t\tactive = _label;\n\t}\n\tfunction handle_mouseout(): void {\n\t\tactive = null;\n\t}\n\n\tfunction handle_click(i: number): void {\n\t\tgradio.dispatch(\"select\", {\n\t\t\tvalue: label,\n\t\t\tindex: i\n\t\t});\n\t}\n</script>\n\n<Block\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\tpadding={false}\n\t{height}\n\t{width}\n\tallow_overflow={false}\n\t{container}\n\t{scale}\n\t{min_width}\n>\n\t<StatusTracker {...loading_status} />\n\t<BlockLabel {show_label} Icon={Image} label={label || $_(\"image.image\")} />\n\n\t<div class=\"container\">\n\t\t{#if _value == null}\n\t\t\t<Empty size=\"large\" unpadded_box={true}><Image /></Empty>\n\t\t{:else}\n\t\t\t<div class=\"image-container\">\n\t\t\t\t<!-- svelte-ignore a11y-missing-attribute -->\n\t\t\t\t<img\n\t\t\t\t\tclass=\"base-image\"\n\t\t\t\t\tclass:fit-height={height}\n\t\t\t\t\tsrc={_value ? _value[0].data : null}\n\t\t\t\t/>\n\t\t\t\t{#each _value ? _value[1] : [] as [file, label], i}\n\t\t\t\t\t<!-- svelte-ignore a11y-missing-attribute -->\n\t\t\t\t\t<img\n\t\t\t\t\t\tclass=\"mask fit-height\"\n\t\t\t\t\t\tclass:active={active == label}\n\t\t\t\t\t\tclass:inactive={active != label && active != null}\n\t\t\t\t\t\tsrc={file.data}\n\t\t\t\t\t\tstyle={color_map && label in color_map\n\t\t\t\t\t\t\t? null\n\t\t\t\t\t\t\t: `filter: hue-rotate(${Math.round(\n\t\t\t\t\t\t\t\t\t(i * 360) / _value[1].length\n\t\t\t\t\t\t\t  )}deg);`}\n\t\t\t\t\t/>\n\t\t\t\t{/each}\n\t\t\t</div>\n\t\t\t{#if show_legend && _value}\n\t\t\t\t<div class=\"legend\">\n\t\t\t\t\t{#each _value[1] as [_, label], i}\n\t\t\t\t\t\t<!-- TODO: fix -->\n\t\t\t\t\t\t<!-- svelte-ignore a11y-click-events-have-key-events -->\n\t\t\t\t\t\t<!-- svelte-ignore a11y-no-static-element-interactions -->\n\t\t\t\t\t\t<div\n\t\t\t\t\t\t\tclass=\"legend-item\"\n\t\t\t\t\t\t\tstyle=\"background-color: {color_map && label in color_map\n\t\t\t\t\t\t\t\t? color_map[label] + '88'\n\t\t\t\t\t\t\t\t: `hsla(${Math.round(\n\t\t\t\t\t\t\t\t\t\t(i * 360) / _value[1].length\n\t\t\t\t\t\t\t\t  )}, 100%, 50%, 0.3)`}\"\n\t\t\t\t\t\t\ton:mouseover={() => handle_mouseover(label)}\n\t\t\t\t\t\t\ton:focus={() => handle_mouseover(label)}\n\t\t\t\t\t\t\ton:mouseout={() => handle_mouseout()}\n\t\t\t\t\t\t\ton:blur={() => handle_mouseout()}\n\t\t\t\t\t\t\ton:click={() => handle_click(i)}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{label}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t{/if}\n\t</div>\n</Block>\n\n<style>\n\t.base-image {\n\t\tdisplay: block;\n\t\twidth: 100%;\n\t\theight: auto;\n\t}\n\t.container {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\t.image-container {\n\t\tposition: relative;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tflex-grow: 1;\n\t\twidth: 100%;\n\t\toverflow: hidden;\n\t}\n\t.fit-height {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: contain;\n\t}\n\t.mask {\n\t\topacity: 0.85;\n\t\ttransition: all 0.2s ease-in-out;\n\t}\n\t.image-container:hover .mask {\n\t\topacity: 0.3;\n\t}\n\t.mask.active {\n\t\topacity: 1;\n\t}\n\t.mask.inactive {\n\t\topacity: 0;\n\t}\n\t.legend {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tflex-wrap: wrap;\n\t\talign-content: center;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tgap: var(--spacing-sm);\n\t\tpadding: var(--spacing-sm);\n\t}\n\t.legend-item {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tcursor: pointer;\n\t\tborder-radius: var(--radius-sm);\n\t\tpadding: var(--spacing-sm);\n\t}\n</style>\n"], "names": ["ctx", "i", "if_block", "create_if_block_1", "src_url_equal", "img", "img_src_value", "attr", "insert", "target", "div", "anchor", "append", "dirty", "toggle_class", "each_value", "ensure_array_like", "set_data", "t0", "t0_value", "Image", "elem_id", "$$props", "elem_classes", "visible", "value", "old_value", "_value", "label", "$_", "show_label", "show_legend", "height", "width", "color_map", "container", "scale", "min_width", "root", "root_url", "active", "loading_status", "gradio", "handle_mouseover", "_label", "$$invalidate", "handle_mouseout", "handle_click", "mouseover_handler", "focus_handler", "click_handler", "normalise_file", "file"], "mappings": "0oBA2FWA,EAAM,EAAA,EAAGA,EAAM,EAAA,EAAC,CAAC,EAAA,CAAA,CAAA,uBAAtB,OAAIC,GAAA,mBAeF,IAAAC,EAAAF,MAAeA,EAAM,EAAA,GAAAG,EAAAH,CAAA,4IAjBnBI,EAAAC,EAAA,IAAAC,EAAAN,MAASA,EAAM,EAAA,EAAC,CAAC,EAAE,KAAO,IAAI,GAAAO,EAAAF,EAAA,MAAAC,CAAA,mBADjBN,EAAM,CAAA,CAAA,uDAJ1BQ,EAqBKC,EAAAC,EAAAC,CAAA,EAnBJC,EAICF,EAAAL,CAAA,mGADKQ,EAAA,CAAA,EAAA,MAAA,CAAAT,EAAAC,EAAA,IAAAC,EAAAN,MAASA,EAAM,EAAA,EAAC,CAAC,EAAE,KAAO,IAAI,0CADjBA,EAAM,CAAA,CAAA,kBAGlBA,EAAM,EAAA,EAAGA,EAAM,EAAA,EAAC,CAAC,EAAA,CAAA,CAAA,oBAAtB,OAAIC,GAAA,EAAA,iHAAJ,OAeED,MAAeA,EAAM,EAAA,iMAxBQ,qUAe1BI,EAAAC,EAAA,IAAAC,EAAAN,MAAK,IAAI,GAAAO,EAAAF,EAAA,MAAAC,CAAA,gBACPN,EAAS,CAAA,GAAIA,EAAS,EAAA,IAAAA,EAAA,CAAA,EAC1B,2BACsB,KAAK,MAC1BA,EAAC,EAAA,EAAG,IAAOA,EAAO,EAAA,EAAA,CAAC,EAAE,MAAA,QAAA,EANXc,EAAAT,EAAA,SAAAL,OAAUA,EAAK,EAAA,CAAA,EACbc,EAAAT,EAAA,WAAAL,EAAU,EAAA,GAAAA,EAAS,EAAA,GAAAA,OAAU,IAAI,UAHlDQ,EAUCC,EAAAJ,EAAAM,CAAA,UANKE,EAAA,CAAA,EAAA,MAAA,CAAAT,EAAAC,EAAA,IAAAC,EAAAN,MAAK,IAAI,kCACPA,EAAS,CAAA,GAAIA,EAAS,EAAA,IAAAA,EAAA,CAAA,EAC1B,2BACsB,KAAK,MAC1BA,EAAC,EAAA,EAAG,IAAOA,EAAO,EAAA,EAAA,CAAC,EAAE,MAAA,sCANXc,EAAAT,EAAA,SAAAL,OAAUA,EAAK,EAAA,CAAA,cACbc,EAAAT,EAAA,WAAAL,EAAU,EAAA,GAAAA,EAAS,EAAA,GAAAA,OAAU,IAAI,sCAY3Ce,EAAAC,EAAAhB,MAAO,CAAC,CAAA,uBAAb,OAAIC,GAAA,0HADPO,EAqBKC,EAAAC,EAAAC,CAAA,2EApBGI,EAAAC,EAAAhB,MAAO,CAAC,CAAA,oBAAb,OAAIC,GAAA,EAAA,iHAAJ,qDAiBCD,EAAK,EAAA,EAAA,kNAXoBA,EAAS,CAAA,GAAIA,EAAS,EAAA,IAAAA,EAAA,CAAA,EAC7CA,EAAS,CAAA,EAACA,EAAK,EAAA,CAAA,EAAI,aACX,KAAK,MACZA,EAAC,EAAA,EAAG,IAAOA,EAAO,EAAA,EAAA,CAAC,EAAE,MAAA,oBAAA,UAL1BQ,EAcKC,EAAAC,EAAAC,CAAA,qJADHX,EAAK,EAAA,EAAA,KAAAiB,GAAAC,EAAAC,CAAA,oCAXoBnB,EAAS,CAAA,GAAIA,EAAS,EAAA,IAAAA,EAAA,CAAA,EAC7CA,EAAS,CAAA,EAACA,EAAK,EAAA,CAAA,EAAI,aACX,KAAK,MACZA,EAAC,EAAA,EAAG,IAAOA,EAAO,EAAA,EAAA,CAAC,EAAE,MAAA,oBAAA,+OAxCZA,EAAc,EAAA,CAAA,8GACFoB,QAAcpB,EAAK,EAAA,GAAIA,EAAE,EAAA,EAAC,aAAa,0CAGhE,OAAAA,OAAU,KAAI,wLADpBQ,EAmDKC,EAAAC,EAAAC,CAAA,yDAtDcX,EAAc,EAAA,CAAA,CAAA,CAAA,2EACYA,EAAK,EAAA,GAAIA,EAAE,EAAA,EAAC,aAAa,yZAT7D,yCAGO,oiBA/DL,GAAA,CAAA,QAAAqB,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,GACd,MAAAG,CAA8C,EAAAH,EACrDI,EACAC,GACO,MAAAC,EAAQC,EAAG,iCAAiC,CAAA,EAAAP,EAC5C,CAAA,WAAAQ,EAAa,EAAI,EAAAR,EACjB,CAAA,YAAAS,EAAc,EAAI,EAAAT,GAClB,OAAAU,CAA0B,EAAAV,GAC1B,MAAAW,CAAyB,EAAAX,GACzB,UAAAY,CAAiC,EAAAZ,EACjC,CAAA,UAAAa,EAAY,EAAI,EAAAb,EAChB,CAAA,MAAAc,EAAuB,IAAI,EAAAd,EAC3B,CAAA,UAAAe,EAAgC,MAAS,EAAAf,GACzC,KAAAgB,CAAY,EAAAhB,GACZ,SAAAiB,CAAgB,EAAAjB,EACvBkB,EAAwB,MACjB,eAAAC,CAA6B,EAAAnB,GAC7B,OAAAoB,CAGT,EAAApB,EAmBO,SAAAqB,EAAiBC,EAAc,CACvCC,EAAA,GAAAL,EAASI,CAAM,WAEPE,GAAe,CACvBD,EAAA,GAAAL,EAAS,IAAI,EAGL,SAAAO,EAAa9C,EAAS,CAC9ByC,EAAO,SAAS,SAAQ,CACvB,MAAOd,EACP,MAAO3B,CAAA,CAAA,EA2DiB,MAAA+C,EAAApB,GAAAe,EAAiBf,CAAK,EAC1BqB,GAAArB,GAAAe,EAAiBf,CAAK,SACnBkB,WACJA,IACCI,GAAAjD,GAAA8C,EAAa9C,CAAC,ioBAzF/BwB,IAAUC,IACbmB,EAAA,GAAAnB,EAAYD,CAAK,EACjBiB,EAAO,SAAS,QAAQ,GAErBjB,OACHE,EAAM,CACLwB,EAAe1B,EAAM,CAAC,EAAGa,EAAMC,CAAQ,EACvCd,EAAM,CAAC,EAAE,IAAG,CAAA,CAAG2B,EAAMR,EAAM,IAC1B,CAAAO,EAAeC,EAAMd,EAAMC,CAAQ,EACnCK,EAAA,CAAA,IAIFC,EAAA,GAAAlB,EAAS,IAAI"}