`ImageBlur` 노드는 이미지에 가우시안 블러를 적용하여 가장자리를 부드럽게 하고 세부 사항과 노이즈를 줄입니다. 매개변수를 통해 블러의 강도와 확산을 제어할 수 있습니다.

## 입력

| 필드          | 데이터 유형 | 설명                                                                                                |
| ------------- | ----------- | --------------------------------------------------------------------------------------------------- |
| `image`       | `IMAGE`     | 블러를 적용할 입력 이미지입니다. 이는 블러 효과의 주요 대상입니다.                                  |
| `blur_radius` | `INT`       | 블러 효과의 반경을 결정합니다. 더 큰 반경은 더 뚜렷한 블러를 생성합니다.                            |
| `sigma`       | `FLOAT`     | 블러의 확산을 제어합니다. 더 높은 시그마 값은 각 픽셀 주변의 더 넓은 영역에 블러가 영향을 미칩니다. |

## 출력

| 필드    | 데이터 유형 | 설명                                                                                      |
| ------- | ----------- | ----------------------------------------------------------------------------------------- |
| `image` | `IMAGE`     | 출력은 입력 이미지의 블러 처리된 버전이며, 블러의 정도는 입력 매개변수에 의해 결정됩니다. |
