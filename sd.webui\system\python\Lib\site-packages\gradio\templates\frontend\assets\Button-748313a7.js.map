{"version": 3, "file": "Button-748313a7.js", "sources": ["../../../../node_modules/.pnpm/svelte@4.0.0/node_modules/svelte/src/runtime/easing/index.js", "../../../../node_modules/.pnpm/svelte@4.0.0/node_modules/svelte/src/runtime/transition/index.js", "../../../../js/atoms/src/Block.svelte", "../../../../js/upload/src/utils.ts", "../../../../js/button/static/Button.svelte"], "sourcesContent": ["/*\nAdapted from https://github.com/mattdesl\nDistributed under MIT License https://github.com/mattdesl/eases/blob/master/LICENSE.md\n*/\nexport { identity as linear } from '../internal/index.js';\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function backInOut(t) {\n\tconst s = 1.70158 * 1.525;\n\tif ((t *= 2) < 1) return 0.5 * (t * t * ((s + 1) * t - s));\n\treturn 0.5 * ((t -= 2) * t * ((s + 1) * t + s) + 2);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function backIn(t) {\n\tconst s = 1.70158;\n\treturn t * t * ((s + 1) * t - s);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function backOut(t) {\n\tconst s = 1.70158;\n\treturn --t * t * ((s + 1) * t + s) + 1;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function bounceOut(t) {\n\tconst a = 4.0 / 11.0;\n\tconst b = 8.0 / 11.0;\n\tconst c = 9.0 / 10.0;\n\tconst ca = 4356.0 / 361.0;\n\tconst cb = 35442.0 / 1805.0;\n\tconst cc = 16061.0 / 1805.0;\n\tconst t2 = t * t;\n\treturn t < a\n\t\t? 7.5625 * t2\n\t\t: t < b\n\t\t? 9.075 * t2 - 9.9 * t + 3.4\n\t\t: t < c\n\t\t? ca * t2 - cb * t + cc\n\t\t: 10.8 * t * t - 20.52 * t + 10.72;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function bounceInOut(t) {\n\treturn t < 0.5 ? 0.5 * (1.0 - bounceOut(1.0 - t * 2.0)) : 0.5 * bounceOut(t * 2.0 - 1.0) + 0.5;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function bounceIn(t) {\n\treturn 1.0 - bounceOut(1.0 - t);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function circInOut(t) {\n\tif ((t *= 2) < 1) return -0.5 * (Math.sqrt(1 - t * t) - 1);\n\treturn 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function circIn(t) {\n\treturn 1.0 - Math.sqrt(1.0 - t * t);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function circOut(t) {\n\treturn Math.sqrt(1 - --t * t);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function cubicInOut(t) {\n\treturn t < 0.5 ? 4.0 * t * t * t : 0.5 * Math.pow(2.0 * t - 2.0, 3.0) + 1.0;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function cubicIn(t) {\n\treturn t * t * t;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function cubicOut(t) {\n\tconst f = t - 1.0;\n\treturn f * f * f + 1.0;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function elasticInOut(t) {\n\treturn t < 0.5\n\t\t? 0.5 * Math.sin(((+13.0 * Math.PI) / 2) * 2.0 * t) * Math.pow(2.0, 10.0 * (2.0 * t - 1.0))\n\t\t: 0.5 *\n\t\t\t\tMath.sin(((-13.0 * Math.PI) / 2) * (2.0 * t - 1.0 + 1.0)) *\n\t\t\t\tMath.pow(2.0, -10.0 * (2.0 * t - 1.0)) +\n\t\t\t\t1.0;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function elasticIn(t) {\n\treturn Math.sin((13.0 * t * Math.PI) / 2) * Math.pow(2.0, 10.0 * (t - 1.0));\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function elasticOut(t) {\n\treturn Math.sin((-13.0 * (t + 1.0) * Math.PI) / 2) * Math.pow(2.0, -10.0 * t) + 1.0;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function expoInOut(t) {\n\treturn t === 0.0 || t === 1.0\n\t\t? t\n\t\t: t < 0.5\n\t\t? +0.5 * Math.pow(2.0, 20.0 * t - 10.0)\n\t\t: -0.5 * Math.pow(2.0, 10.0 - t * 20.0) + 1.0;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function expoIn(t) {\n\treturn t === 0.0 ? t : Math.pow(2.0, 10.0 * (t - 1.0));\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function expoOut(t) {\n\treturn t === 1.0 ? t : 1.0 - Math.pow(2.0, -10.0 * t);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function quadInOut(t) {\n\tt /= 0.5;\n\tif (t < 1) return 0.5 * t * t;\n\tt--;\n\treturn -0.5 * (t * (t - 2) - 1);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function quadIn(t) {\n\treturn t * t;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function quadOut(t) {\n\treturn -t * (t - 2.0);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function quartInOut(t) {\n\treturn t < 0.5 ? +8.0 * Math.pow(t, 4.0) : -8.0 * Math.pow(t - 1.0, 4.0) + 1.0;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function quartIn(t) {\n\treturn Math.pow(t, 4.0);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function quartOut(t) {\n\treturn Math.pow(t - 1.0, 3.0) * (1.0 - t) + 1.0;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function quintInOut(t) {\n\tif ((t *= 2) < 1) return 0.5 * t * t * t * t * t;\n\treturn 0.5 * ((t -= 2) * t * t * t * t + 2);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function quintIn(t) {\n\treturn t * t * t * t * t;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function quintOut(t) {\n\treturn --t * t * t * t * t + 1;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function sineInOut(t) {\n\treturn -0.5 * (Math.cos(Math.PI * t) - 1);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function sineIn(t) {\n\tconst v = Math.cos(t * Math.PI * 0.5);\n\tif (Math.abs(v) < 1e-14) return 1;\n\telse return 1 - v;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function sineOut(t) {\n\treturn Math.sin((t * Math.PI) / 2);\n}\n", "import { cubicOut, cubicInOut, linear } from '../easing/index.js';\nimport { assign, split_css_unit, is_function } from '../internal/index.js';\n\n/**\n * Animates a `blur` filter alongside an element's opacity.\n *\n * https://svelte.dev/docs/svelte-transition#blur\n * @param {Element} node\n * @param {import('./public').BlurParams} [params]\n * @returns {import('./public').TransitionConfig}\n */\nexport function blur(\n\tnode,\n\t{ delay = 0, duration = 400, easing = cubicInOut, amount = 5, opacity = 0 } = {}\n) {\n\tconst style = getComputedStyle(node);\n\tconst target_opacity = +style.opacity;\n\tconst f = style.filter === 'none' ? '' : style.filter;\n\tconst od = target_opacity * (1 - opacity);\n\tconst [value, unit] = split_css_unit(amount);\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (_t, u) => `opacity: ${target_opacity - od * u}; filter: ${f} blur(${u * value}${unit});`\n\t};\n}\n\n/**\n * Animates the opacity of an element from 0 to the current opacity for `in` transitions and from the current opacity to 0 for `out` transitions.\n *\n * https://svelte.dev/docs/svelte-transition#fade\n * @param {Element} node\n * @param {import('./public').FadeParams} [params]\n * @returns {import('./public').TransitionConfig}\n */\nexport function fade(node, { delay = 0, duration = 400, easing = linear } = {}) {\n\tconst o = +getComputedStyle(node).opacity;\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (t) => `opacity: ${t * o}`\n\t};\n}\n\n/**\n * Animates the x and y positions and the opacity of an element. `in` transitions animate from the provided values, passed as parameters to the element's default values. `out` transitions animate from the element's default values to the provided values.\n *\n * https://svelte.dev/docs/svelte-transition#fly\n * @param {Element} node\n * @param {import('./public').FlyParams} [params]\n * @returns {import('./public').TransitionConfig}\n */\nexport function fly(\n\tnode,\n\t{ delay = 0, duration = 400, easing = cubicOut, x = 0, y = 0, opacity = 0 } = {}\n) {\n\tconst style = getComputedStyle(node);\n\tconst target_opacity = +style.opacity;\n\tconst transform = style.transform === 'none' ? '' : style.transform;\n\tconst od = target_opacity * (1 - opacity);\n\tconst [xValue, xUnit] = split_css_unit(x);\n\tconst [yValue, yUnit] = split_css_unit(y);\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (t, u) => `\n\t\t\ttransform: ${transform} translate(${(1 - t) * xValue}${xUnit}, ${(1 - t) * yValue}${yUnit});\n\t\t\topacity: ${target_opacity - od * u}`\n\t};\n}\n\n/**\n * Slides an element in and out.\n *\n * https://svelte.dev/docs/svelte-transition#slide\n * @param {Element} node\n * @param {import('./public').SlideParams} [params]\n * @returns {import('./public').TransitionConfig}\n */\nexport function slide(node, { delay = 0, duration = 400, easing = cubicOut, axis = 'y' } = {}) {\n\tconst style = getComputedStyle(node);\n\tconst opacity = +style.opacity;\n\tconst primary_property = axis === 'y' ? 'height' : 'width';\n\tconst primary_property_value = parseFloat(style[primary_property]);\n\tconst secondary_properties = axis === 'y' ? ['top', 'bottom'] : ['left', 'right'];\n\tconst capitalized_secondary_properties = secondary_properties.map(\n\t\t(e) => `${e[0].toUpperCase()}${e.slice(1)}`\n\t);\n\tconst padding_start_value = parseFloat(style[`padding${capitalized_secondary_properties[0]}`]);\n\tconst padding_end_value = parseFloat(style[`padding${capitalized_secondary_properties[1]}`]);\n\tconst margin_start_value = parseFloat(style[`margin${capitalized_secondary_properties[0]}`]);\n\tconst margin_end_value = parseFloat(style[`margin${capitalized_secondary_properties[1]}`]);\n\tconst border_width_start_value = parseFloat(\n\t\tstyle[`border${capitalized_secondary_properties[0]}Width`]\n\t);\n\tconst border_width_end_value = parseFloat(\n\t\tstyle[`border${capitalized_secondary_properties[1]}Width`]\n\t);\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (t) =>\n\t\t\t'overflow: hidden;' +\n\t\t\t`opacity: ${Math.min(t * 20, 1) * opacity};` +\n\t\t\t`${primary_property}: ${t * primary_property_value}px;` +\n\t\t\t`padding-${secondary_properties[0]}: ${t * padding_start_value}px;` +\n\t\t\t`padding-${secondary_properties[1]}: ${t * padding_end_value}px;` +\n\t\t\t`margin-${secondary_properties[0]}: ${t * margin_start_value}px;` +\n\t\t\t`margin-${secondary_properties[1]}: ${t * margin_end_value}px;` +\n\t\t\t`border-${secondary_properties[0]}-width: ${t * border_width_start_value}px;` +\n\t\t\t`border-${secondary_properties[1]}-width: ${t * border_width_end_value}px;`\n\t};\n}\n\n/**\n * Animates the opacity and scale of an element. `in` transitions animate from an element's current (default) values to the provided values, passed as parameters. `out` transitions animate from the provided values to an element's default values.\n *\n * https://svelte.dev/docs/svelte-transition#scale\n * @param {Element} node\n * @param {import('./public').ScaleParams} [params]\n * @returns {import('./public').TransitionConfig}\n */\nexport function scale(\n\tnode,\n\t{ delay = 0, duration = 400, easing = cubicOut, start = 0, opacity = 0 } = {}\n) {\n\tconst style = getComputedStyle(node);\n\tconst target_opacity = +style.opacity;\n\tconst transform = style.transform === 'none' ? '' : style.transform;\n\tconst sd = 1 - start;\n\tconst od = target_opacity * (1 - opacity);\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (_t, u) => `\n\t\t\ttransform: ${transform} scale(${1 - sd * u});\n\t\t\topacity: ${target_opacity - od * u}\n\t\t`\n\t};\n}\n\n/**\n * Animates the stroke of an SVG element, like a snake in a tube. `in` transitions begin with the path invisible and draw the path to the screen over time. `out` transitions start in a visible state and gradually erase the path. `draw` only works with elements that have a `getTotalLength` method, like `<path>` and `<polyline>`.\n *\n * https://svelte.dev/docs/svelte-transition#draw\n * @param {SVGElement & { getTotalLength(): number }} node\n * @param {import('./public').DrawParams} [params]\n * @returns {import('./public').TransitionConfig}\n */\nexport function draw(node, { delay = 0, speed, duration, easing = cubicInOut } = {}) {\n\tlet len = node.getTotalLength();\n\tconst style = getComputedStyle(node);\n\tif (style.strokeLinecap !== 'butt') {\n\t\tlen += parseInt(style.strokeWidth);\n\t}\n\tif (duration === undefined) {\n\t\tif (speed === undefined) {\n\t\t\tduration = 800;\n\t\t} else {\n\t\t\tduration = len / speed;\n\t\t}\n\t} else if (typeof duration === 'function') {\n\t\tduration = duration(len);\n\t}\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (_, u) => `\n\t\t\tstroke-dasharray: ${len};\n\t\t\tstroke-dashoffset: ${u * len};\n\t\t`\n\t};\n}\n\n/**\n * The `crossfade` function creates a pair of [transitions](/docs#template-syntax-element-directives-transition-fn) called `send` and `receive`. When an element is 'sent', it looks for a corresponding element being 'received', and generates a transition that transforms the element to its counterpart's position and fades it out. When an element is 'received', the reverse happens. If there is no counterpart, the `fallback` transition is used.\n *\n * https://svelte.dev/docs/svelte-transition#crossfade\n * @param {import('./public').CrossfadeParams & {\n * \tfallback?: (node: Element, params: import('./public').CrossfadeParams, intro: boolean) => import('./public').TransitionConfig;\n * }} params\n * @returns {[(node: any, params: import('./public').CrossfadeParams & { key: any; }) => () => import('./public').TransitionConfig, (node: any, params: import('./public').CrossfadeParams & { key: any; }) => () => import('./public').TransitionConfig]}\n */\nexport function crossfade({ fallback, ...defaults }) {\n\t/** @type {Map<any, Element>} */\n\tconst to_receive = new Map();\n\t/** @type {Map<any, Element>} */\n\tconst to_send = new Map();\n\t/**\n\t * @param {Element} from_node\n\t * @param {Element} node\n\t * @param {import('./public').CrossfadeParams} params\n\t * @returns {import('./public').TransitionConfig}\n\t */\n\tfunction crossfade(from_node, node, params) {\n\t\tconst {\n\t\t\tdelay = 0,\n\t\t\tduration = (d) => Math.sqrt(d) * 30,\n\t\t\teasing = cubicOut\n\t\t} = assign(assign({}, defaults), params);\n\t\tconst from = from_node.getBoundingClientRect();\n\t\tconst to = node.getBoundingClientRect();\n\t\tconst dx = from.left - to.left;\n\t\tconst dy = from.top - to.top;\n\t\tconst dw = from.width / to.width;\n\t\tconst dh = from.height / to.height;\n\t\tconst d = Math.sqrt(dx * dx + dy * dy);\n\t\tconst style = getComputedStyle(node);\n\t\tconst transform = style.transform === 'none' ? '' : style.transform;\n\t\tconst opacity = +style.opacity;\n\t\treturn {\n\t\t\tdelay,\n\t\t\tduration: is_function(duration) ? duration(d) : duration,\n\t\t\teasing,\n\t\t\tcss: (t, u) => `\n\t\t\t\topacity: ${t * opacity};\n\t\t\t\ttransform-origin: top left;\n\t\t\t\ttransform: ${transform} translate(${u * dx}px,${u * dy}px) scale(${t + (1 - t) * dw}, ${\n\t\t\t\tt + (1 - t) * dh\n\t\t\t});\n\t\t\t`\n\t\t};\n\t}\n\n\t/**\n\t * @param {Map<any, Element>} items\n\t * @param {Map<any, Element>} counterparts\n\t * @param {boolean} intro\n\t * @returns {(node: any, params: import('./public').CrossfadeParams & { key: any; }) => () => import('./public').TransitionConfig}\n\t */\n\tfunction transition(items, counterparts, intro) {\n\t\treturn (node, params) => {\n\t\t\titems.set(params.key, node);\n\t\t\treturn () => {\n\t\t\t\tif (counterparts.has(params.key)) {\n\t\t\t\t\tconst other_node = counterparts.get(params.key);\n\t\t\t\t\tcounterparts.delete(params.key);\n\t\t\t\t\treturn crossfade(other_node, node, params);\n\t\t\t\t}\n\t\t\t\t// if the node is disappearing altogether\n\t\t\t\t// (i.e. wasn't claimed by the other list)\n\t\t\t\t// then we need to supply an outro\n\t\t\t\titems.delete(params.key);\n\t\t\t\treturn fallback && fallback(node, params, intro);\n\t\t\t};\n\t\t};\n\t}\n\treturn [transition(to_send, to_receive, false), transition(to_receive, to_send, true)];\n}\n", "<script lang=\"ts\">\n\texport let height: number | undefined = undefined;\n\texport let width: number | undefined = undefined;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let variant: \"solid\" | \"dashed\" | \"none\" = \"solid\";\n\texport let border_mode: \"base\" | \"focus\" = \"base\";\n\texport let padding = true;\n\texport let type: \"normal\" | \"fieldset\" = \"normal\";\n\texport let test_id: string | undefined = undefined;\n\texport let explicit_call = false;\n\texport let container = true;\n\texport let visible = true;\n\texport let allow_overflow = true;\n\texport let scale: number | null = null;\n\texport let min_width = 0;\n\n\tlet tag = type === \"fieldset\" ? \"fieldset\" : \"div\";\n</script>\n\n<svelte:element\n\tthis={tag}\n\tdata-testid={test_id}\n\tid={elem_id}\n\tclass:hidden={visible === false}\n\tclass=\"block {elem_classes.join(' ')}\"\n\tclass:padded={padding}\n\tclass:border_focus={border_mode === \"focus\"}\n\tclass:hide-container={!explicit_call && !container}\n\tstyle:height={typeof height === \"number\" ? height + \"px\" : undefined}\n\tstyle:width={typeof width === \"number\"\n\t\t? `calc(min(${width}px, 100%))`\n\t\t: undefined}\n\tstyle:border-style={variant}\n\tstyle:overflow={allow_overflow ? \"visible\" : \"hidden\"}\n\tstyle:flex-grow={scale}\n\tstyle:min-width={`calc(min(${min_width}px, 100%))`}\n\tstyle:border-width=\"var(--block-border-width)\"\n>\n\t<slot />\n</svelte:element>\n\n<style>\n\t.block {\n\t\tposition: relative;\n\t\tmargin: 0;\n\t\tbox-shadow: var(--block-shadow);\n\t\tborder-width: var(--block-border-width);\n\t\tborder-color: var(--block-border-color);\n\t\tborder-radius: var(--block-radius);\n\t\tbackground: var(--block-background-fill);\n\t\twidth: 100%;\n\t\tline-height: var(--line-sm);\n\t}\n\n\t.block.border_focus {\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.padded {\n\t\tpadding: var(--block-padding);\n\t}\n\n\t.hidden {\n\t\tdisplay: none;\n\t}\n\t.hide-container {\n\t\tmargin: 0;\n\t\tbox-shadow: none;\n\t\t--block-border-width: 0;\n\t\tbackground: transparent;\n\t\tpadding: 0;\n\t\toverflow: visible;\n\t}\n</style>\n", "import type { FileData } from \"./types\";\n\nexport function normalise_file(\n\tfile: string | FileData | null,\n\troot: string,\n\troot_url: string | null\n): FileData | null;\n\nexport function normalise_file(\n\tfile: FileData[] | null,\n\troot: string,\n\troot_url: string | null\n): FileData[] | null;\n\nexport function normalise_file(\n\tfile: FileData[] | FileData | null,\n\troot: string,\n\troot_url: string | null\n): FileData[] | FileData | null;\n\nexport function normalise_file(\n\tfile: FileData[] | FileData | string | null,\n\troot: string,\n\troot_url: string | null\n): FileData[] | FileData | null {\n\tif (file == null) return null;\n\tif (typeof file === \"string\") {\n\t\treturn {\n\t\t\tname: \"file_data\",\n\t\t\tdata: file\n\t\t};\n\t} else if (Array.isArray(file)) {\n\t\tconst normalized_file: (FileData | null)[] = [];\n\n\t\tfor (const x of file) {\n\t\t\tif (x === null) {\n\t\t\t\tnormalized_file.push(null);\n\t\t\t} else {\n\t\t\t\tnormalized_file.push(normalise_file(x, root, root_url));\n\t\t\t}\n\t\t}\n\n\t\treturn normalized_file as FileData[];\n\t} else if (file.is_file) {\n\t\tfile.data = get_fetchable_url_or_file(file.name, root, root_url);\n\t} else if (file.is_stream) {\n\t\tif (root_url == null) {\n\t\t\tfile.data = root + \"/stream/\" + file.name;\n\t\t} else {\n\t\t\tfile.data = \"/proxy=\" + root_url + \"stream/\" + file.name;\n\t\t}\n\t}\n\treturn file;\n}\n\nfunction is_url(str: string): boolean {\n\ttry {\n\t\tconst url = new URL(str);\n\t\treturn url.protocol === \"http:\" || url.protocol === \"https:\";\n\t} catch {\n\t\treturn false;\n\t}\n}\n\nexport function get_fetchable_url_or_file(\n\tpath: string | null,\n\troot: string,\n\troot_url: string | null\n): string {\n\tif (path == null) {\n\t\treturn root_url ? `/proxy=${root_url}file=` : `${root}/file=`;\n\t}\n\tif (is_url(path)) {\n\t\treturn path;\n\t}\n\treturn root_url ? `/proxy=${root_url}file=${path}` : `${root}/file=${path}`;\n}\n\nexport const blobToBase64 = (blob: File): Promise<string> => {\n\tconst reader = new FileReader();\n\treader.readAsDataURL(blob);\n\treturn new Promise((resolve) => {\n\t\treader.onloadend = (): void => {\n\t\t\tresolve(reader.result as string);\n\t\t};\n\t});\n};\n", "<script lang=\"ts\">\n\timport { get_fetchable_url_or_file } from \"@gradio/upload\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let variant: \"primary\" | \"secondary\" | \"stop\" = \"secondary\";\n\texport let size: \"sm\" | \"lg\" = \"lg\";\n\texport let value: string | null = null;\n\texport let link: string | null = null;\n\texport let icon: string | null = null;\n\texport let disabled = false;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let root = \"\";\n\texport let root_url: string | null = null;\n\t$: icon_path = get_fetchable_url_or_file(icon, root, root_url);\n</script>\n\n{#if link && link.length > 0}\n\t<a\n\t\thref={link}\n\t\trel=\"noopener noreferrer\"\n\t\tclass:hidden={!visible}\n\t\tclass:disabled\n\t\taria-disabled={disabled}\n\t\tclass=\"{size} {variant} {elem_classes.join(' ')}\"\n\t\tstyle:flex-grow={scale}\n\t\tstyle:pointer-events={disabled ? \"none\" : null}\n\t\tstyle:width={scale === 0 ? \"fit-content\" : null}\n\t\tstyle:min-width={typeof min_width === \"number\"\n\t\t\t? `calc(min(${min_width}px, 100%))`\n\t\t\t: null}\n\t\tid={elem_id}\n\t>\n\t\t{#if icon}\n\t\t\t<img class=\"button-icon\" src={icon_path} alt={`${value}-icon`} />\n\t\t{/if}\n\t\t<slot />\n\t</a>\n{:else}\n\t<button\n\t\ton:click\n\t\tclass:hidden={!visible}\n\t\tclass=\"{size} {variant} {elem_classes.join(' ')}\"\n\t\tstyle:flex-grow={scale}\n\t\tstyle:width={scale === 0 ? \"fit-content\" : null}\n\t\tstyle:min-width={typeof min_width === \"number\"\n\t\t\t? `calc(min(${min_width}px, 100%))`\n\t\t\t: null}\n\t\tid={elem_id}\n\t\t{disabled}\n\t>\n\t\t{#if icon}\n\t\t\t<img class=\"button-icon\" src={icon_path} alt={`${value}-icon`} />\n\t\t{/if}\n\t\t<slot />\n\t</button>\n{/if}\n\n<style>\n\tbutton,\n\ta {\n\t\tdisplay: inline-flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\ttransition: var(--button-transition);\n\t\tbox-shadow: var(--button-shadow);\n\t\tpadding: var(--size-0-5) var(--size-2);\n\t\ttext-align: center;\n\t}\n\n\tbutton:hover,\n\tbutton[disabled],\n\ta:hover,\n\ta.disabled {\n\t\tbox-shadow: var(--button-shadow-hover);\n\t}\n\n\tbutton:active,\n\ta:active {\n\t\tbox-shadow: var(--button-shadow-active);\n\t}\n\n\tbutton[disabled],\n\ta.disabled {\n\t\topacity: 0.5;\n\t\tfilter: grayscale(30%);\n\t\tcursor: not-allowed;\n\t}\n\n\t.hidden {\n\t\tdisplay: none;\n\t}\n\n\t.primary {\n\t\tborder: var(--button-border-width) solid var(--button-primary-border-color);\n\t\tbackground: var(--button-primary-background-fill);\n\t\tcolor: var(--button-primary-text-color);\n\t}\n\t.primary:hover,\n\t.primary[disabled] {\n\t\tborder-color: var(--button-primary-border-color-hover);\n\t\tbackground: var(--button-primary-background-fill-hover);\n\t\tcolor: var(--button-primary-text-color-hover);\n\t}\n\n\t.secondary {\n\t\tborder: var(--button-border-width) solid\n\t\t\tvar(--button-secondary-border-color);\n\t\tbackground: var(--button-secondary-background-fill);\n\t\tcolor: var(--button-secondary-text-color);\n\t}\n\n\t.secondary:hover,\n\t.secondary[disabled] {\n\t\tborder-color: var(--button-secondary-border-color-hover);\n\t\tbackground: var(--button-secondary-background-fill-hover);\n\t\tcolor: var(--button-secondary-text-color-hover);\n\t}\n\n\t.stop {\n\t\tborder: var(--button-border-width) solid var(--button-cancel-border-color);\n\t\tbackground: var(--button-cancel-background-fill);\n\t\tcolor: var(--button-cancel-text-color);\n\t}\n\n\t.stop:hover,\n\t.stop[disabled] {\n\t\tborder-color: var(--button-cancel-border-color-hover);\n\t\tbackground: var(--button-cancel-background-fill-hover);\n\t\tcolor: var(--button-cancel-text-color-hover);\n\t}\n\n\t.sm {\n\t\tborder-radius: var(--button-small-radius);\n\t\tpadding: var(--button-small-padding);\n\t\tfont-weight: var(--button-small-text-weight);\n\t\tfont-size: var(--button-small-text-size);\n\t}\n\n\t.lg {\n\t\tborder-radius: var(--button-large-radius);\n\t\tpadding: var(--button-large-padding);\n\t\tfont-weight: var(--button-large-text-weight);\n\t\tfont-size: var(--button-large-text-size);\n\t}\n\n\t.button-icon {\n\t\twidth: var(--text-xl);\n\t\theight: var(--text-xl);\n\t\tmargin-right: var(--spacing-xl);\n\t}\n</style>\n"], "names": ["cubicOut", "t", "f", "fade", "node", "delay", "duration", "easing", "linear", "o", "fly", "x", "y", "opacity", "style", "target_opacity", "transform", "od", "xValue", "xUnit", "split_css_unit", "yValue", "yUnit", "u", "ctx", "svelte_element", "svelte_element_data", "toggle_class", "set_style", "insert", "target", "anchor", "get_spread_update", "svelte_element_levels", "svelte_element_class_value", "create_dynamic_element", "height", "$$props", "width", "elem_id", "elem_classes", "variant", "border_mode", "padding", "type", "test_id", "explicit_call", "container", "visible", "allow_overflow", "scale", "min_width", "tag", "normalise_file", "file", "root", "root_url", "normalized_file", "get_fetchable_url_or_file", "is_url", "str", "url", "path", "blobToBase64", "blob", "reader", "resolve", "create_if_block_2", "attr", "button", "button_class_value", "current", "dirty", "create_if_block_1", "a", "a_class_value", "img", "img_src_value", "size", "value", "link", "icon", "disabled", "$$invalidate", "icon_path"], "mappings": "2OAgIO,SAASA,EAASC,EAAG,CAC3B,MAAMC,EAAID,EAAI,EACd,OAAOC,EAAIA,EAAIA,EAAI,CACpB,CC/FO,SAASC,GAAKC,EAAM,CAAE,MAAAC,EAAQ,EAAG,SAAAC,EAAW,IAAK,OAAAC,EAASC,CAAQ,EAAG,GAAI,CAC/E,MAAMC,EAAI,CAAC,iBAAiBL,CAAI,EAAE,QAClC,MAAO,CACN,MAAAC,EACA,SAAAC,EACA,OAAAC,EACA,IAAMN,GAAM,YAAYA,EAAIQ,GAC9B,CACA,CAUO,SAASC,GACfN,EACA,CAAE,MAAAC,EAAQ,EAAG,SAAAC,EAAW,IAAK,OAAAC,EAASP,EAAU,EAAAW,EAAI,EAAG,EAAAC,EAAI,EAAG,QAAAC,EAAU,CAAG,EAAG,CAAE,EAC/E,CACD,MAAMC,EAAQ,iBAAiBV,CAAI,EAC7BW,EAAiB,CAACD,EAAM,QACxBE,EAAYF,EAAM,YAAc,OAAS,GAAKA,EAAM,UACpDG,EAAKF,GAAkB,EAAIF,GAC3B,CAACK,EAAQC,CAAK,EAAIC,EAAeT,CAAC,EAClC,CAACU,EAAQC,CAAK,EAAIF,EAAeR,CAAC,EACxC,MAAO,CACN,MAAAP,EACA,SAAAC,EACA,OAAAC,EACA,IAAK,CAACN,EAAGsB,IAAM;AAAA,gBACDP,gBAAwB,EAAIf,GAAKiB,IAASC,OAAW,EAAIlB,GAAKoB,IAASC;AAAA,cACzEP,EAAiBE,EAAKM,GACpC,CACA,0FClDcC,EAAO,CAAA,CAAA,MAChBA,EAAO,CAAA,CAAA,oBAEGA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,+EAJ7BA,EAAG,EAAA,CAAA,aAAHA,EAAG,EAAA,CAAA,EAAAC,EAAAC,CAAA,EAGKC,EAAAF,EAAA,SAAAD,QAAY,EAAK,eAEjBA,EAAO,CAAA,CAAA,EACDG,EAAAF,EAAA,eAAAD,OAAgB,OAAO,EACpBG,EAAAF,EAAA,iBAAA,CAAAD,OAAkBA,EAAS,CAAA,CAAA,EAC7BI,EAAAH,EAAA,SAAA,OAAAD,MAAW,SAAWA,KAAS,KAAO,MAAS,EAChDI,EAAAH,EAAA,QAAA,OAAAD,MAAU,qBACfA,EAAK,CAAA,cACjB,MAAS,qBACQA,EAAO,CAAA,CAAA,iBACXA,EAAc,EAAA,EAAG,UAAY,QAAQ,kBACpCA,EAAK,EAAA,CAAA,8BACOA,EAAS,EAAA,aAAA,0DAhBvCK,EAoBgBC,EAAAL,EAAAM,CAAA,wGAnBTP,EAAG,EAAA,CAAA,EAAAC,EAAAC,EAAAM,EAAAC,EAAA,6BACIT,EAAO,CAAA,CAAA,iBAChBA,EAAO,CAAA,CAAA,4BAEGA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,oBAAA,CAAA,MAAAU,CAAA,KADrBP,EAAAF,EAAA,SAAAD,QAAY,EAAK,eAEjBA,EAAO,CAAA,CAAA,EACDG,EAAAF,EAAA,eAAAD,OAAgB,OAAO,EACpBG,EAAAF,EAAA,iBAAA,CAAAD,OAAkBA,EAAS,CAAA,CAAA,OAC7BI,EAAAH,EAAA,SAAA,OAAAD,MAAW,SAAWA,KAAS,KAAO,MAAS,OAChDI,EAAAH,EAAA,QAAA,OAAAD,MAAU,qBACfA,EAAK,CAAA,cACjB,MAAS,2BACQA,EAAO,CAAA,CAAA,yBACXA,EAAc,EAAA,EAAG,UAAY,QAAQ,0BACpCA,EAAK,EAAA,CAAA,sCACOA,EAAS,EAAA,aAAA,4FAfhCA,EAAG,EAAA,GAAAW,GAAAX,CAAA,yDAAHA,EAAG,EAAA,wHApBE,CAAA,OAAAY,EAA6B,MAAS,EAAAC,EACtC,CAAA,MAAAC,EAA4B,MAAS,EAAAD,EACrC,CAAA,QAAAE,EAAU,EAAE,EAAAF,GACZ,aAAAG,EAAY,EAAA,EAAAH,EACZ,CAAA,QAAAI,EAAuC,OAAO,EAAAJ,EAC9C,CAAA,YAAAK,EAAgC,MAAM,EAAAL,EACtC,CAAA,QAAAM,EAAU,EAAI,EAAAN,EACd,CAAA,KAAAO,EAA8B,QAAQ,EAAAP,EACtC,CAAA,QAAAQ,EAA8B,MAAS,EAAAR,EACvC,CAAA,cAAAS,EAAgB,EAAK,EAAAT,EACrB,CAAA,UAAAU,EAAY,EAAI,EAAAV,EAChB,CAAA,QAAAW,EAAU,EAAI,EAAAX,EACd,CAAA,eAAAY,EAAiB,EAAI,EAAAZ,EACrB,CAAA,MAAAa,EAAuB,IAAI,EAAAb,EAC3B,CAAA,UAAAc,EAAY,CAAC,EAAAd,EAEpBe,EAAMR,IAAS,WAAa,WAAa,s1BCG9B,SAAAS,GACfC,EACAC,EACAC,EAC+B,CAC/B,GAAIF,GAAQ,KAAa,OAAA,KACrB,GAAA,OAAOA,GAAS,SACZ,MAAA,CACN,KAAM,YACN,KAAMA,CAAA,EAEG,GAAA,MAAM,QAAQA,CAAI,EAAG,CAC/B,MAAMG,EAAuC,CAAA,EAE7C,UAAW9C,KAAK2C,EACX3C,IAAM,KACT8C,EAAgB,KAAK,IAAI,EAEzBA,EAAgB,KAAKJ,GAAe1C,EAAG4C,EAAMC,CAAQ,CAAC,EAIjD,OAAAC,OACGH,EAAK,QACfA,EAAK,KAAOI,EAA0BJ,EAAK,KAAMC,EAAMC,CAAQ,EACrDF,EAAK,YACXE,GAAY,KACVF,EAAA,KAAOC,EAAO,WAAaD,EAAK,KAErCA,EAAK,KAAO,UAAYE,EAAW,UAAYF,EAAK,MAG/C,OAAAA,CACR,CAEA,SAASK,GAAOC,EAAsB,CACjC,GAAA,CACG,MAAAC,EAAM,IAAI,IAAID,CAAG,EACvB,OAAOC,EAAI,WAAa,SAAWA,EAAI,WAAa,QAAA,MACnD,CACM,MAAA,EACR,CACD,CAEgB,SAAAH,EACfI,EACAP,EACAC,EACS,CACT,OAAIM,GAAQ,KACJN,EAAW,UAAUA,SAAkB,GAAGD,UAE9CI,GAAOG,CAAI,EACPA,EAEDN,EAAW,UAAUA,SAAgBM,IAAS,GAAGP,UAAaO,GACtE,CAEa,MAAAC,GAAgBC,GAAgC,CACtD,MAAAC,EAAS,IAAI,WACnB,OAAAA,EAAO,cAAcD,CAAI,EAClB,IAAI,QAASE,GAAY,CAC/BD,EAAO,UAAY,IAAY,CAC9BC,EAAQD,EAAO,MAAgB,CAAA,CAChC,CACA,CACF,mCCjCOzC,EAAI,CAAA,GAAA2C,EAAA3C,CAAA,6FATD4C,EAAAC,EAAA,QAAAC,EAAA9C,SAAOA,EAAO,CAAA,EAAA,IAAGA,EAAa,CAAA,EAAA,KAAK,GAAG,EAAA,gBAAA,WAM1CA,EAAO,CAAA,CAAA,gCAPIA,EAAO,CAAA,CAAA,kBAELA,EAAK,CAAA,CAAA,EACTI,EAAAyC,EAAA,QAAA7C,EAAU,CAAA,IAAA,EAAI,cAAgB,IAAI,EACvBI,EAAAyC,EAAA,YAAA,OAAA7C,OAAc,qBACvBA,EAAS,EAAA,cACrB,IAAI,UARRK,EAgBQC,EAAAuC,EAAAtC,CAAA,mFAJFP,EAAI,CAAA,iIATD,CAAA+C,GAAAC,EAAA,IAAAF,KAAAA,EAAA9C,SAAOA,EAAO,CAAA,EAAA,IAAGA,EAAa,CAAA,EAAA,KAAK,GAAG,EAAA,uDAM1CA,EAAO,CAAA,CAAA,2DAPIA,EAAO,CAAA,CAAA,yBAELA,EAAK,CAAA,CAAA,SACTI,EAAAyC,EAAA,QAAA7C,EAAU,CAAA,IAAA,EAAI,cAAgB,IAAI,UACvBI,EAAAyC,EAAA,YAAA,OAAA7C,OAAc,qBACvBA,EAAS,EAAA,cACrB,IAAI,oHAdFA,EAAI,CAAA,GAAAiD,EAAAjD,CAAA,mGAdHA,EAAI,CAAA,CAAA,uDAIKA,EAAQ,CAAA,CAAA,EACf4C,EAAAM,EAAA,QAAAC,EAAAnD,SAAOA,EAAO,CAAA,EAAA,IAAGA,EAAa,CAAA,EAAA,KAAK,GAAG,EAAA,gBAAA,WAO1CA,EAAO,CAAA,CAAA,gBAVIA,EAAO,CAAA,CAAA,uCAILA,EAAK,CAAA,CAAA,uBACAA,EAAQ,CAAA,EAAG,OAAS,IAAI,EACjCI,EAAA8C,EAAA,QAAAlD,EAAU,CAAA,IAAA,EAAI,cAAgB,IAAI,EACvBI,EAAA8C,EAAA,YAAA,OAAAlD,OAAc,qBACvBA,EAAS,EAAA,cACrB,IAAI,UAZRK,EAmBGC,EAAA4C,EAAA3C,CAAA,oDAJGP,EAAI,CAAA,uJAdHA,EAAI,CAAA,CAAA,mCAIKA,EAAQ,CAAA,CAAA,GACf,CAAA+C,GAAAC,EAAA,IAAAG,KAAAA,EAAAnD,SAAOA,EAAO,CAAA,EAAA,IAAGA,EAAa,CAAA,EAAA,KAAK,GAAG,EAAA,uDAO1CA,EAAO,CAAA,CAAA,4BAVIA,EAAO,CAAA,CAAA,2DAILA,EAAK,CAAA,CAAA,8BACAA,EAAQ,CAAA,EAAG,OAAS,IAAI,SACjCI,EAAA8C,EAAA,QAAAlD,EAAU,CAAA,IAAA,EAAI,cAAgB,IAAI,UACvBI,EAAA8C,EAAA,YAAA,OAAAlD,OAAc,qBACvBA,EAAS,EAAA,cACrB,IAAI,+KAsBwBA,EAAS,EAAA,CAAA,GAAA4C,EAAAQ,EAAA,MAAAC,CAAA,iBAAUrD,EAAK,CAAA,QAAA,UAAtDK,EAAgEC,EAAA8C,EAAA7C,CAAA,6BAAlCP,EAAS,EAAA,CAAA,gCAAUA,EAAK,CAAA,yIAlBxBA,EAAS,EAAA,CAAA,GAAA4C,EAAAQ,EAAA,MAAAC,CAAA,iBAAUrD,EAAK,CAAA,QAAA,UAAtDK,EAAgEC,EAAA8C,EAAA7C,CAAA,6BAAlCP,EAAS,EAAA,CAAA,gCAAUA,EAAK,CAAA,wGAjBpD,OAAAA,EAAQ,CAAA,GAAAA,EAAK,CAAA,EAAA,OAAS,EAAC,4VAhBhB,CAAA,QAAAe,EAAU,EAAE,EAAAF,GACZ,aAAAG,EAAY,EAAA,EAAAH,EACZ,CAAA,QAAAW,EAAU,EAAI,EAAAX,EACd,CAAA,QAAAI,EAA4C,WAAW,EAAAJ,EACvD,CAAA,KAAAyC,EAAoB,IAAI,EAAAzC,EACxB,CAAA,MAAA0C,EAAuB,IAAI,EAAA1C,EAC3B,CAAA,KAAA2C,EAAsB,IAAI,EAAA3C,EAC1B,CAAA,KAAA4C,EAAsB,IAAI,EAAA5C,EAC1B,CAAA,SAAA6C,EAAW,EAAK,EAAA7C,EAChB,CAAA,MAAAa,EAAuB,IAAI,EAAAb,EAC3B,CAAA,UAAAc,EAAgC,MAAS,EAAAd,EACzC,CAAA,KAAAkB,EAAO,EAAE,EAAAlB,EACT,CAAA,SAAAmB,EAA0B,IAAI,EAAAnB,8gBACxC8C,EAAA,GAAEC,EAAY1B,EAA0BuB,EAAM1B,EAAMC,CAAQ,CAAA", "x_google_ignoreList": [0, 1]}