{"version": 3, "file": "index-7c64fb49.js", "sources": ["../../../../node_modules/.pnpm/@lezer+json@1.0.0/node_modules/@lezer/json/dist/index.es.js", "../../../../node_modules/.pnpm/@codemirror+lang-json@6.0.1/node_modules/@codemirror/lang-json/dist/index.js"], "sourcesContent": ["import { LRParser } from '@lezer/lr';\nimport { styleTags, tags } from '@lezer/highlight';\n\nconst jsonHighlighting = styleTags({\n  String: tags.string,\n  Number: tags.number,\n  \"True False\": tags.bool,\n  PropertyName: tags.propertyName,\n  Null: tags.null,\n  \",\": tags.separator,\n  \"[ ]\": tags.squareBracket,\n  \"{ }\": tags.brace\n});\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst parser = LRParser.deserialize({\n  version: 14,\n  states: \"$bOVQPOOOOQO'#Cb'#CbOnQPO'#CeOvQPO'#CjOOQO'#Cp'#CpQOQPOOOOQO'#Cg'#CgO}QPO'#CfO!SQPO'#CrOOQO,59P,59PO![QPO,59PO!aQPO'#CuOOQO,59U,59UO!iQPO,59UOVQPO,59QOqQPO'#CkO!nQPO,59^OOQO1G.k1G.kOVQPO'#ClO!vQPO,59aOOQO1G.p1G.pOOQO1G.l1G.lOOQO,59V,59VOOQO-E6i-E6iOOQO,59W,59WOOQO-E6j-E6j\",\n  stateData: \"#O~OcOS~OQSORSOSSOTSOWQO]ROePO~OVXOeUO~O[[O~PVOg^O~Oh_OVfX~OVaO~OhbO[iX~O[dO~Oh_OVfa~OhbO[ia~O\",\n  goto: \"!kjPPPPPPkPPkqwPPk{!RPPP!XP!ePP!hXSOR^bQWQRf_TVQ_Q`WRg`QcZRicQTOQZRQe^RhbRYQR]R\",\n  nodeNames: \"⚠ JsonText True False Null Number String } { Object Property PropertyName ] [ Array\",\n  maxTerm: 25,\n  nodeProps: [\n    [\"openedBy\", 7,\"{\",12,\"[\"],\n    [\"closedBy\", 8,\"}\",13,\"]\"]\n  ],\n  propSources: [jsonHighlighting],\n  skippedNodes: [0],\n  repeatNodeCount: 2,\n  tokenData: \"(p~RaXY!WYZ!W]^!Wpq!Wrs!]|}$i}!O$n!Q!R$w!R![&V![!]&h!}#O&m#P#Q&r#Y#Z&w#b#c'f#h#i'}#o#p(f#q#r(k~!]Oc~~!`Upq!]qr!]rs!rs#O!]#O#P!w#P~!]~!wOe~~!zXrs!]!P!Q!]#O#P!]#U#V!]#Y#Z!]#b#c!]#f#g!]#h#i!]#i#j#g~#jR!Q![#s!c!i#s#T#Z#s~#vR!Q![$P!c!i$P#T#Z$P~$SR!Q![$]!c!i$]#T#Z$]~$`R!Q![!]!c!i!]#T#Z!]~$nOh~~$qQ!Q!R$w!R![&V~$|RT~!O!P%V!g!h%k#X#Y%k~%YP!Q![%]~%bRT~!Q![%]!g!h%k#X#Y%k~%nR{|%w}!O%w!Q![%}~%zP!Q![%}~&SPT~!Q![%}~&[ST~!O!P%V!Q![&V!g!h%k#X#Y%k~&mOg~~&rO]~~&wO[~~&zP#T#U&}~'QP#`#a'T~'WP#g#h'Z~'^P#X#Y'a~'fOR~~'iP#i#j'l~'oP#`#a'r~'uP#`#a'x~'}OS~~(QP#f#g(T~(WP#i#j(Z~(^P#X#Y(a~(fOQ~~(kOW~~(pOV~\",\n  tokenizers: [0],\n  topRules: {\"JsonText\":[0,1]},\n  tokenPrec: 0\n});\n\nexport { parser };\n", "import { parser } from '@lezer/json';\nimport { LRLanguage, indentNodeProp, continuedIndent, foldNodeProp, foldInside, LanguageSupport } from '@codemirror/language';\n\n/**\nCalls\n[`JSON.parse`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse)\non the document and, if that throws an error, reports it as a\nsingle diagnostic.\n*/\nconst jsonParseLinter = () => (view) => {\n    try {\n        JSON.parse(view.state.doc.toString());\n    }\n    catch (e) {\n        if (!(e instanceof SyntaxError))\n            throw e;\n        const pos = getErrorPosition(e, view.state.doc);\n        return [{\n                from: pos,\n                message: e.message,\n                severity: 'error',\n                to: pos\n            }];\n    }\n    return [];\n};\nfunction getErrorPosition(error, doc) {\n    let m;\n    if (m = error.message.match(/at position (\\d+)/))\n        return Math.min(+m[1], doc.length);\n    if (m = error.message.match(/at line (\\d+) column (\\d+)/))\n        return Math.min(doc.line(+m[1]).from + (+m[2]) - 1, doc.length);\n    return 0;\n}\n\n/**\nA language provider that provides JSON parsing.\n*/\nconst jsonLanguage = /*@__PURE__*/LRLanguage.define({\n    name: \"json\",\n    parser: /*@__PURE__*/parser.configure({\n        props: [\n            /*@__PURE__*/indentNodeProp.add({\n                Object: /*@__PURE__*/continuedIndent({ except: /^\\s*\\}/ }),\n                Array: /*@__PURE__*/continuedIndent({ except: /^\\s*\\]/ })\n            }),\n            /*@__PURE__*/foldNodeProp.add({\n                \"Object Array\": foldInside\n            })\n        ]\n    }),\n    languageData: {\n        closeBrackets: { brackets: [\"[\", \"{\", '\"'] },\n        indentOnInput: /^\\s*[\\}\\]]$/\n    }\n});\n/**\nJSON language support.\n*/\nfunction json() {\n    return new LanguageSupport(jsonLanguage);\n}\n\nexport { json, jsonLanguage, jsonParseLinter };\n"], "names": ["jsonHighlighting", "styleTags", "tags", "parser", "<PERSON><PERSON><PERSON><PERSON>", "jsonParseLinter", "view", "e", "pos", "getErrorPosition", "error", "doc", "m", "jsonLanguage", "LRLanguage", "indentNodeProp", "continuedIndent", "foldNodeProp", "foldInside", "json", "LanguageSupport"], "mappings": "6LAGA,MAAMA,EAAmBC,EAAU,CACjC,OAAQC,EAAK,OACb,OAAQA,EAAK,OACb,aAAcA,EAAK,KACnB,aAAcA,EAAK,aACnB,KAAMA,EAAK,KACX,IAAKA,EAAK,UACV,MAAOA,EAAK,cACZ,MAAOA,EAAK,KACd,CAAC,EAGKC,EAASC,EAAS,YAAY,CAClC,QAAS,GACT,OAAQ,mRACR,UAAW,iGACX,KAAM,kFACN,UAAW,sFACX,QAAS,GACT,UAAW,CACT,CAAC,WAAY,EAAE,IAAI,GAAG,GAAG,EACzB,CAAC,WAAY,EAAE,IAAI,GAAG,GAAG,CAC1B,EACD,YAAa,CAACJ,CAAgB,EAC9B,aAAc,CAAC,CAAC,EAChB,gBAAiB,EACjB,UAAW,wkBACX,WAAY,CAAC,CAAC,EACd,SAAU,CAAC,SAAW,CAAC,EAAE,CAAC,CAAC,EAC3B,UAAW,CACb,CAAC,ECxBKK,EAAkB,IAAOC,GAAS,CACpC,GAAI,CACA,KAAK,MAAMA,EAAK,MAAM,IAAI,SAAQ,CAAE,CACvC,OACMC,EAAP,CACI,GAAI,EAAEA,aAAa,aACf,MAAMA,EACV,MAAMC,EAAMC,EAAiBF,EAAGD,EAAK,MAAM,GAAG,EAC9C,MAAO,CAAC,CACA,KAAME,EACN,QAASD,EAAE,QACX,SAAU,QACV,GAAIC,CACpB,CAAa,CACR,CACD,MAAO,EACX,EACA,SAASC,EAAiBC,EAAOC,EAAK,CAClC,IAAIC,EACJ,OAAIA,EAAIF,EAAM,QAAQ,MAAM,mBAAmB,GACpC,KAAK,IAAI,CAACE,EAAE,CAAC,EAAGD,EAAI,MAAM,GACjCC,EAAIF,EAAM,QAAQ,MAAM,4BAA4B,GAC7C,KAAK,IAAIC,EAAI,KAAK,CAACC,EAAE,CAAC,CAAC,EAAE,MAAQ,CAACA,EAAE,CAAC,EAAK,EAAGD,EAAI,MAAM,EAC3D,CACX,CAKK,MAACE,EAA4BC,EAAW,OAAO,CAChD,KAAM,OACN,OAAqBX,EAAO,UAAU,CAClC,MAAO,CACUY,EAAe,IAAI,CAC5B,OAAqBC,EAAgB,CAAE,OAAQ,QAAQ,CAAE,EACzD,MAAoBA,EAAgB,CAAE,OAAQ,QAAQ,CAAE,CACxE,CAAa,EACYC,EAAa,IAAI,CAC1B,eAAgBC,CAChC,CAAa,CACJ,CACT,CAAK,EACD,aAAc,CACV,cAAe,CAAE,SAAU,CAAC,IAAK,IAAK,GAAG,CAAG,EAC5C,cAAe,aAClB,CACL,CAAC,EAID,SAASC,GAAO,CACZ,OAAO,IAAIC,EAAgBP,CAAY,CAC3C", "x_google_ignoreList": [0, 1]}