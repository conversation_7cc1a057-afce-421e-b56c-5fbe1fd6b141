import{S as w,e as I,s as k,m,o as p,F as q,g as f,N as b,h as g,j as _,G as v,p as S,w as j,u as B,k as h,H as C,t as E,x as F,E as G}from"./index-afe51b5b.js";import"./Button-b4eb936e.js";function d(l){let e,i;return{c(){e=m("span"),i=E(l[1]),f(e,"class","svelte-1030q2h")},m(a,s){g(a,e,s),_(e,i)},p(a,s){s&2&&F(i,a[1])},d(a){a&&h(e)}}}function H(l){let e,i,a,s,o,c,r,n=l[2]&&d(l);return s=new l[0]({}),{c(){e=m("button"),n&&n.c(),i=p(),a=m("div"),q(s.$$.fragment),f(a,"class","svelte-1030q2h"),f(e,"aria-label",l[1]),f(e,"title",l[1]),f(e,"class","svelte-1030q2h"),b(e,"pending",l[3])},m(t,u){g(t,e,u),n&&n.m(e,null),_(e,i),_(e,a),v(s,a,null),o=!0,c||(r=S(e,"click",l[4]),c=!0)},p(t,[u]){t[2]?n?n.p(t,u):(n=d(t),n.c(),n.m(e,i)):n&&(n.d(1),n=null),(!o||u&2)&&f(e,"aria-label",t[1]),(!o||u&2)&&f(e,"title",t[1]),(!o||u&8)&&b(e,"pending",t[3])},i(t){o||(j(s.$$.fragment,t),o=!0)},o(t){B(s.$$.fragment,t),o=!1},d(t){t&&h(e),n&&n.d(),C(s),c=!1,r()}}}function N(l,e,i){let{Icon:a}=e,{label:s=""}=e,{show_label:o=!1}=e,{pending:c=!1}=e;function r(n){G.call(this,l,n)}return l.$$set=n=>{"Icon"in n&&i(0,a=n.Icon),"label"in n&&i(1,s=n.label),"show_label"in n&&i(2,o=n.show_label),"pending"in n&&i(3,c=n.pending)},[a,s,o,c,r]}class D extends w{constructor(e){super(),I(this,e,N,H,k,{Icon:0,label:1,show_label:2,pending:3})}}export{D as I};
//# sourceMappingURL=IconButton-12dccad1.js.map
