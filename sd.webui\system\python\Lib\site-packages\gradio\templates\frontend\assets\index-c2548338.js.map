{"version": 3, "file": "index-c2548338.js", "sources": ["../../../../js/icons/src/Chat.svelte", "../../../../js/chatbot/utils.ts", "../../../../js/chatbot/static/Copy.svelte", "../../../../js/chatbot/static/ChatBot.svelte", "../../../../js/chatbot/static/StaticChatbot.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\taria-hidden=\"true\"\n\trole=\"img\"\n\tclass=\"iconify iconify--carbon\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tpreserveAspectRatio=\"xMidYMid meet\"\n\tviewBox=\"0 0 32 32\"\n>\n\t<path\n\t\tfill=\"currentColor\"\n\t\td=\"M17.74 30L16 29l4-7h6a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h9v2H6a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4h20a4 4 0 0 1 4 4v12a4 4 0 0 1-4 4h-4.84Z\"\n\t/>\n\t<path fill=\"currentColor\" d=\"M8 10h16v2H8zm0 6h10v2H8z\" />\n</svg>\n", "import type { FileData } from \"@gradio/upload\";\nimport { uploadToHuggingFace } from \"@gradio/utils\";\n\nexport const format_chat_for_sharing = async (\n\tchat: [string | FileData | null, string | FileData | null][]\n): Promise<string> => {\n\tlet messages = await Promise.all(\n\t\tchat.map(async (message_pair) => {\n\t\t\treturn await Promise.all(\n\t\t\t\tmessage_pair.map(async (message, i) => {\n\t\t\t\t\tif (message === null) return \"\";\n\t\t\t\t\tlet speaker_emoji = i === 0 ? \"😃\" : \"🤖\";\n\t\t\t\t\tlet html_content = \"\";\n\n\t\t\t\t\tif (typeof message === \"string\") {\n\t\t\t\t\t\tconst regexPatterns = {\n\t\t\t\t\t\t\taudio: /<audio.*?src=\"(\\/file=.*?)\"/g,\n\t\t\t\t\t\t\tvideo: /<video.*?src=\"(\\/file=.*?)\"/g,\n\t\t\t\t\t\t\timage: /<img.*?src=\"(\\/file=.*?)\".*?\\/>|!\\[.*?\\]\\((\\/file=.*?)\\)/g\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\thtml_content = message;\n\n\t\t\t\t\t\tfor (let [_, regex] of Object.entries(regexPatterns)) {\n\t\t\t\t\t\t\tlet match;\n\n\t\t\t\t\t\t\twhile ((match = regex.exec(message)) !== null) {\n\t\t\t\t\t\t\t\tconst fileUrl = match[1] || match[2];\n\t\t\t\t\t\t\t\tconst newUrl = await uploadToHuggingFace(fileUrl, \"url\");\n\t\t\t\t\t\t\t\thtml_content = html_content.replace(fileUrl, newUrl);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconst file_url = await uploadToHuggingFace(message.data, \"url\");\n\t\t\t\t\t\tif (message.mime_type?.includes(\"audio\")) {\n\t\t\t\t\t\t\thtml_content = `<audio controls src=\"${file_url}\"></audio>`;\n\t\t\t\t\t\t} else if (message.mime_type?.includes(\"video\")) {\n\t\t\t\t\t\t\thtml_content = file_url;\n\t\t\t\t\t\t} else if (message.mime_type?.includes(\"image\")) {\n\t\t\t\t\t\t\thtml_content = `<img src=\"${file_url}\" />`;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\treturn `${speaker_emoji}: ${html_content}`;\n\t\t\t\t})\n\t\t\t);\n\t\t})\n\t);\n\treturn messages\n\t\t.map((message_pair) =>\n\t\t\tmessage_pair.join(\n\t\t\t\tmessage_pair[0] !== \"\" && message_pair[1] !== \"\" ? \"\\n\" : \"\"\n\t\t\t)\n\t\t)\n\t\t.join(\"\\n\");\n};\n", "<script lang=\"ts\">\n\timport { onDestroy } from \"svelte\";\n\timport { Copy, Check } from \"@gradio/icons\";\n\n\tlet copied = false;\n\texport let value: string;\n\tlet timer: NodeJS.Timeout;\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 2000);\n\t}\n\n\tasync function handle_copy(): Promise<void> {\n\t\tif (\"clipboard\" in navigator) {\n\t\t\tawait navigator.clipboard.writeText(value);\n\t\t\tcopy_feedback();\n\t\t} else {\n\t\t\tconst textArea = document.createElement(\"textarea\");\n\t\t\ttextArea.value = value;\n\n\t\t\ttextArea.style.position = \"absolute\";\n\t\t\ttextArea.style.left = \"-999999px\";\n\n\t\t\tdocument.body.prepend(textArea);\n\t\t\ttextArea.select();\n\n\t\t\ttry {\n\t\t\t\tdocument.execCommand(\"copy\");\n\t\t\t\tcopy_feedback();\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error(error);\n\t\t\t} finally {\n\t\t\t\ttextArea.remove();\n\t\t\t}\n\t\t}\n\t}\n\n\tonDestroy(() => {\n\t\tif (timer) clearTimeout(timer);\n\t});\n</script>\n\n<button on:click={handle_copy} title=\"copy\">\n\t{#if !copied}\n\t\t<span><Copy /> </span>\n\t{/if}\n\t{#if copied}\n\t\t<span><Check /></span>\n\t{/if}\n</button>\n\n<style>\n\tbutton {\n\t\tposition: relative;\n\t\ttop: 0;\n\t\tright: 0;\n\n\t\twidth: 22px;\n\t\theight: 22px;\n\n\t\tpadding: 5px;\n\n\t\tcursor: pointer;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { format_chat_for_sharing } from \"../utils\";\n\timport { copy } from \"@gradio/utils\";\n\n\timport { beforeUpdate, afterUpdate, createEventDispatcher } from \"svelte\";\n\timport { ShareButton } from \"@gradio/atoms\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport type { ThemeMode } from \"js/app/src/components/types\";\n\timport type { FileData } from \"@gradio/upload\";\n\timport { MarkdownCode as Markdown } from \"@gradio/markdown/static\";\n\timport { get_fetchable_url_or_file } from \"@gradio/upload\";\n\timport Copy from \"./Copy.svelte\";\n\n\texport let value:\n\t\t| [string | FileData | null, string | FileData | null][]\n\t\t| null;\n\tlet old_value: [string | FileData | null, string | FileData | null][] | null =\n\t\tnull;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let pending_message = false;\n\texport let feedback: string[] | null = null;\n\texport let selectable = false;\n\texport let show_share_button = false;\n\texport let rtl = false;\n\texport let show_copy_button = false;\n\texport let avatar_images: [string | null, string | null] = [null, null];\n\texport let root: string;\n\texport let root_url: null | string;\n\n\tlet div: HTMLDivElement;\n\tlet autoscroll: boolean;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tselect: SelectData;\n\t}>();\n\n\tbeforeUpdate(() => {\n\t\tautoscroll =\n\t\t\tdiv && div.offsetHeight + div.scrollTop > div.scrollHeight - 100;\n\t});\n\n\tconst scroll = (): void => {\n\t\tif (autoscroll) {\n\t\t\tdiv.scrollTo(0, div.scrollHeight);\n\t\t}\n\t};\n\tafterUpdate(() => {\n\t\tif (autoscroll) {\n\t\t\tscroll();\n\t\t\tdiv.querySelectorAll(\"img\").forEach((n) => {\n\t\t\t\tn.addEventListener(\"load\", () => {\n\t\t\t\t\tscroll();\n\t\t\t\t});\n\t\t\t});\n\t\t}\n\t});\n\n\t$: {\n\t\tif (value !== old_value) {\n\t\t\told_value = value;\n\t\t\tdispatch(\"change\");\n\t\t}\n\t}\n\n\tfunction handle_select(\n\t\ti: number,\n\t\tj: number,\n\t\tmessage: string | FileData | null\n\t): void {\n\t\tdispatch(\"select\", {\n\t\t\tindex: [i, j],\n\t\t\tvalue: message\n\t\t});\n\t}\n</script>\n\n{#if show_share_button && value !== null && value.length > 0}\n\t<div class=\"icon-button\">\n\t\t<ShareButton\n\t\t\ton:error\n\t\t\ton:share\n\t\t\tformatter={format_chat_for_sharing}\n\t\t\t{value}\n\t\t/>\n\t</div>\n{/if}\n\n<div class=\"wrap\" bind:this={div}>\n\t<div class=\"message-wrap\" use:copy>\n\t\t{#if value !== null}\n\t\t\t{#each value as message_pair, i}\n\t\t\t\t{#each message_pair as message, j}\n\t\t\t\t\t<div class=\"message-row {j == 0 ? 'user-row' : 'bot-row'}\">\n\t\t\t\t\t\t{#if avatar_images[j] !== null}\n\t\t\t\t\t\t\t<div class=\"avatar-container\">\n\t\t\t\t\t\t\t\t<img\n\t\t\t\t\t\t\t\t\tclass=\"avatar-image\"\n\t\t\t\t\t\t\t\t\tsrc={get_fetchable_url_or_file(\n\t\t\t\t\t\t\t\t\t\tavatar_images[j],\n\t\t\t\t\t\t\t\t\t\troot,\n\t\t\t\t\t\t\t\t\t\troot_url\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\talt=\"avatar-{j == 0 ? 'user' : 'bot'}\"\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t<!-- TODO: fix-->\n\t\t\t\t\t\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t\t\t\t\t\t<!-- svelte-ignore a11y-click-events-have-key-events -->\n\t\t\t\t\t\t<div\n\t\t\t\t\t\t\tdata-testid={j == 0 ? \"user\" : \"bot\"}\n\t\t\t\t\t\t\tclass:latest={i === value.length - 1}\n\t\t\t\t\t\t\tclass=\"message {j == 0 ? 'user' : 'bot'}\"\n\t\t\t\t\t\t\tclass:hide={message === null}\n\t\t\t\t\t\t\tclass:selectable\n\t\t\t\t\t\t\ton:click={() => handle_select(i, j, message)}\n\t\t\t\t\t\t\tdir={rtl ? \"rtl\" : \"ltr\"}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{#if typeof message === \"string\"}\n\t\t\t\t\t\t\t\t<Markdown {message} {latex_delimiters} on:load={scroll} />\n\t\t\t\t\t\t\t\t{#if feedback && j == 1}\n\t\t\t\t\t\t\t\t\t<div class=\"feedback\">\n\t\t\t\t\t\t\t\t\t\t{#each feedback as f}\n\t\t\t\t\t\t\t\t\t\t\t<button>{f}</button>\n\t\t\t\t\t\t\t\t\t\t{/each}\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t{/if}\n\n\t\t\t\t\t\t\t\t{#if show_copy_button && message}\n\t\t\t\t\t\t\t\t\t<div class=\"icon-button\">\n\t\t\t\t\t\t\t\t\t\t<Copy value={message} />\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t{:else if message !== null && message.mime_type?.includes(\"audio\")}\n\t\t\t\t\t\t\t\t<audio\n\t\t\t\t\t\t\t\t\tdata-testid=\"chatbot-audio\"\n\t\t\t\t\t\t\t\t\tcontrols\n\t\t\t\t\t\t\t\t\tpreload=\"metadata\"\n\t\t\t\t\t\t\t\t\tsrc={message.data}\n\t\t\t\t\t\t\t\t\ttitle={message.alt_text}\n\t\t\t\t\t\t\t\t\ton:play\n\t\t\t\t\t\t\t\t\ton:pause\n\t\t\t\t\t\t\t\t\ton:ended\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t{:else if message !== null && message.mime_type?.includes(\"video\")}\n\t\t\t\t\t\t\t\t<video\n\t\t\t\t\t\t\t\t\tdata-testid=\"chatbot-video\"\n\t\t\t\t\t\t\t\t\tcontrols\n\t\t\t\t\t\t\t\t\tsrc={message.data}\n\t\t\t\t\t\t\t\t\ttitle={message.alt_text}\n\t\t\t\t\t\t\t\t\tpreload=\"auto\"\n\t\t\t\t\t\t\t\t\ton:play\n\t\t\t\t\t\t\t\t\ton:pause\n\t\t\t\t\t\t\t\t\ton:ended\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<track kind=\"captions\" />\n\t\t\t\t\t\t\t\t</video>\n\t\t\t\t\t\t\t{:else if message !== null && message.mime_type?.includes(\"image\")}\n\t\t\t\t\t\t\t\t<img\n\t\t\t\t\t\t\t\t\tdata-testid=\"chatbot-image\"\n\t\t\t\t\t\t\t\t\tsrc={message.data}\n\t\t\t\t\t\t\t\t\talt={message.alt_text}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t{:else if message !== null && message.data !== null}\n\t\t\t\t\t\t\t\t<a\n\t\t\t\t\t\t\t\t\tdata-testid=\"chatbot-file\"\n\t\t\t\t\t\t\t\t\thref={message.data}\n\t\t\t\t\t\t\t\t\ttarget=\"_blank\"\n\t\t\t\t\t\t\t\t\tdownload={window.__is_colab__\n\t\t\t\t\t\t\t\t\t\t? null\n\t\t\t\t\t\t\t\t\t\t: message.orig_name || message.name}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{message.orig_name || message.name}\n\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t{/each}\n\t\t\t{/each}\n\t\t{/if}\n\t\t{#if pending_message}\n\t\t\t<div class=\"message pending\">\n\t\t\t\t<div class=\"dot-flashing\" />\n\t\t\t\t&nbsp;\n\t\t\t\t<div class=\"dot-flashing\" />\n\t\t\t\t&nbsp;\n\t\t\t\t<div class=\"dot-flashing\" />\n\t\t\t</div>\n\t\t{/if}\n\t</div>\n</div>\n\n<style>\n\t.wrap {\n\t\tpadding: var(--block-padding);\n\t\twidth: 100%;\n\t\toverflow-y: auto;\n\t}\n\n\t.message-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xxl);\n\t}\n\n\t.message-wrap > div :not(.avatar-container) :global(img) {\n\t\tborder-radius: 13px;\n\t\tmax-width: 30vw;\n\t}\n\n\t.message-wrap > div :global(p:not(:first-child)) {\n\t\tmargin-top: var(--spacing-xxl);\n\t}\n\n\t.message-wrap :global(audio) {\n\t\twidth: 100%;\n\t}\n\n\t.message {\n\t\tposition: relative;\n\t\talign-self: flex-start;\n\t\tborder-width: 1px;\n\t\tborder-radius: var(--radius-xxl);\n\t\tbackground: var(--background-fill-secondary);\n\t\tpadding: var(--spacing-xxl);\n\t\tpadding-right: calc(var(--spacing-xxl) + var(--spacing-md));\n\t\twidth: calc(100% - var(--spacing-xxl));\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--text-lg);\n\t\tline-height: var(--line-lg);\n\t\toverflow-wrap: break-word;\n\t}\n\t.user {\n\t\talign-self: flex-end;\n\t\tborder-bottom-right-radius: 0;\n\t}\n\t.bot {\n\t\tborder-bottom-left-radius: 0;\n\t\tpadding-left: var(--spacing-xxl);\n\t}\n\n\t/* Colors */\n\t.bot,\n\t.pending {\n\t\tborder-color: var(--border-color-primary);\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\t.user {\n\t\tborder-color: var(--border-color-accent-subdued);\n\t\tbackground-color: var(--color-accent-soft);\n\t}\n\t.message-row {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t}\n\n\t@media (max-width: 480px) {\n\t\t.user-row {\n\t\t\talign-self: flex-end;\n\t\t}\n\n\t\t.bot-row {\n\t\t\talign-self: flex-start;\n\t\t}\n\t\t.message {\n\t\t\twidth: auto;\n\t\t}\n\t\t.bot {\n\t\t\tpadding-left: var(--spacing-xxl);\n\t\t}\n\t}\n\t.avatar-container {\n\t\talign-self: flex-end;\n\t\tposition: relative;\n\t\tjustify-content: center;\n\t\twidth: 35px;\n\t\theight: 35px;\n\t\tbottom: 0;\n\t}\n\t.user-row > .avatar-container {\n\t\torder: 2;\n\t\tmargin-left: 10px;\n\t}\n\t.bot-row > .avatar-container {\n\t\tmargin-right: 10px;\n\t}\n\timg.avatar-image {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: cover;\n\t\tborder-radius: 50%;\n\t}\n\n\t.feedback {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: var(--spacing-xl);\n\t\tright: calc(var(--spacing-xxl) + var(--spacing-xl));\n\t\tgap: var(--spacing-lg);\n\t\tfont-size: var(--text-sm);\n\t}\n\t.feedback button {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\t.feedback button:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\t.selectable {\n\t\tcursor: pointer;\n\t}\n\n\t.pending {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\talign-self: center;\n\t\tgap: 2px;\n\t}\n\t.dot-flashing {\n\t\tanimation: dot-flashing 1s infinite linear alternate;\n\t\tborder-radius: 5px;\n\t\tbackground-color: var(--body-text-color);\n\t\twidth: 5px;\n\t\theight: 5px;\n\t\tcolor: var(--body-text-color);\n\t}\n\t.dot-flashing:nth-child(2) {\n\t\tanimation-delay: 0.33s;\n\t}\n\t.dot-flashing:nth-child(3) {\n\t\tanimation-delay: 0.66s;\n\t}\n\n\t/* Small screen */\n\t@media (max-width: 480px) {\n\t\t.user {\n\t\t\talign-self: flex-end;\n\t\t}\n\t\t.bot {\n\t\t\talign-self: flex-start;\n\t\t\tpadding-left: var(--size-3);\n\t\t}\n\t}\n\n\t@keyframes dot-flashing {\n\t\t0% {\n\t\t\topacity: 0.8;\n\t\t}\n\t\t50% {\n\t\t\topacity: 0.5;\n\t\t}\n\t\t100% {\n\t\t\topacity: 0.8;\n\t\t}\n\t}\n\t.message-wrap .message :global(img) {\n\t\tmargin: var(--size-2);\n\t\tmax-height: 200px;\n\t}\n\t.message-wrap .message :global(a) {\n\t\tcolor: var(--color-text-link);\n\t\ttext-decoration: underline;\n\t}\n\n\t.hide {\n\t\tdisplay: none;\n\t}\n\n\t.message-wrap .bot :global(table),\n\t.message-wrap .bot :global(tr),\n\t.message-wrap .bot :global(td),\n\t.message-wrap .bot :global(th) {\n\t\tborder: 1px solid var(--border-color-primary);\n\t}\n\n\t.message-wrap .user :global(table),\n\t.message-wrap .user :global(tr),\n\t.message-wrap .user :global(td),\n\t.message-wrap .user :global(th) {\n\t\tborder: 1px solid var(--border-color-accent);\n\t}\n\n\t/* Lists */\n\t.message-wrap :global(ol),\n\t.message-wrap :global(ul) {\n\t\tpadding-inline-start: 2em;\n\t}\n\n\t/* KaTeX */\n\t.message-wrap :global(span.katex) {\n\t\tfont-size: var(--text-lg);\n\t\tdirection: ltr;\n\t}\n\n\t/* Copy button */\n\t.message-wrap :global(div[class*=\"code_wrap\"] > button) {\n\t\tposition: absolute;\n\t\ttop: var(--spacing-md);\n\t\tright: var(--spacing-md);\n\t\tz-index: 1;\n\t\tcursor: pointer;\n\t\tborder-bottom-left-radius: var(--radius-sm);\n\t\tpadding: 5px;\n\t\tpadding: var(--spacing-md);\n\t\twidth: 25px;\n\t\theight: 25px;\n\t}\n\n\t.message-wrap :global(code > button > span) {\n\t\tposition: absolute;\n\t\ttop: var(--spacing-md);\n\t\tright: var(--spacing-md);\n\t\twidth: 12px;\n\t\theight: 12px;\n\t}\n\t.message-wrap :global(.check) {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\topacity: 0;\n\t\tz-index: var(--layer-top);\n\t\ttransition: opacity 0.2s;\n\t\tbackground: var(--background-fill-primary);\n\t\tpadding: var(--size-1);\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.message-wrap :global(pre) {\n\t\tposition: relative;\n\t}\n\n\t.icon-button {\n\t\tposition: absolute;\n\t\ttop: 6px;\n\t\tright: 6px;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\n\timport ChatBot from \"./ChatBot.svelte\";\n\timport { Block, BlockLabel } from \"@gradio/atoms\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { Chat } from \"@gradio/icons\";\n\timport type { FileData } from \"@gradio/upload\";\n\timport { normalise_file } from \"@gradio/upload\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: [string | FileData | null, string | FileData | null][] = [];\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let label: string;\n\texport let show_label = true;\n\texport let root: string;\n\texport let root_url: null | string;\n\texport let selectable = false;\n\texport let show_share_button = false;\n\texport let rtl = false;\n\texport let show_copy_button = false;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let gradio: Gradio<{\n\t\tchange: typeof value;\n\t\tselect: SelectData;\n\t\tshare: ShareData;\n\t\terror: string;\n\t}>;\n\texport let avatar_images: [string | null, string | null] = [null, null];\n\n\tlet _value: [string | FileData | null, string | FileData | null][];\n\n\tconst redirect_src_url = (src: string): string =>\n\t\tsrc.replace('src=\"/file', `src=\"${root}file`);\n\n\t$: _value = value\n\t\t? value.map(([user_msg, bot_msg]) => [\n\t\t\t\ttypeof user_msg === \"string\"\n\t\t\t\t\t? redirect_src_url(user_msg)\n\t\t\t\t\t: normalise_file(user_msg, root, root_url),\n\t\t\t\ttypeof bot_msg === \"string\"\n\t\t\t\t\t? redirect_src_url(bot_msg)\n\t\t\t\t\t: normalise_file(bot_msg, root, root_url)\n\t\t  ])\n\t\t: [];\n\n\texport let loading_status: LoadingStatus | undefined = undefined;\n\texport let height = 400;\n</script>\n\n<Block\n\t{elem_id}\n\t{elem_classes}\n\t{visible}\n\tpadding={false}\n\t{scale}\n\t{min_width}\n\t{height}\n\tallow_overflow={false}\n>\n\t{#if loading_status}\n\t\t<StatusTracker\n\t\t\t{...loading_status}\n\t\t\tshow_progress={loading_status.show_progress === \"hidden\"\n\t\t\t\t? \"hidden\"\n\t\t\t\t: \"minimal\"}\n\t\t/>\n\t{/if}\n\t<div class=\"wrapper\">\n\t\t{#if show_label}\n\t\t\t<BlockLabel\n\t\t\t\t{show_label}\n\t\t\t\tIcon={Chat}\n\t\t\t\tfloat={false}\n\t\t\t\tlabel={label || \"Chatbot\"}\n\t\t\t/>\n\t\t{/if}\n\t\t<ChatBot\n\t\t\t{selectable}\n\t\t\t{show_share_button}\n\t\t\tvalue={_value}\n\t\t\t{latex_delimiters}\n\t\t\tpending_message={loading_status?.status === \"pending\"}\n\t\t\t{rtl}\n\t\t\t{show_copy_button}\n\t\t\ton:change={() => gradio.dispatch(\"change\", value)}\n\t\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\t\ton:share={(e) => gradio.dispatch(\"share\", e.detail)}\n\t\t\ton:error={(e) => gradio.dispatch(\"error\", e.detail)}\n\t\t\t{avatar_images}\n\t\t\t{root_url}\n\t\t\t{root}\n\t\t/>\n\t</div>\n</Block>\n\n<style>\n\t.wrapper {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-direction: column;\n\t\talign-items: start;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path0", "path1", "format_chat_for_sharing", "chat", "message_pair", "message", "i", "speaker_emoji", "html_content", "regexPatterns", "_", "regex", "match", "fileUrl", "newUrl", "uploadToHuggingFace", "file_url", "span", "ctx", "create_if_block_1", "create_if_block", "button", "copied", "value", "$$props", "timer", "copy_feedback", "$$invalidate", "handle_copy", "textArea", "error", "onDestroy", "div_1", "each_blocks", "src_url_equal", "img", "img_src_value", "get_fetchable_url_or_file", "attr", "dirty", "t_value", "a", "a_href_value", "a_download_value", "set_data", "img_alt_value", "video", "video_src_value", "video_title_value", "track", "audio", "audio_src_value", "audio_title_value", "create_if_block_4", "if_block1", "create_if_block_3", "create_if_block_9", "div0", "toggle_class", "div1", "div3", "create_if_block_10", "old_value", "latex_delimiters", "pending_message", "feedback", "selectable", "show_share_button", "rtl", "show_copy_button", "avatar_images", "root", "root_url", "div", "autoscroll", "dispatch", "createEventDispatcher", "beforeUpdate", "scroll", "afterUpdate", "n", "handle_select", "j", "click_handler", "$$value", "Cha<PERSON>", "blocklabel_changes", "elem_id", "elem_classes", "visible", "scale", "min_width", "label", "show_label", "gradio", "_value", "redirect_src_url", "src", "loading_status", "height", "change_handler", "e", "user_msg", "bot_msg", "normalise_file"], "mappings": "mwCAAAA,EAgBKC,EAAAC,EAAAC,CAAA,EALJC,EAGCF,EAAAG,CAAA,EACDD,EAAyDF,EAAAI,CAAA,gGCZ7C,MAAAC,GAA0B,MACtCC,IAEe,MAAM,QAAQ,IAC5BA,EAAK,IAAI,MAAOC,GACR,MAAM,QAAQ,IACpBA,EAAa,IAAI,MAAOC,EAASC,IAAM,CACtC,GAAID,IAAY,KAAa,MAAA,GACzB,IAAAE,EAAgBD,IAAM,EAAI,KAAO,KACjCE,EAAe,GAEf,GAAA,OAAOH,GAAY,SAAU,CAChC,MAAMI,EAAgB,CACrB,MAAO,+BACP,MAAO,+BACP,MAAO,2DAAA,EAGOD,EAAAH,EAEf,OAAS,CAACK,EAAGC,CAAK,IAAK,OAAO,QAAQF,CAAa,EAAG,CACjD,IAAAG,EAEJ,MAAQA,EAAQD,EAAM,KAAKN,CAAO,KAAO,MAAM,CAC9C,MAAMQ,EAAUD,EAAM,CAAC,GAAKA,EAAM,CAAC,EAC7BE,EAAS,MAAMC,GAAoBF,EAAS,KAAK,EACxCL,EAAAA,EAAa,QAAQK,EAASC,CAAM,QAG/C,CACN,MAAME,EAAW,MAAMD,GAAoBV,EAAQ,KAAM,KAAK,EAC1DA,EAAQ,WAAW,SAAS,OAAO,EACtCG,EAAe,wBAAwBQ,cAC7BX,EAAQ,WAAW,SAAS,OAAO,EAC9BG,EAAAQ,EACLX,EAAQ,WAAW,SAAS,OAAO,IAC7CG,EAAe,aAAaQ,SAI9B,MAAO,GAAGT,MAAkBC,GAAA,CAC5B,CAAA,CAEF,CAAA,GAGA,IAAKJ,GACLA,EAAa,KACZA,EAAa,CAAC,IAAM,IAAMA,EAAa,CAAC,IAAM,GAAK;AAAA,EAAO,EAC3D,CAAA,EAEA,KAAK;AAAA,CAAI,yFCNVT,EAAqBC,EAAAqB,EAAAnB,CAAA,gMAGrBH,EAAqBC,EAAAqB,EAAAnB,CAAA,yIAJhBoB,EAAM,CAAA,GAAAC,GAAA,IAGPD,EAAM,CAAA,GAAAE,GAAA,4GAJZzB,EAOQC,EAAAyB,EAAAvB,CAAA,8DAPUoB,EAAW,CAAA,CAAA,kBACtBA,EAAM,CAAA,kFAGPA,EAAM,CAAA,kMA9CP,IAAAI,EAAS,IACF,MAAAC,CAAa,EAAAC,EACpBC,WAEKC,GAAa,CACrBC,EAAA,EAAAL,EAAS,EAAI,EACTG,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,gBACPE,EAAA,EAAAL,EAAS,EAAK,GACZ,oBAGWM,GAAW,CACrB,GAAA,cAAe,UACZ,MAAA,UAAU,UAAU,UAAUL,CAAK,EACzCG,SAEM,MAAAG,EAAW,SAAS,cAAc,UAAU,EAClDA,EAAS,MAAQN,EAEjBM,EAAS,MAAM,SAAW,WAC1BA,EAAS,MAAM,KAAO,YAEtB,SAAS,KAAK,QAAQA,CAAQ,EAC9BA,EAAS,OAAM,MAGd,SAAS,YAAY,MAAM,EAC3BH,UACQI,GACR,QAAQ,MAAMA,CAAK,UAEnBD,EAAS,OAAM,IAKlB,OAAAE,GAAS,IAAA,CACJN,GAAO,aAAaA,CAAK,kXC4CjBvB,8IAJbP,EAOKC,EAAAoC,EAAAlC,CAAA,wLAMIoB,EAAK,CAAA,CAAA,uBAAV,OAAIZ,GAAA,kNAACY,EAAK,CAAA,CAAA,oBAAV,OAAIZ,GAAA,EAAA,mHAAJ,OAAIA,EAAA2B,EAAA,OAAA3B,GAAA,yCAAJ,OAAIA,GAAA,gNAOK4B,EAAAC,EAAA,IAAAC,EAAAC,GACJnB,EAAa,CAAA,EAACA,EAAC,EAAA,CAAA,EACfA,EACA,CAAA,EAAAA,EAAA,EAAA,CAAA,CAAA,GAAAoB,EAAAH,EAAA,MAAAC,CAAA,EAEYE,EAAAH,EAAA,MAAA,WAAAjB,EAAK,EAAA,GAAA,EAAI,OAAS,MAAK,yDARtCvB,EAUKC,EAAAoC,EAAAlC,CAAA,EATJC,EAQCiC,EAAAG,CAAA,UANKI,EAAA,CAAA,EAAA,MAAA,CAAAL,EAAAC,EAAA,IAAAC,EAAAC,GACJnB,EAAa,CAAA,EAACA,EAAC,EAAA,CAAA,EACfA,EACA,CAAA,EAAAA,EAAA,EAAA,CAAA,CAAA,qDAwEAsB,GAAAtB,EAAQ,EAAA,EAAA,WAAaA,MAAQ,MAAI,uEAN5BoB,EAAAG,EAAA,OAAAC,EAAAxB,MAAQ,IAAI,yBAERoB,EAAAG,EAAA,WAAAE,EAAA,OAAO,aACd,KACAzB,EAAQ,EAAA,EAAA,WAAaA,MAAQ,IAAI,wCANrCvB,EASGC,EAAA6C,EAAA3C,CAAA,iBADDyC,EAAA,CAAA,EAAA,GAAAC,KAAAA,GAAAtB,EAAQ,EAAA,EAAA,WAAaA,MAAQ,MAAI,KAAA0B,GAAA,EAAAJ,CAAA,EAN5BD,EAAA,CAAA,EAAA,GAAAG,KAAAA,EAAAxB,MAAQ,qBAEJqB,EAAA,CAAA,EAAA,GAAAI,KAAAA,EAAA,OAAO,aACd,KACAzB,EAAQ,EAAA,EAAA,WAAaA,MAAQ,mIAV3BgB,EAAAC,EAAA,IAAAC,EAAAlB,MAAQ,IAAI,GAAAoB,EAAAH,EAAA,MAAAC,CAAA,EACZE,EAAAH,EAAA,MAAAU,EAAA3B,MAAQ,QAAQ,wCAHtBvB,EAICC,EAAAuC,EAAArC,CAAA,UAFKyC,EAAA,CAAA,EAAA,GAAA,CAAAL,EAAAC,EAAA,IAAAC,EAAAlB,MAAQ,IAAI,gBACZqB,EAAA,CAAA,EAAA,GAAAM,KAAAA,EAAA3B,MAAQ,0NAbRgB,EAAAY,EAAA,IAAAC,EAAA7B,MAAQ,IAAI,GAAAoB,EAAAQ,EAAA,MAAAC,CAAA,EACVT,EAAAQ,EAAA,QAAAE,EAAA9B,MAAQ,QAAQ,8DAJxBvB,EAWOC,EAAAkD,EAAAhD,CAAA,EADNC,EAAwB+C,EAAAG,CAAA,gFAPnBV,EAAA,CAAA,EAAA,GAAA,CAAAL,EAAAY,EAAA,IAAAC,EAAA7B,MAAQ,IAAI,gBACVqB,EAAA,CAAA,EAAA,GAAAS,KAAAA,EAAA9B,MAAQ,6LAXVgB,EAAAgB,EAAA,IAAAC,EAAAjC,MAAQ,IAAI,GAAAoB,EAAAY,EAAA,MAAAC,CAAA,EACVb,EAAAY,EAAA,QAAAE,EAAAlC,MAAQ,QAAQ,wCALxBvB,EASCC,EAAAsD,EAAApD,CAAA,gFALKyC,EAAA,CAAA,EAAA,GAAA,CAAAL,EAAAgB,EAAA,IAAAC,EAAAjC,MAAQ,IAAI,gBACVqB,EAAA,CAAA,EAAA,GAAAa,KAAAA,EAAAlC,MAAQ,+JApBgCA,EAAM,EAAA,CAAA,QACjDA,EAAQ,CAAA,GAAIA,EAAC,EAAA,GAAI,GAACmC,GAAAnC,CAAA,EAQlBoC,EAAApC,MAAoBA,EAAO,EAAA,GAAAqC,GAAArC,CAAA,qOAR3BA,EAAQ,CAAA,GAAIA,EAAC,EAAA,GAAI,oEAQjBA,MAAoBA,EAAO,EAAA,yQANvBA,EAAQ,CAAA,CAAA,uBAAb,OAAIZ,GAAA,+HADPX,EAIKC,EAAAoC,EAAAlC,CAAA,0EAHGoB,EAAQ,CAAA,CAAA,oBAAb,OAAIZ,GAAA,EAAA,mHAAJ,uDACQY,EAAC,EAAA,EAAA,4EAAVvB,EAAmBC,EAAAyB,EAAAvB,CAAA,gCAAVoB,EAAC,EAAA,EAAA,KAAA0B,GAAA,EAAAJ,CAAA,yEAOEtB,EAAO,EAAA,CAAA,CAAA,CAAA,qFADrBvB,EAEKC,EAAAoC,EAAAlC,CAAA,uDADSoB,EAAO,EAAA,kJArCnBA,EAAa,CAAA,EAACA,EAAC,EAAA,CAAA,IAAM,MAAIsC,GAAAtC,CAAA,gDAyBjB,0DAAA,OAAAA,OAAY,SAAQ,kBAetBA,EAAO,EAAA,IAAK,MAAQA,MAAQ,WAAW,SAAS,OAAO,wBAWvDA,EAAO,EAAA,IAAK,MAAQA,MAAQ,WAAW,SAAS,OAAO,wBAavDA,EAAO,EAAA,IAAK,MAAQA,MAAQ,WAAW,SAAS,OAAO,QAMvDA,QAAY,MAAQA,EAAQ,EAAA,EAAA,OAAS,KAAI,wJArDtCoB,EAAAmB,EAAA,cAAAvC,EAAK,EAAA,GAAA,EAAI,OAAS,KAAK,EAEpBoB,EAAAmB,EAAA,QAAA,YAAAvC,EAAK,EAAA,GAAA,EAAI,OAAS,OAAK,iBAAA,cAIlCA,EAAG,CAAA,EAAG,MAAQ,KAAK,EALVwC,EAAAD,EAAA,SAAAvC,EAAM,EAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,CAAC,EAExBwC,EAAAD,EAAA,OAAAvC,QAAY,IAAI,yBArBLoB,EAAAqB,EAAA,QAAA,gBAAAzC,EAAK,EAAA,GAAA,EAAI,WAAa,WAAS,iBAAA,UAAxDvB,EAoFKC,EAAA+D,EAAA7D,CAAA,wBAnEJC,EAkEK4D,EAAAF,CAAA,wEAlFAvC,EAAa,CAAA,EAACA,EAAC,EAAA,CAAA,IAAM,2OAuBpBA,EAAG,CAAA,EAAG,MAAQ,oCALLwC,EAAAD,EAAA,SAAAvC,EAAM,EAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,CAAC,gBAExBwC,EAAAD,EAAA,OAAAvC,QAAY,IAAI,uJAtBxBA,EAAY,EAAA,CAAA,uBAAjB,OAAIZ,GAAA,kNAACY,EAAY,EAAA,CAAA,oBAAjB,OAAIZ,GAAA,EAAA,mHAAJ,OAAIA,EAAA2B,EAAA,OAAA3B,GAAA,yCAAJ,OAAIA,GAAA;;;;2GA0FPX,EAMKC,EAAAgE,EAAA9D,CAAA,qDA/GHoB,EAAiB,CAAA,GAAIA,EAAU,CAAA,IAAA,MAAQA,EAAK,CAAA,EAAC,OAAS,GAAC2C,GAAA3C,CAAA,EAarDoC,EAAApC,OAAU,MAAIC,GAAAD,CAAA,IA2FdA,EAAe,CAAA,GAAAE,GAAA,qLA7FtBzB,EAuGKC,EAAA+D,EAAA7D,CAAA,EAtGJC,EAqGK4D,EAAAF,CAAA,6FAjHDvC,EAAiB,CAAA,GAAIA,EAAU,CAAA,IAAA,MAAQA,EAAK,CAAA,EAAC,OAAS,6GAapDA,OAAU,oGA2FVA,EAAe,CAAA,iMA5KV,MAAAK,CAEJ,EAAAC,EACHsC,EACH,MACU,iBAAAC,CAIR,EAAAvC,EACQ,CAAA,gBAAAwC,EAAkB,EAAK,EAAAxC,EACvB,CAAA,SAAAyC,EAA4B,IAAI,EAAAzC,EAChC,CAAA,WAAA0C,EAAa,EAAK,EAAA1C,EAClB,CAAA,kBAAA2C,EAAoB,EAAK,EAAA3C,EACzB,CAAA,IAAA4C,EAAM,EAAK,EAAA5C,EACX,CAAA,iBAAA6C,EAAmB,EAAK,EAAA7C,GACxB,cAAA8C,EAAa,CAAoC,KAAM,IAAI,CAAA,EAAA9C,GAC3D,KAAA+C,CAAY,EAAA/C,GACZ,SAAAgD,CAAuB,EAAAhD,EAE9BiD,EACAC,EAEE,MAAAC,EAAWC,KAKjBC,GAAY,IAAA,CACXH,EACCD,GAAOA,EAAI,aAAeA,EAAI,UAAYA,EAAI,aAAe,YAGzDK,EAAM,IAAA,CACPJ,GACHD,EAAI,SAAS,EAAGA,EAAI,YAAY,GAGlCM,GAAW,IAAA,CACNL,IACHI,IACAL,EAAI,iBAAiB,KAAK,EAAE,QAASO,GAAC,CACrCA,EAAE,iBAAiB,OAAM,IAAA,CACxBF,WAaK,SAAAG,EACR3E,EACA4E,EACA7E,GAAiC,CAEjCsE,EAAS,SAAQ,CAChB,MAAK,CAAGrE,EAAG4E,CAAC,EACZ,MAAO7E,EAAA,CAAA,0PA4Ca,MAAA8E,EAAA,CAAA7E,EAAA4E,EAAA7E,KAAA4E,EAAc3E,EAAG4E,EAAG7E,EAAO,4CA5BrBoE,EAAGW,mfA7B1B7D,IAAUuC,IACbnC,EAAA,GAAAmC,EAAYvC,CAAK,EACjBoD,EAAS,QAAQ,yTCKbzD,EAAc,EAAA,iBACHA,EAAc,EAAA,EAAC,gBAAkB,SAC7C,SACA,uKAHCA,EAAc,EAAA,CAAA,iBACHA,EAAc,EAAA,EAAC,gBAAkB,SAC7C,SACA,8KAOImE,SACC,GACA,MAAAnE,MAAS,uGAATqB,EAAA,KAAA+C,EAAA,MAAApE,MAAS,oIAddA,EAAc,EAAA,GAAAC,GAAAD,CAAA,IASbA,EAAU,CAAA,GAAAE,GAAAF,CAAA,yEAWPA,EAAM,EAAA,yCAEIA,EAAc,EAAA,GAAE,SAAW,iTAd9CvB,EAyBKC,EAAA6E,EAAA3E,CAAA,iDAjCAoB,EAAc,EAAA,8GASbA,EAAU,CAAA,+LAWPA,EAAM,EAAA,oEAEIA,EAAc,EAAA,GAAE,SAAW,oYA5BrC,yDAIO,mZAvDL,GAAA,CAAA,QAAAqE,EAAU,EAAE,EAAA/D,GACZ,aAAAgE,EAAY,EAAA,EAAAhE,EACZ,CAAA,QAAAiE,EAAU,EAAI,EAAAjE,GACd,MAAAD,EAAK,EAAA,EAAAC,EACL,CAAA,MAAAkE,EAAuB,IAAI,EAAAlE,EAC3B,CAAA,UAAAmE,EAAgC,MAAS,EAAAnE,GACzC,MAAAoE,CAAa,EAAApE,EACb,CAAA,WAAAqE,EAAa,EAAI,EAAArE,GACjB,KAAA+C,CAAY,EAAA/C,GACZ,SAAAgD,CAAuB,EAAAhD,EACvB,CAAA,WAAA0C,EAAa,EAAK,EAAA1C,EAClB,CAAA,kBAAA2C,EAAoB,EAAK,EAAA3C,EACzB,CAAA,IAAA4C,EAAM,EAAK,EAAA5C,EACX,CAAA,iBAAA6C,EAAmB,EAAK,EAAA7C,GACxB,iBAAAuC,CAIR,EAAAvC,GACQ,OAAAsE,CAKT,EAAAtE,GACS,cAAA8C,EAAa,CAAoC,KAAM,IAAI,CAAA,EAAA9C,EAElEuE,QAEEC,EAAoBC,GACzBA,EAAI,QAAQ,aAAY,QAAU1B,OAAI,EAa5B,GAAA,CAAA,eAAA2B,EAA4C,MAAS,EAAA1E,EACrD,CAAA,OAAA2E,EAAS,GAAG,EAAA3E,EAsCJ,MAAA4E,EAAA,IAAAN,EAAO,SAAS,SAAUvE,CAAK,IACpC8E,GAAMP,EAAO,SAAS,SAAUO,EAAE,MAAM,IACzCA,GAAMP,EAAO,SAAS,QAASO,EAAE,MAAM,IACvCA,GAAMP,EAAO,SAAS,QAASO,EAAE,MAAM,gvBArDnD1E,EAAA,GAAEoE,EAASxE,EACTA,EAAM,IAAM,CAAA,CAAA+E,EAAUC,CAAO,IAAA,CACtB,OAAAD,GAAa,SACjBN,EAAiBM,CAAQ,EACzBE,GAAeF,EAAU/B,EAAMC,CAAQ,EACnC,OAAA+B,GAAY,SAChBP,EAAiBO,CAAO,EACxBC,GAAeD,EAAShC,EAAMC,CAAQ"}