{"version": 3, "file": "StaticTabs-7f435bd9.js", "sources": ["../../../../js/tabs/static/Tabs.svelte", "../../../../js/tabs/static/StaticTabs.svelte"], "sourcesContent": ["<script context=\"module\">\n\texport const TABS = {};\n</script>\n\n<script lang=\"ts\">\n\timport { setContext, createEventDispatcher, tick } from \"svelte\";\n\timport { writable } from \"svelte/store\";\n\timport type { SelectData } from \"@gradio/utils\";\n\n\tinterface Tab {\n\t\tname: string;\n\t\tid: object;\n\t}\n\n\texport let visible = true;\n\texport let elem_id = \"id\";\n\texport let elem_classes: string[] = [];\n\texport let selected: number | string | object;\n\n\tlet tabs: Tab[] = [];\n\n\tconst selected_tab = writable<false | object | number | string>(false);\n\tconst selected_tab_index = writable<number>(0);\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tselect: SelectData;\n\t}>();\n\n\tsetContext(TABS, {\n\t\tregister_tab: (tab: Tab) => {\n\t\t\ttabs.push({ name: tab.name, id: tab.id });\n\t\t\tselected_tab.update((current) => current ?? tab.id);\n\t\t\ttabs = tabs;\n\t\t\treturn tabs.length - 1;\n\t\t},\n\t\tunregister_tab: (tab: Tab) => {\n\t\t\tconst i = tabs.findIndex((t) => t.id === tab.id);\n\t\t\ttabs.splice(i, 1);\n\t\t\tselected_tab.update((current) =>\n\t\t\t\tcurrent === tab.id ? tabs[i]?.id || tabs[tabs.length - 1]?.id : current\n\t\t\t);\n\t\t},\n\t\tselected_tab,\n\t\tselected_tab_index\n\t});\n\n\tfunction change_tab(id: object | string | number): void {\n\t\tselected = id;\n\t\t$selected_tab = id;\n\t\t$selected_tab_index = tabs.findIndex((t) => t.id === id);\n\t\tdispatch(\"change\");\n\t}\n\n\t$: selected !== null && change_tab(selected);\n</script>\n\n<div class=\"tabs {elem_classes.join(' ')}\" class:hide={!visible} id={elem_id}>\n\t<div class=\"tab-nav scroll-hide\">\n\t\t{#each tabs as t, i (t.id)}\n\t\t\t{#if t.id === $selected_tab}\n\t\t\t\t<button class=\"selected\">\n\t\t\t\t\t{t.name}\n\t\t\t\t</button>\n\t\t\t{:else}\n\t\t\t\t<button\n\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\tchange_tab(t.id);\n\t\t\t\t\t\tdispatch(\"select\", { value: t.name, index: i });\n\t\t\t\t\t}}\n\t\t\t\t>\n\t\t\t\t\t{t.name}\n\t\t\t\t</button>\n\t\t\t{/if}\n\t\t{/each}\n\t</div>\n\t<slot />\n</div>\n\n<style>\n\t.tabs {\n\t\tposition: relative;\n\t}\n\n\t.hide {\n\t\tdisplay: none;\n\t}\n\n\t.tab-nav {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-wrap: wrap;\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t}\n\n\tbutton {\n\t\tmargin-bottom: -1px;\n\t\tborder: 1px solid transparent;\n\t\tborder-color: transparent;\n\t\tborder-bottom: none;\n\t\tborder-top-right-radius: var(--container-radius);\n\t\tborder-top-left-radius: var(--container-radius);\n\t\tpadding: var(--size-1) var(--size-4);\n\t\tcolor: var(--body-text-color-subdued);\n\t\tfont-weight: var(--section-header-text-weight);\n\t\tfont-size: var(--section-header-text-size);\n\t}\n\n\tbutton:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\t.selected {\n\t\tborder-color: var(--border-color-primary);\n\t\tbackground: var(--background-fill-primary);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.bar {\n\t\tdisplay: block;\n\t\tposition: absolute;\n\t\tbottom: -2px;\n\t\tleft: 0;\n\t\tz-index: 999;\n\t\tbackground: var(--background-fill-primary);\n\t\twidth: 100%;\n\t\theight: 2px;\n\t\tcontent: \"\";\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport Tabs from \"./Tabs.svelte\";\n\n\tconst dispatch = createEventDispatcher();\n\n\texport let visible = true;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let selected: number | string;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tselect: SelectData;\n\t}>;\n\n\t$: dispatch(\"prop_change\", { selected });\n</script>\n\n<Tabs\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\tbind:selected\n\ton:change={() => gradio.dispatch(\"change\")}\n\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n>\n\t<slot />\n</Tabs>\n"], "names": ["t0_value", "ctx", "insert", "target", "button", "anchor", "dirty", "set_data", "t0", "create_if_block", "get_key", "i", "div1", "append", "div0", "TABS", "visible", "$$props", "elem_id", "elem_classes", "selected", "tabs", "selected_tab", "writable", "selected_tab_index", "dispatch", "createEventDispatcher", "setContext", "tab", "current", "t", "change_tab", "id", "$$invalidate", "set_store_value", "$selected_tab", "$selected_tab_index", "gradio", "e"], "mappings": "yWAsEMA,EAAAC,MAAE,KAAI,6HANRC,EAOQC,EAAAC,EAAAC,CAAA,uDADNC,EAAA,GAAAN,KAAAA,EAAAC,MAAE,KAAI,KAAAM,EAAAC,EAAAR,CAAA,gDATNA,EAAAC,MAAE,KAAI,2FADRC,EAEQC,EAAAC,EAAAC,CAAA,wBADNC,EAAA,GAAAN,KAAAA,EAAAC,MAAE,KAAI,KAAAM,EAAAC,EAAAR,CAAA,iEAFJC,EAAC,EAAA,EAAC,KAAOA,EAAa,CAAA,EAAAQ,wRADrBR,EAAI,CAAA,CAAA,EAAU,MAAAS,EAAAT,GAAAA,MAAE,mBAArB,OAAIU,GAAA,EAAA,kPAFUV,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,gBAAA,WAA8BA,EAAO,CAAA,CAAA,cAApBA,EAAO,CAAA,CAAA,UAA/DC,EAoBKC,EAAAS,EAAAP,CAAA,EAnBJQ,EAiBKD,EAAAE,CAAA,oGAhBGb,EAAI,CAAA,CAAA,mIAFKA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,uDAA8BA,EAAO,CAAA,CAAA,yBAApBA,EAAO,CAAA,CAAA,gHAvDjDc,GAAI,CAAA,wDAaN,CAAA,QAAAC,EAAU,EAAI,EAAAC,EACd,CAAA,QAAAC,EAAU,IAAI,EAAAD,GACd,aAAAE,EAAY,EAAA,EAAAF,GACZ,SAAAG,CAAkC,EAAAH,EAEzCI,EAAI,CAAA,QAEFC,EAAeC,EAA2C,EAAK,2BAC/DC,EAAqBD,EAAiB,CAAC,sBACvC,MAAAE,EAAWC,IAKjBC,EAAWZ,GAAI,CACd,aAAea,IACdP,EAAK,KAAO,CAAA,KAAMO,EAAI,KAAM,GAAIA,EAAI,EAAE,CAAA,EACtCN,EAAa,OAAQO,GAAYA,GAAWD,EAAI,EAAE,SAE3CP,EAAK,OAAS,GAEtB,eAAiBO,GAAQ,CAClB,MAAAjB,EAAIU,EAAK,UAAWS,GAAMA,EAAE,KAAOF,EAAI,EAAE,EAC/CP,EAAK,OAAOV,EAAG,CAAC,EAChBW,EAAa,OAAQO,GACpBA,IAAYD,EAAI,GAAKP,EAAKV,CAAC,GAAG,IAAMU,EAAKA,EAAK,OAAS,CAAC,GAAG,GAAKQ,CAAO,GAGzE,aAAAP,EACA,mBAAAE,IAGQ,SAAAO,EAAWC,EAA4B,CAC/CC,EAAA,EAAAb,EAAWY,CAAE,EACbE,EAAAZ,EAAAa,EAAgBH,EAAEG,CAAA,MAClBC,EAAsBf,EAAK,UAAWS,GAAMA,EAAE,KAAOE,CAAE,EAAAI,CAAA,EACvDX,EAAS,QAAQ,kBAgBbM,EAAWD,EAAE,EAAE,EACfL,EAAS,SAAQ,CAAI,MAAOK,EAAE,KAAM,MAAOnB,CAAC,CAAA,qOAd9CS,IAAa,MAAQW,EAAWX,CAAQ,8+BChDrC,MAAAK,EAAWC,IAEN,GAAA,CAAA,QAAAV,EAAU,EAAI,EAAAC,EACd,CAAA,QAAAC,EAAU,EAAE,EAAAD,GACZ,aAAAE,EAAY,EAAA,EAAAF,GACZ,SAAAG,CAAyB,EAAAH,GACzB,OAAAoB,CAGT,EAAApB,uCAUeoB,EAAO,SAAS,QAAQ,IAC7BC,GAAMD,EAAO,SAAS,SAAUC,EAAE,MAAM,+PATjDb,EAAS,eAAiB,SAAAL,CAAQ,CAAA"}