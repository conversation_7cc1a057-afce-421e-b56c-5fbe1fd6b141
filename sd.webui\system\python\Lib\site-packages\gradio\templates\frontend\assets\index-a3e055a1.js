import{S as C,e as F,s as G,F as h,G as r,w as d,u as g,H as w,al as H,Z as U,ad as V,o as W,h as Z,V as z,W as A,k as E}from"./index-2519a27e.js";import{B as I}from"./Button-748313a7.js";import{T as K}from"./Table-be00801a.js";import"./utils-c3e3db58.js";import"./Upload-b0a38490.js";import"./StaticMarkdown-e77fff97.js";import"./dsv-576afacd.js";function L(n){let t,l,i,f;const _=[n[15]];let m={};for(let a=0;a<_.length;a+=1)m=U(m,_[a]);return t=new V({props:m}),i=new K({props:{label:n[7],row_count:n[6],col_count:n[5],values:n[0],headers:n[1],wrap:n[8],datatype:n[9],latex_delimiters:n[13],editable:!1,height:n[14]}}),i.$on("change",n[18]),i.$on("select",n[19]),{c(){h(t.$$.fragment),l=W(),h(i.$$.fragment)},m(a,s){r(t,a,s),Z(a,l,s),r(i,a,s),f=!0},p(a,s){const c=s&32768?z(_,[A(a[15])]):{};t.$set(c);const u={};s&128&&(u.label=a[7]),s&64&&(u.row_count=a[6]),s&32&&(u.col_count=a[5]),s&1&&(u.values=a[0]),s&2&&(u.headers=a[1]),s&256&&(u.wrap=a[8]),s&512&&(u.datatype=a[9]),s&8192&&(u.latex_delimiters=a[13]),s&16384&&(u.height=a[14]),i.$set(u)},i(a){f||(d(t.$$.fragment,a),d(i.$$.fragment,a),f=!0)},o(a){g(t.$$.fragment,a),g(i.$$.fragment,a),f=!1},d(a){a&&E(l),w(t,a),w(i,a)}}}function M(n){let t,l;return t=new I({props:{visible:n[4],padding:!1,elem_id:n[2],elem_classes:n[3],container:!1,scale:n[10],min_width:n[11],allow_overflow:!1,$$slots:{default:[L]},$$scope:{ctx:n}}}),{c(){h(t.$$.fragment)},m(i,f){r(t,i,f),l=!0},p(i,[f]){const _={};f&16&&(_.visible=i[4]),f&4&&(_.elem_id=i[2]),f&8&&(_.elem_classes=i[3]),f&1024&&(_.scale=i[10]),f&2048&&(_.min_width=i[11]),f&2159587&&(_.$$scope={dirty:f,ctx:i}),t.$set(_)},i(i){l||(d(t.$$.fragment,i),l=!0)},o(i){g(t.$$.fragment,i),l=!1},d(i){w(t,i)}}}function P(n,t,l){let{headers:i=[]}=t,{elem_id:f=""}=t,{elem_classes:_=[]}=t,{visible:m=!0}=t,{value:a={data:[["","",""]],headers:["1","2","3"]}}=t,s=JSON.stringify(a),{value_is_output:c=!1}=t,{col_count:u}=t,{row_count:b}=t,{label:k=null}=t,{wrap:S}=t,{datatype:J}=t,{scale:N=null}=t,{min_width:O=void 0}=t,{gradio:o}=t,{latex_delimiters:B}=t,{height:D=void 0}=t,{loading_status:T}=t;function j(){o.dispatch("change"),c||o.dispatch("input")}H(()=>{l(16,c=!1)});const q=({detail:e})=>{l(0,a=e)},v=e=>o.dispatch("select",e.detail);return n.$$set=e=>{"headers"in e&&l(1,i=e.headers),"elem_id"in e&&l(2,f=e.elem_id),"elem_classes"in e&&l(3,_=e.elem_classes),"visible"in e&&l(4,m=e.visible),"value"in e&&l(0,a=e.value),"value_is_output"in e&&l(16,c=e.value_is_output),"col_count"in e&&l(5,u=e.col_count),"row_count"in e&&l(6,b=e.row_count),"label"in e&&l(7,k=e.label),"wrap"in e&&l(8,S=e.wrap),"datatype"in e&&l(9,J=e.datatype),"scale"in e&&l(10,N=e.scale),"min_width"in e&&l(11,O=e.min_width),"gradio"in e&&l(12,o=e.gradio),"latex_delimiters"in e&&l(13,B=e.latex_delimiters),"height"in e&&l(14,D=e.height),"loading_status"in e&&l(15,T=e.loading_status)},n.$$.update=()=>{n.$$.dirty&131073&&JSON.stringify(a)!==s&&(l(17,s=JSON.stringify(a)),j())},[a,i,f,_,m,u,b,k,S,J,N,O,o,B,D,T,c,s,q,v]}class Q extends C{constructor(t){super(),F(this,t,P,M,G,{headers:1,elem_id:2,elem_classes:3,visible:4,value:0,value_is_output:16,col_count:5,row_count:6,label:7,wrap:8,datatype:9,scale:10,min_width:11,gradio:12,latex_delimiters:13,height:14,loading_status:15})}}const ee=Q;export{ee as default};
//# sourceMappingURL=index-a3e055a1.js.map
