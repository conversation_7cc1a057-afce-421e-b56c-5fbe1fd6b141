import{S as Bl,e as Pl,s as El,f as Nr,g as et,h as Rl,j as Ll,n as ai,k as Il,_ as Se,m as Vr,C as kc,D as vc,R as Sc}from"./index-2519a27e.js";function Cc(n){let e,t;return{c(){e=Nr("svg"),t=Nr("path"),et(t,"fill","currentColor"),et(t,"d","m31 16l-7 7l-1.41-1.41L28.17 16l-5.58-5.59L24 9l7 7zM1 16l7-7l1.41 1.41L3.83 16l5.58 5.59L8 23l-7-7zm11.42 9.484L17.64 6l1.932.517L14.352 26z"),et(e,"width","100%"),et(e,"height","100%"),et(e,"viewBox","0 0 32 32")},m(i,s){Rl(i,e,s),Ll(e,t)},p:ai,i:ai,o:ai,d(i){i&&Il(e)}}}let Jg=class extends Bl{constructor(e){super(),Pl(this,e,null,Cc,El,{})}};class V{constructor(){}lineAt(e){if(e<0||e>this.length)throw new RangeError(`Invalid position ${e} in document of length ${this.length}`);return this.lineInner(e,!1,1,0)}line(e){if(e<1||e>this.lines)throw new RangeError(`Invalid line number ${e} in ${this.lines}-line document`);return this.lineInner(e,!0,1,0)}replace(e,t,i){let s=[];return this.decompose(0,e,s,2),i.length&&i.decompose(0,i.length,s,3),this.decompose(t,this.length,s,1),We.from(s,this.length-(t-e)+i.length)}append(e){return this.replace(this.length,this.length,e)}slice(e,t=this.length){let i=[];return this.decompose(e,t,i,0),We.from(i,t-e)}eq(e){if(e==this)return!0;if(e.length!=this.length||e.lines!=this.lines)return!1;let t=this.scanIdentical(e,1),i=this.length-this.scanIdentical(e,-1),s=new hi(this),r=new hi(e);for(let o=t,l=t;;){if(s.next(o),r.next(o),o=0,s.lineBreak!=r.lineBreak||s.done!=r.done||s.value!=r.value)return!1;if(l+=s.value.length,s.done||l>=i)return!0}}iter(e=1){return new hi(this,e)}iterRange(e,t=this.length){return new Nl(this,e,t)}iterLines(e,t){let i;if(e==null)i=this.iter();else{t==null&&(t=this.lines+1);let s=this.line(e).from;i=this.iterRange(s,Math.max(s,t==this.lines+1?this.length:t<=1?0:this.line(t-1).to))}return new Vl(i)}toString(){return this.sliceString(0)}toJSON(){let e=[];return this.flatten(e),e}static of(e){if(e.length==0)throw new RangeError("A document must have at least one line");return e.length==1&&!e[0]?V.empty:e.length<=32?new Q(e):We.from(Q.split(e,[]))}}class Q extends V{constructor(e,t=Ac(e)){super(),this.text=e,this.length=t}get lines(){return this.text.length}get children(){return null}lineInner(e,t,i,s){for(let r=0;;r++){let o=this.text[r],l=s+o.length;if((t?i:l)>=e)return new Mc(s,l,i,o);s=l+1,i++}}decompose(e,t,i,s){let r=e<=0&&t>=this.length?this:new Q(Fr(this.text,e,t),Math.min(t,this.length)-Math.max(0,e));if(s&1){let o=i.pop(),l=Xi(r.text,o.text.slice(),0,r.length);if(l.length<=32)i.push(new Q(l,o.length+r.length));else{let a=l.length>>1;i.push(new Q(l.slice(0,a)),new Q(l.slice(a)))}}else i.push(r)}replace(e,t,i){if(!(i instanceof Q))return super.replace(e,t,i);let s=Xi(this.text,Xi(i.text,Fr(this.text,0,e)),t),r=this.length+i.length-(t-e);return s.length<=32?new Q(s,r):We.from(Q.split(s,[]),r)}sliceString(e,t=this.length,i=`
`){let s="";for(let r=0,o=0;r<=t&&o<this.text.length;o++){let l=this.text[o],a=r+l.length;r>e&&o&&(s+=i),e<a&&t>r&&(s+=l.slice(Math.max(0,e-r),t-r)),r=a+1}return s}flatten(e){for(let t of this.text)e.push(t)}scanIdentical(){return 0}static split(e,t){let i=[],s=-1;for(let r of e)i.push(r),s+=r.length+1,i.length==32&&(t.push(new Q(i,s)),i=[],s=-1);return s>-1&&t.push(new Q(i,s)),t}}class We extends V{constructor(e,t){super(),this.children=e,this.length=t,this.lines=0;for(let i of e)this.lines+=i.lines}lineInner(e,t,i,s){for(let r=0;;r++){let o=this.children[r],l=s+o.length,a=i+o.lines-1;if((t?a:l)>=e)return o.lineInner(e,t,i,s);s=l+1,i=a+1}}decompose(e,t,i,s){for(let r=0,o=0;o<=t&&r<this.children.length;r++){let l=this.children[r],a=o+l.length;if(e<=a&&t>=o){let h=s&((o<=e?1:0)|(a>=t?2:0));o>=e&&a<=t&&!h?i.push(l):l.decompose(e-o,t-o,i,h)}o=a+1}}replace(e,t,i){if(i.lines<this.lines)for(let s=0,r=0;s<this.children.length;s++){let o=this.children[s],l=r+o.length;if(e>=r&&t<=l){let a=o.replace(e-r,t-r,i),h=this.lines-o.lines+a.lines;if(a.lines<h>>5-1&&a.lines>h>>5+1){let c=this.children.slice();return c[s]=a,new We(c,this.length-(t-e)+i.length)}return super.replace(r,l,a)}r=l+1}return super.replace(e,t,i)}sliceString(e,t=this.length,i=`
`){let s="";for(let r=0,o=0;r<this.children.length&&o<=t;r++){let l=this.children[r],a=o+l.length;o>e&&r&&(s+=i),e<a&&t>o&&(s+=l.sliceString(e-o,t-o,i)),o=a+1}return s}flatten(e){for(let t of this.children)t.flatten(e)}scanIdentical(e,t){if(!(e instanceof We))return 0;let i=0,[s,r,o,l]=t>0?[0,0,this.children.length,e.children.length]:[this.children.length-1,e.children.length-1,-1,-1];for(;;s+=t,r+=t){if(s==o||r==l)return i;let a=this.children[s],h=e.children[r];if(a!=h)return i+a.scanIdentical(h,t);i+=a.length+1}}static from(e,t=e.reduce((i,s)=>i+s.length+1,-1)){let i=0;for(let d of e)i+=d.lines;if(i<32){let d=[];for(let p of e)p.flatten(d);return new Q(d,t)}let s=Math.max(32,i>>5),r=s<<1,o=s>>1,l=[],a=0,h=-1,c=[];function f(d){let p;if(d.lines>r&&d instanceof We)for(let y of d.children)f(y);else d.lines>o&&(a>o||!a)?(u(),l.push(d)):d instanceof Q&&a&&(p=c[c.length-1])instanceof Q&&d.lines+p.lines<=32?(a+=d.lines,h+=d.length+1,c[c.length-1]=new Q(p.text.concat(d.text),p.length+1+d.length)):(a+d.lines>s&&u(),a+=d.lines,h+=d.length+1,c.push(d))}function u(){a!=0&&(l.push(c.length==1?c[0]:We.from(c,h)),h=-1,a=c.length=0)}for(let d of e)f(d);return u(),l.length==1?l[0]:new We(l,t)}}V.empty=new Q([""],0);function Ac(n){let e=-1;for(let t of n)e+=t.length+1;return e}function Xi(n,e,t=0,i=1e9){for(let s=0,r=0,o=!0;r<n.length&&s<=i;r++){let l=n[r],a=s+l.length;a>=t&&(a>i&&(l=l.slice(0,i-s)),s<t&&(l=l.slice(t-s)),o?(e[e.length-1]+=l,o=!1):e.push(l)),s=a+1}return e}function Fr(n,e,t){return Xi(n,[""],e,t)}class hi{constructor(e,t=1){this.dir=t,this.done=!1,this.lineBreak=!1,this.value="",this.nodes=[e],this.offsets=[t>0?1:(e instanceof Q?e.text.length:e.children.length)<<1]}nextInner(e,t){for(this.done=this.lineBreak=!1;;){let i=this.nodes.length-1,s=this.nodes[i],r=this.offsets[i],o=r>>1,l=s instanceof Q?s.text.length:s.children.length;if(o==(t>0?l:0)){if(i==0)return this.done=!0,this.value="",this;t>0&&this.offsets[i-1]++,this.nodes.pop(),this.offsets.pop()}else if((r&1)==(t>0?0:1)){if(this.offsets[i]+=t,e==0)return this.lineBreak=!0,this.value=`
`,this;e--}else if(s instanceof Q){let a=s.text[o+(t<0?-1:0)];if(this.offsets[i]+=t,a.length>Math.max(0,e))return this.value=e==0?a:t>0?a.slice(e):a.slice(0,a.length-e),this;e-=a.length}else{let a=s.children[o+(t<0?-1:0)];e>a.length?(e-=a.length,this.offsets[i]+=t):(t<0&&this.offsets[i]--,this.nodes.push(a),this.offsets.push(t>0?1:(a instanceof Q?a.text.length:a.children.length)<<1))}}}next(e=0){return e<0&&(this.nextInner(-e,-this.dir),e=this.value.length),this.nextInner(e,this.dir)}}class Nl{constructor(e,t,i){this.value="",this.done=!1,this.cursor=new hi(e,t>i?-1:1),this.pos=t>i?e.length:0,this.from=Math.min(t,i),this.to=Math.max(t,i)}nextInner(e,t){if(t<0?this.pos<=this.from:this.pos>=this.to)return this.value="",this.done=!0,this;e+=Math.max(0,t<0?this.pos-this.to:this.from-this.pos);let i=t<0?this.pos-this.from:this.to-this.pos;e>i&&(e=i),i-=e;let{value:s}=this.cursor.next(e);return this.pos+=(s.length+e)*t,this.value=s.length<=i?s:t<0?s.slice(s.length-i):s.slice(0,i),this.done=!this.value,this}next(e=0){return e<0?e=Math.max(e,this.from-this.pos):e>0&&(e=Math.min(e,this.to-this.pos)),this.nextInner(e,this.cursor.dir)}get lineBreak(){return this.cursor.lineBreak&&this.value!=""}}class Vl{constructor(e){this.inner=e,this.afterBreak=!0,this.value="",this.done=!1}next(e=0){let{done:t,lineBreak:i,value:s}=this.inner.next(e);return t?(this.done=!0,this.value=""):i?this.afterBreak?this.value="":(this.afterBreak=!0,this.next()):(this.value=s,this.afterBreak=!1),this}get lineBreak(){return!1}}typeof Symbol<"u"&&(V.prototype[Symbol.iterator]=function(){return this.iter()},hi.prototype[Symbol.iterator]=Nl.prototype[Symbol.iterator]=Vl.prototype[Symbol.iterator]=function(){return this});class Mc{constructor(e,t,i,s){this.from=e,this.to=t,this.number=i,this.text=s}get length(){return this.to-this.from}}let Vt="lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o".split(",").map(n=>n?parseInt(n,36):1);for(let n=1;n<Vt.length;n++)Vt[n]+=Vt[n-1];function Dc(n){for(let e=1;e<Vt.length;e+=2)if(Vt[e]>n)return Vt[e-1]<=n;return!1}function Hr(n){return n>=127462&&n<=127487}const Wr=8205;function ke(n,e,t=!0,i=!0){return(t?Fl:Tc)(n,e,i)}function Fl(n,e,t){if(e==n.length)return e;e&&Hl(n.charCodeAt(e))&&Wl(n.charCodeAt(e-1))&&e--;let i=ce(n,e);for(e+=Ce(i);e<n.length;){let s=ce(n,e);if(i==Wr||s==Wr||t&&Dc(s))e+=Ce(s),i=s;else if(Hr(s)){let r=0,o=e-2;for(;o>=0&&Hr(ce(n,o));)r++,o-=2;if(r%2==0)break;e+=2}else break}return e}function Tc(n,e,t){for(;e>0;){let i=Fl(n,e-2,t);if(i<e)return i;e--}return 0}function Hl(n){return n>=56320&&n<57344}function Wl(n){return n>=55296&&n<56320}function ce(n,e){let t=n.charCodeAt(e);if(!Wl(t)||e+1==n.length)return t;let i=n.charCodeAt(e+1);return Hl(i)?(t-55296<<10)+(i-56320)+65536:t}function zl(n){return n<=65535?String.fromCharCode(n):(n-=65536,String.fromCharCode((n>>10)+55296,(n&1023)+56320))}function Ce(n){return n<65536?1:2}const us=/\r\n?|\n/;var le=function(n){return n[n.Simple=0]="Simple",n[n.TrackDel=1]="TrackDel",n[n.TrackBefore=2]="TrackBefore",n[n.TrackAfter=3]="TrackAfter",n}(le||(le={}));class je{constructor(e){this.sections=e}get length(){let e=0;for(let t=0;t<this.sections.length;t+=2)e+=this.sections[t];return e}get newLength(){let e=0;for(let t=0;t<this.sections.length;t+=2){let i=this.sections[t+1];e+=i<0?this.sections[t]:i}return e}get empty(){return this.sections.length==0||this.sections.length==2&&this.sections[1]<0}iterGaps(e){for(let t=0,i=0,s=0;t<this.sections.length;){let r=this.sections[t++],o=this.sections[t++];o<0?(e(i,s,r),s+=r):s+=o,i+=r}}iterChangedRanges(e,t=!1){ds(this,e,t)}get invertedDesc(){let e=[];for(let t=0;t<this.sections.length;){let i=this.sections[t++],s=this.sections[t++];s<0?e.push(i,s):e.push(s,i)}return new je(e)}composeDesc(e){return this.empty?e:e.empty?this:_l(this,e)}mapDesc(e,t=!1){return e.empty?this:ps(this,e,t)}mapPos(e,t=-1,i=le.Simple){let s=0,r=0;for(let o=0;o<this.sections.length;){let l=this.sections[o++],a=this.sections[o++],h=s+l;if(a<0){if(h>e)return r+(e-s);r+=l}else{if(i!=le.Simple&&h>=e&&(i==le.TrackDel&&s<e&&h>e||i==le.TrackBefore&&s<e||i==le.TrackAfter&&h>e))return null;if(h>e||h==e&&t<0&&!l)return e==s||t<0?r:r+a;r+=a}s=h}if(e>s)throw new RangeError(`Position ${e} is out of range for changeset of length ${s}`);return r}touchesRange(e,t=e){for(let i=0,s=0;i<this.sections.length&&s<=t;){let r=this.sections[i++],o=this.sections[i++],l=s+r;if(o>=0&&s<=t&&l>=e)return s<e&&l>t?"cover":!0;s=l}return!1}toString(){let e="";for(let t=0;t<this.sections.length;){let i=this.sections[t++],s=this.sections[t++];e+=(e?" ":"")+i+(s>=0?":"+s:"")}return e}toJSON(){return this.sections}static fromJSON(e){if(!Array.isArray(e)||e.length%2||e.some(t=>typeof t!="number"))throw new RangeError("Invalid JSON representation of ChangeDesc");return new je(e)}static create(e){return new je(e)}}class te extends je{constructor(e,t){super(e),this.inserted=t}apply(e){if(this.length!=e.length)throw new RangeError("Applying change set to a document with the wrong length");return ds(this,(t,i,s,r,o)=>e=e.replace(s,s+(i-t),o),!1),e}mapDesc(e,t=!1){return ps(this,e,t,!0)}invert(e){let t=this.sections.slice(),i=[];for(let s=0,r=0;s<t.length;s+=2){let o=t[s],l=t[s+1];if(l>=0){t[s]=l,t[s+1]=o;let a=s>>1;for(;i.length<a;)i.push(V.empty);i.push(o?e.slice(r,r+o):V.empty)}r+=o}return new te(t,i)}compose(e){return this.empty?e:e.empty?this:_l(this,e,!0)}map(e,t=!1){return e.empty?this:ps(this,e,t,!0)}iterChanges(e,t=!1){ds(this,e,t)}get desc(){return je.create(this.sections)}filter(e){let t=[],i=[],s=[],r=new pi(this);e:for(let o=0,l=0;;){let a=o==e.length?1e9:e[o++];for(;l<a||l==a&&r.len==0;){if(r.done)break e;let c=Math.min(r.len,a-l);he(s,c,-1);let f=r.ins==-1?-1:r.off==0?r.ins:0;he(t,c,f),f>0&&it(i,t,r.text),r.forward(c),l+=c}let h=e[o++];for(;l<h;){if(r.done)break e;let c=Math.min(r.len,h-l);he(t,c,-1),he(s,c,r.ins==-1?-1:r.off==0?r.ins:0),r.forward(c),l+=c}}return{changes:new te(t,i),filtered:je.create(s)}}toJSON(){let e=[];for(let t=0;t<this.sections.length;t+=2){let i=this.sections[t],s=this.sections[t+1];s<0?e.push(i):s==0?e.push([i]):e.push([i].concat(this.inserted[t>>1].toJSON()))}return e}static of(e,t,i){let s=[],r=[],o=0,l=null;function a(c=!1){if(!c&&!s.length)return;o<t&&he(s,t-o,-1);let f=new te(s,r);l=l?l.compose(f.map(l)):f,s=[],r=[],o=0}function h(c){if(Array.isArray(c))for(let f of c)h(f);else if(c instanceof te){if(c.length!=t)throw new RangeError(`Mismatched change set length (got ${c.length}, expected ${t})`);a(),l=l?l.compose(c.map(l)):c}else{let{from:f,to:u=f,insert:d}=c;if(f>u||f<0||u>t)throw new RangeError(`Invalid change range ${f} to ${u} (in doc of length ${t})`);let p=d?typeof d=="string"?V.of(d.split(i||us)):d:V.empty,y=p.length;if(f==u&&y==0)return;f<o&&a(),f>o&&he(s,f-o,-1),he(s,u-f,y),it(r,s,p),o=u}}return h(e),a(!l),l}static empty(e){return new te(e?[e,-1]:[],[])}static fromJSON(e){if(!Array.isArray(e))throw new RangeError("Invalid JSON representation of ChangeSet");let t=[],i=[];for(let s=0;s<e.length;s++){let r=e[s];if(typeof r=="number")t.push(r,-1);else{if(!Array.isArray(r)||typeof r[0]!="number"||r.some((o,l)=>l&&typeof o!="string"))throw new RangeError("Invalid JSON representation of ChangeSet");if(r.length==1)t.push(r[0],0);else{for(;i.length<s;)i.push(V.empty);i[s]=V.of(r.slice(1)),t.push(r[0],i[s].length)}}}return new te(t,i)}static createSet(e,t){return new te(e,t)}}function he(n,e,t,i=!1){if(e==0&&t<=0)return;let s=n.length-2;s>=0&&t<=0&&t==n[s+1]?n[s]+=e:e==0&&n[s]==0?n[s+1]+=t:i?(n[s]+=e,n[s+1]+=t):n.push(e,t)}function it(n,e,t){if(t.length==0)return;let i=e.length-2>>1;if(i<n.length)n[n.length-1]=n[n.length-1].append(t);else{for(;n.length<i;)n.push(V.empty);n.push(t)}}function ds(n,e,t){let i=n.inserted;for(let s=0,r=0,o=0;o<n.sections.length;){let l=n.sections[o++],a=n.sections[o++];if(a<0)s+=l,r+=l;else{let h=s,c=r,f=V.empty;for(;h+=l,c+=a,a&&i&&(f=f.append(i[o-2>>1])),!(t||o==n.sections.length||n.sections[o+1]<0);)l=n.sections[o++],a=n.sections[o++];e(s,h,r,c,f),s=h,r=c}}}function ps(n,e,t,i=!1){let s=[],r=i?[]:null,o=new pi(n),l=new pi(e);for(let a=-1;;)if(o.ins==-1&&l.ins==-1){let h=Math.min(o.len,l.len);he(s,h,-1),o.forward(h),l.forward(h)}else if(l.ins>=0&&(o.ins<0||a==o.i||o.off==0&&(l.len<o.len||l.len==o.len&&!t))){let h=l.len;for(he(s,l.ins,-1);h;){let c=Math.min(o.len,h);o.ins>=0&&a<o.i&&o.len<=c&&(he(s,0,o.ins),r&&it(r,s,o.text),a=o.i),o.forward(c),h-=c}l.next()}else if(o.ins>=0){let h=0,c=o.len;for(;c;)if(l.ins==-1){let f=Math.min(c,l.len);h+=f,c-=f,l.forward(f)}else if(l.ins==0&&l.len<c)c-=l.len,l.next();else break;he(s,h,a<o.i?o.ins:0),r&&a<o.i&&it(r,s,o.text),a=o.i,o.forward(o.len-c)}else{if(o.done&&l.done)return r?te.createSet(s,r):je.create(s);throw new Error("Mismatched change set lengths")}}function _l(n,e,t=!1){let i=[],s=t?[]:null,r=new pi(n),o=new pi(e);for(let l=!1;;){if(r.done&&o.done)return s?te.createSet(i,s):je.create(i);if(r.ins==0)he(i,r.len,0,l),r.next();else if(o.len==0&&!o.done)he(i,0,o.ins,l),s&&it(s,i,o.text),o.next();else{if(r.done||o.done)throw new Error("Mismatched change set lengths");{let a=Math.min(r.len2,o.len),h=i.length;if(r.ins==-1){let c=o.ins==-1?-1:o.off?0:o.ins;he(i,a,c,l),s&&c&&it(s,i,o.text)}else o.ins==-1?(he(i,r.off?0:r.len,a,l),s&&it(s,i,r.textBit(a))):(he(i,r.off?0:r.len,o.off?0:o.ins,l),s&&!o.off&&it(s,i,o.text));l=(r.ins>a||o.ins>=0&&o.len>a)&&(l||i.length>h),r.forward2(a),o.forward(a)}}}}class pi{constructor(e){this.set=e,this.i=0,this.next()}next(){let{sections:e}=this.set;this.i<e.length?(this.len=e[this.i++],this.ins=e[this.i++]):(this.len=0,this.ins=-2),this.off=0}get done(){return this.ins==-2}get len2(){return this.ins<0?this.len:this.ins}get text(){let{inserted:e}=this.set,t=this.i-2>>1;return t>=e.length?V.empty:e[t]}textBit(e){let{inserted:t}=this.set,i=this.i-2>>1;return i>=t.length&&!e?V.empty:t[i].slice(this.off,e==null?void 0:this.off+e)}forward(e){e==this.len?this.next():(this.len-=e,this.off+=e)}forward2(e){this.ins==-1?this.forward(e):e==this.ins?this.next():(this.ins-=e,this.off+=e)}}class yt{constructor(e,t,i){this.from=e,this.to=t,this.flags=i}get anchor(){return this.flags&16?this.to:this.from}get head(){return this.flags&16?this.from:this.to}get empty(){return this.from==this.to}get assoc(){return this.flags&4?-1:this.flags&8?1:0}get bidiLevel(){let e=this.flags&3;return e==3?null:e}get goalColumn(){let e=this.flags>>5;return e==33554431?void 0:e}map(e,t=-1){let i,s;return this.empty?i=s=e.mapPos(this.from,t):(i=e.mapPos(this.from,1),s=e.mapPos(this.to,-1)),i==this.from&&s==this.to?this:new yt(i,s,this.flags)}extend(e,t=e){if(e<=this.anchor&&t>=this.anchor)return w.range(e,t);let i=Math.abs(e-this.anchor)>Math.abs(t-this.anchor)?e:t;return w.range(this.anchor,i)}eq(e){return this.anchor==e.anchor&&this.head==e.head}toJSON(){return{anchor:this.anchor,head:this.head}}static fromJSON(e){if(!e||typeof e.anchor!="number"||typeof e.head!="number")throw new RangeError("Invalid JSON representation for SelectionRange");return w.range(e.anchor,e.head)}static create(e,t,i){return new yt(e,t,i)}}class w{constructor(e,t){this.ranges=e,this.mainIndex=t}map(e,t=-1){return e.empty?this:w.create(this.ranges.map(i=>i.map(e,t)),this.mainIndex)}eq(e){if(this.ranges.length!=e.ranges.length||this.mainIndex!=e.mainIndex)return!1;for(let t=0;t<this.ranges.length;t++)if(!this.ranges[t].eq(e.ranges[t]))return!1;return!0}get main(){return this.ranges[this.mainIndex]}asSingle(){return this.ranges.length==1?this:new w([this.main],0)}addRange(e,t=!0){return w.create([e].concat(this.ranges),t?0:this.mainIndex+1)}replaceRange(e,t=this.mainIndex){let i=this.ranges.slice();return i[t]=e,w.create(i,this.mainIndex)}toJSON(){return{ranges:this.ranges.map(e=>e.toJSON()),main:this.mainIndex}}static fromJSON(e){if(!e||!Array.isArray(e.ranges)||typeof e.main!="number"||e.main>=e.ranges.length)throw new RangeError("Invalid JSON representation for EditorSelection");return new w(e.ranges.map(t=>yt.fromJSON(t)),e.main)}static single(e,t=e){return new w([w.range(e,t)],0)}static create(e,t=0){if(e.length==0)throw new RangeError("A selection needs at least one range");for(let i=0,s=0;s<e.length;s++){let r=e[s];if(r.empty?r.from<=i:r.from<i)return w.normalized(e.slice(),t);i=r.to}return new w(e,t)}static cursor(e,t=0,i,s){return yt.create(e,e,(t==0?0:t<0?4:8)|(i==null?3:Math.min(2,i))|(s??33554431)<<5)}static range(e,t,i){let s=(i??33554431)<<5;return t<e?yt.create(t,e,16|s|8):yt.create(e,t,s|(t>e?4:0))}static normalized(e,t=0){let i=e[t];e.sort((s,r)=>s.from-r.from),t=e.indexOf(i);for(let s=1;s<e.length;s++){let r=e[s],o=e[s-1];if(r.empty?r.from<=o.to:r.from<o.to){let l=o.from,a=Math.max(r.to,o.to);s<=t&&t--,e.splice(--s,2,r.anchor>r.head?w.range(a,l):w.range(l,a))}}return new w(e,t)}}function ql(n,e){for(let t of n.ranges)if(t.to>e)throw new RangeError("Selection points outside of document")}let sr=0;class D{constructor(e,t,i,s,r){this.combine=e,this.compareInput=t,this.compare=i,this.isStatic=s,this.id=sr++,this.default=e([]),this.extensions=typeof r=="function"?r(this):r}static define(e={}){return new D(e.combine||(t=>t),e.compareInput||((t,i)=>t===i),e.compare||(e.combine?(t,i)=>t===i:rr),!!e.static,e.enables)}of(e){return new Zi([],this,0,e)}compute(e,t){if(this.isStatic)throw new Error("Can't compute a static facet");return new Zi(e,this,1,t)}computeN(e,t){if(this.isStatic)throw new Error("Can't compute a static facet");return new Zi(e,this,2,t)}from(e,t){return t||(t=i=>i),this.compute([e],i=>t(i.field(e)))}}function rr(n,e){return n==e||n.length==e.length&&n.every((t,i)=>t===e[i])}class Zi{constructor(e,t,i,s){this.dependencies=e,this.facet=t,this.type=i,this.value=s,this.id=sr++}dynamicSlot(e){var t;let i=this.value,s=this.facet.compareInput,r=this.id,o=e[r]>>1,l=this.type==2,a=!1,h=!1,c=[];for(let f of this.dependencies)f=="doc"?a=!0:f=="selection"?h=!0:((t=e[f.id])!==null&&t!==void 0?t:1)&1||c.push(e[f.id]);return{create(f){return f.values[o]=i(f),1},update(f,u){if(a&&u.docChanged||h&&(u.docChanged||u.selection)||ms(f,c)){let d=i(f);if(l?!zr(d,f.values[o],s):!s(d,f.values[o]))return f.values[o]=d,1}return 0},reconfigure:(f,u)=>{let d=i(f),p=u.config.address[r];if(p!=null){let y=rn(u,p);if(this.dependencies.every(g=>g instanceof D?u.facet(g)===f.facet(g):g instanceof be?u.field(g,!1)==f.field(g,!1):!0)||(l?zr(d,y,s):s(d,y)))return f.values[o]=y,0}return f.values[o]=d,1}}}}function zr(n,e,t){if(n.length!=e.length)return!1;for(let i=0;i<n.length;i++)if(!t(n[i],e[i]))return!1;return!0}function ms(n,e){let t=!1;for(let i of e)ci(n,i)&1&&(t=!0);return t}function Oc(n,e,t){let i=t.map(a=>n[a.id]),s=t.map(a=>a.type),r=i.filter(a=>!(a&1)),o=n[e.id]>>1;function l(a){let h=[];for(let c=0;c<i.length;c++){let f=rn(a,i[c]);if(s[c]==2)for(let u of f)h.push(u);else h.push(f)}return e.combine(h)}return{create(a){for(let h of i)ci(a,h);return a.values[o]=l(a),1},update(a,h){if(!ms(a,r))return 0;let c=l(a);return e.compare(c,a.values[o])?0:(a.values[o]=c,1)},reconfigure(a,h){let c=ms(a,i),f=h.config.facets[e.id],u=h.facet(e);if(f&&!c&&rr(t,f))return a.values[o]=u,0;let d=l(a);return e.compare(d,u)?(a.values[o]=u,0):(a.values[o]=d,1)}}}const _r=D.define({static:!0});class be{constructor(e,t,i,s,r){this.id=e,this.createF=t,this.updateF=i,this.compareF=s,this.spec=r,this.provides=void 0}static define(e){let t=new be(sr++,e.create,e.update,e.compare||((i,s)=>i===s),e);return e.provide&&(t.provides=e.provide(t)),t}create(e){let t=e.facet(_r).find(i=>i.field==this);return(t?.create||this.createF)(e)}slot(e){let t=e[this.id]>>1;return{create:i=>(i.values[t]=this.create(i),1),update:(i,s)=>{let r=i.values[t],o=this.updateF(r,s);return this.compareF(r,o)?0:(i.values[t]=o,1)},reconfigure:(i,s)=>s.config.address[this.id]!=null?(i.values[t]=s.field(this),0):(i.values[t]=this.create(i),1)}}init(e){return[this,_r.of({field:this,create:e})]}get extension(){return this}}const mt={lowest:4,low:3,default:2,high:1,highest:0};function Xt(n){return e=>new jl(e,n)}const Ai={highest:Xt(mt.highest),high:Xt(mt.high),default:Xt(mt.default),low:Xt(mt.low),lowest:Xt(mt.lowest)};class jl{constructor(e,t){this.inner=e,this.prec=t}}class kn{of(e){return new gs(this,e)}reconfigure(e){return kn.reconfigure.of({compartment:this,extension:e})}get(e){return e.config.compartments.get(this)}}class gs{constructor(e,t){this.compartment=e,this.inner=t}}class sn{constructor(e,t,i,s,r,o){for(this.base=e,this.compartments=t,this.dynamicSlots=i,this.address=s,this.staticValues=r,this.facets=o,this.statusTemplate=[];this.statusTemplate.length<i.length;)this.statusTemplate.push(0)}staticFacet(e){let t=this.address[e.id];return t==null?e.default:this.staticValues[t>>1]}static resolve(e,t,i){let s=[],r=Object.create(null),o=new Map;for(let u of Bc(e,t,o))u instanceof be?s.push(u):(r[u.facet.id]||(r[u.facet.id]=[])).push(u);let l=Object.create(null),a=[],h=[];for(let u of s)l[u.id]=h.length<<1,h.push(d=>u.slot(d));let c=i?.config.facets;for(let u in r){let d=r[u],p=d[0].facet,y=c&&c[u]||[];if(d.every(g=>g.type==0))if(l[p.id]=a.length<<1|1,rr(y,d))a.push(i.facet(p));else{let g=p.combine(d.map(b=>b.value));a.push(i&&p.compare(g,i.facet(p))?i.facet(p):g)}else{for(let g of d)g.type==0?(l[g.id]=a.length<<1|1,a.push(g.value)):(l[g.id]=h.length<<1,h.push(b=>g.dynamicSlot(b)));l[p.id]=h.length<<1,h.push(g=>Oc(g,p,d))}}let f=h.map(u=>u(l));return new sn(e,o,f,l,a,r)}}function Bc(n,e,t){let i=[[],[],[],[],[]],s=new Map;function r(o,l){let a=s.get(o);if(a!=null){if(a<=l)return;let h=i[a].indexOf(o);h>-1&&i[a].splice(h,1),o instanceof gs&&t.delete(o.compartment)}if(s.set(o,l),Array.isArray(o))for(let h of o)r(h,l);else if(o instanceof gs){if(t.has(o.compartment))throw new RangeError("Duplicate use of compartment in extensions");let h=e.get(o.compartment)||o.inner;t.set(o.compartment,h),r(h,l)}else if(o instanceof jl)r(o.inner,o.prec);else if(o instanceof be)i[l].push(o),o.provides&&r(o.provides,l);else if(o instanceof Zi)i[l].push(o),o.facet.extensions&&r(o.facet.extensions,mt.default);else{let h=o.extension;if(!h)throw new Error(`Unrecognized extension value in extension set (${o}). This sometimes happens because multiple instances of @codemirror/state are loaded, breaking instanceof checks.`);r(h,l)}}return r(n,mt.default),i.reduce((o,l)=>o.concat(l))}function ci(n,e){if(e&1)return 2;let t=e>>1,i=n.status[t];if(i==4)throw new Error("Cyclic dependency between fields and/or facets");if(i&2)return i;n.status[t]=4;let s=n.computeSlot(n,n.config.dynamicSlots[t]);return n.status[t]=2|s}function rn(n,e){return e&1?n.config.staticValues[e>>1]:n.values[e>>1]}const Kl=D.define(),Ul=D.define({combine:n=>n.some(e=>e),static:!0}),Gl=D.define({combine:n=>n.length?n[0]:void 0,static:!0}),Jl=D.define(),$l=D.define(),Yl=D.define(),Xl=D.define({combine:n=>n.length?n[0]:!1});class Dt{constructor(e,t){this.type=e,this.value=t}static define(){return new Pc}}class Pc{of(e){return new Dt(this,e)}}class Ec{constructor(e){this.map=e}of(e){return new R(this,e)}}class R{constructor(e,t){this.type=e,this.value=t}map(e){let t=this.type.map(this.value,e);return t===void 0?void 0:t==this.value?this:new R(this.type,t)}is(e){return this.type==e}static define(e={}){return new Ec(e.map||(t=>t))}static mapEffects(e,t){if(!e.length)return e;let i=[];for(let s of e){let r=s.map(t);r&&i.push(r)}return i}}R.reconfigure=R.define();R.appendConfig=R.define();class ie{constructor(e,t,i,s,r,o){this.startState=e,this.changes=t,this.selection=i,this.effects=s,this.annotations=r,this.scrollIntoView=o,this._doc=null,this._state=null,i&&ql(i,t.newLength),r.some(l=>l.type==ie.time)||(this.annotations=r.concat(ie.time.of(Date.now())))}static create(e,t,i,s,r,o){return new ie(e,t,i,s,r,o)}get newDoc(){return this._doc||(this._doc=this.changes.apply(this.startState.doc))}get newSelection(){return this.selection||this.startState.selection.map(this.changes)}get state(){return this._state||this.startState.applyTransaction(this),this._state}annotation(e){for(let t of this.annotations)if(t.type==e)return t.value}get docChanged(){return!this.changes.empty}get reconfigured(){return this.startState.config!=this.state.config}isUserEvent(e){let t=this.annotation(ie.userEvent);return!!(t&&(t==e||t.length>e.length&&t.slice(0,e.length)==e&&t[e.length]=="."))}}ie.time=Dt.define();ie.userEvent=Dt.define();ie.addToHistory=Dt.define();ie.remote=Dt.define();function Rc(n,e){let t=[];for(let i=0,s=0;;){let r,o;if(i<n.length&&(s==e.length||e[s]>=n[i]))r=n[i++],o=n[i++];else if(s<e.length)r=e[s++],o=e[s++];else return t;!t.length||t[t.length-1]<r?t.push(r,o):t[t.length-1]<o&&(t[t.length-1]=o)}}function Zl(n,e,t){var i;let s,r,o;return t?(s=e.changes,r=te.empty(e.changes.length),o=n.changes.compose(e.changes)):(s=e.changes.map(n.changes),r=n.changes.mapDesc(e.changes,!0),o=n.changes.compose(s)),{changes:o,selection:e.selection?e.selection.map(r):(i=n.selection)===null||i===void 0?void 0:i.map(s),effects:R.mapEffects(n.effects,s).concat(R.mapEffects(e.effects,r)),annotations:n.annotations.length?n.annotations.concat(e.annotations):e.annotations,scrollIntoView:n.scrollIntoView||e.scrollIntoView}}function ys(n,e,t){let i=e.selection,s=Ft(e.annotations);return e.userEvent&&(s=s.concat(ie.userEvent.of(e.userEvent))),{changes:e.changes instanceof te?e.changes:te.of(e.changes||[],t,n.facet(Gl)),selection:i&&(i instanceof w?i:w.single(i.anchor,i.head)),effects:Ft(e.effects),annotations:s,scrollIntoView:!!e.scrollIntoView}}function Ql(n,e,t){let i=ys(n,e.length?e[0]:{},n.doc.length);e.length&&e[0].filter===!1&&(t=!1);for(let r=1;r<e.length;r++){e[r].filter===!1&&(t=!1);let o=!!e[r].sequential;i=Zl(i,ys(n,e[r],o?i.changes.newLength:n.doc.length),o)}let s=ie.create(n,i.changes,i.selection,i.effects,i.annotations,i.scrollIntoView);return Ic(t?Lc(s):s)}function Lc(n){let e=n.startState,t=!0;for(let s of e.facet(Jl)){let r=s(n);if(r===!1){t=!1;break}Array.isArray(r)&&(t=t===!0?r:Rc(t,r))}if(t!==!0){let s,r;if(t===!1)r=n.changes.invertedDesc,s=te.empty(e.doc.length);else{let o=n.changes.filter(t);s=o.changes,r=o.filtered.mapDesc(o.changes).invertedDesc}n=ie.create(e,s,n.selection&&n.selection.map(r),R.mapEffects(n.effects,r),n.annotations,n.scrollIntoView)}let i=e.facet($l);for(let s=i.length-1;s>=0;s--){let r=i[s](n);r instanceof ie?n=r:Array.isArray(r)&&r.length==1&&r[0]instanceof ie?n=r[0]:n=Ql(e,Ft(r),!1)}return n}function Ic(n){let e=n.startState,t=e.facet(Yl),i=n;for(let s=t.length-1;s>=0;s--){let r=t[s](n);r&&Object.keys(r).length&&(i=Zl(i,ys(e,r,n.changes.newLength),!0))}return i==n?n:ie.create(e,n.changes,n.selection,i.effects,i.annotations,i.scrollIntoView)}const Nc=[];function Ft(n){return n==null?Nc:Array.isArray(n)?n:[n]}var Ae=function(n){return n[n.Word=0]="Word",n[n.Space=1]="Space",n[n.Other=2]="Other",n}(Ae||(Ae={}));const Vc=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;let bs;try{bs=new RegExp("[\\p{Alphabetic}\\p{Number}_]","u")}catch{}function Fc(n){if(bs)return bs.test(n);for(let e=0;e<n.length;e++){let t=n[e];if(/\w/.test(t)||t>""&&(t.toUpperCase()!=t.toLowerCase()||Vc.test(t)))return!0}return!1}function Hc(n){return e=>{if(!/\S/.test(e))return Ae.Space;if(Fc(e))return Ae.Word;for(let t=0;t<n.length;t++)if(e.indexOf(n[t])>-1)return Ae.Word;return Ae.Other}}class N{constructor(e,t,i,s,r,o){this.config=e,this.doc=t,this.selection=i,this.values=s,this.status=e.statusTemplate.slice(),this.computeSlot=r,o&&(o._state=this);for(let l=0;l<this.config.dynamicSlots.length;l++)ci(this,l<<1);this.computeSlot=null}field(e,t=!0){let i=this.config.address[e.id];if(i==null){if(t)throw new RangeError("Field is not present in this state");return}return ci(this,i),rn(this,i)}update(...e){return Ql(this,e,!0)}applyTransaction(e){let t=this.config,{base:i,compartments:s}=t;for(let o of e.effects)o.is(kn.reconfigure)?(t&&(s=new Map,t.compartments.forEach((l,a)=>s.set(a,l)),t=null),s.set(o.value.compartment,o.value.extension)):o.is(R.reconfigure)?(t=null,i=o.value):o.is(R.appendConfig)&&(t=null,i=Ft(i).concat(o.value));let r;t?r=e.startState.values.slice():(t=sn.resolve(i,s,this),r=new N(t,this.doc,this.selection,t.dynamicSlots.map(()=>null),(l,a)=>a.reconfigure(l,this),null).values),new N(t,e.newDoc,e.newSelection,r,(o,l)=>l.update(o,e),e)}replaceSelection(e){return typeof e=="string"&&(e=this.toText(e)),this.changeByRange(t=>({changes:{from:t.from,to:t.to,insert:e},range:w.cursor(t.from+e.length)}))}changeByRange(e){let t=this.selection,i=e(t.ranges[0]),s=this.changes(i.changes),r=[i.range],o=Ft(i.effects);for(let l=1;l<t.ranges.length;l++){let a=e(t.ranges[l]),h=this.changes(a.changes),c=h.map(s);for(let u=0;u<l;u++)r[u]=r[u].map(c);let f=s.mapDesc(h,!0);r.push(a.range.map(f)),s=s.compose(c),o=R.mapEffects(o,c).concat(R.mapEffects(Ft(a.effects),f))}return{changes:s,selection:w.create(r,t.mainIndex),effects:o}}changes(e=[]){return e instanceof te?e:te.of(e,this.doc.length,this.facet(N.lineSeparator))}toText(e){return V.of(e.split(this.facet(N.lineSeparator)||us))}sliceDoc(e=0,t=this.doc.length){return this.doc.sliceString(e,t,this.lineBreak)}facet(e){let t=this.config.address[e.id];return t==null?e.default:(ci(this,t),rn(this,t))}toJSON(e){let t={doc:this.sliceDoc(),selection:this.selection.toJSON()};if(e)for(let i in e){let s=e[i];s instanceof be&&this.config.address[s.id]!=null&&(t[i]=s.spec.toJSON(this.field(e[i]),this))}return t}static fromJSON(e,t={},i){if(!e||typeof e.doc!="string")throw new RangeError("Invalid JSON representation for EditorState");let s=[];if(i){for(let r in i)if(Object.prototype.hasOwnProperty.call(e,r)){let o=i[r],l=e[r];s.push(o.init(a=>o.spec.fromJSON(l,a)))}}return N.create({doc:e.doc,selection:w.fromJSON(e.selection),extensions:t.extensions?s.concat([t.extensions]):s})}static create(e={}){let t=sn.resolve(e.extensions||[],new Map),i=e.doc instanceof V?e.doc:V.of((e.doc||"").split(t.staticFacet(N.lineSeparator)||us)),s=e.selection?e.selection instanceof w?e.selection:w.single(e.selection.anchor,e.selection.head):w.single(0);return ql(s,i.length),t.staticFacet(Ul)||(s=s.asSingle()),new N(t,i,s,t.dynamicSlots.map(()=>null),(r,o)=>o.create(r),null)}get tabSize(){return this.facet(N.tabSize)}get lineBreak(){return this.facet(N.lineSeparator)||`
`}get readOnly(){return this.facet(Xl)}phrase(e,...t){for(let i of this.facet(N.phrases))if(Object.prototype.hasOwnProperty.call(i,e)){e=i[e];break}return t.length&&(e=e.replace(/\$(\$|\d*)/g,(i,s)=>{if(s=="$")return"$";let r=+(s||1);return!r||r>t.length?i:t[r-1]})),e}languageDataAt(e,t,i=-1){let s=[];for(let r of this.facet(Kl))for(let o of r(this,t,i))Object.prototype.hasOwnProperty.call(o,e)&&s.push(o[e]);return s}charCategorizer(e){return Hc(this.languageDataAt("wordChars",e).join(""))}wordAt(e){let{text:t,from:i,length:s}=this.doc.lineAt(e),r=this.charCategorizer(e),o=e-i,l=e-i;for(;o>0;){let a=ke(t,o,!1);if(r(t.slice(a,o))!=Ae.Word)break;o=a}for(;l<s;){let a=ke(t,l);if(r(t.slice(l,a))!=Ae.Word)break;l=a}return o==l?null:w.range(o+i,l+i)}}N.allowMultipleSelections=Ul;N.tabSize=D.define({combine:n=>n.length?n[0]:4});N.lineSeparator=Gl;N.readOnly=Xl;N.phrases=D.define({compare(n,e){let t=Object.keys(n),i=Object.keys(e);return t.length==i.length&&t.every(s=>n[s]==e[s])}});N.languageData=Kl;N.changeFilter=Jl;N.transactionFilter=$l;N.transactionExtender=Yl;kn.reconfigure=R.define();function Tt(n,e,t={}){let i={};for(let s of n)for(let r of Object.keys(s)){let o=s[r],l=i[r];if(l===void 0)i[r]=o;else if(!(l===o||o===void 0))if(Object.hasOwnProperty.call(t,r))i[r]=t[r](l,o);else throw new Error("Config merge conflict for field "+r)}for(let s in e)i[s]===void 0&&(i[s]=e[s]);return i}class kt{eq(e){return this==e}range(e,t=e){return ws.create(e,t,this)}}kt.prototype.startSide=kt.prototype.endSide=0;kt.prototype.point=!1;kt.prototype.mapMode=le.TrackDel;let ws=class ea{constructor(e,t,i){this.from=e,this.to=t,this.value=i}static create(e,t,i){return new ea(e,t,i)}};function xs(n,e){return n.from-e.from||n.value.startSide-e.value.startSide}class or{constructor(e,t,i,s){this.from=e,this.to=t,this.value=i,this.maxPoint=s}get length(){return this.to[this.to.length-1]}findIndex(e,t,i,s=0){let r=i?this.to:this.from;for(let o=s,l=r.length;;){if(o==l)return o;let a=o+l>>1,h=r[a]-e||(i?this.value[a].endSide:this.value[a].startSide)-t;if(a==o)return h>=0?o:l;h>=0?l=a:o=a+1}}between(e,t,i,s){for(let r=this.findIndex(t,-1e9,!0),o=this.findIndex(i,1e9,!1,r);r<o;r++)if(s(this.from[r]+e,this.to[r]+e,this.value[r])===!1)return!1}map(e,t){let i=[],s=[],r=[],o=-1,l=-1;for(let a=0;a<this.value.length;a++){let h=this.value[a],c=this.from[a]+e,f=this.to[a]+e,u,d;if(c==f){let p=t.mapPos(c,h.startSide,h.mapMode);if(p==null||(u=d=p,h.startSide!=h.endSide&&(d=t.mapPos(c,h.endSide),d<u)))continue}else if(u=t.mapPos(c,h.startSide),d=t.mapPos(f,h.endSide),u>d||u==d&&h.startSide>0&&h.endSide<=0)continue;(d-u||h.endSide-h.startSide)<0||(o<0&&(o=u),h.point&&(l=Math.max(l,d-u)),i.push(h),s.push(u-o),r.push(d-o))}return{mapped:i.length?new or(s,r,i,l):null,pos:o}}}class H{constructor(e,t,i,s){this.chunkPos=e,this.chunk=t,this.nextLayer=i,this.maxPoint=s}static create(e,t,i,s){return new H(e,t,i,s)}get length(){let e=this.chunk.length-1;return e<0?0:Math.max(this.chunkEnd(e),this.nextLayer.length)}get size(){if(this.isEmpty)return 0;let e=this.nextLayer.size;for(let t of this.chunk)e+=t.value.length;return e}chunkEnd(e){return this.chunkPos[e]+this.chunk[e].length}update(e){let{add:t=[],sort:i=!1,filterFrom:s=0,filterTo:r=this.length}=e,o=e.filter;if(t.length==0&&!o)return this;if(i&&(t=t.slice().sort(xs)),this.isEmpty)return t.length?H.of(t):this;let l=new ta(this,null,-1).goto(0),a=0,h=[],c=new vt;for(;l.value||a<t.length;)if(a<t.length&&(l.from-t[a].from||l.startSide-t[a].value.startSide)>=0){let f=t[a++];c.addInner(f.from,f.to,f.value)||h.push(f)}else l.rangeIndex==1&&l.chunkIndex<this.chunk.length&&(a==t.length||this.chunkEnd(l.chunkIndex)<t[a].from)&&(!o||s>this.chunkEnd(l.chunkIndex)||r<this.chunkPos[l.chunkIndex])&&c.addChunk(this.chunkPos[l.chunkIndex],this.chunk[l.chunkIndex])?l.nextChunk():((!o||s>l.to||r<l.from||o(l.from,l.to,l.value))&&(c.addInner(l.from,l.to,l.value)||h.push(ws.create(l.from,l.to,l.value))),l.next());return c.finishInner(this.nextLayer.isEmpty&&!h.length?H.empty:this.nextLayer.update({add:h,filter:o,filterFrom:s,filterTo:r}))}map(e){if(e.empty||this.isEmpty)return this;let t=[],i=[],s=-1;for(let o=0;o<this.chunk.length;o++){let l=this.chunkPos[o],a=this.chunk[o],h=e.touchesRange(l,l+a.length);if(h===!1)s=Math.max(s,a.maxPoint),t.push(a),i.push(e.mapPos(l));else if(h===!0){let{mapped:c,pos:f}=a.map(l,e);c&&(s=Math.max(s,c.maxPoint),t.push(c),i.push(f))}}let r=this.nextLayer.map(e);return t.length==0?r:new H(i,t,r||H.empty,s)}between(e,t,i){if(!this.isEmpty){for(let s=0;s<this.chunk.length;s++){let r=this.chunkPos[s],o=this.chunk[s];if(t>=r&&e<=r+o.length&&o.between(r,e-r,t-r,i)===!1)return}this.nextLayer.between(e,t,i)}}iter(e=0){return mi.from([this]).goto(e)}get isEmpty(){return this.nextLayer==this}static iter(e,t=0){return mi.from(e).goto(t)}static compare(e,t,i,s,r=-1){let o=e.filter(f=>f.maxPoint>0||!f.isEmpty&&f.maxPoint>=r),l=t.filter(f=>f.maxPoint>0||!f.isEmpty&&f.maxPoint>=r),a=qr(o,l,i),h=new Zt(o,a,r),c=new Zt(l,a,r);i.iterGaps((f,u,d)=>jr(h,f,c,u,d,s)),i.empty&&i.length==0&&jr(h,0,c,0,0,s)}static eq(e,t,i=0,s){s==null&&(s=1e9);let r=e.filter(c=>!c.isEmpty&&t.indexOf(c)<0),o=t.filter(c=>!c.isEmpty&&e.indexOf(c)<0);if(r.length!=o.length)return!1;if(!r.length)return!0;let l=qr(r,o),a=new Zt(r,l,0).goto(i),h=new Zt(o,l,0).goto(i);for(;;){if(a.to!=h.to||!ks(a.active,h.active)||a.point&&(!h.point||!a.point.eq(h.point)))return!1;if(a.to>s)return!0;a.next(),h.next()}}static spans(e,t,i,s,r=-1){let o=new Zt(e,null,r).goto(t),l=t,a=o.openStart;for(;;){let h=Math.min(o.to,i);if(o.point?(s.point(l,h,o.point,o.activeForPoint(o.to),a,o.pointRank),a=o.openEnd(h)+(o.to>h?1:0)):h>l&&(s.span(l,h,o.active,a),a=o.openEnd(h)),o.to>i)break;l=o.to,o.next()}return a}static of(e,t=!1){let i=new vt;for(let s of e instanceof ws?[e]:t?Wc(e):e)i.add(s.from,s.to,s.value);return i.finish()}}H.empty=new H([],[],null,-1);function Wc(n){if(n.length>1)for(let e=n[0],t=1;t<n.length;t++){let i=n[t];if(xs(e,i)>0)return n.slice().sort(xs);e=i}return n}H.empty.nextLayer=H.empty;class vt{constructor(){this.chunks=[],this.chunkPos=[],this.chunkStart=-1,this.last=null,this.lastFrom=-1e9,this.lastTo=-1e9,this.from=[],this.to=[],this.value=[],this.maxPoint=-1,this.setMaxPoint=-1,this.nextLayer=null}finishChunk(e){this.chunks.push(new or(this.from,this.to,this.value,this.maxPoint)),this.chunkPos.push(this.chunkStart),this.chunkStart=-1,this.setMaxPoint=Math.max(this.setMaxPoint,this.maxPoint),this.maxPoint=-1,e&&(this.from=[],this.to=[],this.value=[])}add(e,t,i){this.addInner(e,t,i)||(this.nextLayer||(this.nextLayer=new vt)).add(e,t,i)}addInner(e,t,i){let s=e-this.lastTo||i.startSide-this.last.endSide;if(s<=0&&(e-this.lastFrom||i.startSide-this.last.startSide)<0)throw new Error("Ranges must be added sorted by `from` position and `startSide`");return s<0?!1:(this.from.length==250&&this.finishChunk(!0),this.chunkStart<0&&(this.chunkStart=e),this.from.push(e-this.chunkStart),this.to.push(t-this.chunkStart),this.last=i,this.lastFrom=e,this.lastTo=t,this.value.push(i),i.point&&(this.maxPoint=Math.max(this.maxPoint,t-e)),!0)}addChunk(e,t){if((e-this.lastTo||t.value[0].startSide-this.last.endSide)<0)return!1;this.from.length&&this.finishChunk(!0),this.setMaxPoint=Math.max(this.setMaxPoint,t.maxPoint),this.chunks.push(t),this.chunkPos.push(e);let i=t.value.length-1;return this.last=t.value[i],this.lastFrom=t.from[i]+e,this.lastTo=t.to[i]+e,!0}finish(){return this.finishInner(H.empty)}finishInner(e){if(this.from.length&&this.finishChunk(!1),this.chunks.length==0)return e;let t=H.create(this.chunkPos,this.chunks,this.nextLayer?this.nextLayer.finishInner(e):e,this.setMaxPoint);return this.from=null,t}}function qr(n,e,t){let i=new Map;for(let r of n)for(let o=0;o<r.chunk.length;o++)r.chunk[o].maxPoint<=0&&i.set(r.chunk[o],r.chunkPos[o]);let s=new Set;for(let r of e)for(let o=0;o<r.chunk.length;o++){let l=i.get(r.chunk[o]);l!=null&&(t?t.mapPos(l):l)==r.chunkPos[o]&&!t?.touchesRange(l,l+r.chunk[o].length)&&s.add(r.chunk[o])}return s}class ta{constructor(e,t,i,s=0){this.layer=e,this.skip=t,this.minPoint=i,this.rank=s}get startSide(){return this.value?this.value.startSide:0}get endSide(){return this.value?this.value.endSide:0}goto(e,t=-1e9){return this.chunkIndex=this.rangeIndex=0,this.gotoInner(e,t,!1),this}gotoInner(e,t,i){for(;this.chunkIndex<this.layer.chunk.length;){let s=this.layer.chunk[this.chunkIndex];if(!(this.skip&&this.skip.has(s)||this.layer.chunkEnd(this.chunkIndex)<e||s.maxPoint<this.minPoint))break;this.chunkIndex++,i=!1}if(this.chunkIndex<this.layer.chunk.length){let s=this.layer.chunk[this.chunkIndex].findIndex(e-this.layer.chunkPos[this.chunkIndex],t,!0);(!i||this.rangeIndex<s)&&this.setRangeIndex(s)}this.next()}forward(e,t){(this.to-e||this.endSide-t)<0&&this.gotoInner(e,t,!0)}next(){for(;;)if(this.chunkIndex==this.layer.chunk.length){this.from=this.to=1e9,this.value=null;break}else{let e=this.layer.chunkPos[this.chunkIndex],t=this.layer.chunk[this.chunkIndex],i=e+t.from[this.rangeIndex];if(this.from=i,this.to=e+t.to[this.rangeIndex],this.value=t.value[this.rangeIndex],this.setRangeIndex(this.rangeIndex+1),this.minPoint<0||this.value.point&&this.to-this.from>=this.minPoint)break}}setRangeIndex(e){if(e==this.layer.chunk[this.chunkIndex].value.length){if(this.chunkIndex++,this.skip)for(;this.chunkIndex<this.layer.chunk.length&&this.skip.has(this.layer.chunk[this.chunkIndex]);)this.chunkIndex++;this.rangeIndex=0}else this.rangeIndex=e}nextChunk(){this.chunkIndex++,this.rangeIndex=0,this.next()}compare(e){return this.from-e.from||this.startSide-e.startSide||this.rank-e.rank||this.to-e.to||this.endSide-e.endSide}}class mi{constructor(e){this.heap=e}static from(e,t=null,i=-1){let s=[];for(let r=0;r<e.length;r++)for(let o=e[r];!o.isEmpty;o=o.nextLayer)o.maxPoint>=i&&s.push(new ta(o,t,i,r));return s.length==1?s[0]:new mi(s)}get startSide(){return this.value?this.value.startSide:0}goto(e,t=-1e9){for(let i of this.heap)i.goto(e,t);for(let i=this.heap.length>>1;i>=0;i--)Nn(this.heap,i);return this.next(),this}forward(e,t){for(let i of this.heap)i.forward(e,t);for(let i=this.heap.length>>1;i>=0;i--)Nn(this.heap,i);(this.to-e||this.value.endSide-t)<0&&this.next()}next(){if(this.heap.length==0)this.from=this.to=1e9,this.value=null,this.rank=-1;else{let e=this.heap[0];this.from=e.from,this.to=e.to,this.value=e.value,this.rank=e.rank,e.value&&e.next(),Nn(this.heap,0)}}}function Nn(n,e){for(let t=n[e];;){let i=(e<<1)+1;if(i>=n.length)break;let s=n[i];if(i+1<n.length&&s.compare(n[i+1])>=0&&(s=n[i+1],i++),t.compare(s)<0)break;n[i]=t,n[e]=s,e=i}}class Zt{constructor(e,t,i){this.minPoint=i,this.active=[],this.activeTo=[],this.activeRank=[],this.minActive=-1,this.point=null,this.pointFrom=0,this.pointRank=0,this.to=-1e9,this.endSide=0,this.openStart=-1,this.cursor=mi.from(e,t,i)}goto(e,t=-1e9){return this.cursor.goto(e,t),this.active.length=this.activeTo.length=this.activeRank.length=0,this.minActive=-1,this.to=e,this.endSide=t,this.openStart=-1,this.next(),this}forward(e,t){for(;this.minActive>-1&&(this.activeTo[this.minActive]-e||this.active[this.minActive].endSide-t)<0;)this.removeActive(this.minActive);this.cursor.forward(e,t)}removeActive(e){Ei(this.active,e),Ei(this.activeTo,e),Ei(this.activeRank,e),this.minActive=Kr(this.active,this.activeTo)}addActive(e){let t=0,{value:i,to:s,rank:r}=this.cursor;for(;t<this.activeRank.length&&this.activeRank[t]<=r;)t++;Ri(this.active,t,i),Ri(this.activeTo,t,s),Ri(this.activeRank,t,r),e&&Ri(e,t,this.cursor.from),this.minActive=Kr(this.active,this.activeTo)}next(){let e=this.to,t=this.point;this.point=null;let i=this.openStart<0?[]:null,s=0;for(;;){let r=this.minActive;if(r>-1&&(this.activeTo[r]-this.cursor.from||this.active[r].endSide-this.cursor.startSide)<0){if(this.activeTo[r]>e){this.to=this.activeTo[r],this.endSide=this.active[r].endSide;break}this.removeActive(r),i&&Ei(i,r)}else if(this.cursor.value)if(this.cursor.from>e){this.to=this.cursor.from,this.endSide=this.cursor.startSide;break}else{let o=this.cursor.value;if(!o.point)this.addActive(i),this.cursor.from<e&&this.cursor.to>e&&s++,this.cursor.next();else if(t&&this.cursor.to==this.to&&this.cursor.from<this.cursor.to)this.cursor.next();else{this.point=o,this.pointFrom=this.cursor.from,this.pointRank=this.cursor.rank,this.to=this.cursor.to,this.endSide=o.endSide,this.cursor.from<e&&(s=1),this.cursor.next(),this.forward(this.to,this.endSide);break}}else{this.to=this.endSide=1e9;break}}if(i){let r=0;for(;r<i.length&&i[r]<e;)r++;this.openStart=r+s}}activeForPoint(e){if(!this.active.length)return this.active;let t=[];for(let i=this.active.length-1;i>=0&&!(this.activeRank[i]<this.pointRank);i--)(this.activeTo[i]>e||this.activeTo[i]==e&&this.active[i].endSide>=this.point.endSide)&&t.push(this.active[i]);return t.reverse()}openEnd(e){let t=0;for(let i=this.activeTo.length-1;i>=0&&this.activeTo[i]>e;i--)t++;return t}}function jr(n,e,t,i,s,r){n.goto(e),t.goto(i);let o=i+s,l=i,a=i-e;for(;;){let h=n.to+a-t.to||n.endSide-t.endSide,c=h<0?n.to+a:t.to,f=Math.min(c,o);if(n.point||t.point?n.point&&t.point&&(n.point==t.point||n.point.eq(t.point))&&ks(n.activeForPoint(n.to+a),t.activeForPoint(t.to))||r.comparePoint(l,f,n.point,t.point):f>l&&!ks(n.active,t.active)&&r.compareRange(l,f,n.active,t.active),c>o)break;l=c,h<=0&&n.next(),h>=0&&t.next()}}function ks(n,e){if(n.length!=e.length)return!1;for(let t=0;t<n.length;t++)if(n[t]!=e[t]&&!n[t].eq(e[t]))return!1;return!0}function Ei(n,e){for(let t=e,i=n.length-1;t<i;t++)n[t]=n[t+1];n.pop()}function Ri(n,e,t){for(let i=n.length-1;i>=e;i--)n[i+1]=n[i];n[e]=t}function Kr(n,e){let t=-1,i=1e9;for(let s=0;s<e.length;s++)(e[s]-i||n[s].endSide-n[t].endSide)<0&&(t=s,i=e[s]);return t}function Mi(n,e,t=n.length){let i=0;for(let s=0;s<t;)n.charCodeAt(s)==9?(i+=e-i%e,s++):(i++,s=ke(n,s));return i}function vs(n,e,t,i){for(let s=0,r=0;;){if(r>=e)return s;if(s==n.length)break;r+=n.charCodeAt(s)==9?t-r%t:1,s=ke(n,s)}return i===!0?-1:n.length}const Ss="ͼ",Ur=typeof Symbol>"u"?"__"+Ss:Symbol.for(Ss),Cs=typeof Symbol>"u"?"__styleSet"+Math.floor(Math.random()*1e8):Symbol("styleSet"),Gr=typeof globalThis<"u"?globalThis:typeof window<"u"?window:{};class ot{constructor(e,t){this.rules=[];let{finish:i}=t||{};function s(o){return/^@/.test(o)?[o]:o.split(/,\s*/)}function r(o,l,a,h){let c=[],f=/^@(\w+)\b/.exec(o[0]),u=f&&f[1]=="keyframes";if(f&&l==null)return a.push(o[0]+";");for(let d in l){let p=l[d];if(/&/.test(d))r(d.split(/,\s*/).map(y=>o.map(g=>y.replace(/&/,g))).reduce((y,g)=>y.concat(g)),p,a);else if(p&&typeof p=="object"){if(!f)throw new RangeError("The value of a property ("+d+") should be a primitive value.");r(s(d),p,c,u)}else p!=null&&c.push(d.replace(/_.*/,"").replace(/[A-Z]/g,y=>"-"+y.toLowerCase())+": "+p+";")}(c.length||u)&&a.push((i&&!f&&!h?o.map(i):o).join(", ")+" {"+c.join(" ")+"}")}for(let o in e)r(s(o),e[o],this.rules)}getRules(){return this.rules.join(`
`)}static newName(){let e=Gr[Ur]||1;return Gr[Ur]=e+1,Ss+e.toString(36)}static mount(e,t){(e[Cs]||new zc(e)).mount(Array.isArray(t)?t:[t])}}let Li=null;class zc{constructor(e){if(!e.head&&e.adoptedStyleSheets&&typeof CSSStyleSheet<"u"){if(Li)return e.adoptedStyleSheets=[Li.sheet].concat(e.adoptedStyleSheets),e[Cs]=Li;this.sheet=new CSSStyleSheet,e.adoptedStyleSheets=[this.sheet].concat(e.adoptedStyleSheets),Li=this}else{this.styleTag=(e.ownerDocument||e).createElement("style");let t=e.head||e;t.insertBefore(this.styleTag,t.firstChild)}this.modules=[],e[Cs]=this}mount(e){let t=this.sheet,i=0,s=0;for(let r=0;r<e.length;r++){let o=e[r],l=this.modules.indexOf(o);if(l<s&&l>-1&&(this.modules.splice(l,1),s--,l=-1),l==-1){if(this.modules.splice(s++,0,o),t)for(let a=0;a<o.rules.length;a++)t.insertRule(o.rules[a],i++)}else{for(;s<l;)i+=this.modules[s++].rules.length;i+=o.rules.length,s++}}if(!t){let r="";for(let o=0;o<this.modules.length;o++)r+=this.modules[o].getRules()+`
`;this.styleTag.textContent=r}}}var lt={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},gi={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},Jr=typeof navigator<"u"&&/Chrome\/(\d+)/.exec(navigator.userAgent),_c=typeof navigator<"u"&&/Mac/.test(navigator.platform),qc=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),jc=_c||Jr&&+Jr[1]<57;for(var oe=0;oe<10;oe++)lt[48+oe]=lt[96+oe]=String(oe);for(var oe=1;oe<=24;oe++)lt[oe+111]="F"+oe;for(var oe=65;oe<=90;oe++)lt[oe]=String.fromCharCode(oe+32),gi[oe]=String.fromCharCode(oe);for(var Vn in lt)gi.hasOwnProperty(Vn)||(gi[Vn]=lt[Vn]);function Kc(n){var e=jc&&(n.ctrlKey||n.altKey||n.metaKey)||qc&&n.shiftKey&&n.key&&n.key.length==1||n.key=="Unidentified",t=!e&&n.key||(n.shiftKey?gi:lt)[n.keyCode]||n.key||"Unidentified";return t=="Esc"&&(t="Escape"),t=="Del"&&(t="Delete"),t=="Left"&&(t="ArrowLeft"),t=="Up"&&(t="ArrowUp"),t=="Right"&&(t="ArrowRight"),t=="Down"&&(t="ArrowDown"),t}function on(n){let e;return n.nodeType==11?e=n.getSelection?n:n.ownerDocument:e=n,e.getSelection()}function zt(n,e){return e?n==e||n.contains(e.nodeType!=1?e.parentNode:e):!1}function Uc(n){let e=n.activeElement;for(;e&&e.shadowRoot;)e=e.shadowRoot.activeElement;return e}function Qi(n,e){if(!e.anchorNode)return!1;try{return zt(n,e.anchorNode)}catch{return!1}}function yi(n){return n.nodeType==3?_t(n,0,n.nodeValue.length).getClientRects():n.nodeType==1?n.getClientRects():[]}function ln(n,e,t,i){return t?$r(n,e,t,i,-1)||$r(n,e,t,i,1):!1}function an(n){for(var e=0;;e++)if(n=n.previousSibling,!n)return e}function $r(n,e,t,i,s){for(;;){if(n==t&&e==i)return!0;if(e==(s<0?0:bi(n))){if(n.nodeName=="DIV")return!1;let r=n.parentNode;if(!r||r.nodeType!=1)return!1;e=an(n)+(s<0?0:1),n=r}else if(n.nodeType==1){if(n=n.childNodes[e+(s<0?-1:0)],n.nodeType==1&&n.contentEditable=="false")return!1;e=s<0?bi(n):0}else return!1}}function bi(n){return n.nodeType==3?n.nodeValue.length:n.childNodes.length}const ia={left:0,right:0,top:0,bottom:0};function lr(n,e){let t=e?n.left:n.right;return{left:t,right:t,top:n.top,bottom:n.bottom}}function Gc(n){return{left:0,right:n.innerWidth,top:0,bottom:n.innerHeight}}function Jc(n,e,t,i,s,r,o,l){let a=n.ownerDocument,h=a.defaultView||window;for(let c=n;c;)if(c.nodeType==1){let f,u=c==a.body;if(u)f=Gc(h);else{if(c.scrollHeight<=c.clientHeight&&c.scrollWidth<=c.clientWidth){c=c.assignedSlot||c.parentNode;continue}let y=c.getBoundingClientRect();f={left:y.left,right:y.left+c.clientWidth,top:y.top,bottom:y.top+c.clientHeight}}let d=0,p=0;if(s=="nearest")e.top<f.top?(p=-(f.top-e.top+o),t>0&&e.bottom>f.bottom+p&&(p=e.bottom-f.bottom+p+o)):e.bottom>f.bottom&&(p=e.bottom-f.bottom+o,t<0&&e.top-p<f.top&&(p=-(f.top+p-e.top+o)));else{let y=e.bottom-e.top,g=f.bottom-f.top;p=(s=="center"&&y<=g?e.top+y/2-g/2:s=="start"||s=="center"&&t<0?e.top-o:e.bottom-g+o)-f.top}if(i=="nearest"?e.left<f.left?(d=-(f.left-e.left+r),t>0&&e.right>f.right+d&&(d=e.right-f.right+d+r)):e.right>f.right&&(d=e.right-f.right+r,t<0&&e.left<f.left+d&&(d=-(f.left+d-e.left+r))):d=(i=="center"?e.left+(e.right-e.left)/2-(f.right-f.left)/2:i=="start"==l?e.left-r:e.right-(f.right-f.left)+r)-f.left,d||p)if(u)h.scrollBy(d,p);else{let y=0,g=0;if(p){let b=c.scrollTop;c.scrollTop+=p,g=c.scrollTop-b}if(d){let b=c.scrollLeft;c.scrollLeft+=d,y=c.scrollLeft-b}e={left:e.left-y,top:e.top-g,right:e.right-y,bottom:e.bottom-g},y&&Math.abs(y-d)<1&&(i="nearest"),g&&Math.abs(g-p)<1&&(s="nearest")}if(u)break;c=c.assignedSlot||c.parentNode}else if(c.nodeType==11)c=c.host;else break}class $c{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}eq(e){return this.anchorNode==e.anchorNode&&this.anchorOffset==e.anchorOffset&&this.focusNode==e.focusNode&&this.focusOffset==e.focusOffset}setRange(e){this.set(e.anchorNode,e.anchorOffset,e.focusNode,e.focusOffset)}set(e,t,i,s){this.anchorNode=e,this.anchorOffset=t,this.focusNode=i,this.focusOffset=s}}let Pt=null;function na(n){if(n.setActive)return n.setActive();if(Pt)return n.focus(Pt);let e=[];for(let t=n;t&&(e.push(t,t.scrollTop,t.scrollLeft),t!=t.ownerDocument);t=t.parentNode);if(n.focus(Pt==null?{get preventScroll(){return Pt={preventScroll:!0},!0}}:void 0),!Pt){Pt=!1;for(let t=0;t<e.length;){let i=e[t++],s=e[t++],r=e[t++];i.scrollTop!=s&&(i.scrollTop=s),i.scrollLeft!=r&&(i.scrollLeft=r)}}}let Yr;function _t(n,e,t=e){let i=Yr||(Yr=document.createRange());return i.setEnd(n,t),i.setStart(n,e),i}function Ht(n,e,t){let i={key:e,code:e,keyCode:t,which:t,cancelable:!0},s=new KeyboardEvent("keydown",i);s.synthetic=!0,n.dispatchEvent(s);let r=new KeyboardEvent("keyup",i);return r.synthetic=!0,n.dispatchEvent(r),s.defaultPrevented||r.defaultPrevented}function Yc(n){for(;n;){if(n&&(n.nodeType==9||n.nodeType==11&&n.host))return n;n=n.assignedSlot||n.parentNode}return null}function sa(n){for(;n.attributes.length;)n.removeAttributeNode(n.attributes[0])}function Xc(n,e){let t=e.focusNode,i=e.focusOffset;if(!t||e.anchorNode!=t||e.anchorOffset!=i)return!1;for(;;)if(i){if(t.nodeType!=1)return!1;let s=t.childNodes[i-1];s.contentEditable=="false"?i--:(t=s,i=bi(t))}else{if(t==n)return!0;i=an(t),t=t.parentNode}}class fe{constructor(e,t,i=!0){this.node=e,this.offset=t,this.precise=i}static before(e,t){return new fe(e.parentNode,an(e),t)}static after(e,t){return new fe(e.parentNode,an(e)+1,t)}}const ar=[];class q{constructor(){this.parent=null,this.dom=null,this.dirty=2}get editorView(){if(!this.parent)throw new Error("Accessing view in orphan content view");return this.parent.editorView}get overrideDOMText(){return null}get posAtStart(){return this.parent?this.parent.posBefore(this):0}get posAtEnd(){return this.posAtStart+this.length}posBefore(e){let t=this.posAtStart;for(let i of this.children){if(i==e)return t;t+=i.length+i.breakAfter}throw new RangeError("Invalid child in posBefore")}posAfter(e){return this.posBefore(e)+e.length}coordsAt(e,t){return null}sync(e){if(this.dirty&2){let t=this.dom,i=null,s;for(let r of this.children){if(r.dirty){if(!r.dom&&(s=i?i.nextSibling:t.firstChild)){let o=q.get(s);(!o||!o.parent&&o.canReuseDOM(r))&&r.reuseDOM(s)}r.sync(e),r.dirty=0}if(s=i?i.nextSibling:t.firstChild,e&&!e.written&&e.node==t&&s!=r.dom&&(e.written=!0),r.dom.parentNode==t)for(;s&&s!=r.dom;)s=Xr(s);else t.insertBefore(r.dom,s);i=r.dom}for(s=i?i.nextSibling:t.firstChild,s&&e&&e.node==t&&(e.written=!0);s;)s=Xr(s)}else if(this.dirty&1)for(let t of this.children)t.dirty&&(t.sync(e),t.dirty=0)}reuseDOM(e){}localPosFromDOM(e,t){let i;if(e==this.dom)i=this.dom.childNodes[t];else{let s=bi(e)==0?0:t==0?-1:1;for(;;){let r=e.parentNode;if(r==this.dom)break;s==0&&r.firstChild!=r.lastChild&&(e==r.firstChild?s=-1:s=1),e=r}s<0?i=e:i=e.nextSibling}if(i==this.dom.firstChild)return 0;for(;i&&!q.get(i);)i=i.nextSibling;if(!i)return this.length;for(let s=0,r=0;;s++){let o=this.children[s];if(o.dom==i)return r;r+=o.length+o.breakAfter}}domBoundsAround(e,t,i=0){let s=-1,r=-1,o=-1,l=-1;for(let a=0,h=i,c=i;a<this.children.length;a++){let f=this.children[a],u=h+f.length;if(h<e&&u>t)return f.domBoundsAround(e,t,h);if(u>=e&&s==-1&&(s=a,r=h),h>t&&f.dom.parentNode==this.dom){o=a,l=c;break}c=u,h=u+f.breakAfter}return{from:r,to:l<0?i+this.length:l,startDOM:(s?this.children[s-1].dom.nextSibling:null)||this.dom.firstChild,endDOM:o<this.children.length&&o>=0?this.children[o].dom:null}}markDirty(e=!1){this.dirty|=2,this.markParentsDirty(e)}markParentsDirty(e){for(let t=this.parent;t;t=t.parent){if(e&&(t.dirty|=2),t.dirty&1)return;t.dirty|=1,e=!1}}setParent(e){this.parent!=e&&(this.parent=e,this.dirty&&this.markParentsDirty(!0))}setDOM(e){this.dom&&(this.dom.cmView=null),this.dom=e,e.cmView=this}get rootView(){for(let e=this;;){let t=e.parent;if(!t)return e;e=t}}replaceChildren(e,t,i=ar){this.markDirty();for(let s=e;s<t;s++){let r=this.children[s];r.parent==this&&r.destroy()}this.children.splice(e,t-e,...i);for(let s=0;s<i.length;s++)i[s].setParent(this)}ignoreMutation(e){return!1}ignoreEvent(e){return!1}childCursor(e=this.length){return new ra(this.children,e,this.children.length)}childPos(e,t=1){return this.childCursor().findPos(e,t)}toString(){let e=this.constructor.name.replace("View","");return e+(this.children.length?"("+this.children.join()+")":this.length?"["+(e=="Text"?this.text:this.length)+"]":"")+(this.breakAfter?"#":"")}static get(e){return e.cmView}get isEditable(){return!0}merge(e,t,i,s,r,o){return!1}become(e){return!1}canReuseDOM(e){return e.constructor==this.constructor}getSide(){return 0}destroy(){this.parent=null}}q.prototype.breakAfter=0;function Xr(n){let e=n.nextSibling;return n.parentNode.removeChild(n),e}class ra{constructor(e,t,i){this.children=e,this.pos=t,this.i=i,this.off=0}findPos(e,t=1){for(;;){if(e>this.pos||e==this.pos&&(t>0||this.i==0||this.children[this.i-1].breakAfter))return this.off=e-this.pos,this;let i=this.children[--this.i];this.pos-=i.length+i.breakAfter}}}function oa(n,e,t,i,s,r,o,l,a){let{children:h}=n,c=h.length?h[e]:null,f=r.length?r[r.length-1]:null,u=f?f.breakAfter:o;if(!(e==i&&c&&!o&&!u&&r.length<2&&c.merge(t,s,r.length?f:null,t==0,l,a))){if(i<h.length){let d=h[i];d&&s<d.length?(e==i&&(d=d.split(s),s=0),!u&&f&&d.merge(0,s,f,!0,0,a)?r[r.length-1]=d:(s&&d.merge(0,s,null,!1,0,a),r.push(d))):d?.breakAfter&&(f?f.breakAfter=1:o=1),i++}for(c&&(c.breakAfter=o,t>0&&(!o&&r.length&&c.merge(t,c.length,r[0],!1,l,0)?c.breakAfter=r.shift().breakAfter:(t<c.length||c.children.length&&c.children[c.children.length-1].length==0)&&c.merge(t,c.length,null,!1,l,0),e++));e<i&&r.length;)if(h[i-1].become(r[r.length-1]))i--,r.pop(),a=r.length?0:l;else if(h[e].become(r[0]))e++,r.shift(),l=r.length?0:a;else break;!r.length&&e&&i<h.length&&!h[e-1].breakAfter&&h[i].merge(0,0,h[e-1],!1,l,a)&&e--,(e<i||r.length)&&n.replaceChildren(e,i,r)}}function la(n,e,t,i,s,r){let o=n.childCursor(),{i:l,off:a}=o.findPos(t,1),{i:h,off:c}=o.findPos(e,-1),f=e-t;for(let u of i)f+=u.length;n.length+=f,oa(n,h,c,l,a,i,0,s,r)}let xe=typeof navigator<"u"?navigator:{userAgent:"",vendor:"",platform:""},As=typeof document<"u"?document:{documentElement:{style:{}}};const Ms=/Edge\/(\d+)/.exec(xe.userAgent),aa=/MSIE \d/.test(xe.userAgent),Ds=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(xe.userAgent),vn=!!(aa||Ds||Ms),Zr=!vn&&/gecko\/(\d+)/i.test(xe.userAgent),Fn=!vn&&/Chrome\/(\d+)/.exec(xe.userAgent),Qr="webkitFontSmoothing"in As.documentElement.style,ha=!vn&&/Apple Computer/.test(xe.vendor),eo=ha&&(/Mobile\/\w+/.test(xe.userAgent)||xe.maxTouchPoints>2);var A={mac:eo||/Mac/.test(xe.platform),windows:/Win/.test(xe.platform),linux:/Linux|X11/.test(xe.platform),ie:vn,ie_version:aa?As.documentMode||6:Ds?+Ds[1]:Ms?+Ms[1]:0,gecko:Zr,gecko_version:Zr?+(/Firefox\/(\d+)/.exec(xe.userAgent)||[0,0])[1]:0,chrome:!!Fn,chrome_version:Fn?+Fn[1]:0,ios:eo,android:/Android\b/.test(xe.userAgent),webkit:Qr,safari:ha,webkit_version:Qr?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0,tabSize:As.documentElement.style.tabSize!=null?"tab-size":"-moz-tab-size"};const Zc=256;class at extends q{constructor(e){super(),this.text=e}get length(){return this.text.length}createDOM(e){this.setDOM(e||document.createTextNode(this.text))}sync(e){this.dom||this.createDOM(),this.dom.nodeValue!=this.text&&(e&&e.node==this.dom&&(e.written=!0),this.dom.nodeValue=this.text)}reuseDOM(e){e.nodeType==3&&this.createDOM(e)}merge(e,t,i){return i&&(!(i instanceof at)||this.length-(t-e)+i.length>Zc)?!1:(this.text=this.text.slice(0,e)+(i?i.text:"")+this.text.slice(t),this.markDirty(),!0)}split(e){let t=new at(this.text.slice(e));return this.text=this.text.slice(0,e),this.markDirty(),t}localPosFromDOM(e,t){return e==this.dom?t:t?this.text.length:0}domAtPos(e){return new fe(this.dom,e)}domBoundsAround(e,t,i){return{from:i,to:i+this.length,startDOM:this.dom,endDOM:this.dom.nextSibling}}coordsAt(e,t){return Ts(this.dom,e,t)}}class Ue extends q{constructor(e,t=[],i=0){super(),this.mark=e,this.children=t,this.length=i;for(let s of t)s.setParent(this)}setAttrs(e){if(sa(e),this.mark.class&&(e.className=this.mark.class),this.mark.attrs)for(let t in this.mark.attrs)e.setAttribute(t,this.mark.attrs[t]);return e}reuseDOM(e){e.nodeName==this.mark.tagName.toUpperCase()&&(this.setDOM(e),this.dirty|=6)}sync(e){this.dom?this.dirty&4&&this.setAttrs(this.dom):this.setDOM(this.setAttrs(document.createElement(this.mark.tagName))),super.sync(e)}merge(e,t,i,s,r,o){return i&&(!(i instanceof Ue&&i.mark.eq(this.mark))||e&&r<=0||t<this.length&&o<=0)?!1:(la(this,e,t,i?i.children:[],r-1,o-1),this.markDirty(),!0)}split(e){let t=[],i=0,s=-1,r=0;for(let l of this.children){let a=i+l.length;a>e&&t.push(i<e?l.split(e-i):l),s<0&&i>=e&&(s=r),i=a,r++}let o=this.length-e;return this.length=e,s>-1&&(this.children.length=s,this.markDirty()),new Ue(this.mark,t,o)}domAtPos(e){return ua(this,e)}coordsAt(e,t){return pa(this,e,t)}}function Ts(n,e,t){let i=n.nodeValue.length;e>i&&(e=i);let s=e,r=e,o=0;e==0&&t<0||e==i&&t>=0?A.chrome||A.gecko||(e?(s--,o=1):r<i&&(r++,o=-1)):t<0?s--:r<i&&r++;let l=_t(n,s,r).getClientRects();if(!l.length)return ia;let a=l[(o?o<0:t>=0)?0:l.length-1];return A.safari&&!o&&a.width==0&&(a=Array.prototype.find.call(l,h=>h.width)||a),o?lr(a,o<0):a||null}class nt extends q{constructor(e,t,i){super(),this.widget=e,this.length=t,this.side=i,this.prevWidget=null}static create(e,t,i){return new(e.customView||nt)(e,t,i)}split(e){let t=nt.create(this.widget,this.length-e,this.side);return this.length-=e,t}sync(){(!this.dom||!this.widget.updateDOM(this.dom))&&(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(this.editorView)),this.dom.contentEditable="false")}getSide(){return this.side}merge(e,t,i,s,r,o){return i&&(!(i instanceof nt)||!this.widget.compare(i.widget)||e>0&&r<=0||t<this.length&&o<=0)?!1:(this.length=e+(i?i.length:0)+(this.length-t),!0)}become(e){return e.length==this.length&&e instanceof nt&&e.side==this.side&&this.widget.constructor==e.widget.constructor?(this.widget.eq(e.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=e.widget,!0):!1}ignoreMutation(){return!0}ignoreEvent(e){return this.widget.ignoreEvent(e)}get overrideDOMText(){if(this.length==0)return V.empty;let e=this;for(;e.parent;)e=e.parent;let t=e.editorView,i=t&&t.state.doc,s=this.posAtStart;return i?i.slice(s,s+this.length):V.empty}domAtPos(e){return e==0?fe.before(this.dom):fe.after(this.dom,e==this.length)}domBoundsAround(){return null}coordsAt(e,t){let i=this.dom.getClientRects(),s=null;if(!i.length)return ia;for(let r=e>0?i.length-1:0;s=i[r],!(e>0?r==0:r==i.length-1||s.top<s.bottom);r+=e>0?-1:1);return this.length?s:lr(s,this.side>0)}get isEditable(){return!1}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}}class ca extends nt{domAtPos(e){let{topView:t,text:i}=this.widget;return t?Os(e,0,t,i,(s,r)=>s.domAtPos(r),s=>new fe(i,Math.min(s,i.nodeValue.length))):new fe(i,Math.min(e,i.nodeValue.length))}sync(){this.setDOM(this.widget.toDOM())}localPosFromDOM(e,t){let{topView:i,text:s}=this.widget;return i?fa(e,t,i,s):Math.min(t,this.length)}ignoreMutation(){return!1}get overrideDOMText(){return null}coordsAt(e,t){let{topView:i,text:s}=this.widget;return i?Os(e,t,i,s,(r,o,l)=>r.coordsAt(o,l),(r,o)=>Ts(s,r,o)):Ts(s,e,t)}destroy(){var e;super.destroy(),(e=this.widget.topView)===null||e===void 0||e.destroy()}get isEditable(){return!0}canReuseDOM(){return!0}}function Os(n,e,t,i,s,r){if(t instanceof Ue){for(let o=t.dom.firstChild;o;o=o.nextSibling){let l=q.get(o);if(!l)return r(n,e);let a=zt(o,i),h=l.length+(a?i.nodeValue.length:0);if(n<h||n==h&&l.getSide()<=0)return a?Os(n,e,l,i,s,r):s(l,n,e);n-=h}return s(t,t.length,-1)}else return t.dom==i?r(n,e):s(t,n,e)}function fa(n,e,t,i){if(t instanceof Ue)for(let s of t.children){let r=0,o=zt(s.dom,i);if(zt(s.dom,n))return r+(o?fa(n,e,s,i):s.localPosFromDOM(n,e));r+=o?i.nodeValue.length:s.length}else if(t.dom==i)return Math.min(e,i.nodeValue.length);return t.localPosFromDOM(n,e)}class qt extends q{constructor(e){super(),this.side=e}get length(){return 0}merge(){return!1}become(e){return e instanceof qt&&e.side==this.side}split(){return new qt(this.side)}sync(){if(!this.dom){let e=document.createElement("img");e.className="cm-widgetBuffer",e.setAttribute("aria-hidden","true"),this.setDOM(e)}}getSide(){return this.side}domAtPos(e){return fe.before(this.dom)}localPosFromDOM(){return 0}domBoundsAround(){return null}coordsAt(e){let t=this.dom.getBoundingClientRect(),i=Qc(this,this.side>0?-1:1);return i&&i.top<t.bottom&&i.bottom>t.top?{left:t.left,right:t.right,top:i.top,bottom:i.bottom}:t}get overrideDOMText(){return V.empty}}at.prototype.children=nt.prototype.children=qt.prototype.children=ar;function Qc(n,e){let t=n.parent,i=t?t.children.indexOf(n):-1;for(;t&&i>=0;)if(e<0?i>0:i<t.children.length){let s=t.children[i+e];if(s instanceof at){let r=s.coordsAt(e<0?s.length:0,e);if(r)return r}i+=e}else if(t instanceof Ue&&t.parent)i=t.parent.children.indexOf(t)+(e<0?0:1),t=t.parent;else{let s=t.dom.lastChild;if(s&&s.nodeName=="BR")return s.getClientRects()[0];break}}function ua(n,e){let t=n.dom,{children:i}=n,s=0;for(let r=0;s<i.length;s++){let o=i[s],l=r+o.length;if(!(l==r&&o.getSide()<=0)){if(e>r&&e<l&&o.dom.parentNode==t)return o.domAtPos(e-r);if(e<=r)break;r=l}}for(let r=s;r>0;r--){let o=i[r-1];if(o.dom.parentNode==t)return o.domAtPos(o.length)}for(let r=s;r<i.length;r++){let o=i[r];if(o.dom.parentNode==t)return o.domAtPos(0)}return new fe(t,0)}function da(n,e,t){let i,{children:s}=n;t>0&&e instanceof Ue&&s.length&&(i=s[s.length-1])instanceof Ue&&i.mark.eq(e.mark)?da(i,e.children[0],t-1):(s.push(e),e.setParent(n)),n.length+=e.length}function pa(n,e,t){let i=null,s=-1,r=null,o=-1;function l(h,c){for(let f=0,u=0;f<h.children.length&&u<=c;f++){let d=h.children[f],p=u+d.length;p>=c&&(d.children.length?l(d,c-u):!r&&(p>c||u==p&&d.getSide()>0)?(r=d,o=c-u):(u<c||u==p&&d.getSide()<0)&&(i=d,s=c-u)),u=p}}l(n,e);let a=(t<0?i:r)||i||r;return a?a.coordsAt(Math.max(0,a==i?s:o),t):ef(n)}function ef(n){let e=n.dom.lastChild;if(!e)return n.dom.getBoundingClientRect();let t=yi(e);return t[t.length-1]||null}function Bs(n,e){for(let t in n)t=="class"&&e.class?e.class+=" "+n.class:t=="style"&&e.style?e.style+=";"+n.style:e[t]=n[t];return e}function hr(n,e){if(n==e)return!0;if(!n||!e)return!1;let t=Object.keys(n),i=Object.keys(e);if(t.length!=i.length)return!1;for(let s of t)if(i.indexOf(s)==-1||n[s]!==e[s])return!1;return!0}function Ps(n,e,t){let i=null;if(e)for(let s in e)t&&s in t||n.removeAttribute(i=s);if(t)for(let s in t)e&&e[s]==t[s]||n.setAttribute(i=s,t[s]);return!!i}class Ge{eq(e){return!1}updateDOM(e){return!1}compare(e){return this==e||this.constructor==e.constructor&&this.eq(e)}get estimatedHeight(){return-1}ignoreEvent(e){return!0}get customView(){return null}destroy(e){}}var W=function(n){return n[n.Text=0]="Text",n[n.WidgetBefore=1]="WidgetBefore",n[n.WidgetAfter=2]="WidgetAfter",n[n.WidgetRange=3]="WidgetRange",n}(W||(W={}));class E extends kt{constructor(e,t,i,s){super(),this.startSide=e,this.endSide=t,this.widget=i,this.spec=s}get heightRelevant(){return!1}static mark(e){return new Sn(e)}static widget(e){let t=e.side||0,i=!!e.block;return t+=i?t>0?3e8:-4e8:t>0?1e8:-1e8,new St(e,t,t,i,e.widget||null,!1)}static replace(e){let t=!!e.block,i,s;if(e.isBlockGap)i=-5e8,s=4e8;else{let{start:r,end:o}=ma(e,t);i=(r?t?-3e8:-1:5e8)-1,s=(o?t?2e8:1:-6e8)+1}return new St(e,i,s,t,e.widget||null,!0)}static line(e){return new Di(e)}static set(e,t=!1){return H.of(e,t)}hasHeight(){return this.widget?this.widget.estimatedHeight>-1:!1}}E.none=H.empty;class Sn extends E{constructor(e){let{start:t,end:i}=ma(e);super(t?-1:5e8,i?1:-6e8,null,e),this.tagName=e.tagName||"span",this.class=e.class||"",this.attrs=e.attributes||null}eq(e){return this==e||e instanceof Sn&&this.tagName==e.tagName&&this.class==e.class&&hr(this.attrs,e.attrs)}range(e,t=e){if(e>=t)throw new RangeError("Mark decorations may not be empty");return super.range(e,t)}}Sn.prototype.point=!1;class Di extends E{constructor(e){super(-2e8,-2e8,null,e)}eq(e){return e instanceof Di&&hr(this.spec.attributes,e.spec.attributes)}range(e,t=e){if(t!=e)throw new RangeError("Line decoration ranges must be zero-length");return super.range(e,t)}}Di.prototype.mapMode=le.TrackBefore;Di.prototype.point=!0;class St extends E{constructor(e,t,i,s,r,o){super(t,i,r,e),this.block=s,this.isReplace=o,this.mapMode=s?t<=0?le.TrackBefore:le.TrackAfter:le.TrackDel}get type(){return this.startSide<this.endSide?W.WidgetRange:this.startSide<=0?W.WidgetBefore:W.WidgetAfter}get heightRelevant(){return this.block||!!this.widget&&this.widget.estimatedHeight>=5}eq(e){return e instanceof St&&tf(this.widget,e.widget)&&this.block==e.block&&this.startSide==e.startSide&&this.endSide==e.endSide}range(e,t=e){if(this.isReplace&&(e>t||e==t&&this.startSide>0&&this.endSide<=0))throw new RangeError("Invalid range for replacement decoration");if(!this.isReplace&&t!=e)throw new RangeError("Widget decorations can only have zero-length ranges");return super.range(e,t)}}St.prototype.point=!0;function ma(n,e=!1){let{inclusiveStart:t,inclusiveEnd:i}=n;return t==null&&(t=n.inclusive),i==null&&(i=n.inclusive),{start:t??e,end:i??e}}function tf(n,e){return n==e||!!(n&&e&&n.compare(e))}function Es(n,e,t,i=0){let s=t.length-1;s>=0&&t[s]+i>=n?t[s]=Math.max(t[s],e):t.push(n,e)}class pe extends q{constructor(){super(...arguments),this.children=[],this.length=0,this.prevAttrs=void 0,this.attrs=null,this.breakAfter=0}merge(e,t,i,s,r,o){if(i){if(!(i instanceof pe))return!1;this.dom||i.transferDOM(this)}return s&&this.setDeco(i?i.attrs:null),la(this,e,t,i?i.children:[],r,o),!0}split(e){let t=new pe;if(t.breakAfter=this.breakAfter,this.length==0)return t;let{i,off:s}=this.childPos(e);s&&(t.append(this.children[i].split(s),0),this.children[i].merge(s,this.children[i].length,null,!1,0,0),i++);for(let r=i;r<this.children.length;r++)t.append(this.children[r],0);for(;i>0&&this.children[i-1].length==0;)this.children[--i].destroy();return this.children.length=i,this.markDirty(),this.length=e,t}transferDOM(e){this.dom&&(this.markDirty(),e.setDOM(this.dom),e.prevAttrs=this.prevAttrs===void 0?this.attrs:this.prevAttrs,this.prevAttrs=void 0,this.dom=null)}setDeco(e){hr(this.attrs,e)||(this.dom&&(this.prevAttrs=this.attrs,this.markDirty()),this.attrs=e)}append(e,t){da(this,e,t)}addLineDeco(e){let t=e.spec.attributes,i=e.spec.class;t&&(this.attrs=Bs(t,this.attrs||{})),i&&(this.attrs=Bs({class:i},this.attrs||{}))}domAtPos(e){return ua(this,e)}reuseDOM(e){e.nodeName=="DIV"&&(this.setDOM(e),this.dirty|=6)}sync(e){var t;this.dom?this.dirty&4&&(sa(this.dom),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0):(this.setDOM(document.createElement("div")),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0),this.prevAttrs!==void 0&&(Ps(this.dom,this.prevAttrs,this.attrs),this.dom.classList.add("cm-line"),this.prevAttrs=void 0),super.sync(e);let i=this.dom.lastChild;for(;i&&q.get(i)instanceof Ue;)i=i.lastChild;if(!i||!this.length||i.nodeName!="BR"&&((t=q.get(i))===null||t===void 0?void 0:t.isEditable)==!1&&(!A.ios||!this.children.some(s=>s instanceof at))){let s=document.createElement("BR");s.cmIgnore=!0,this.dom.appendChild(s)}}measureTextSize(){if(this.children.length==0||this.length>20)return null;let e=0;for(let t of this.children){if(!(t instanceof at)||/[^ -~]/.test(t.text))return null;let i=yi(t.dom);if(i.length!=1)return null;e+=i[0].width}return e?{lineHeight:this.dom.getBoundingClientRect().height,charWidth:e/this.length}:null}coordsAt(e,t){return pa(this,e,t)}become(e){return!1}get type(){return W.Text}static find(e,t){for(let i=0,s=0;i<e.children.length;i++){let r=e.children[i],o=s+r.length;if(o>=t){if(r instanceof pe)return r;if(o>t)break}s=o+r.breakAfter}return null}}class xt extends q{constructor(e,t,i){super(),this.widget=e,this.length=t,this.type=i,this.breakAfter=0,this.prevWidget=null}merge(e,t,i,s,r,o){return i&&(!(i instanceof xt)||!this.widget.compare(i.widget)||e>0&&r<=0||t<this.length&&o<=0)?!1:(this.length=e+(i?i.length:0)+(this.length-t),!0)}domAtPos(e){return e==0?fe.before(this.dom):fe.after(this.dom,e==this.length)}split(e){let t=this.length-e;this.length=e;let i=new xt(this.widget,t,this.type);return i.breakAfter=this.breakAfter,i}get children(){return ar}sync(){(!this.dom||!this.widget.updateDOM(this.dom))&&(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(this.editorView)),this.dom.contentEditable="false")}get overrideDOMText(){return this.parent?this.parent.view.state.doc.slice(this.posAtStart,this.posAtEnd):V.empty}domBoundsAround(){return null}become(e){return e instanceof xt&&e.type==this.type&&e.widget.constructor==this.widget.constructor?(e.widget.eq(this.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=e.widget,this.length=e.length,this.breakAfter=e.breakAfter,!0):!1}ignoreMutation(){return!0}ignoreEvent(e){return this.widget.ignoreEvent(e)}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}}class cr{constructor(e,t,i,s){this.doc=e,this.pos=t,this.end=i,this.disallowBlockEffectsFor=s,this.content=[],this.curLine=null,this.breakAtStart=0,this.pendingBuffer=0,this.atCursorPos=!0,this.openStart=-1,this.openEnd=-1,this.text="",this.textOff=0,this.cursor=e.iter(),this.skip=t}posCovered(){if(this.content.length==0)return!this.breakAtStart&&this.doc.lineAt(this.pos).from!=this.pos;let e=this.content[this.content.length-1];return!e.breakAfter&&!(e instanceof xt&&e.type==W.WidgetBefore)}getLine(){return this.curLine||(this.content.push(this.curLine=new pe),this.atCursorPos=!0),this.curLine}flushBuffer(e){this.pendingBuffer&&(this.curLine.append(Ii(new qt(-1),e),e.length),this.pendingBuffer=0)}addBlockWidget(e){this.flushBuffer([]),this.curLine=null,this.content.push(e)}finish(e){e?this.pendingBuffer=0:this.flushBuffer([]),this.posCovered()||this.getLine()}buildText(e,t,i){for(;e>0;){if(this.textOff==this.text.length){let{value:r,lineBreak:o,done:l}=this.cursor.next(this.skip);if(this.skip=0,l)throw new Error("Ran out of text content when drawing inline views");if(o){this.posCovered()||this.getLine(),this.content.length?this.content[this.content.length-1].breakAfter=1:this.breakAtStart=1,this.flushBuffer([]),this.curLine=null,e--;continue}else this.text=r,this.textOff=0}let s=Math.min(this.text.length-this.textOff,e,512);this.flushBuffer(t.slice(0,i)),this.getLine().append(Ii(new at(this.text.slice(this.textOff,this.textOff+s)),t),i),this.atCursorPos=!0,this.textOff+=s,e-=s,i=0}}span(e,t,i,s){this.buildText(t-e,i,s),this.pos=t,this.openStart<0&&(this.openStart=s)}point(e,t,i,s,r,o){if(this.disallowBlockEffectsFor[o]&&i instanceof St){if(i.block)throw new RangeError("Block decorations may not be specified via plugins");if(t>this.doc.lineAt(this.pos).to)throw new RangeError("Decorations that replace line breaks may not be specified via plugins")}let l=t-e;if(i instanceof St)if(i.block){let{type:a}=i;a==W.WidgetAfter&&!this.posCovered()&&this.getLine(),this.addBlockWidget(new xt(i.widget||new to("div"),l,a))}else{let a=nt.create(i.widget||new to("span"),l,l?0:i.startSide),h=this.atCursorPos&&!a.isEditable&&r<=s.length&&(e<t||i.startSide>0),c=!a.isEditable&&(e<t||i.startSide<=0),f=this.getLine();this.pendingBuffer==2&&!h&&(this.pendingBuffer=0),this.flushBuffer(s),h&&(f.append(Ii(new qt(1),s),r),r=s.length+Math.max(0,r-s.length)),f.append(Ii(a,s),r),this.atCursorPos=c,this.pendingBuffer=c?e<t?1:2:0}else this.doc.lineAt(this.pos).from==this.pos&&this.getLine().addLineDeco(i);l&&(this.textOff+l<=this.text.length?this.textOff+=l:(this.skip+=l-(this.text.length-this.textOff),this.text="",this.textOff=0),this.pos=t),this.openStart<0&&(this.openStart=r)}static build(e,t,i,s,r){let o=new cr(e,t,i,r);return o.openEnd=H.spans(s,t,i,o),o.openStart<0&&(o.openStart=o.openEnd),o.finish(o.openEnd),o}}function Ii(n,e){for(let t of e)n=new Ue(t,[n],n.length);return n}class to extends Ge{constructor(e){super(),this.tag=e}eq(e){return e.tag==this.tag}toDOM(){return document.createElement(this.tag)}updateDOM(e){return e.nodeName.toLowerCase()==this.tag}}const ga=D.define(),ya=D.define(),ba=D.define(),wa=D.define(),Rs=D.define(),xa=D.define(),ka=D.define({combine:n=>n.some(e=>e)}),va=D.define({combine:n=>n.some(e=>e)});class hn{constructor(e,t="nearest",i="nearest",s=5,r=5){this.range=e,this.y=t,this.x=i,this.yMargin=s,this.xMargin=r}map(e){return e.empty?this:new hn(this.range.map(e),this.y,this.x,this.yMargin,this.xMargin)}}const io=R.define({map:(n,e)=>n.map(e)});function Ee(n,e,t){let i=n.facet(wa);i.length?i[0](e):window.onerror?window.onerror(String(e),t,void 0,void 0,e):t?console.error(t+":",e):console.error(e)}const Cn=D.define({combine:n=>n.length?n[0]:!0});let nf=0;const si=D.define();class ue{constructor(e,t,i,s){this.id=e,this.create=t,this.domEventHandlers=i,this.extension=s(this)}static define(e,t){const{eventHandlers:i,provide:s,decorations:r}=t||{};return new ue(nf++,e,i,o=>{let l=[si.of(o)];return r&&l.push(wi.of(a=>{let h=a.plugin(o);return h?r(h):E.none})),s&&l.push(s(o)),l})}static fromClass(e,t){return ue.define(i=>new e(i),t)}}class Hn{constructor(e){this.spec=e,this.mustUpdate=null,this.value=null}update(e){if(this.value){if(this.mustUpdate){let t=this.mustUpdate;if(this.mustUpdate=null,this.value.update)try{this.value.update(t)}catch(i){if(Ee(t.state,i,"CodeMirror plugin crashed"),this.value.destroy)try{this.value.destroy()}catch{}this.deactivate()}}}else if(this.spec)try{this.value=this.spec.create(e)}catch(t){Ee(e.state,t,"CodeMirror plugin crashed"),this.deactivate()}return this}destroy(e){var t;if(!((t=this.value)===null||t===void 0)&&t.destroy)try{this.value.destroy()}catch(i){Ee(e.state,i,"CodeMirror plugin crashed")}}deactivate(){this.spec=this.value=null}}const Sa=D.define(),Ca=D.define(),wi=D.define(),Aa=D.define(),Ma=D.define(),ri=D.define();class Ke{constructor(e,t,i,s){this.fromA=e,this.toA=t,this.fromB=i,this.toB=s}join(e){return new Ke(Math.min(this.fromA,e.fromA),Math.max(this.toA,e.toA),Math.min(this.fromB,e.fromB),Math.max(this.toB,e.toB))}addToSet(e){let t=e.length,i=this;for(;t>0;t--){let s=e[t-1];if(!(s.fromA>i.toA)){if(s.toA<i.fromA)break;i=i.join(s),e.splice(t-1,1)}}return e.splice(t,0,i),e}static extendWithRanges(e,t){if(t.length==0)return e;let i=[];for(let s=0,r=0,o=0,l=0;;s++){let a=s==e.length?null:e[s],h=o-l,c=a?a.fromB:1e9;for(;r<t.length&&t[r]<c;){let f=t[r],u=t[r+1],d=Math.max(l,f),p=Math.min(c,u);if(d<=p&&new Ke(d+h,p+h,d,p).addToSet(i),u>c)break;r+=2}if(!a)return i;new Ke(a.fromA,a.toA,a.fromB,a.toB).addToSet(i),o=a.toA,l=a.toB}}}class cn{constructor(e,t,i){this.view=e,this.state=t,this.transactions=i,this.flags=0,this.startState=e.state,this.changes=te.empty(this.startState.doc.length);for(let o of i)this.changes=this.changes.compose(o.changes);let s=[];this.changes.iterChangedRanges((o,l,a,h)=>s.push(new Ke(o,l,a,h))),this.changedRanges=s;let r=e.hasFocus;r!=e.inputState.notifiedFocused&&(e.inputState.notifiedFocused=r,this.flags|=1)}static create(e,t,i){return new cn(e,t,i)}get viewportChanged(){return(this.flags&4)>0}get heightChanged(){return(this.flags&2)>0}get geometryChanged(){return this.docChanged||(this.flags&10)>0}get focusChanged(){return(this.flags&1)>0}get docChanged(){return!this.changes.empty}get selectionSet(){return this.transactions.some(e=>e.selection)}get empty(){return this.flags==0&&this.transactions.length==0}}var Y=function(n){return n[n.LTR=0]="LTR",n[n.RTL=1]="RTL",n}(Y||(Y={}));const Ls=Y.LTR,sf=Y.RTL;function Da(n){let e=[];for(let t=0;t<n.length;t++)e.push(1<<+n[t]);return e}const rf=Da("88888888888888888888888888888888888666888888787833333333337888888000000000000000000000000008888880000000000000000000000000088888888888888888888888888888888888887866668888088888663380888308888800000000000000000000000800000000000000000000000000000008"),of=Da("4444448826627288999999999992222222222222222222222222222222222222222222222229999999999999999999994444444444644222822222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222999999949999999229989999223333333333"),Is=Object.create(null),Le=[];for(let n of["()","[]","{}"]){let e=n.charCodeAt(0),t=n.charCodeAt(1);Is[e]=t,Is[t]=-e}function lf(n){return n<=247?rf[n]:1424<=n&&n<=1524?2:1536<=n&&n<=1785?of[n-1536]:1774<=n&&n<=2220?4:8192<=n&&n<=8203?256:64336<=n&&n<=65023?4:n==8204?256:1}const af=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\ufb50-\ufdff]/;class Wt{constructor(e,t,i){this.from=e,this.to=t,this.level=i}get dir(){return this.level%2?sf:Ls}side(e,t){return this.dir==t==e?this.to:this.from}static find(e,t,i,s){let r=-1;for(let o=0;o<e.length;o++){let l=e[o];if(l.from<=t&&l.to>=t){if(l.level==i)return o;(r<0||(s!=0?s<0?l.from<t:l.to>t:e[r].level>l.level))&&(r=o)}}if(r<0)throw new RangeError("Index out of range");return r}}const $=[];function hf(n,e){let t=n.length,i=e==Ls?1:2,s=e==Ls?2:1;if(!n||i==1&&!af.test(n))return Ta(t);for(let o=0,l=i,a=i;o<t;o++){let h=lf(n.charCodeAt(o));h==512?h=l:h==8&&a==4&&(h=16),$[o]=h==4?2:h,h&7&&(a=h),l=h}for(let o=0,l=i,a=i;o<t;o++){let h=$[o];if(h==128)o<t-1&&l==$[o+1]&&l&24?h=$[o]=l:$[o]=256;else if(h==64){let c=o+1;for(;c<t&&$[c]==64;)c++;let f=o&&l==8||c<t&&$[c]==8?a==1?1:8:256;for(let u=o;u<c;u++)$[u]=f;o=c-1}else h==8&&a==1&&($[o]=1);l=h,h&7&&(a=h)}for(let o=0,l=0,a=0,h,c,f;o<t;o++)if(c=Is[h=n.charCodeAt(o)])if(c<0){for(let u=l-3;u>=0;u-=3)if(Le[u+1]==-c){let d=Le[u+2],p=d&2?i:d&4?d&1?s:i:0;p&&($[o]=$[Le[u]]=p),l=u;break}}else{if(Le.length==189)break;Le[l++]=o,Le[l++]=h,Le[l++]=a}else if((f=$[o])==2||f==1){let u=f==i;a=u?0:1;for(let d=l-3;d>=0;d-=3){let p=Le[d+2];if(p&2)break;if(u)Le[d+2]|=2;else{if(p&4)break;Le[d+2]|=4}}}for(let o=0;o<t;o++)if($[o]==256){let l=o+1;for(;l<t&&$[l]==256;)l++;let a=(o?$[o-1]:i)==1,h=(l<t?$[l]:i)==1,c=a==h?a?1:2:i;for(let f=o;f<l;f++)$[f]=c;o=l-1}let r=[];if(i==1)for(let o=0;o<t;){let l=o,a=$[o++]!=1;for(;o<t&&a==($[o]!=1);)o++;if(a)for(let h=o;h>l;){let c=h,f=$[--h]!=2;for(;h>l&&f==($[h-1]!=2);)h--;r.push(new Wt(h,c,f?2:1))}else r.push(new Wt(l,o,0))}else for(let o=0;o<t;){let l=o,a=$[o++]==2;for(;o<t&&a==($[o]==2);)o++;r.push(new Wt(l,o,a?1:2))}return r}function Ta(n){return[new Wt(0,n,0)]}let Oa="";function cf(n,e,t,i,s){var r;let o=i.head-n.from,l=-1;if(o==0){if(!s||!n.length)return null;e[0].level!=t&&(o=e[0].side(!1,t),l=0)}else if(o==n.length){if(s)return null;let u=e[e.length-1];u.level!=t&&(o=u.side(!0,t),l=e.length-1)}l<0&&(l=Wt.find(e,o,(r=i.bidiLevel)!==null&&r!==void 0?r:-1,i.assoc));let a=e[l];o==a.side(s,t)&&(a=e[l+=s?1:-1],o=a.side(!s,t));let h=s==(a.dir==t),c=ke(n.text,o,h);if(Oa=n.text.slice(Math.min(o,c),Math.max(o,c)),c!=a.side(s,t))return w.cursor(c+n.from,h?-1:1,a.level);let f=l==(s?e.length-1:0)?null:e[l+(s?1:-1)];return!f&&a.level!=t?w.cursor(s?n.to:n.from,s?-1:1,t):f&&f.level<a.level?w.cursor(f.side(!s,t)+n.from,s?1:-1,f.level):w.cursor(c+n.from,s?-1:1,a.level)}const st="￿";class Ba{constructor(e,t){this.points=e,this.text="",this.lineSeparator=t.facet(N.lineSeparator)}append(e){this.text+=e}lineBreak(){this.text+=st}readRange(e,t){if(!e)return this;let i=e.parentNode;for(let s=e;;){this.findPointBefore(i,s),this.readNode(s);let r=s.nextSibling;if(r==t)break;let o=q.get(s),l=q.get(r);(o&&l?o.breakAfter:(o?o.breakAfter:no(s))||no(r)&&(s.nodeName!="BR"||s.cmIgnore))&&this.lineBreak(),s=r}return this.findPointBefore(i,t),this}readTextNode(e){let t=e.nodeValue;for(let i of this.points)i.node==e&&(i.pos=this.text.length+Math.min(i.offset,t.length));for(let i=0,s=this.lineSeparator?null:/\r\n?|\n/g;;){let r=-1,o=1,l;if(this.lineSeparator?(r=t.indexOf(this.lineSeparator,i),o=this.lineSeparator.length):(l=s.exec(t))&&(r=l.index,o=l[0].length),this.append(t.slice(i,r<0?t.length:r)),r<0)break;if(this.lineBreak(),o>1)for(let a of this.points)a.node==e&&a.pos>this.text.length&&(a.pos-=o-1);i=r+o}}readNode(e){if(e.cmIgnore)return;let t=q.get(e),i=t&&t.overrideDOMText;if(i!=null){this.findPointInside(e,i.length);for(let s=i.iter();!s.next().done;)s.lineBreak?this.lineBreak():this.append(s.value)}else e.nodeType==3?this.readTextNode(e):e.nodeName=="BR"?e.nextSibling&&this.lineBreak():e.nodeType==1&&this.readRange(e.firstChild,null)}findPointBefore(e,t){for(let i of this.points)i.node==e&&e.childNodes[i.offset]==t&&(i.pos=this.text.length)}findPointInside(e,t){for(let i of this.points)(e.nodeType==3?i.node==e:e.contains(i.node))&&(i.pos=this.text.length+Math.min(t,i.offset))}}function no(n){return n.nodeType==1&&/^(DIV|P|LI|UL|OL|BLOCKQUOTE|DD|DT|H\d|SECTION|PRE)$/.test(n.nodeName)}class so{constructor(e,t){this.node=e,this.offset=t,this.pos=-1}}class ro extends q{constructor(e){super(),this.view=e,this.compositionDeco=E.none,this.decorations=[],this.dynamicDecorationMap=[],this.minWidth=0,this.minWidthFrom=0,this.minWidthTo=0,this.impreciseAnchor=null,this.impreciseHead=null,this.forceSelection=!1,this.lastUpdate=Date.now(),this.setDOM(e.contentDOM),this.children=[new pe],this.children[0].setParent(this),this.updateDeco(),this.updateInner([new Ke(0,0,0,e.state.doc.length)],0)}get editorView(){return this.view}get length(){return this.view.state.doc.length}update(e){let t=e.changedRanges;this.minWidth>0&&t.length&&(t.every(({fromA:o,toA:l})=>l<this.minWidthFrom||o>this.minWidthTo)?(this.minWidthFrom=e.changes.mapPos(this.minWidthFrom,1),this.minWidthTo=e.changes.mapPos(this.minWidthTo,1)):this.minWidth=this.minWidthFrom=this.minWidthTo=0),this.view.inputState.composing<0?this.compositionDeco=E.none:(e.transactions.length||this.dirty)&&(this.compositionDeco=uf(this.view,e.changes)),(A.ie||A.chrome)&&!this.compositionDeco.size&&e&&e.state.doc.lines!=e.startState.doc.lines&&(this.forceSelection=!0);let i=this.decorations,s=this.updateDeco(),r=gf(i,s,e.changes);return t=Ke.extendWithRanges(t,r),this.dirty==0&&t.length==0?!1:(this.updateInner(t,e.startState.doc.length),e.transactions.length&&(this.lastUpdate=Date.now()),!0)}updateInner(e,t){this.view.viewState.mustMeasureContent=!0,this.updateChildren(e,t);let{observer:i}=this.view;i.ignore(()=>{this.dom.style.height=this.view.viewState.contentHeight+"px",this.dom.style.flexBasis=this.minWidth?this.minWidth+"px":"";let r=A.chrome||A.ios?{node:i.selectionRange.focusNode,written:!1}:void 0;this.sync(r),this.dirty=0,r&&(r.written||i.selectionRange.focusNode!=r.node)&&(this.forceSelection=!0),this.dom.style.height=""});let s=[];if(this.view.viewport.from||this.view.viewport.to<this.view.state.doc.length)for(let r of this.children)r instanceof xt&&r.widget instanceof oo&&s.push(r.dom);i.updateGaps(s)}updateChildren(e,t){let i=this.childCursor(t);for(let s=e.length-1;;s--){let r=s>=0?e[s]:null;if(!r)break;let{fromA:o,toA:l,fromB:a,toB:h}=r,{content:c,breakAtStart:f,openStart:u,openEnd:d}=cr.build(this.view.state.doc,a,h,this.decorations,this.dynamicDecorationMap),{i:p,off:y}=i.findPos(l,1),{i:g,off:b}=i.findPos(o,-1);oa(this,g,b,p,y,c,f,u,d)}}updateSelection(e=!1,t=!1){if((e||!this.view.observer.selectionRange.focusNode)&&this.view.observer.readSelectionRange(),!(t||this.mayControlSelection()))return;let i=this.forceSelection;this.forceSelection=!1;let s=this.view.state.selection.main,r=this.domAtPos(s.anchor),o=s.empty?r:this.domAtPos(s.head);if(A.gecko&&s.empty&&ff(r)){let a=document.createTextNode("");this.view.observer.ignore(()=>r.node.insertBefore(a,r.node.childNodes[r.offset]||null)),r=o=new fe(a,0),i=!0}let l=this.view.observer.selectionRange;(i||!l.focusNode||!ln(r.node,r.offset,l.anchorNode,l.anchorOffset)||!ln(o.node,o.offset,l.focusNode,l.focusOffset))&&(this.view.observer.ignore(()=>{A.android&&A.chrome&&this.dom.contains(l.focusNode)&&yf(l.focusNode,this.dom)&&(this.dom.blur(),this.dom.focus({preventScroll:!0}));let a=on(this.view.root);if(a)if(s.empty){if(A.gecko){let h=pf(r.node,r.offset);if(h&&h!=3){let c=Ea(r.node,r.offset,h==1?1:-1);c&&(r=new fe(c,h==1?0:c.nodeValue.length))}}a.collapse(r.node,r.offset),s.bidiLevel!=null&&l.cursorBidiLevel!=null&&(l.cursorBidiLevel=s.bidiLevel)}else if(a.extend){a.collapse(r.node,r.offset);try{a.extend(o.node,o.offset)}catch{}}else{let h=document.createRange();s.anchor>s.head&&([r,o]=[o,r]),h.setEnd(o.node,o.offset),h.setStart(r.node,r.offset),a.removeAllRanges(),a.addRange(h)}}),this.view.observer.setSelectionRange(r,o)),this.impreciseAnchor=r.precise?null:new fe(l.anchorNode,l.anchorOffset),this.impreciseHead=o.precise?null:new fe(l.focusNode,l.focusOffset)}enforceCursorAssoc(){if(this.compositionDeco.size)return;let{view:e}=this,t=e.state.selection.main,i=on(e.root),{anchorNode:s,anchorOffset:r}=e.observer.selectionRange;if(!i||!t.empty||!t.assoc||!i.modify)return;let o=pe.find(this,t.head);if(!o)return;let l=o.posAtStart;if(t.head==l||t.head==l+o.length)return;let a=this.coordsAt(t.head,-1),h=this.coordsAt(t.head,1);if(!a||!h||a.bottom>h.top)return;let c=this.domAtPos(t.head+t.assoc);i.collapse(c.node,c.offset),i.modify("move",t.assoc<0?"forward":"backward","lineboundary"),e.observer.readSelectionRange();let f=e.observer.selectionRange;e.docView.posFromDOM(f.anchorNode,f.anchorOffset)!=t.from&&i.collapse(s,r)}mayControlSelection(){let e=this.view.root.activeElement;return e==this.dom||Qi(this.dom,this.view.observer.selectionRange)&&!(e&&this.dom.contains(e))}nearest(e){for(let t=e;t;){let i=q.get(t);if(i&&i.rootView==this)return i;t=t.parentNode}return null}posFromDOM(e,t){let i=this.nearest(e);if(!i)throw new RangeError("Trying to find position for a DOM position outside of the document");return i.localPosFromDOM(e,t)+i.posAtStart}domAtPos(e){let{i:t,off:i}=this.childCursor().findPos(e,-1);for(;t<this.children.length-1;){let s=this.children[t];if(i<s.length||s instanceof pe)break;t++,i=0}return this.children[t].domAtPos(i)}coordsAt(e,t){for(let i=this.length,s=this.children.length-1;;s--){let r=this.children[s],o=i-r.breakAfter-r.length;if(e>o||e==o&&r.type!=W.WidgetBefore&&r.type!=W.WidgetAfter&&(!s||t==2||this.children[s-1].breakAfter||this.children[s-1].type==W.WidgetBefore&&t>-2))return r.coordsAt(e-o,t);i=o}}measureVisibleLineHeights(e){let t=[],{from:i,to:s}=e,r=this.view.contentDOM.clientWidth,o=r>Math.max(this.view.scrollDOM.clientWidth,this.minWidth)+1,l=-1,a=this.view.textDirection==Y.LTR;for(let h=0,c=0;c<this.children.length;c++){let f=this.children[c],u=h+f.length;if(u>s)break;if(h>=i){let d=f.dom.getBoundingClientRect();if(t.push(d.height),o){let p=f.dom.lastChild,y=p?yi(p):[];if(y.length){let g=y[y.length-1],b=a?g.right-d.left:d.right-g.left;b>l&&(l=b,this.minWidth=r,this.minWidthFrom=h,this.minWidthTo=u)}}}h=u+f.breakAfter}return t}textDirectionAt(e){let{i:t}=this.childPos(e,1);return getComputedStyle(this.children[t].dom).direction=="rtl"?Y.RTL:Y.LTR}measureTextSize(){for(let s of this.children)if(s instanceof pe){let r=s.measureTextSize();if(r)return r}let e=document.createElement("div"),t,i;return e.className="cm-line",e.style.width="99999px",e.textContent="abc def ghi jkl mno pqr stu",this.view.observer.ignore(()=>{this.dom.appendChild(e);let s=yi(e.firstChild)[0];t=e.getBoundingClientRect().height,i=s?s.width/27:7,e.remove()}),{lineHeight:t,charWidth:i}}childCursor(e=this.length){let t=this.children.length;return t&&(e-=this.children[--t].length),new ra(this.children,e,t)}computeBlockGapDeco(){let e=[],t=this.view.viewState;for(let i=0,s=0;;s++){let r=s==t.viewports.length?null:t.viewports[s],o=r?r.from-1:this.length;if(o>i){let l=t.lineBlockAt(o).bottom-t.lineBlockAt(i).top;e.push(E.replace({widget:new oo(l),block:!0,inclusive:!0,isBlockGap:!0}).range(i,o))}if(!r)break;i=r.to+1}return E.set(e)}updateDeco(){let e=this.view.state.facet(wi).map((t,i)=>(this.dynamicDecorationMap[i]=typeof t=="function")?t(this.view):t);for(let t=e.length;t<e.length+3;t++)this.dynamicDecorationMap[t]=!1;return this.decorations=[...e,this.compositionDeco,this.computeBlockGapDeco(),this.view.viewState.lineGapDeco]}scrollIntoView(e){let{range:t}=e,i=this.coordsAt(t.head,t.empty?t.assoc:t.head>t.anchor?-1:1),s;if(!i)return;!t.empty&&(s=this.coordsAt(t.anchor,t.anchor>t.head?-1:1))&&(i={left:Math.min(i.left,s.left),top:Math.min(i.top,s.top),right:Math.max(i.right,s.right),bottom:Math.max(i.bottom,s.bottom)});let r=0,o=0,l=0,a=0;for(let c of this.view.state.facet(Ma).map(f=>f(this.view)))if(c){let{left:f,right:u,top:d,bottom:p}=c;f!=null&&(r=Math.max(r,f)),u!=null&&(o=Math.max(o,u)),d!=null&&(l=Math.max(l,d)),p!=null&&(a=Math.max(a,p))}let h={left:i.left-r,top:i.top-l,right:i.right+o,bottom:i.bottom+a};Jc(this.view.scrollDOM,h,t.head<t.anchor?-1:1,e.x,e.y,e.xMargin,e.yMargin,this.view.textDirection==Y.LTR)}}function ff(n){return n.node.nodeType==1&&n.node.firstChild&&(n.offset==0||n.node.childNodes[n.offset-1].contentEditable=="false")&&(n.offset==n.node.childNodes.length||n.node.childNodes[n.offset].contentEditable=="false")}class oo extends Ge{constructor(e){super(),this.height=e}toDOM(){let e=document.createElement("div");return this.updateDOM(e),e}eq(e){return e.height==this.height}updateDOM(e){return e.style.height=this.height+"px",!0}get estimatedHeight(){return this.height}}function Pa(n){let e=n.observer.selectionRange,t=e.focusNode&&Ea(e.focusNode,e.focusOffset,0);if(!t)return null;let i=n.docView.nearest(t);if(!i)return null;if(i instanceof pe){let s=t;for(;s.parentNode!=i.dom;)s=s.parentNode;let r=s.previousSibling;for(;r&&!q.get(r);)r=r.previousSibling;let o=r?q.get(r).posAtEnd:i.posAtStart;return{from:o,to:o,node:s,text:t}}else{for(;;){let{parent:r}=i;if(!r)return null;if(r instanceof pe)break;i=r}let s=i.posAtStart;return{from:s,to:s+i.length,node:i.dom,text:t}}}function uf(n,e){let t=Pa(n);if(!t)return E.none;let{from:i,to:s,node:r,text:o}=t,l=e.mapPos(i,1),a=Math.max(l,e.mapPos(s,-1)),{state:h}=n,c=r.nodeType==3?r.nodeValue:new Ba([],h).readRange(r.firstChild,null).text;if(a-l<c.length)if(h.doc.sliceString(l,Math.min(h.doc.length,l+c.length),st)==c)a=l+c.length;else if(h.doc.sliceString(Math.max(0,a-c.length),a,st)==c)l=a-c.length;else return E.none;else if(h.doc.sliceString(l,a,st)!=c)return E.none;let f=q.get(r);return f instanceof ca?f=f.widget.topView:f&&(f.parent=null),E.set(E.replace({widget:new df(r,o,f),inclusive:!0}).range(l,a))}class df extends Ge{constructor(e,t,i){super(),this.top=e,this.text=t,this.topView=i}eq(e){return this.top==e.top&&this.text==e.text}toDOM(){return this.top}ignoreEvent(){return!1}get customView(){return ca}}function Ea(n,e,t){for(;;){if(n.nodeType==3)return n;if(n.nodeType==1&&e>0&&t<=0)n=n.childNodes[e-1],e=bi(n);else if(n.nodeType==1&&e<n.childNodes.length&&t>=0)n=n.childNodes[e],e=0;else return null}}function pf(n,e){return n.nodeType!=1?0:(e&&n.childNodes[e-1].contentEditable=="false"?1:0)|(e<n.childNodes.length&&n.childNodes[e].contentEditable=="false"?2:0)}class mf{constructor(){this.changes=[]}compareRange(e,t){Es(e,t,this.changes)}comparePoint(e,t){Es(e,t,this.changes)}}function gf(n,e,t){let i=new mf;return H.compare(n,e,t,i),i.changes}function yf(n,e){for(let t=n;t&&t!=e;t=t.assignedSlot||t.parentNode)if(t.nodeType==1&&t.contentEditable=="false")return!0;return!1}function bf(n,e,t=1){let i=n.charCategorizer(e),s=n.doc.lineAt(e),r=e-s.from;if(s.length==0)return w.cursor(e);r==0?t=1:r==s.length&&(t=-1);let o=r,l=r;t<0?o=ke(s.text,r,!1):l=ke(s.text,r);let a=i(s.text.slice(o,l));for(;o>0;){let h=ke(s.text,o,!1);if(i(s.text.slice(h,o))!=a)break;o=h}for(;l<s.length;){let h=ke(s.text,l);if(i(s.text.slice(l,h))!=a)break;l=h}return w.range(o+s.from,l+s.from)}function wf(n,e){return e.left>n?e.left-n:Math.max(0,n-e.right)}function xf(n,e){return e.top>n?e.top-n:Math.max(0,n-e.bottom)}function Wn(n,e){return n.top<e.bottom-1&&n.bottom>e.top+1}function lo(n,e){return e<n.top?{top:e,left:n.left,right:n.right,bottom:n.bottom}:n}function ao(n,e){return e>n.bottom?{top:n.top,left:n.left,right:n.right,bottom:e}:n}function Ns(n,e,t){let i,s,r,o,l=!1,a,h,c,f;for(let p=n.firstChild;p;p=p.nextSibling){let y=yi(p);for(let g=0;g<y.length;g++){let b=y[g];s&&Wn(s,b)&&(b=lo(ao(b,s.bottom),s.top));let k=wf(e,b),v=xf(t,b);if(k==0&&v==0)return p.nodeType==3?ho(p,e,t):Ns(p,e,t);(!i||o>v||o==v&&r>k)&&(i=p,s=b,r=k,o=v,l=!k||(k>0?g<y.length-1:g>0)),k==0?t>b.bottom&&(!c||c.bottom<b.bottom)?(a=p,c=b):t<b.top&&(!f||f.top>b.top)&&(h=p,f=b):c&&Wn(c,b)?c=ao(c,b.bottom):f&&Wn(f,b)&&(f=lo(f,b.top))}}if(c&&c.bottom>=t?(i=a,s=c):f&&f.top<=t&&(i=h,s=f),!i)return{node:n,offset:0};let u=Math.max(s.left,Math.min(s.right,e));if(i.nodeType==3)return ho(i,u,t);if(l&&i.contentEditable!="false")return Ns(i,u,t);let d=Array.prototype.indexOf.call(n.childNodes,i)+(e>=(s.left+s.right)/2?1:0);return{node:n,offset:d}}function ho(n,e,t){let i=n.nodeValue.length,s=-1,r=1e9,o=0;for(let l=0;l<i;l++){let a=_t(n,l,l+1).getClientRects();for(let h=0;h<a.length;h++){let c=a[h];if(c.top==c.bottom)continue;o||(o=e-c.left);let f=(c.top>t?c.top-t:t-c.bottom)-1;if(c.left-1<=e&&c.right+1>=e&&f<r){let u=e>=(c.left+c.right)/2,d=u;if((A.chrome||A.gecko)&&_t(n,l).getBoundingClientRect().left==c.right&&(d=!u),f<=0)return{node:n,offset:l+(d?1:0)};s=l+(d?1:0),r=f}}}return{node:n,offset:s>-1?s:o>0?n.nodeValue.length:0}}function Ra(n,{x:e,y:t},i,s=-1){var r;let o=n.contentDOM.getBoundingClientRect(),l=o.top+n.viewState.paddingTop,a,{docHeight:h}=n.viewState,c=t-l;if(c<0)return 0;if(c>h)return n.state.doc.length;for(let b=n.defaultLineHeight/2,k=!1;a=n.elementAtHeight(c),a.type!=W.Text;)for(;c=s>0?a.bottom+b:a.top-b,!(c>=0&&c<=h);){if(k)return i?null:0;k=!0,s=-s}t=l+c;let f=a.from;if(f<n.viewport.from)return n.viewport.from==0?0:i?null:co(n,o,a,e,t);if(f>n.viewport.to)return n.viewport.to==n.state.doc.length?n.state.doc.length:i?null:co(n,o,a,e,t);let u=n.dom.ownerDocument,d=n.root.elementFromPoint?n.root:u,p=d.elementFromPoint(e,t);p&&!n.contentDOM.contains(p)&&(p=null),p||(e=Math.max(o.left+1,Math.min(o.right-1,e)),p=d.elementFromPoint(e,t),p&&!n.contentDOM.contains(p)&&(p=null));let y,g=-1;if(p&&((r=n.docView.nearest(p))===null||r===void 0?void 0:r.isEditable)!=!1){if(u.caretPositionFromPoint){let b=u.caretPositionFromPoint(e,t);b&&({offsetNode:y,offset:g}=b)}else if(u.caretRangeFromPoint){let b=u.caretRangeFromPoint(e,t);b&&({startContainer:y,startOffset:g}=b,(!n.contentDOM.contains(y)||A.safari&&kf(y,g,e)||A.chrome&&vf(y,g,e))&&(y=void 0))}}if(!y||!n.docView.dom.contains(y)){let b=pe.find(n.docView,f);if(!b)return c>a.top+a.height/2?a.to:a.from;({node:y,offset:g}=Ns(b.dom,e,t))}return n.docView.posFromDOM(y,g)}function co(n,e,t,i,s){let r=Math.round((i-e.left)*n.defaultCharacterWidth);if(n.lineWrapping&&t.height>n.defaultLineHeight*1.5){let l=Math.floor((s-t.top)/n.defaultLineHeight);r+=l*n.viewState.heightOracle.lineLength}let o=n.state.sliceDoc(t.from,t.to);return t.from+vs(o,r,n.state.tabSize)}function kf(n,e,t){let i;if(n.nodeType!=3||e!=(i=n.nodeValue.length))return!1;for(let s=n.nextSibling;s;s=s.nextSibling)if(s.nodeType!=1||s.nodeName!="BR")return!1;return _t(n,i-1,i).getBoundingClientRect().left>t}function vf(n,e,t){if(e!=0)return!1;for(let s=n;;){let r=s.parentNode;if(!r||r.nodeType!=1||r.firstChild!=s)return!1;if(r.classList.contains("cm-line"))break;s=r}let i=n.nodeType==1?n.getBoundingClientRect():_t(n,0,Math.max(n.nodeValue.length,1)).getBoundingClientRect();return t-i.left>5}function Sf(n,e,t,i){let s=n.state.doc.lineAt(e.head),r=!i||!n.lineWrapping?null:n.coordsAtPos(e.assoc<0&&e.head>s.from?e.head-1:e.head);if(r){let a=n.dom.getBoundingClientRect(),h=n.textDirectionAt(s.from),c=n.posAtCoords({x:t==(h==Y.LTR)?a.right-1:a.left+1,y:(r.top+r.bottom)/2});if(c!=null)return w.cursor(c,t?-1:1)}let o=pe.find(n.docView,e.head),l=o?t?o.posAtEnd:o.posAtStart:t?s.to:s.from;return w.cursor(l,t?-1:1)}function fo(n,e,t,i){let s=n.state.doc.lineAt(e.head),r=n.bidiSpans(s),o=n.textDirectionAt(s.from);for(let l=e,a=null;;){let h=cf(s,r,o,l,t),c=Oa;if(!h){if(s.number==(t?n.state.doc.lines:1))return l;c=`
`,s=n.state.doc.line(s.number+(t?1:-1)),r=n.bidiSpans(s),h=w.cursor(t?s.from:s.to)}if(a){if(!a(c))return l}else{if(!i)return h;a=i(c)}l=h}}function Cf(n,e,t){let i=n.state.charCategorizer(e),s=i(t);return r=>{let o=i(r);return s==Ae.Space&&(s=o),s==o}}function Af(n,e,t,i){let s=e.head,r=t?1:-1;if(s==(t?n.state.doc.length:0))return w.cursor(s,e.assoc);let o=e.goalColumn,l,a=n.contentDOM.getBoundingClientRect(),h=n.coordsAtPos(s),c=n.documentTop;if(h)o==null&&(o=h.left-a.left),l=r<0?h.top:h.bottom;else{let d=n.viewState.lineBlockAt(s);o==null&&(o=Math.min(a.right-a.left,n.defaultCharacterWidth*(s-d.from))),l=(r<0?d.top:d.bottom)+c}let f=a.left+o,u=i??n.defaultLineHeight>>1;for(let d=0;;d+=10){let p=l+(u+d)*r,y=Ra(n,{x:f,y:p},!1,r);if(p<a.top||p>a.bottom||(r<0?y<s:y>s))return w.cursor(y,e.assoc,void 0,o)}}function zn(n,e,t){let i=n.state.facet(Aa).map(s=>s(n));for(;;){let s=!1;for(let r of i)r.between(t.from-1,t.from+1,(o,l,a)=>{t.from>o&&t.from<l&&(t=e.head>t.from?w.cursor(o,1):w.cursor(l,-1),s=!0)});if(!s)return t}}class Mf{constructor(e){this.lastKeyCode=0,this.lastKeyTime=0,this.lastTouchTime=0,this.lastFocusTime=0,this.lastScrollTop=0,this.lastScrollLeft=0,this.chromeScrollHack=-1,this.pendingIOSKey=void 0,this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastEscPress=0,this.lastContextMenu=0,this.scrollHandlers=[],this.registeredEvents=[],this.customHandlers=[],this.composing=-1,this.compositionFirstChange=null,this.compositionEndedAt=0,this.mouseSelection=null;for(let t in ne){let i=ne[t];e.contentDOM.addEventListener(t,s=>{!uo(e,s)||this.ignoreDuringComposition(s)||t=="keydown"&&this.keydown(e,s)||(this.mustFlushObserver(s)&&e.observer.forceFlush(),this.runCustomHandlers(t,e,s)?s.preventDefault():i(e,s))},Vs[t]),this.registeredEvents.push(t)}A.chrome&&A.chrome_version==102&&e.scrollDOM.addEventListener("wheel",()=>{this.chromeScrollHack<0?e.contentDOM.style.pointerEvents="none":window.clearTimeout(this.chromeScrollHack),this.chromeScrollHack=setTimeout(()=>{this.chromeScrollHack=-1,e.contentDOM.style.pointerEvents=""},100)},{passive:!0}),this.notifiedFocused=e.hasFocus,A.safari&&e.contentDOM.addEventListener("input",()=>null)}setSelectionOrigin(e){this.lastSelectionOrigin=e,this.lastSelectionTime=Date.now()}ensureHandlers(e,t){var i;let s;this.customHandlers=[];for(let r of t)if(s=(i=r.update(e).spec)===null||i===void 0?void 0:i.domEventHandlers){this.customHandlers.push({plugin:r.value,handlers:s});for(let o in s)this.registeredEvents.indexOf(o)<0&&o!="scroll"&&(this.registeredEvents.push(o),e.contentDOM.addEventListener(o,l=>{uo(e,l)&&this.runCustomHandlers(o,e,l)&&l.preventDefault()}))}}runCustomHandlers(e,t,i){for(let s of this.customHandlers){let r=s.handlers[e];if(r)try{if(r.call(s.plugin,i,t)||i.defaultPrevented)return!0}catch(o){Ee(t.state,o)}}return!1}runScrollHandlers(e,t){this.lastScrollTop=e.scrollDOM.scrollTop,this.lastScrollLeft=e.scrollDOM.scrollLeft;for(let i of this.customHandlers){let s=i.handlers.scroll;if(s)try{s.call(i.plugin,t,e)}catch(r){Ee(e.state,r)}}}keydown(e,t){if(this.lastKeyCode=t.keyCode,this.lastKeyTime=Date.now(),t.keyCode==9&&Date.now()<this.lastEscPress+2e3)return!0;if(A.android&&A.chrome&&!t.synthetic&&(t.keyCode==13||t.keyCode==8))return e.observer.delayAndroidKey(t.key,t.keyCode),!0;let i;return A.ios&&!t.synthetic&&!t.altKey&&!t.metaKey&&((i=La.find(s=>s.keyCode==t.keyCode))&&!t.ctrlKey||Df.indexOf(t.key)>-1&&t.ctrlKey&&!t.shiftKey)?(this.pendingIOSKey=i||t,setTimeout(()=>this.flushIOSKey(e),250),!0):!1}flushIOSKey(e){let t=this.pendingIOSKey;return t?(this.pendingIOSKey=void 0,Ht(e.contentDOM,t.key,t.keyCode)):!1}ignoreDuringComposition(e){return/^key/.test(e.type)?this.composing>0?!0:A.safari&&!A.ios&&Date.now()-this.compositionEndedAt<100?(this.compositionEndedAt=0,!0):!1:!1}mustFlushObserver(e){return e.type=="keydown"&&e.keyCode!=229}startMouseSelection(e){this.mouseSelection&&this.mouseSelection.destroy(),this.mouseSelection=e}update(e){this.mouseSelection&&this.mouseSelection.update(e),e.transactions.length&&(this.lastKeyCode=this.lastSelectionTime=0)}destroy(){this.mouseSelection&&this.mouseSelection.destroy()}}const La=[{key:"Backspace",keyCode:8,inputType:"deleteContentBackward"},{key:"Enter",keyCode:13,inputType:"insertParagraph"},{key:"Delete",keyCode:46,inputType:"deleteContentForward"}],Df="dthko",Ia=[16,17,18,20,91,92,224,225];class Tf{constructor(e,t,i,s){this.view=e,this.style=i,this.mustSelect=s,this.lastEvent=t;let r=e.contentDOM.ownerDocument;r.addEventListener("mousemove",this.move=this.move.bind(this)),r.addEventListener("mouseup",this.up=this.up.bind(this)),this.extend=t.shiftKey,this.multiple=e.state.facet(N.allowMultipleSelections)&&Of(e,t),this.dragMove=Bf(e,t),this.dragging=Pf(e,t)&&Ha(t)==1?null:!1,this.dragging===!1&&(t.preventDefault(),this.select(t))}move(e){if(e.buttons==0)return this.destroy();this.dragging===!1&&this.select(this.lastEvent=e)}up(e){this.dragging==null&&this.select(this.lastEvent),this.dragging||e.preventDefault(),this.destroy()}destroy(){let e=this.view.contentDOM.ownerDocument;e.removeEventListener("mousemove",this.move),e.removeEventListener("mouseup",this.up),this.view.inputState.mouseSelection=null}select(e){let t=this.style.get(e,this.extend,this.multiple);(this.mustSelect||!t.eq(this.view.state.selection)||t.main.assoc!=this.view.state.selection.main.assoc)&&this.view.dispatch({selection:t,userEvent:"select.pointer",scrollIntoView:!0}),this.mustSelect=!1}update(e){e.docChanged&&this.dragging&&(this.dragging=this.dragging.map(e.changes)),this.style.update(e)&&setTimeout(()=>this.select(this.lastEvent),20)}}function Of(n,e){let t=n.state.facet(ga);return t.length?t[0](e):A.mac?e.metaKey:e.ctrlKey}function Bf(n,e){let t=n.state.facet(ya);return t.length?t[0](e):A.mac?!e.altKey:!e.ctrlKey}function Pf(n,e){let{main:t}=n.state.selection;if(t.empty)return!1;let i=on(n.root);if(!i||i.rangeCount==0)return!0;let s=i.getRangeAt(0).getClientRects();for(let r=0;r<s.length;r++){let o=s[r];if(o.left<=e.clientX&&o.right>=e.clientX&&o.top<=e.clientY&&o.bottom>=e.clientY)return!0}return!1}function uo(n,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let t=e.target,i;t!=n.contentDOM;t=t.parentNode)if(!t||t.nodeType==11||(i=q.get(t))&&i.ignoreEvent(e))return!1;return!0}const ne=Object.create(null),Vs=Object.create(null),Na=A.ie&&A.ie_version<15||A.ios&&A.webkit_version<604;function Ef(n){let e=n.dom.parentNode;if(!e)return;let t=e.appendChild(document.createElement("textarea"));t.style.cssText="position: fixed; left: -10000px; top: 10px",t.focus(),setTimeout(()=>{n.focus(),t.remove(),Va(n,t.value)},50)}function Va(n,e){let{state:t}=n,i,s=1,r=t.toText(e),o=r.lines==t.selection.ranges.length;if(Fs!=null&&t.selection.ranges.every(a=>a.empty)&&Fs==r.toString()){let a=-1;i=t.changeByRange(h=>{let c=t.doc.lineAt(h.from);if(c.from==a)return{range:h};a=c.from;let f=t.toText((o?r.line(s++).text:e)+t.lineBreak);return{changes:{from:c.from,insert:f},range:w.cursor(h.from+f.length)}})}else o?i=t.changeByRange(a=>{let h=r.line(s++);return{changes:{from:a.from,to:a.to,insert:h.text},range:w.cursor(a.from+h.length)}}):i=t.replaceSelection(r);n.dispatch(i,{userEvent:"input.paste",scrollIntoView:!0})}ne.keydown=(n,e)=>{n.inputState.setSelectionOrigin("select"),e.keyCode==27?n.inputState.lastEscPress=Date.now():Ia.indexOf(e.keyCode)<0&&(n.inputState.lastEscPress=0)};ne.touchstart=(n,e)=>{n.inputState.lastTouchTime=Date.now(),n.inputState.setSelectionOrigin("select.pointer")};ne.touchmove=n=>{n.inputState.setSelectionOrigin("select.pointer")};Vs.touchstart=Vs.touchmove={passive:!0};ne.mousedown=(n,e)=>{if(n.observer.flush(),n.inputState.lastTouchTime>Date.now()-2e3)return;let t=null;for(let i of n.state.facet(ba))if(t=i(n,e),t)break;if(!t&&e.button==0&&(t=If(n,e)),t){let i=n.root.activeElement!=n.contentDOM;i&&n.observer.ignore(()=>na(n.contentDOM)),n.inputState.startMouseSelection(new Tf(n,e,t,i))}};function po(n,e,t,i){if(i==1)return w.cursor(e,t);if(i==2)return bf(n.state,e,t);{let s=pe.find(n.docView,e),r=n.state.doc.lineAt(s?s.posAtEnd:e),o=s?s.posAtStart:r.from,l=s?s.posAtEnd:r.to;return l<n.state.doc.length&&l==r.to&&l++,w.range(o,l)}}let Fa=(n,e)=>n>=e.top&&n<=e.bottom,mo=(n,e,t)=>Fa(e,t)&&n>=t.left&&n<=t.right;function Rf(n,e,t,i){let s=pe.find(n.docView,e);if(!s)return 1;let r=e-s.posAtStart;if(r==0)return 1;if(r==s.length)return-1;let o=s.coordsAt(r,-1);if(o&&mo(t,i,o))return-1;let l=s.coordsAt(r,1);return l&&mo(t,i,l)?1:o&&Fa(i,o)?-1:1}function go(n,e){let t=n.posAtCoords({x:e.clientX,y:e.clientY},!1);return{pos:t,bias:Rf(n,t,e.clientX,e.clientY)}}const Lf=A.ie&&A.ie_version<=11;let yo=null,bo=0,wo=0;function Ha(n){if(!Lf)return n.detail;let e=yo,t=wo;return yo=n,wo=Date.now(),bo=!e||t>Date.now()-400&&Math.abs(e.clientX-n.clientX)<2&&Math.abs(e.clientY-n.clientY)<2?(bo+1)%3:1}function If(n,e){let t=go(n,e),i=Ha(e),s=n.state.selection,r=t,o=e;return{update(l){l.docChanged&&(t.pos=l.changes.mapPos(t.pos),s=s.map(l.changes),o=null)},get(l,a,h){let c;o&&l.clientX==o.clientX&&l.clientY==o.clientY?c=r:(c=r=go(n,l),o=l);let f=po(n,c.pos,c.bias,i);if(t.pos!=c.pos&&!a){let u=po(n,t.pos,t.bias,i),d=Math.min(u.from,f.from),p=Math.max(u.to,f.to);f=d<f.from?w.range(d,p):w.range(p,d)}return a?s.replaceRange(s.main.extend(f.from,f.to)):h&&s.ranges.length>1&&s.ranges.some(u=>u.eq(f))?Nf(s,f):h?s.addRange(f):w.create([f])}}}function Nf(n,e){for(let t=0;;t++)if(n.ranges[t].eq(e))return w.create(n.ranges.slice(0,t).concat(n.ranges.slice(t+1)),n.mainIndex==t?0:n.mainIndex-(n.mainIndex>t?1:0))}ne.dragstart=(n,e)=>{let{selection:{main:t}}=n.state,{mouseSelection:i}=n.inputState;i&&(i.dragging=t),e.dataTransfer&&(e.dataTransfer.setData("Text",n.state.sliceDoc(t.from,t.to)),e.dataTransfer.effectAllowed="copyMove")};function xo(n,e,t,i){if(!t)return;let s=n.posAtCoords({x:e.clientX,y:e.clientY},!1);e.preventDefault();let{mouseSelection:r}=n.inputState,o=i&&r&&r.dragging&&r.dragMove?{from:r.dragging.from,to:r.dragging.to}:null,l={from:s,insert:t},a=n.state.changes(o?[o,l]:l);n.focus(),n.dispatch({changes:a,selection:{anchor:a.mapPos(s,-1),head:a.mapPos(s,1)},userEvent:o?"move.drop":"input.drop"})}ne.drop=(n,e)=>{if(!e.dataTransfer)return;if(n.state.readOnly)return e.preventDefault();let t=e.dataTransfer.files;if(t&&t.length){e.preventDefault();let i=Array(t.length),s=0,r=()=>{++s==t.length&&xo(n,e,i.filter(o=>o!=null).join(n.state.lineBreak),!1)};for(let o=0;o<t.length;o++){let l=new FileReader;l.onerror=r,l.onload=()=>{/[\x00-\x08\x0e-\x1f]{2}/.test(l.result)||(i[o]=l.result),r()},l.readAsText(t[o])}}else xo(n,e,e.dataTransfer.getData("Text"),!0)};ne.paste=(n,e)=>{if(n.state.readOnly)return e.preventDefault();n.observer.flush();let t=Na?null:e.clipboardData;t?(Va(n,t.getData("text/plain")),e.preventDefault()):Ef(n)};function Vf(n,e){let t=n.dom.parentNode;if(!t)return;let i=t.appendChild(document.createElement("textarea"));i.style.cssText="position: fixed; left: -10000px; top: 10px",i.value=e,i.focus(),i.selectionEnd=e.length,i.selectionStart=0,setTimeout(()=>{i.remove(),n.focus()},50)}function Ff(n){let e=[],t=[],i=!1;for(let s of n.selection.ranges)s.empty||(e.push(n.sliceDoc(s.from,s.to)),t.push(s));if(!e.length){let s=-1;for(let{from:r}of n.selection.ranges){let o=n.doc.lineAt(r);o.number>s&&(e.push(o.text),t.push({from:o.from,to:Math.min(n.doc.length,o.to+1)})),s=o.number}i=!0}return{text:e.join(n.lineBreak),ranges:t,linewise:i}}let Fs=null;ne.copy=ne.cut=(n,e)=>{let{text:t,ranges:i,linewise:s}=Ff(n.state);if(!t&&!s)return;Fs=s?t:null;let r=Na?null:e.clipboardData;r?(e.preventDefault(),r.clearData(),r.setData("text/plain",t)):Vf(n,t),e.type=="cut"&&!n.state.readOnly&&n.dispatch({changes:i,scrollIntoView:!0,userEvent:"delete.cut"})};function Wa(n){setTimeout(()=>{n.hasFocus!=n.inputState.notifiedFocused&&n.update([])},10)}ne.focus=n=>{n.inputState.lastFocusTime=Date.now(),!n.scrollDOM.scrollTop&&(n.inputState.lastScrollTop||n.inputState.lastScrollLeft)&&(n.scrollDOM.scrollTop=n.inputState.lastScrollTop,n.scrollDOM.scrollLeft=n.inputState.lastScrollLeft),Wa(n)};ne.blur=n=>{n.observer.clearSelectionRange(),Wa(n)};ne.compositionstart=ne.compositionupdate=n=>{n.inputState.compositionFirstChange==null&&(n.inputState.compositionFirstChange=!0),n.inputState.composing<0&&(n.inputState.composing=0)};ne.compositionend=n=>{n.inputState.composing=-1,n.inputState.compositionEndedAt=Date.now(),n.inputState.compositionFirstChange=null,A.chrome&&A.android&&n.observer.flushSoon(),setTimeout(()=>{n.inputState.composing<0&&n.docView.compositionDeco.size&&n.update([])},50)};ne.contextmenu=n=>{n.inputState.lastContextMenu=Date.now()};ne.beforeinput=(n,e)=>{var t;let i;if(A.chrome&&A.android&&(i=La.find(s=>s.inputType==e.inputType))&&(n.observer.delayAndroidKey(i.key,i.keyCode),i.key=="Backspace"||i.key=="Delete")){let s=((t=window.visualViewport)===null||t===void 0?void 0:t.height)||0;setTimeout(()=>{var r;(((r=window.visualViewport)===null||r===void 0?void 0:r.height)||0)>s+10&&n.hasFocus&&(n.contentDOM.blur(),n.focus())},100)}};const ko=["pre-wrap","normal","pre-line","break-spaces"];class Hf{constructor(){this.doc=V.empty,this.lineWrapping=!1,this.heightSamples={},this.lineHeight=14,this.charWidth=7,this.lineLength=30,this.heightChanged=!1}heightForGap(e,t){let i=this.doc.lineAt(t).number-this.doc.lineAt(e).number+1;return this.lineWrapping&&(i+=Math.ceil((t-e-i*this.lineLength*.5)/this.lineLength)),this.lineHeight*i}heightForLine(e){return this.lineWrapping?(1+Math.max(0,Math.ceil((e-this.lineLength)/(this.lineLength-5))))*this.lineHeight:this.lineHeight}setDoc(e){return this.doc=e,this}mustRefreshForWrapping(e){return ko.indexOf(e)>-1!=this.lineWrapping}mustRefreshForHeights(e){let t=!1;for(let i=0;i<e.length;i++){let s=e[i];s<0?i++:this.heightSamples[Math.floor(s*10)]||(t=!0,this.heightSamples[Math.floor(s*10)]=!0)}return t}refresh(e,t,i,s,r){let o=ko.indexOf(e)>-1,l=Math.round(t)!=Math.round(this.lineHeight)||this.lineWrapping!=o;if(this.lineWrapping=o,this.lineHeight=t,this.charWidth=i,this.lineLength=s,l){this.heightSamples={};for(let a=0;a<r.length;a++){let h=r[a];h<0?a++:this.heightSamples[Math.floor(h*10)]=!0}}return l}}class Wf{constructor(e,t){this.from=e,this.heights=t,this.index=0}get more(){return this.index<this.heights.length}}class rt{constructor(e,t,i,s,r){this.from=e,this.length=t,this.top=i,this.height=s,this.type=r}get to(){return this.from+this.length}get bottom(){return this.top+this.height}join(e){let t=(Array.isArray(this.type)?this.type:[this]).concat(Array.isArray(e.type)?e.type:[e]);return new rt(this.from,this.length+e.length,this.top,this.height+e.height,t)}}var _=function(n){return n[n.ByPos=0]="ByPos",n[n.ByHeight=1]="ByHeight",n[n.ByPosNoHeight=2]="ByPosNoHeight",n}(_||(_={}));const en=.001;class me{constructor(e,t,i=2){this.length=e,this.height=t,this.flags=i}get outdated(){return(this.flags&2)>0}set outdated(e){this.flags=(e?2:0)|this.flags&-3}setHeight(e,t){this.height!=t&&(Math.abs(this.height-t)>en&&(e.heightChanged=!0),this.height=t)}replace(e,t,i){return me.of(i)}decomposeLeft(e,t){t.push(this)}decomposeRight(e,t){t.push(this)}applyChanges(e,t,i,s){let r=this;for(let o=s.length-1;o>=0;o--){let{fromA:l,toA:a,fromB:h,toB:c}=s[o],f=r.lineAt(l,_.ByPosNoHeight,t,0,0),u=f.to>=a?f:r.lineAt(a,_.ByPosNoHeight,t,0,0);for(c+=u.to-a,a=u.to;o>0&&f.from<=s[o-1].toA;)l=s[o-1].fromA,h=s[o-1].fromB,o--,l<f.from&&(f=r.lineAt(l,_.ByPosNoHeight,t,0,0));h+=f.from-l,l=f.from;let d=fr.build(i,e,h,c);r=r.replace(l,a,d)}return r.updateHeight(i,0)}static empty(){return new we(0,0)}static of(e){if(e.length==1)return e[0];let t=0,i=e.length,s=0,r=0;for(;;)if(t==i)if(s>r*2){let l=e[t-1];l.break?e.splice(--t,1,l.left,null,l.right):e.splice(--t,1,l.left,l.right),i+=1+l.break,s-=l.size}else if(r>s*2){let l=e[i];l.break?e.splice(i,1,l.left,null,l.right):e.splice(i,1,l.left,l.right),i+=2+l.break,r-=l.size}else break;else if(s<r){let l=e[t++];l&&(s+=l.size)}else{let l=e[--i];l&&(r+=l.size)}let o=0;return e[t-1]==null?(o=1,t--):e[t]==null&&(o=1,i++),new zf(me.of(e.slice(0,t)),o,me.of(e.slice(i)))}}me.prototype.size=1;class za extends me{constructor(e,t,i){super(e,t),this.type=i}blockAt(e,t,i,s){return new rt(s,this.length,i,this.height,this.type)}lineAt(e,t,i,s,r){return this.blockAt(0,i,s,r)}forEachLine(e,t,i,s,r,o){e<=r+this.length&&t>=r&&o(this.blockAt(0,i,s,r))}updateHeight(e,t=0,i=!1,s){return s&&s.from<=t&&s.more&&this.setHeight(e,s.heights[s.index++]),this.outdated=!1,this}toString(){return`block(${this.length})`}}class we extends za{constructor(e,t){super(e,t,W.Text),this.collapsed=0,this.widgetHeight=0}replace(e,t,i){let s=i[0];return i.length==1&&(s instanceof we||s instanceof re&&s.flags&4)&&Math.abs(this.length-s.length)<10?(s instanceof re?s=new we(s.length,this.height):s.height=this.height,this.outdated||(s.outdated=!1),s):me.of(i)}updateHeight(e,t=0,i=!1,s){return s&&s.from<=t&&s.more?this.setHeight(e,s.heights[s.index++]):(i||this.outdated)&&this.setHeight(e,Math.max(this.widgetHeight,e.heightForLine(this.length-this.collapsed))),this.outdated=!1,this}toString(){return`line(${this.length}${this.collapsed?-this.collapsed:""}${this.widgetHeight?":"+this.widgetHeight:""})`}}class re extends me{constructor(e){super(e,0)}lines(e,t){let i=e.lineAt(t).number,s=e.lineAt(t+this.length).number;return{firstLine:i,lastLine:s,lineHeight:this.height/(s-i+1)}}blockAt(e,t,i,s){let{firstLine:r,lastLine:o,lineHeight:l}=this.lines(t,s),a=Math.max(0,Math.min(o-r,Math.floor((e-i)/l))),{from:h,length:c}=t.line(r+a);return new rt(h,c,i+l*a,l,W.Text)}lineAt(e,t,i,s,r){if(t==_.ByHeight)return this.blockAt(e,i,s,r);if(t==_.ByPosNoHeight){let{from:f,to:u}=i.lineAt(e);return new rt(f,u-f,0,0,W.Text)}let{firstLine:o,lineHeight:l}=this.lines(i,r),{from:a,length:h,number:c}=i.lineAt(e);return new rt(a,h,s+l*(c-o),l,W.Text)}forEachLine(e,t,i,s,r,o){let{firstLine:l,lineHeight:a}=this.lines(i,r);for(let h=Math.max(e,r),c=Math.min(r+this.length,t);h<=c;){let f=i.lineAt(h);h==e&&(s+=a*(f.number-l)),o(new rt(f.from,f.length,s,a,W.Text)),s+=a,h=f.to+1}}replace(e,t,i){let s=this.length-t;if(s>0){let r=i[i.length-1];r instanceof re?i[i.length-1]=new re(r.length+s):i.push(null,new re(s-1))}if(e>0){let r=i[0];r instanceof re?i[0]=new re(e+r.length):i.unshift(new re(e-1),null)}return me.of(i)}decomposeLeft(e,t){t.push(new re(e-1),null)}decomposeRight(e,t){t.push(null,new re(this.length-e-1))}updateHeight(e,t=0,i=!1,s){let r=t+this.length;if(s&&s.from<=t+this.length&&s.more){let o=[],l=Math.max(t,s.from),a=-1,h=e.heightChanged;for(s.from>t&&o.push(new re(s.from-t-1).updateHeight(e,t));l<=r&&s.more;){let f=e.doc.lineAt(l).length;o.length&&o.push(null);let u=s.heights[s.index++];a==-1?a=u:Math.abs(u-a)>=en&&(a=-2);let d=new we(f,u);d.outdated=!1,o.push(d),l+=f+1}l<=r&&o.push(null,new re(r-l).updateHeight(e,l));let c=me.of(o);return e.heightChanged=h||a<0||Math.abs(c.height-this.height)>=en||Math.abs(a-this.lines(e.doc,t).lineHeight)>=en,c}else(i||this.outdated)&&(this.setHeight(e,e.heightForGap(t,t+this.length)),this.outdated=!1);return this}toString(){return`gap(${this.length})`}}class zf extends me{constructor(e,t,i){super(e.length+t+i.length,e.height+i.height,t|(e.outdated||i.outdated?2:0)),this.left=e,this.right=i,this.size=e.size+i.size}get break(){return this.flags&1}blockAt(e,t,i,s){let r=i+this.left.height;return e<r?this.left.blockAt(e,t,i,s):this.right.blockAt(e,t,r,s+this.left.length+this.break)}lineAt(e,t,i,s,r){let o=s+this.left.height,l=r+this.left.length+this.break,a=t==_.ByHeight?e<o:e<l,h=a?this.left.lineAt(e,t,i,s,r):this.right.lineAt(e,t,i,o,l);if(this.break||(a?h.to<l:h.from>l))return h;let c=t==_.ByPosNoHeight?_.ByPosNoHeight:_.ByPos;return a?h.join(this.right.lineAt(l,c,i,o,l)):this.left.lineAt(l,c,i,s,r).join(h)}forEachLine(e,t,i,s,r,o){let l=s+this.left.height,a=r+this.left.length+this.break;if(this.break)e<a&&this.left.forEachLine(e,t,i,s,r,o),t>=a&&this.right.forEachLine(e,t,i,l,a,o);else{let h=this.lineAt(a,_.ByPos,i,s,r);e<h.from&&this.left.forEachLine(e,h.from-1,i,s,r,o),h.to>=e&&h.from<=t&&o(h),t>h.to&&this.right.forEachLine(h.to+1,t,i,l,a,o)}}replace(e,t,i){let s=this.left.length+this.break;if(t<s)return this.balanced(this.left.replace(e,t,i),this.right);if(e>this.left.length)return this.balanced(this.left,this.right.replace(e-s,t-s,i));let r=[];e>0&&this.decomposeLeft(e,r);let o=r.length;for(let l of i)r.push(l);if(e>0&&vo(r,o-1),t<this.length){let l=r.length;this.decomposeRight(t,r),vo(r,l)}return me.of(r)}decomposeLeft(e,t){let i=this.left.length;if(e<=i)return this.left.decomposeLeft(e,t);t.push(this.left),this.break&&(i++,e>=i&&t.push(null)),e>i&&this.right.decomposeLeft(e-i,t)}decomposeRight(e,t){let i=this.left.length,s=i+this.break;if(e>=s)return this.right.decomposeRight(e-s,t);e<i&&this.left.decomposeRight(e,t),this.break&&e<s&&t.push(null),t.push(this.right)}balanced(e,t){return e.size>2*t.size||t.size>2*e.size?me.of(this.break?[e,null,t]:[e,t]):(this.left=e,this.right=t,this.height=e.height+t.height,this.outdated=e.outdated||t.outdated,this.size=e.size+t.size,this.length=e.length+this.break+t.length,this)}updateHeight(e,t=0,i=!1,s){let{left:r,right:o}=this,l=t+r.length+this.break,a=null;return s&&s.from<=t+r.length&&s.more?a=r=r.updateHeight(e,t,i,s):r.updateHeight(e,t,i),s&&s.from<=l+o.length&&s.more?a=o=o.updateHeight(e,l,i,s):o.updateHeight(e,l,i),a?this.balanced(r,o):(this.height=this.left.height+this.right.height,this.outdated=!1,this)}toString(){return this.left+(this.break?" ":"-")+this.right}}function vo(n,e){let t,i;n[e]==null&&(t=n[e-1])instanceof re&&(i=n[e+1])instanceof re&&n.splice(e-1,3,new re(t.length+1+i.length))}const _f=5;class fr{constructor(e,t){this.pos=e,this.oracle=t,this.nodes=[],this.lineStart=-1,this.lineEnd=-1,this.covering=null,this.writtenTo=e}get isCovered(){return this.covering&&this.nodes[this.nodes.length-1]==this.covering}span(e,t){if(this.lineStart>-1){let i=Math.min(t,this.lineEnd),s=this.nodes[this.nodes.length-1];s instanceof we?s.length+=i-this.pos:(i>this.pos||!this.isCovered)&&this.nodes.push(new we(i-this.pos,-1)),this.writtenTo=i,t>i&&(this.nodes.push(null),this.writtenTo++,this.lineStart=-1)}this.pos=t}point(e,t,i){if(e<t||i.heightRelevant){let s=i.widget?i.widget.estimatedHeight:0;s<0&&(s=this.oracle.lineHeight);let r=t-e;i.block?this.addBlock(new za(r,s,i.type)):(r||s>=_f)&&this.addLineDeco(s,r)}else t>e&&this.span(e,t);this.lineEnd>-1&&this.lineEnd<this.pos&&(this.lineEnd=this.oracle.doc.lineAt(this.pos).to)}enterLine(){if(this.lineStart>-1)return;let{from:e,to:t}=this.oracle.doc.lineAt(this.pos);this.lineStart=e,this.lineEnd=t,this.writtenTo<e&&((this.writtenTo<e-1||this.nodes[this.nodes.length-1]==null)&&this.nodes.push(this.blankContent(this.writtenTo,e-1)),this.nodes.push(null)),this.pos>e&&this.nodes.push(new we(this.pos-e,-1)),this.writtenTo=this.pos}blankContent(e,t){let i=new re(t-e);return this.oracle.doc.lineAt(e).to==t&&(i.flags|=4),i}ensureLine(){this.enterLine();let e=this.nodes.length?this.nodes[this.nodes.length-1]:null;if(e instanceof we)return e;let t=new we(0,-1);return this.nodes.push(t),t}addBlock(e){this.enterLine(),e.type==W.WidgetAfter&&!this.isCovered&&this.ensureLine(),this.nodes.push(e),this.writtenTo=this.pos=this.pos+e.length,e.type!=W.WidgetBefore&&(this.covering=e)}addLineDeco(e,t){let i=this.ensureLine();i.length+=t,i.collapsed+=t,i.widgetHeight=Math.max(i.widgetHeight,e),this.writtenTo=this.pos=this.pos+t}finish(e){let t=this.nodes.length==0?null:this.nodes[this.nodes.length-1];this.lineStart>-1&&!(t instanceof we)&&!this.isCovered?this.nodes.push(new we(0,-1)):(this.writtenTo<this.pos||t==null)&&this.nodes.push(this.blankContent(this.writtenTo,this.pos));let i=e;for(let s of this.nodes)s instanceof we&&s.updateHeight(this.oracle,i),i+=s?s.length:1;return this.nodes}static build(e,t,i,s){let r=new fr(i,e);return H.spans(t,i,s,r,0),r.finish(i)}}function qf(n,e,t){let i=new jf;return H.compare(n,e,t,i,0),i.changes}class jf{constructor(){this.changes=[]}compareRange(){}comparePoint(e,t,i,s){(e<t||i&&i.heightRelevant||s&&s.heightRelevant)&&Es(e,t,this.changes,5)}}function Kf(n,e){let t=n.getBoundingClientRect(),i=n.ownerDocument,s=i.defaultView||window,r=Math.max(0,t.left),o=Math.min(s.innerWidth,t.right),l=Math.max(0,t.top),a=Math.min(s.innerHeight,t.bottom);for(let h=n.parentNode;h&&h!=i.body;)if(h.nodeType==1){let c=h,f=window.getComputedStyle(c);if((c.scrollHeight>c.clientHeight||c.scrollWidth>c.clientWidth)&&f.overflow!="visible"){let u=c.getBoundingClientRect();r=Math.max(r,u.left),o=Math.min(o,u.right),l=Math.max(l,u.top),a=h==n.parentNode?u.bottom:Math.min(a,u.bottom)}h=f.position=="absolute"||f.position=="fixed"?c.offsetParent:c.parentNode}else if(h.nodeType==11)h=h.host;else break;return{left:r-t.left,right:Math.max(r,o)-t.left,top:l-(t.top+e),bottom:Math.max(l,a)-(t.top+e)}}function Uf(n,e){let t=n.getBoundingClientRect();return{left:0,right:t.right-t.left,top:e,bottom:t.bottom-(t.top+e)}}class _n{constructor(e,t,i){this.from=e,this.to=t,this.size=i}static same(e,t){if(e.length!=t.length)return!1;for(let i=0;i<e.length;i++){let s=e[i],r=t[i];if(s.from!=r.from||s.to!=r.to||s.size!=r.size)return!1}return!0}draw(e){return E.replace({widget:new Gf(this.size,e)}).range(this.from,this.to)}}class Gf extends Ge{constructor(e,t){super(),this.size=e,this.vertical=t}eq(e){return e.size==this.size&&e.vertical==this.vertical}toDOM(){let e=document.createElement("div");return this.vertical?e.style.height=this.size+"px":(e.style.width=this.size+"px",e.style.height="2px",e.style.display="inline-block"),e}get estimatedHeight(){return this.vertical?this.size:-1}}class So{constructor(e){this.state=e,this.pixelViewport={left:0,right:window.innerWidth,top:0,bottom:0},this.inView=!0,this.paddingTop=0,this.paddingBottom=0,this.contentDOMWidth=0,this.contentDOMHeight=0,this.editorHeight=0,this.editorWidth=0,this.heightOracle=new Hf,this.scaler=Co,this.scrollTarget=null,this.printing=!1,this.mustMeasureContent=!0,this.defaultTextDirection=Y.LTR,this.visibleRanges=[],this.mustEnforceCursorAssoc=!1,this.stateDeco=e.facet(wi).filter(t=>typeof t!="function"),this.heightMap=me.empty().applyChanges(this.stateDeco,V.empty,this.heightOracle.setDoc(e.doc),[new Ke(0,0,0,e.doc.length)]),this.viewport=this.getViewport(0,null),this.updateViewportLines(),this.updateForViewport(),this.lineGaps=this.ensureLineGaps([]),this.lineGapDeco=E.set(this.lineGaps.map(t=>t.draw(!1))),this.computeVisibleRanges()}updateForViewport(){let e=[this.viewport],{main:t}=this.state.selection;for(let i=0;i<=1;i++){let s=i?t.head:t.anchor;if(!e.some(({from:r,to:o})=>s>=r&&s<=o)){let{from:r,to:o}=this.lineBlockAt(s);e.push(new Ni(r,o))}}this.viewports=e.sort((i,s)=>i.from-s.from),this.scaler=this.heightMap.height<=7e6?Co:new Yf(this.heightOracle.doc,this.heightMap,this.viewports)}updateViewportLines(){this.viewportLines=[],this.heightMap.forEachLine(this.viewport.from,this.viewport.to,this.state.doc,0,0,e=>{this.viewportLines.push(this.scaler.scale==1?e:oi(e,this.scaler))})}update(e,t=null){this.state=e.state;let i=this.stateDeco;this.stateDeco=this.state.facet(wi).filter(h=>typeof h!="function");let s=e.changedRanges,r=Ke.extendWithRanges(s,qf(i,this.stateDeco,e?e.changes:te.empty(this.state.doc.length))),o=this.heightMap.height;this.heightMap=this.heightMap.applyChanges(this.stateDeco,e.startState.doc,this.heightOracle.setDoc(this.state.doc),r),this.heightMap.height!=o&&(e.flags|=2);let l=r.length?this.mapViewport(this.viewport,e.changes):this.viewport;(t&&(t.range.head<l.from||t.range.head>l.to)||!this.viewportIsAppropriate(l))&&(l=this.getViewport(0,t));let a=!e.changes.empty||e.flags&2||l.from!=this.viewport.from||l.to!=this.viewport.to;this.viewport=l,this.updateForViewport(),a&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>2e3<<1)&&this.updateLineGaps(this.ensureLineGaps(this.mapLineGaps(this.lineGaps,e.changes))),e.flags|=this.computeVisibleRanges(),t&&(this.scrollTarget=t),!this.mustEnforceCursorAssoc&&e.selectionSet&&e.view.lineWrapping&&e.state.selection.main.empty&&e.state.selection.main.assoc&&!e.state.facet(va)&&(this.mustEnforceCursorAssoc=!0)}measure(e){let t=e.contentDOM,i=window.getComputedStyle(t),s=this.heightOracle,r=i.whiteSpace;this.defaultTextDirection=i.direction=="rtl"?Y.RTL:Y.LTR;let o=this.heightOracle.mustRefreshForWrapping(r),l=o||this.mustMeasureContent||this.contentDOMHeight!=t.clientHeight;this.contentDOMHeight=t.clientHeight,this.mustMeasureContent=!1;let a=0,h=0,c=parseInt(i.paddingTop)||0,f=parseInt(i.paddingBottom)||0;(this.paddingTop!=c||this.paddingBottom!=f)&&(this.paddingTop=c,this.paddingBottom=f,a|=10),this.editorWidth!=e.scrollDOM.clientWidth&&(s.lineWrapping&&(l=!0),this.editorWidth=e.scrollDOM.clientWidth,a|=8);let u=(this.printing?Uf:Kf)(t,this.paddingTop),d=u.top-this.pixelViewport.top,p=u.bottom-this.pixelViewport.bottom;this.pixelViewport=u;let y=this.pixelViewport.bottom>this.pixelViewport.top&&this.pixelViewport.right>this.pixelViewport.left;if(y!=this.inView&&(this.inView=y,y&&(l=!0)),!this.inView&&!this.scrollTarget)return 0;let g=t.clientWidth;if((this.contentDOMWidth!=g||this.editorHeight!=e.scrollDOM.clientHeight)&&(this.contentDOMWidth=g,this.editorHeight=e.scrollDOM.clientHeight,a|=8),l){let k=e.docView.measureVisibleLineHeights(this.viewport);if(s.mustRefreshForHeights(k)&&(o=!0),o||s.lineWrapping&&Math.abs(g-this.contentDOMWidth)>s.charWidth){let{lineHeight:v,charWidth:S}=e.docView.measureTextSize();o=v>0&&s.refresh(r,v,S,g/S,k),o&&(e.docView.minWidth=0,a|=8)}d>0&&p>0?h=Math.max(d,p):d<0&&p<0&&(h=Math.min(d,p)),s.heightChanged=!1;for(let v of this.viewports){let S=v.from==this.viewport.from?k:e.docView.measureVisibleLineHeights(v);this.heightMap=o?me.empty().applyChanges(this.stateDeco,V.empty,this.heightOracle,[new Ke(0,0,0,e.state.doc.length)]):this.heightMap.updateHeight(s,0,o,new Wf(v.from,S))}s.heightChanged&&(a|=2)}let b=!this.viewportIsAppropriate(this.viewport,h)||this.scrollTarget&&(this.scrollTarget.range.head<this.viewport.from||this.scrollTarget.range.head>this.viewport.to);return b&&(this.viewport=this.getViewport(h,this.scrollTarget)),this.updateForViewport(),(a&2||b)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>2e3<<1)&&this.updateLineGaps(this.ensureLineGaps(o?[]:this.lineGaps,e)),a|=this.computeVisibleRanges(),this.mustEnforceCursorAssoc&&(this.mustEnforceCursorAssoc=!1,e.docView.enforceCursorAssoc()),a}get visibleTop(){return this.scaler.fromDOM(this.pixelViewport.top)}get visibleBottom(){return this.scaler.fromDOM(this.pixelViewport.bottom)}getViewport(e,t){let i=.5-Math.max(-.5,Math.min(.5,e/1e3/2)),s=this.heightMap,r=this.state.doc,{visibleTop:o,visibleBottom:l}=this,a=new Ni(s.lineAt(o-i*1e3,_.ByHeight,r,0,0).from,s.lineAt(l+(1-i)*1e3,_.ByHeight,r,0,0).to);if(t){let{head:h}=t.range;if(h<a.from||h>a.to){let c=Math.min(this.editorHeight,this.pixelViewport.bottom-this.pixelViewport.top),f=s.lineAt(h,_.ByPos,r,0,0),u;t.y=="center"?u=(f.top+f.bottom)/2-c/2:t.y=="start"||t.y=="nearest"&&h<a.from?u=f.top:u=f.bottom-c,a=new Ni(s.lineAt(u-1e3/2,_.ByHeight,r,0,0).from,s.lineAt(u+c+1e3/2,_.ByHeight,r,0,0).to)}}return a}mapViewport(e,t){let i=t.mapPos(e.from,-1),s=t.mapPos(e.to,1);return new Ni(this.heightMap.lineAt(i,_.ByPos,this.state.doc,0,0).from,this.heightMap.lineAt(s,_.ByPos,this.state.doc,0,0).to)}viewportIsAppropriate({from:e,to:t},i=0){if(!this.inView)return!0;let{top:s}=this.heightMap.lineAt(e,_.ByPos,this.state.doc,0,0),{bottom:r}=this.heightMap.lineAt(t,_.ByPos,this.state.doc,0,0),{visibleTop:o,visibleBottom:l}=this;return(e==0||s<=o-Math.max(10,Math.min(-i,250)))&&(t==this.state.doc.length||r>=l+Math.max(10,Math.min(i,250)))&&s>o-2*1e3&&r<l+2*1e3}mapLineGaps(e,t){if(!e.length||t.empty)return e;let i=[];for(let s of e)t.touchesRange(s.from,s.to)||i.push(new _n(t.mapPos(s.from),t.mapPos(s.to),s.size));return i}ensureLineGaps(e,t){let i=this.heightOracle.lineWrapping,s=i?1e4:2e3,r=s>>1,o=s<<1;if(this.defaultTextDirection!=Y.LTR&&!i)return[];let l=[],a=(h,c,f,u)=>{if(c-h<r)return;let d=this.state.selection.main,p=[d.from];d.empty||p.push(d.to);for(let g of p)if(g>h&&g<c){a(h,g-10,f,u),a(g+10,c,f,u);return}let y=$f(e,g=>g.from>=f.from&&g.to<=f.to&&Math.abs(g.from-h)<r&&Math.abs(g.to-c)<r&&!p.some(b=>g.from<b&&g.to>b));if(!y){if(c<f.to&&t&&i&&t.visibleRanges.some(g=>g.from<=c&&g.to>=c)){let g=t.moveToLineBoundary(w.cursor(c),!1,!0).head;g>h&&(c=g)}y=new _n(h,c,this.gapSize(f,h,c,u))}l.push(y)};for(let h of this.viewportLines){if(h.length<o)continue;let c=Jf(h.from,h.to,this.stateDeco);if(c.total<o)continue;let f=this.scrollTarget?this.scrollTarget.range.head:null,u,d;if(i){let p=s/this.heightOracle.lineLength*this.heightOracle.lineHeight,y,g;if(f!=null){let b=Fi(c,f),k=((this.visibleBottom-this.visibleTop)/2+p)/h.height;y=b-k,g=b+k}else y=(this.visibleTop-h.top-p)/h.height,g=(this.visibleBottom-h.top+p)/h.height;u=Vi(c,y),d=Vi(c,g)}else{let p=c.total*this.heightOracle.charWidth,y=s*this.heightOracle.charWidth,g,b;if(f!=null){let k=Fi(c,f),v=((this.pixelViewport.right-this.pixelViewport.left)/2+y)/p;g=k-v,b=k+v}else g=(this.pixelViewport.left-y)/p,b=(this.pixelViewport.right+y)/p;u=Vi(c,g),d=Vi(c,b)}u>h.from&&a(h.from,u,h,c),d<h.to&&a(d,h.to,h,c)}return l}gapSize(e,t,i,s){let r=Fi(s,i)-Fi(s,t);return this.heightOracle.lineWrapping?e.height*r:s.total*this.heightOracle.charWidth*r}updateLineGaps(e){_n.same(e,this.lineGaps)||(this.lineGaps=e,this.lineGapDeco=E.set(e.map(t=>t.draw(this.heightOracle.lineWrapping))))}computeVisibleRanges(){let e=this.stateDeco;this.lineGaps.length&&(e=e.concat(this.lineGapDeco));let t=[];H.spans(e,this.viewport.from,this.viewport.to,{span(s,r){t.push({from:s,to:r})},point(){}},20);let i=t.length!=this.visibleRanges.length||this.visibleRanges.some((s,r)=>s.from!=t[r].from||s.to!=t[r].to);return this.visibleRanges=t,i?4:0}lineBlockAt(e){return e>=this.viewport.from&&e<=this.viewport.to&&this.viewportLines.find(t=>t.from<=e&&t.to>=e)||oi(this.heightMap.lineAt(e,_.ByPos,this.state.doc,0,0),this.scaler)}lineBlockAtHeight(e){return oi(this.heightMap.lineAt(this.scaler.fromDOM(e),_.ByHeight,this.state.doc,0,0),this.scaler)}elementAtHeight(e){return oi(this.heightMap.blockAt(this.scaler.fromDOM(e),this.state.doc,0,0),this.scaler)}get docHeight(){return this.scaler.toDOM(this.heightMap.height)}get contentHeight(){return this.docHeight+this.paddingTop+this.paddingBottom}}class Ni{constructor(e,t){this.from=e,this.to=t}}function Jf(n,e,t){let i=[],s=n,r=0;return H.spans(t,n,e,{span(){},point(o,l){o>s&&(i.push({from:s,to:o}),r+=o-s),s=l}},20),s<e&&(i.push({from:s,to:e}),r+=e-s),{total:r,ranges:i}}function Vi({total:n,ranges:e},t){if(t<=0)return e[0].from;if(t>=1)return e[e.length-1].to;let i=Math.floor(n*t);for(let s=0;;s++){let{from:r,to:o}=e[s],l=o-r;if(i<=l)return r+i;i-=l}}function Fi(n,e){let t=0;for(let{from:i,to:s}of n.ranges){if(e<=s){t+=e-i;break}t+=s-i}return t/n.total}function $f(n,e){for(let t of n)if(e(t))return t}const Co={toDOM(n){return n},fromDOM(n){return n},scale:1};class Yf{constructor(e,t,i){let s=0,r=0,o=0;this.viewports=i.map(({from:l,to:a})=>{let h=t.lineAt(l,_.ByPos,e,0,0).top,c=t.lineAt(a,_.ByPos,e,0,0).bottom;return s+=c-h,{from:l,to:a,top:h,bottom:c,domTop:0,domBottom:0}}),this.scale=(7e6-s)/(t.height-s);for(let l of this.viewports)l.domTop=o+(l.top-r)*this.scale,o=l.domBottom=l.domTop+(l.bottom-l.top),r=l.bottom}toDOM(e){for(let t=0,i=0,s=0;;t++){let r=t<this.viewports.length?this.viewports[t]:null;if(!r||e<r.top)return s+(e-i)*this.scale;if(e<=r.bottom)return r.domTop+(e-r.top);i=r.bottom,s=r.domBottom}}fromDOM(e){for(let t=0,i=0,s=0;;t++){let r=t<this.viewports.length?this.viewports[t]:null;if(!r||e<r.domTop)return i+(e-s)/this.scale;if(e<=r.domBottom)return r.top+(e-r.domTop);i=r.bottom,s=r.domBottom}}}function oi(n,e){if(e.scale==1)return n;let t=e.toDOM(n.top),i=e.toDOM(n.bottom);return new rt(n.from,n.length,t,i-t,Array.isArray(n.type)?n.type.map(s=>oi(s,e)):n.type)}const Hi=D.define({combine:n=>n.join(" ")}),Hs=D.define({combine:n=>n.indexOf(!0)>-1}),Ws=ot.newName(),_a=ot.newName(),qa=ot.newName(),ja={"&light":"."+_a,"&dark":"."+qa};function zs(n,e,t){return new ot(e,{finish(i){return/&/.test(i)?i.replace(/&\w*/,s=>{if(s=="&")return n;if(!t||!t[s])throw new RangeError(`Unsupported selector: ${s}`);return t[s]}):n+" "+i}})}const Xf=zs("."+Ws,{"&.cm-editor":{position:"relative !important",boxSizing:"border-box","&.cm-focused":{outline:"1px dotted #212121"},display:"flex !important",flexDirection:"column"},".cm-scroller":{display:"flex !important",alignItems:"flex-start !important",fontFamily:"monospace",lineHeight:1.4,height:"100%",overflowX:"auto",position:"relative",zIndex:0},".cm-content":{margin:0,flexGrow:2,flexShrink:0,minHeight:"100%",display:"block",whiteSpace:"pre",wordWrap:"normal",boxSizing:"border-box",padding:"4px 0",outline:"none","&[contenteditable=true]":{WebkitUserModify:"read-write-plaintext-only"}},".cm-lineWrapping":{whiteSpace_fallback:"pre-wrap",whiteSpace:"break-spaces",wordBreak:"break-word",overflowWrap:"anywhere",flexShrink:1},"&light .cm-content":{caretColor:"black"},"&dark .cm-content":{caretColor:"white"},".cm-line":{display:"block",padding:"0 2px 0 4px"},".cm-selectionLayer":{zIndex:-1,contain:"size style"},".cm-selectionBackground":{position:"absolute"},"&light .cm-selectionBackground":{background:"#d9d9d9"},"&dark .cm-selectionBackground":{background:"#222"},"&light.cm-focused .cm-selectionBackground":{background:"#d7d4f0"},"&dark.cm-focused .cm-selectionBackground":{background:"#233"},".cm-cursorLayer":{zIndex:100,contain:"size style",pointerEvents:"none"},"&.cm-focused .cm-cursorLayer":{animation:"steps(1) cm-blink 1.2s infinite"},"@keyframes cm-blink":{"0%":{},"50%":{opacity:0},"100%":{}},"@keyframes cm-blink2":{"0%":{},"50%":{opacity:0},"100%":{}},".cm-cursor, .cm-dropCursor":{position:"absolute",borderLeft:"1.2px solid black",marginLeft:"-0.6px",pointerEvents:"none"},".cm-cursor":{display:"none"},"&dark .cm-cursor":{borderLeftColor:"#444"},"&.cm-focused .cm-cursor":{display:"block"},"&light .cm-activeLine":{backgroundColor:"#cceeff44"},"&dark .cm-activeLine":{backgroundColor:"#99eeff33"},"&light .cm-specialChar":{color:"red"},"&dark .cm-specialChar":{color:"#f78"},".cm-gutters":{flexShrink:0,display:"flex",height:"100%",boxSizing:"border-box",left:0,zIndex:200},"&light .cm-gutters":{backgroundColor:"#f5f5f5",color:"#6c6c6c",borderRight:"1px solid #ddd"},"&dark .cm-gutters":{backgroundColor:"#333338",color:"#ccc"},".cm-gutter":{display:"flex !important",flexDirection:"column",flexShrink:0,boxSizing:"border-box",minHeight:"100%",overflow:"hidden"},".cm-gutterElement":{boxSizing:"border-box"},".cm-lineNumbers .cm-gutterElement":{padding:"0 3px 0 5px",minWidth:"20px",textAlign:"right",whiteSpace:"nowrap"},"&light .cm-activeLineGutter":{backgroundColor:"#e2f2ff"},"&dark .cm-activeLineGutter":{backgroundColor:"#222227"},".cm-panels":{boxSizing:"border-box",position:"sticky",left:0,right:0},"&light .cm-panels":{backgroundColor:"#f5f5f5",color:"black"},"&light .cm-panels-top":{borderBottom:"1px solid #ddd"},"&light .cm-panels-bottom":{borderTop:"1px solid #ddd"},"&dark .cm-panels":{backgroundColor:"#333338",color:"white"},".cm-tab":{display:"inline-block",overflow:"hidden",verticalAlign:"bottom"},".cm-widgetBuffer":{verticalAlign:"text-top",height:"1em",width:0,display:"inline"},".cm-placeholder":{color:"#888",display:"inline-block",verticalAlign:"top"},".cm-button":{verticalAlign:"middle",color:"inherit",fontSize:"70%",padding:".2em 1em",borderRadius:"1px"},"&light .cm-button":{backgroundImage:"linear-gradient(#eff1f5, #d9d9df)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#b4b4b4, #d0d3d6)"}},"&dark .cm-button":{backgroundImage:"linear-gradient(#393939, #111)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#111, #333)"}},".cm-textfield":{verticalAlign:"middle",color:"inherit",fontSize:"70%",border:"1px solid silver",padding:".2em .5em"},"&light .cm-textfield":{backgroundColor:"white"},"&dark .cm-textfield":{border:"1px solid #555",backgroundColor:"inherit"}},ja);class Zf{constructor(e,t,i,s){this.typeOver=s,this.bounds=null,this.text="";let{impreciseHead:r,impreciseAnchor:o}=e.docView;if(t>-1&&!e.state.readOnly&&(this.bounds=e.docView.domBoundsAround(t,i,0))){let l=r||o?[]:eu(e),a=new Ba(l,e.state);a.readRange(this.bounds.startDOM,this.bounds.endDOM),this.text=a.text,this.newSel=tu(l,this.bounds.from)}else{let l=e.observer.selectionRange,a=r&&r.node==l.focusNode&&r.offset==l.focusOffset||!zt(e.contentDOM,l.focusNode)?e.state.selection.main.head:e.docView.posFromDOM(l.focusNode,l.focusOffset),h=o&&o.node==l.anchorNode&&o.offset==l.anchorOffset||!zt(e.contentDOM,l.anchorNode)?e.state.selection.main.anchor:e.docView.posFromDOM(l.anchorNode,l.anchorOffset);this.newSel=w.single(h,a)}}}function Ka(n,e){let t,{newSel:i}=e,s=n.state.selection.main;if(e.bounds){let{from:r,to:o}=e.bounds,l=s.from,a=null;(n.inputState.lastKeyCode===8&&n.inputState.lastKeyTime>Date.now()-100||A.android&&e.text.length<o-r)&&(l=s.to,a="end");let h=Qf(n.state.doc.sliceString(r,o,st),e.text,l-r,a);h&&(A.chrome&&n.inputState.lastKeyCode==13&&h.toB==h.from+2&&e.text.slice(h.from,h.toB)==st+st&&h.toB--,t={from:r+h.from,to:r+h.toA,insert:V.of(e.text.slice(h.from,h.toB).split(st))})}else i&&(!n.hasFocus||!n.state.facet(Cn)||i.main.eq(s))&&(i=null);if(!t&&!i)return!1;if(!t&&e.typeOver&&!s.empty&&i&&i.main.empty?t={from:s.from,to:s.to,insert:n.state.doc.slice(s.from,s.to)}:t&&t.from>=s.from&&t.to<=s.to&&(t.from!=s.from||t.to!=s.to)&&s.to-s.from-(t.to-t.from)<=4?t={from:s.from,to:s.to,insert:n.state.doc.slice(s.from,t.from).append(t.insert).append(n.state.doc.slice(t.to,s.to))}:(A.mac||A.android)&&t&&t.from==t.to&&t.from==s.head-1&&/^\. ?$/.test(t.insert.toString())?(i&&t.insert.length==2&&(i=w.single(i.main.anchor-1,i.main.head-1)),t={from:s.from,to:s.to,insert:V.of([" "])}):A.chrome&&t&&t.from==t.to&&t.from==s.head&&t.insert.toString()==`
 `&&n.lineWrapping&&(i&&(i=w.single(i.main.anchor-1,i.main.head-1)),t={from:s.from,to:s.to,insert:V.of([" "])}),t){let r=n.state;if(A.ios&&n.inputState.flushIOSKey(n)||A.android&&(t.from==s.from&&t.to==s.to&&t.insert.length==1&&t.insert.lines==2&&Ht(n.contentDOM,"Enter",13)||t.from==s.from-1&&t.to==s.to&&t.insert.length==0&&Ht(n.contentDOM,"Backspace",8)||t.from==s.from&&t.to==s.to+1&&t.insert.length==0&&Ht(n.contentDOM,"Delete",46)))return!0;let o=t.insert.toString();if(n.state.facet(xa).some(h=>h(n,t.from,t.to,o)))return!0;n.inputState.composing>=0&&n.inputState.composing++;let l;if(t.from>=s.from&&t.to<=s.to&&t.to-t.from>=(s.to-s.from)/3&&(!i||i.main.empty&&i.main.from==t.from+t.insert.length)&&n.inputState.composing<0){let h=s.from<t.from?r.sliceDoc(s.from,t.from):"",c=s.to>t.to?r.sliceDoc(t.to,s.to):"";l=r.replaceSelection(n.state.toText(h+t.insert.sliceString(0,void 0,n.state.lineBreak)+c))}else{let h=r.changes(t),c=i&&!r.selection.main.eq(i.main)&&i.main.to<=h.newLength?i.main:void 0;if(r.selection.ranges.length>1&&n.inputState.composing>=0&&t.to<=s.to&&t.to>=s.to-10){let f=n.state.sliceDoc(t.from,t.to),u=Pa(n)||n.state.doc.lineAt(s.head),d=s.to-t.to,p=s.to-s.from;l=r.changeByRange(y=>{if(y.from==s.from&&y.to==s.to)return{changes:h,range:c||y.map(h)};let g=y.to-d,b=g-f.length;if(y.to-y.from!=p||n.state.sliceDoc(b,g)!=f||u&&y.to>=u.from&&y.from<=u.to)return{range:y};let k=r.changes({from:b,to:g,insert:t.insert}),v=y.to-s.to;return{changes:k,range:c?w.range(Math.max(0,c.anchor+v),Math.max(0,c.head+v)):y.map(k)}})}else l={changes:h,selection:c&&r.selection.replaceRange(c)}}let a="input.type";return n.composing&&(a+=".compose",n.inputState.compositionFirstChange&&(a+=".start",n.inputState.compositionFirstChange=!1)),n.dispatch(l,{scrollIntoView:!0,userEvent:a}),!0}else if(i&&!i.main.eq(s)){let r=!1,o="select";return n.inputState.lastSelectionTime>Date.now()-50&&(n.inputState.lastSelectionOrigin=="select"&&(r=!0),o=n.inputState.lastSelectionOrigin),n.dispatch({selection:i,scrollIntoView:r,userEvent:o}),!0}else return!1}function Qf(n,e,t,i){let s=Math.min(n.length,e.length),r=0;for(;r<s&&n.charCodeAt(r)==e.charCodeAt(r);)r++;if(r==s&&n.length==e.length)return null;let o=n.length,l=e.length;for(;o>0&&l>0&&n.charCodeAt(o-1)==e.charCodeAt(l-1);)o--,l--;if(i=="end"){let a=Math.max(0,r-Math.min(o,l));t-=o+a-r}if(o<r&&n.length<e.length){let a=t<=r&&t>=o?r-t:0;r-=a,l=r+(l-o),o=r}else if(l<r){let a=t<=r&&t>=l?r-t:0;r-=a,o=r+(o-l),l=r}return{from:r,toA:o,toB:l}}function eu(n){let e=[];if(n.root.activeElement!=n.contentDOM)return e;let{anchorNode:t,anchorOffset:i,focusNode:s,focusOffset:r}=n.observer.selectionRange;return t&&(e.push(new so(t,i)),(s!=t||r!=i)&&e.push(new so(s,r))),e}function tu(n,e){if(n.length==0)return null;let t=n[0].pos,i=n.length==2?n[1].pos:t;return t>-1&&i>-1?w.single(t+e,i+e):null}const iu={childList:!0,characterData:!0,subtree:!0,attributes:!0,characterDataOldValue:!0},qn=A.ie&&A.ie_version<=11;class nu{constructor(e){this.view=e,this.active=!1,this.selectionRange=new $c,this.selectionChanged=!1,this.delayedFlush=-1,this.resizeTimeout=-1,this.queue=[],this.delayedAndroidKey=null,this.flushingAndroidKey=-1,this.lastChange=0,this.scrollTargets=[],this.intersection=null,this.resize=null,this.intersecting=!1,this.gapIntersection=null,this.gaps=[],this.parentCheck=-1,this.dom=e.contentDOM,this.observer=new MutationObserver(t=>{for(let i of t)this.queue.push(i);(A.ie&&A.ie_version<=11||A.ios&&e.composing)&&t.some(i=>i.type=="childList"&&i.removedNodes.length||i.type=="characterData"&&i.oldValue.length>i.target.nodeValue.length)?this.flushSoon():this.flush()}),qn&&(this.onCharData=t=>{this.queue.push({target:t.target,type:"characterData",oldValue:t.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this),this.onResize=this.onResize.bind(this),this.onPrint=this.onPrint.bind(this),this.onScroll=this.onScroll.bind(this),typeof ResizeObserver=="function"&&(this.resize=new ResizeObserver(()=>{var t;((t=this.view.docView)===null||t===void 0?void 0:t.lastUpdate)<Date.now()-75&&this.onResize()}),this.resize.observe(e.scrollDOM)),this.addWindowListeners(this.win=e.win),this.start(),typeof IntersectionObserver=="function"&&(this.intersection=new IntersectionObserver(t=>{this.parentCheck<0&&(this.parentCheck=setTimeout(this.listenForScroll.bind(this),1e3)),t.length>0&&t[t.length-1].intersectionRatio>0!=this.intersecting&&(this.intersecting=!this.intersecting,this.intersecting!=this.view.inView&&this.onScrollChanged(document.createEvent("Event")))},{}),this.intersection.observe(this.dom),this.gapIntersection=new IntersectionObserver(t=>{t.length>0&&t[t.length-1].intersectionRatio>0&&this.onScrollChanged(document.createEvent("Event"))},{})),this.listenForScroll(),this.readSelectionRange()}onScrollChanged(e){this.view.inputState.runScrollHandlers(this.view,e),this.intersecting&&this.view.measure()}onScroll(e){this.intersecting&&this.flush(!1),this.onScrollChanged(e)}onResize(){this.resizeTimeout<0&&(this.resizeTimeout=setTimeout(()=>{this.resizeTimeout=-1,this.view.requestMeasure()},50))}onPrint(){this.view.viewState.printing=!0,this.view.measure(),setTimeout(()=>{this.view.viewState.printing=!1,this.view.requestMeasure()},500)}updateGaps(e){if(this.gapIntersection&&(e.length!=this.gaps.length||this.gaps.some((t,i)=>t!=e[i]))){this.gapIntersection.disconnect();for(let t of e)this.gapIntersection.observe(t);this.gaps=e}}onSelectionChange(e){let t=this.selectionChanged;if(!this.readSelectionRange()||this.delayedAndroidKey)return;let{view:i}=this,s=this.selectionRange;if(i.state.facet(Cn)?i.root.activeElement!=this.dom:!Qi(i.dom,s))return;let r=s.anchorNode&&i.docView.nearest(s.anchorNode);if(r&&r.ignoreEvent(e)){t||(this.selectionChanged=!1);return}(A.ie&&A.ie_version<=11||A.android&&A.chrome)&&!i.state.selection.main.empty&&s.focusNode&&ln(s.focusNode,s.focusOffset,s.anchorNode,s.anchorOffset)?this.flushSoon():this.flush(!1)}readSelectionRange(){let{view:e}=this,t=A.safari&&e.root.nodeType==11&&Uc(this.dom.ownerDocument)==this.dom&&su(this.view)||on(e.root);if(!t||this.selectionRange.eq(t))return!1;let i=Qi(this.dom,t);return i&&!this.selectionChanged&&e.inputState.lastFocusTime>Date.now()-200&&e.inputState.lastTouchTime<Date.now()-300&&Xc(this.dom,t)?(this.view.inputState.lastFocusTime=0,e.docView.updateSelection(),!1):(this.selectionRange.setRange(t),i&&(this.selectionChanged=!0),!0)}setSelectionRange(e,t){this.selectionRange.set(e.node,e.offset,t.node,t.offset),this.selectionChanged=!1}clearSelectionRange(){this.selectionRange.set(null,0,null,0)}listenForScroll(){this.parentCheck=-1;let e=0,t=null;for(let i=this.dom;i;)if(i.nodeType==1)!t&&e<this.scrollTargets.length&&this.scrollTargets[e]==i?e++:t||(t=this.scrollTargets.slice(0,e)),t&&t.push(i),i=i.assignedSlot||i.parentNode;else if(i.nodeType==11)i=i.host;else break;if(e<this.scrollTargets.length&&!t&&(t=this.scrollTargets.slice(0,e)),t){for(let i of this.scrollTargets)i.removeEventListener("scroll",this.onScroll);for(let i of this.scrollTargets=t)i.addEventListener("scroll",this.onScroll)}}ignore(e){if(!this.active)return e();try{return this.stop(),e()}finally{this.start(),this.clear()}}start(){this.active||(this.observer.observe(this.dom,iu),qn&&this.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.active=!0)}stop(){this.active&&(this.active=!1,this.observer.disconnect(),qn&&this.dom.removeEventListener("DOMCharacterDataModified",this.onCharData))}clear(){this.processRecords(),this.queue.length=0,this.selectionChanged=!1}delayAndroidKey(e,t){var i;if(!this.delayedAndroidKey){let s=()=>{let r=this.delayedAndroidKey;r&&(this.clearDelayedAndroidKey(),!this.flush()&&r.force&&Ht(this.dom,r.key,r.keyCode))};this.flushingAndroidKey=this.view.win.requestAnimationFrame(s)}(!this.delayedAndroidKey||e=="Enter")&&(this.delayedAndroidKey={key:e,keyCode:t,force:this.lastChange<Date.now()-50||!!(!((i=this.delayedAndroidKey)===null||i===void 0)&&i.force)})}clearDelayedAndroidKey(){this.win.cancelAnimationFrame(this.flushingAndroidKey),this.delayedAndroidKey=null,this.flushingAndroidKey=-1}flushSoon(){this.delayedFlush<0&&(this.delayedFlush=this.view.win.requestAnimationFrame(()=>{this.delayedFlush=-1,this.flush()}))}forceFlush(){this.delayedFlush>=0&&(this.view.win.cancelAnimationFrame(this.delayedFlush),this.delayedFlush=-1),this.flush()}processRecords(){let e=this.queue;for(let r of this.observer.takeRecords())e.push(r);e.length&&(this.queue=[]);let t=-1,i=-1,s=!1;for(let r of e){let o=this.readMutation(r);o&&(o.typeOver&&(s=!0),t==-1?{from:t,to:i}=o:(t=Math.min(o.from,t),i=Math.max(o.to,i)))}return{from:t,to:i,typeOver:s}}readChange(){let{from:e,to:t,typeOver:i}=this.processRecords(),s=this.selectionChanged&&Qi(this.dom,this.selectionRange);return e<0&&!s?null:(e>-1&&(this.lastChange=Date.now()),this.view.inputState.lastFocusTime=0,this.selectionChanged=!1,new Zf(this.view,e,t,i))}flush(e=!0){if(this.delayedFlush>=0||this.delayedAndroidKey)return!1;e&&this.readSelectionRange();let t=this.readChange();if(!t)return!1;let i=this.view.state,s=Ka(this.view,t);return this.view.state==i&&this.view.update([]),s}readMutation(e){let t=this.view.docView.nearest(e.target);if(!t||t.ignoreMutation(e))return null;if(t.markDirty(e.type=="attributes"),e.type=="attributes"&&(t.dirty|=4),e.type=="childList"){let i=Ao(t,e.previousSibling||e.target.previousSibling,-1),s=Ao(t,e.nextSibling||e.target.nextSibling,1);return{from:i?t.posAfter(i):t.posAtStart,to:s?t.posBefore(s):t.posAtEnd,typeOver:!1}}else return e.type=="characterData"?{from:t.posAtStart,to:t.posAtEnd,typeOver:e.target.nodeValue==e.oldValue}:null}setWindow(e){e!=this.win&&(this.removeWindowListeners(this.win),this.win=e,this.addWindowListeners(this.win))}addWindowListeners(e){e.addEventListener("resize",this.onResize),e.addEventListener("beforeprint",this.onPrint),e.addEventListener("scroll",this.onScroll),e.document.addEventListener("selectionchange",this.onSelectionChange)}removeWindowListeners(e){e.removeEventListener("scroll",this.onScroll),e.removeEventListener("resize",this.onResize),e.removeEventListener("beforeprint",this.onPrint),e.document.removeEventListener("selectionchange",this.onSelectionChange)}destroy(){var e,t,i;this.stop(),(e=this.intersection)===null||e===void 0||e.disconnect(),(t=this.gapIntersection)===null||t===void 0||t.disconnect(),(i=this.resize)===null||i===void 0||i.disconnect();for(let s of this.scrollTargets)s.removeEventListener("scroll",this.onScroll);this.removeWindowListeners(this.win),clearTimeout(this.parentCheck),clearTimeout(this.resizeTimeout),this.win.cancelAnimationFrame(this.delayedFlush),this.win.cancelAnimationFrame(this.flushingAndroidKey)}}function Ao(n,e,t){for(;e;){let i=q.get(e);if(i&&i.parent==n)return i;let s=e.parentNode;e=s!=n.dom?s:t>0?e.nextSibling:e.previousSibling}return null}function su(n){let e=null;function t(a){a.preventDefault(),a.stopImmediatePropagation(),e=a.getTargetRanges()[0]}if(n.contentDOM.addEventListener("beforeinput",t,!0),n.dom.ownerDocument.execCommand("indent"),n.contentDOM.removeEventListener("beforeinput",t,!0),!e)return null;let i=e.startContainer,s=e.startOffset,r=e.endContainer,o=e.endOffset,l=n.docView.domAtPos(n.state.selection.main.anchor);return ln(l.node,l.offset,r,o)&&([i,s,r,o]=[r,o,i,s]),{anchorNode:i,anchorOffset:s,focusNode:r,focusOffset:o}}class O{constructor(e={}){this.plugins=[],this.pluginMap=new Map,this.editorAttrs={},this.contentAttrs={},this.bidiCache=[],this.destroyed=!1,this.updateState=2,this.measureScheduled=-1,this.measureRequests=[],this.contentDOM=document.createElement("div"),this.scrollDOM=document.createElement("div"),this.scrollDOM.tabIndex=-1,this.scrollDOM.className="cm-scroller",this.scrollDOM.appendChild(this.contentDOM),this.announceDOM=document.createElement("div"),this.announceDOM.style.cssText="position: absolute; top: -10000px",this.announceDOM.setAttribute("aria-live","polite"),this.dom=document.createElement("div"),this.dom.appendChild(this.announceDOM),this.dom.appendChild(this.scrollDOM),this._dispatch=e.dispatch||(t=>this.update([t])),this.dispatch=this.dispatch.bind(this),this._root=e.root||Yc(e.parent)||document,this.viewState=new So(e.state||N.create(e)),this.plugins=this.state.facet(si).map(t=>new Hn(t));for(let t of this.plugins)t.update(this);this.observer=new nu(this),this.inputState=new Mf(this),this.inputState.ensureHandlers(this,this.plugins),this.docView=new ro(this),this.mountStyles(),this.updateAttrs(),this.updateState=0,this.requestMeasure(),e.parent&&e.parent.appendChild(this.dom)}get state(){return this.viewState.state}get viewport(){return this.viewState.viewport}get visibleRanges(){return this.viewState.visibleRanges}get inView(){return this.viewState.inView}get composing(){return this.inputState.composing>0}get compositionStarted(){return this.inputState.composing>=0}get root(){return this._root}get win(){return this.dom.ownerDocument.defaultView||window}dispatch(...e){this._dispatch(e.length==1&&e[0]instanceof ie?e[0]:this.state.update(...e))}update(e){if(this.updateState!=0)throw new Error("Calls to EditorView.update are not allowed while an update is in progress");let t=!1,i=!1,s,r=this.state;for(let h of e){if(h.startState!=r)throw new RangeError("Trying to update state with a transaction that doesn't start from the previous state.");r=h.state}if(this.destroyed){this.viewState.state=r;return}let o=this.observer.delayedAndroidKey,l=null;if(o?(this.observer.clearDelayedAndroidKey(),l=this.observer.readChange(),(l&&!this.state.doc.eq(r.doc)||!this.state.selection.eq(r.selection))&&(l=null)):this.observer.clear(),r.facet(N.phrases)!=this.state.facet(N.phrases))return this.setState(r);s=cn.create(this,r,e);let a=this.viewState.scrollTarget;try{this.updateState=2;for(let h of e){if(a&&(a=a.map(h.changes)),h.scrollIntoView){let{main:c}=h.state.selection;a=new hn(c.empty?c:w.cursor(c.head,c.head>c.anchor?-1:1))}for(let c of h.effects)c.is(io)&&(a=c.value)}this.viewState.update(s,a),this.bidiCache=fn.update(this.bidiCache,s.changes),s.empty||(this.updatePlugins(s),this.inputState.update(s)),t=this.docView.update(s),this.state.facet(ri)!=this.styleModules&&this.mountStyles(),i=this.updateAttrs(),this.showAnnouncements(e),this.docView.updateSelection(t,e.some(h=>h.isUserEvent("select.pointer")))}finally{this.updateState=0}if(s.startState.facet(Hi)!=s.state.facet(Hi)&&(this.viewState.mustMeasureContent=!0),(t||i||a||this.viewState.mustEnforceCursorAssoc||this.viewState.mustMeasureContent)&&this.requestMeasure(),!s.empty)for(let h of this.state.facet(Rs))h(s);l&&!Ka(this,l)&&o.force&&Ht(this.contentDOM,o.key,o.keyCode)}setState(e){if(this.updateState!=0)throw new Error("Calls to EditorView.setState are not allowed while an update is in progress");if(this.destroyed){this.viewState.state=e;return}this.updateState=2;let t=this.hasFocus;try{for(let i of this.plugins)i.destroy(this);this.viewState=new So(e),this.plugins=e.facet(si).map(i=>new Hn(i)),this.pluginMap.clear();for(let i of this.plugins)i.update(this);this.docView=new ro(this),this.inputState.ensureHandlers(this,this.plugins),this.mountStyles(),this.updateAttrs(),this.bidiCache=[]}finally{this.updateState=0}t&&this.focus(),this.requestMeasure()}updatePlugins(e){let t=e.startState.facet(si),i=e.state.facet(si);if(t!=i){let s=[];for(let r of i){let o=t.indexOf(r);if(o<0)s.push(new Hn(r));else{let l=this.plugins[o];l.mustUpdate=e,s.push(l)}}for(let r of this.plugins)r.mustUpdate!=e&&r.destroy(this);this.plugins=s,this.pluginMap.clear(),this.inputState.ensureHandlers(this,this.plugins)}else for(let s of this.plugins)s.mustUpdate=e;for(let s=0;s<this.plugins.length;s++)this.plugins[s].update(this)}measure(e=!0){if(this.destroyed)return;this.measureScheduled>-1&&cancelAnimationFrame(this.measureScheduled),this.measureScheduled=0,e&&this.observer.forceFlush();let t=null,{scrollHeight:i,scrollTop:s,clientHeight:r}=this.scrollDOM,o=s>i-r-4?i:s;try{for(let l=0;;l++){this.updateState=1;let a=this.viewport,h=this.viewState.lineBlockAtHeight(o),c=this.viewState.measure(this);if(!c&&!this.measureRequests.length&&this.viewState.scrollTarget==null)break;if(l>5){console.warn(this.measureRequests.length?"Measure loop restarted more than 5 times":"Viewport failed to stabilize");break}let f=[];c&4||([this.measureRequests,f]=[f,this.measureRequests]);let u=f.map(g=>{try{return g.read(this)}catch(b){return Ee(this.state,b),Mo}}),d=cn.create(this,this.state,[]),p=!1,y=!1;d.flags|=c,t?t.flags|=c:t=d,this.updateState=2,d.empty||(this.updatePlugins(d),this.inputState.update(d),this.updateAttrs(),p=this.docView.update(d));for(let g=0;g<f.length;g++)if(u[g]!=Mo)try{let b=f[g];b.write&&b.write(u[g],this)}catch(b){Ee(this.state,b)}if(this.viewState.editorHeight)if(this.viewState.scrollTarget)this.docView.scrollIntoView(this.viewState.scrollTarget),this.viewState.scrollTarget=null,y=!0;else{let g=this.viewState.lineBlockAt(h.from).top-h.top;(g>1||g<-1)&&(this.scrollDOM.scrollTop+=g,y=!0)}if(p&&this.docView.updateSelection(!0),this.viewport.from==a.from&&this.viewport.to==a.to&&!y&&this.measureRequests.length==0)break}}finally{this.updateState=0,this.measureScheduled=-1}if(t&&!t.empty)for(let l of this.state.facet(Rs))l(t)}get themeClasses(){return Ws+" "+(this.state.facet(Hs)?qa:_a)+" "+this.state.facet(Hi)}updateAttrs(){let e=Do(this,Sa,{class:"cm-editor"+(this.hasFocus?" cm-focused ":" ")+this.themeClasses}),t={spellcheck:"false",autocorrect:"off",autocapitalize:"off",translate:"no",contenteditable:this.state.facet(Cn)?"true":"false",class:"cm-content",style:`${A.tabSize}: ${this.state.tabSize}`,role:"textbox","aria-multiline":"true"};this.state.readOnly&&(t["aria-readonly"]="true"),Do(this,Ca,t);let i=this.observer.ignore(()=>{let s=Ps(this.contentDOM,this.contentAttrs,t),r=Ps(this.dom,this.editorAttrs,e);return s||r});return this.editorAttrs=e,this.contentAttrs=t,i}showAnnouncements(e){let t=!0;for(let i of e)for(let s of i.effects)if(s.is(O.announce)){t&&(this.announceDOM.textContent=""),t=!1;let r=this.announceDOM.appendChild(document.createElement("div"));r.textContent=s.value}}mountStyles(){this.styleModules=this.state.facet(ri),ot.mount(this.root,this.styleModules.concat(Xf).reverse())}readMeasured(){if(this.updateState==2)throw new Error("Reading the editor layout isn't allowed during an update");this.updateState==0&&this.measureScheduled>-1&&this.measure(!1)}requestMeasure(e){if(this.measureScheduled<0&&(this.measureScheduled=this.win.requestAnimationFrame(()=>this.measure())),e){if(e.key!=null){for(let t=0;t<this.measureRequests.length;t++)if(this.measureRequests[t].key===e.key){this.measureRequests[t]=e;return}}this.measureRequests.push(e)}}plugin(e){let t=this.pluginMap.get(e);return(t===void 0||t&&t.spec!=e)&&this.pluginMap.set(e,t=this.plugins.find(i=>i.spec==e)||null),t&&t.update(this).value}get documentTop(){return this.contentDOM.getBoundingClientRect().top+this.viewState.paddingTop}get documentPadding(){return{top:this.viewState.paddingTop,bottom:this.viewState.paddingBottom}}elementAtHeight(e){return this.readMeasured(),this.viewState.elementAtHeight(e)}lineBlockAtHeight(e){return this.readMeasured(),this.viewState.lineBlockAtHeight(e)}get viewportLineBlocks(){return this.viewState.viewportLines}lineBlockAt(e){return this.viewState.lineBlockAt(e)}get contentHeight(){return this.viewState.contentHeight}moveByChar(e,t,i){return zn(this,e,fo(this,e,t,i))}moveByGroup(e,t){return zn(this,e,fo(this,e,t,i=>Cf(this,e.head,i)))}moveToLineBoundary(e,t,i=!0){return Sf(this,e,t,i)}moveVertically(e,t,i){return zn(this,e,Af(this,e,t,i))}domAtPos(e){return this.docView.domAtPos(e)}posAtDOM(e,t=0){return this.docView.posFromDOM(e,t)}posAtCoords(e,t=!0){return this.readMeasured(),Ra(this,e,t)}coordsAtPos(e,t=1){this.readMeasured();let i=this.docView.coordsAt(e,t);if(!i||i.left==i.right)return i;let s=this.state.doc.lineAt(e),r=this.bidiSpans(s),o=r[Wt.find(r,e-s.from,-1,t)];return lr(i,o.dir==Y.LTR==t>0)}get defaultCharacterWidth(){return this.viewState.heightOracle.charWidth}get defaultLineHeight(){return this.viewState.heightOracle.lineHeight}get textDirection(){return this.viewState.defaultTextDirection}textDirectionAt(e){return!this.state.facet(ka)||e<this.viewport.from||e>this.viewport.to?this.textDirection:(this.readMeasured(),this.docView.textDirectionAt(e))}get lineWrapping(){return this.viewState.heightOracle.lineWrapping}bidiSpans(e){if(e.length>ru)return Ta(e.length);let t=this.textDirectionAt(e.from);for(let s of this.bidiCache)if(s.from==e.from&&s.dir==t)return s.order;let i=hf(e.text,t);return this.bidiCache.push(new fn(e.from,e.to,t,i)),i}get hasFocus(){var e;return(this.dom.ownerDocument.hasFocus()||A.safari&&((e=this.inputState)===null||e===void 0?void 0:e.lastContextMenu)>Date.now()-3e4)&&this.root.activeElement==this.contentDOM}focus(){this.observer.ignore(()=>{na(this.contentDOM),this.docView.updateSelection()})}setRoot(e){this._root!=e&&(this._root=e,this.observer.setWindow((e.nodeType==9?e:e.ownerDocument).defaultView||window),this.mountStyles())}destroy(){for(let e of this.plugins)e.destroy(this);this.plugins=[],this.inputState.destroy(),this.dom.remove(),this.observer.destroy(),this.measureScheduled>-1&&cancelAnimationFrame(this.measureScheduled),this.destroyed=!0}static scrollIntoView(e,t={}){return io.of(new hn(typeof e=="number"?w.cursor(e):e,t.y,t.x,t.yMargin,t.xMargin))}static domEventHandlers(e){return ue.define(()=>({}),{eventHandlers:e})}static theme(e,t){let i=ot.newName(),s=[Hi.of(i),ri.of(zs(`.${i}`,e))];return t&&t.dark&&s.push(Hs.of(!0)),s}static baseTheme(e){return Ai.lowest(ri.of(zs("."+Ws,e,ja)))}static findFromDOM(e){var t;let i=e.querySelector(".cm-content"),s=i&&q.get(i)||q.get(e);return((t=s?.rootView)===null||t===void 0?void 0:t.view)||null}}O.styleModule=ri;O.inputHandler=xa;O.perLineTextDirection=ka;O.exceptionSink=wa;O.updateListener=Rs;O.editable=Cn;O.mouseSelectionStyle=ba;O.dragMovesSelection=ya;O.clickAddsSelectionRange=ga;O.decorations=wi;O.atomicRanges=Aa;O.scrollMargins=Ma;O.darkTheme=Hs;O.contentAttributes=Ca;O.editorAttributes=Sa;O.lineWrapping=O.contentAttributes.of({class:"cm-lineWrapping"});O.announce=R.define();const ru=4096,Mo={};class fn{constructor(e,t,i,s){this.from=e,this.to=t,this.dir=i,this.order=s}static update(e,t){if(t.empty)return e;let i=[],s=e.length?e[e.length-1].dir:Y.LTR;for(let r=Math.max(0,e.length-10);r<e.length;r++){let o=e[r];o.dir==s&&!t.touchesRange(o.from,o.to)&&i.push(new fn(t.mapPos(o.from,1),t.mapPos(o.to,-1),o.dir,o.order))}return i}}function Do(n,e,t){for(let i=n.state.facet(e),s=i.length-1;s>=0;s--){let r=i[s],o=typeof r=="function"?r(n):r;o&&Bs(o,t)}return t}const ou=A.mac?"mac":A.windows?"win":A.linux?"linux":"key";function lu(n,e){const t=n.split(/-(?!$)/);let i=t[t.length-1];i=="Space"&&(i=" ");let s,r,o,l;for(let a=0;a<t.length-1;++a){const h=t[a];if(/^(cmd|meta|m)$/i.test(h))l=!0;else if(/^a(lt)?$/i.test(h))s=!0;else if(/^(c|ctrl|control)$/i.test(h))r=!0;else if(/^s(hift)?$/i.test(h))o=!0;else if(/^mod$/i.test(h))e=="mac"?l=!0:r=!0;else throw new Error("Unrecognized modifier name: "+h)}return s&&(i="Alt-"+i),r&&(i="Ctrl-"+i),l&&(i="Meta-"+i),o&&(i="Shift-"+i),i}function Wi(n,e,t){return e.altKey&&(n="Alt-"+n),e.ctrlKey&&(n="Ctrl-"+n),e.metaKey&&(n="Meta-"+n),t!==!1&&e.shiftKey&&(n="Shift-"+n),n}const au=Ai.default(O.domEventHandlers({keydown(n,e){return uu(hu(e.state),n,e,"editor")}})),An=D.define({enables:au}),To=new WeakMap;function hu(n){let e=n.facet(An),t=To.get(e);return t||To.set(e,t=fu(e.reduce((i,s)=>i.concat(s),[]))),t}let tt=null;const cu=4e3;function fu(n,e=ou){let t=Object.create(null),i=Object.create(null),s=(o,l)=>{let a=i[o];if(a==null)i[o]=l;else if(a!=l)throw new Error("Key binding "+o+" is used both as a regular binding and as a multi-stroke prefix")},r=(o,l,a,h)=>{var c,f;let u=t[o]||(t[o]=Object.create(null)),d=l.split(/ (?!$)/).map(g=>lu(g,e));for(let g=1;g<d.length;g++){let b=d.slice(0,g).join(" ");s(b,!0),u[b]||(u[b]={preventDefault:!0,run:[k=>{let v=tt={view:k,prefix:b,scope:o};return setTimeout(()=>{tt==v&&(tt=null)},cu),!0}]})}let p=d.join(" ");s(p,!1);let y=u[p]||(u[p]={preventDefault:!1,run:((f=(c=u._any)===null||c===void 0?void 0:c.run)===null||f===void 0?void 0:f.slice())||[]});a&&y.run.push(a),h&&(y.preventDefault=!0)};for(let o of n){let l=o.scope?o.scope.split(" "):["editor"];if(o.any)for(let h of l){let c=t[h]||(t[h]=Object.create(null));c._any||(c._any={preventDefault:!1,run:[]});for(let f in c)c[f].run.push(o.any)}let a=o[e]||o.key;if(a)for(let h of l)r(h,a,o.run,o.preventDefault),o.shift&&r(h,"Shift-"+a,o.shift,o.preventDefault)}return t}function uu(n,e,t,i){let s=Kc(e),r=ce(s,0),o=Ce(r)==s.length&&s!=" ",l="",a=!1;tt&&tt.view==t&&tt.scope==i&&(l=tt.prefix+" ",(a=Ia.indexOf(e.keyCode)<0)&&(tt=null));let h=new Set,c=p=>{if(p){for(let y of p.run)if(!h.has(y)&&(h.add(y),y(t,e)))return!0;p.preventDefault&&(a=!0)}return!1},f=n[i],u,d;if(f){if(c(f[l+Wi(s,e,!o)]))return!0;if(o&&(e.shiftKey||e.altKey||e.metaKey||r>127)&&(u=lt[e.keyCode])&&u!=s){if(c(f[l+Wi(u,e,!0)]))return!0;if(e.shiftKey&&(d=gi[e.keyCode])!=s&&d!=u&&c(f[l+Wi(d,e,!1)]))return!0}else if(o&&e.shiftKey&&c(f[l+Wi(s,e,!0)]))return!0;if(c(f._any))return!0}return a}const Ua=!A.ios,li=D.define({combine(n){return Tt(n,{cursorBlinkRate:1200,drawRangeCursor:!0},{cursorBlinkRate:(e,t)=>Math.min(e,t),drawRangeCursor:(e,t)=>e||t})}});function du(n={}){return[li.of(n),pu,mu,va.of(!0)]}class Ga{constructor(e,t,i,s,r){this.left=e,this.top=t,this.width=i,this.height=s,this.className=r}draw(){let e=document.createElement("div");return e.className=this.className,this.adjust(e),e}adjust(e){e.style.left=this.left+"px",e.style.top=this.top+"px",this.width>=0&&(e.style.width=this.width+"px"),e.style.height=this.height+"px"}eq(e){return this.left==e.left&&this.top==e.top&&this.width==e.width&&this.height==e.height&&this.className==e.className}}const pu=ue.fromClass(class{constructor(n){this.view=n,this.rangePieces=[],this.cursors=[],this.measureReq={read:this.readPos.bind(this),write:this.drawSel.bind(this)},this.selectionLayer=n.scrollDOM.appendChild(document.createElement("div")),this.selectionLayer.className="cm-selectionLayer",this.selectionLayer.setAttribute("aria-hidden","true"),this.cursorLayer=n.scrollDOM.appendChild(document.createElement("div")),this.cursorLayer.className="cm-cursorLayer",this.cursorLayer.setAttribute("aria-hidden","true"),n.requestMeasure(this.measureReq),this.setBlinkRate()}setBlinkRate(){this.cursorLayer.style.animationDuration=this.view.state.facet(li).cursorBlinkRate+"ms"}update(n){let e=n.startState.facet(li)!=n.state.facet(li);(e||n.selectionSet||n.geometryChanged||n.viewportChanged)&&this.view.requestMeasure(this.measureReq),n.transactions.some(t=>t.scrollIntoView)&&(this.cursorLayer.style.animationName=this.cursorLayer.style.animationName=="cm-blink"?"cm-blink2":"cm-blink"),e&&this.setBlinkRate()}readPos(){let{state:n}=this.view,e=n.facet(li),t=n.selection.ranges.map(s=>s.empty?[]:gu(this.view,s)).reduce((s,r)=>s.concat(r)),i=[];for(let s of n.selection.ranges){let r=s==n.selection.main;if(s.empty?!r||Ua:e.drawRangeCursor){let o=yu(this.view,s,r);o&&i.push(o)}}return{rangePieces:t,cursors:i}}drawSel({rangePieces:n,cursors:e}){if(n.length!=this.rangePieces.length||n.some((t,i)=>!t.eq(this.rangePieces[i]))){this.selectionLayer.textContent="";for(let t of n)this.selectionLayer.appendChild(t.draw());this.rangePieces=n}if(e.length!=this.cursors.length||e.some((t,i)=>!t.eq(this.cursors[i]))){let t=this.cursorLayer.children;if(t.length!==e.length){this.cursorLayer.textContent="";for(const i of e)this.cursorLayer.appendChild(i.draw())}else e.forEach((i,s)=>i.adjust(t[s]));this.cursors=e}}destroy(){this.selectionLayer.remove(),this.cursorLayer.remove()}}),Ja={".cm-line":{"& ::selection":{backgroundColor:"transparent !important"},"&::selection":{backgroundColor:"transparent !important"}}};Ua&&(Ja[".cm-line"].caretColor="transparent !important");const mu=Ai.highest(O.theme(Ja));function $a(n){let e=n.scrollDOM.getBoundingClientRect();return{left:(n.textDirection==Y.LTR?e.left:e.right-n.scrollDOM.clientWidth)-n.scrollDOM.scrollLeft,top:e.top-n.scrollDOM.scrollTop}}function Oo(n,e,t){let i=w.cursor(e);return{from:Math.max(t.from,n.moveToLineBoundary(i,!1,!0).from),to:Math.min(t.to,n.moveToLineBoundary(i,!0,!0).from),type:W.Text}}function Bo(n,e){let t=n.lineBlockAt(e);if(Array.isArray(t.type)){for(let i of t.type)if(i.to>e||i.to==e&&(i.to==t.to||i.type==W.Text))return i}return t}function gu(n,e){if(e.to<=n.viewport.from||e.from>=n.viewport.to)return[];let t=Math.max(e.from,n.viewport.from),i=Math.min(e.to,n.viewport.to),s=n.textDirection==Y.LTR,r=n.contentDOM,o=r.getBoundingClientRect(),l=$a(n),a=window.getComputedStyle(r.firstChild),h=o.left+parseInt(a.paddingLeft)+Math.min(0,parseInt(a.textIndent)),c=o.right-parseInt(a.paddingRight),f=Bo(n,t),u=Bo(n,i),d=f.type==W.Text?f:null,p=u.type==W.Text?u:null;if(n.lineWrapping&&(d&&(d=Oo(n,t,d)),p&&(p=Oo(n,i,p))),d&&p&&d.from==p.from)return g(b(e.from,e.to,d));{let v=d?b(e.from,null,d):k(f,!1),S=p?b(null,e.to,p):k(u,!0),C=[];return(d||f).to<(p||u).from-1?C.push(y(h,v.bottom,c,S.top)):v.bottom<S.top&&n.elementAtHeight((v.bottom+S.top)/2).type==W.Text&&(v.bottom=S.top=(v.bottom+S.top)/2),g(v).concat(C).concat(g(S))}function y(v,S,C,T){return new Ga(v-l.left,S-l.top-.01,C-v,T-S+.01,"cm-selectionBackground")}function g({top:v,bottom:S,horizontal:C}){let T=[];for(let B=0;B<C.length;B+=2)T.push(y(C[B],v,C[B+1],S));return T}function b(v,S,C){let T=1e9,B=-1e9,j=[];function I(K,X,M,U,J){let se=n.coordsAtPos(K,K==C.to?-2:2),G=n.coordsAtPos(M,M==C.from?2:-2);T=Math.min(se.top,G.top,T),B=Math.max(se.bottom,G.bottom,B),J==Y.LTR?j.push(s&&X?h:se.left,s&&U?c:G.right):j.push(!s&&U?h:G.left,!s&&X?c:se.right)}let P=v??C.from,F=S??C.to;for(let K of n.visibleRanges)if(K.to>P&&K.from<F)for(let X=Math.max(K.from,P),M=Math.min(K.to,F);;){let U=n.state.doc.lineAt(X);for(let J of n.bidiSpans(U)){let se=J.from+U.from,G=J.to+U.from;if(se>=M)break;G>X&&I(Math.max(se,X),v==null&&se<=P,Math.min(G,M),S==null&&G>=F,J.dir)}if(X=U.to+1,X>=M)break}return j.length==0&&I(P,v==null,F,S==null,n.textDirection),{top:T,bottom:B,horizontal:j}}function k(v,S){let C=o.top+(S?v.top:v.bottom);return{top:C,bottom:C,horizontal:[]}}}function yu(n,e,t){let i=n.coordsAtPos(e.head,e.assoc||1);if(!i)return null;let s=$a(n);return new Ga(i.left-s.left,i.top-s.top,-1,i.bottom-i.top,t?"cm-cursor cm-cursor-primary":"cm-cursor cm-cursor-secondary")}function Po(n,e,t,i,s){e.lastIndex=0;for(let r=n.iterRange(t,i),o=t,l;!r.next().done;o+=r.value.length)if(!r.lineBreak)for(;l=e.exec(r.value);)s(o+l.index,l)}function bu(n,e){let t=n.visibleRanges;if(t.length==1&&t[0].from==n.viewport.from&&t[0].to==n.viewport.to)return t;let i=[];for(let{from:s,to:r}of t)s=Math.max(n.state.doc.lineAt(s).from,s-e),r=Math.min(n.state.doc.lineAt(r).to,r+e),i.length&&i[i.length-1].to>=s?i[i.length-1].to=r:i.push({from:s,to:r});return i}class wu{constructor(e){const{regexp:t,decoration:i,decorate:s,boundary:r,maxLength:o=1e3}=e;if(!t.global)throw new RangeError("The regular expression given to MatchDecorator should have its 'g' flag set");if(this.regexp=t,s)this.addMatch=(l,a,h,c)=>s(c,h,h+l[0].length,l,a);else if(typeof i=="function")this.addMatch=(l,a,h,c)=>{let f=i(l,a,h);f&&c(h,h+l[0].length,f)};else if(i)this.addMatch=(l,a,h,c)=>c(h,h+l[0].length,i);else throw new RangeError("Either 'decorate' or 'decoration' should be provided to MatchDecorator");this.boundary=r,this.maxLength=o}createDeco(e){let t=new vt,i=t.add.bind(t);for(let{from:s,to:r}of bu(e,this.maxLength))Po(e.state.doc,this.regexp,s,r,(o,l)=>this.addMatch(l,e,o,i));return t.finish()}updateDeco(e,t){let i=1e9,s=-1;return e.docChanged&&e.changes.iterChanges((r,o,l,a)=>{a>e.view.viewport.from&&l<e.view.viewport.to&&(i=Math.min(l,i),s=Math.max(a,s))}),e.viewportChanged||s-i>1e3?this.createDeco(e.view):s>-1?this.updateRange(e.view,t.map(e.changes),i,s):t}updateRange(e,t,i,s){for(let r of e.visibleRanges){let o=Math.max(r.from,i),l=Math.min(r.to,s);if(l>o){let a=e.state.doc.lineAt(o),h=a.to<l?e.state.doc.lineAt(l):a,c=Math.max(r.from,a.from),f=Math.min(r.to,h.to);if(this.boundary){for(;o>a.from;o--)if(this.boundary.test(a.text[o-1-a.from])){c=o;break}for(;l<h.to;l++)if(this.boundary.test(h.text[l-h.from])){f=l;break}}let u=[],d,p=(y,g,b)=>u.push(b.range(y,g));if(a==h)for(this.regexp.lastIndex=c-a.from;(d=this.regexp.exec(a.text))&&d.index<f-a.from;)this.addMatch(d,e,d.index+a.from,p);else Po(e.state.doc,this.regexp,c,f,(y,g)=>this.addMatch(g,e,y,p));t=t.update({filterFrom:c,filterTo:f,filter:(y,g)=>y<c||g>f,add:u})}}return t}}const _s=/x/.unicode!=null?"gu":"g",xu=new RegExp(`[\0-\b
--­؜​‎‏\u2028\u2029‭‮⁦⁧⁩\uFEFF￹-￼]`,_s),ku={0:"null",7:"bell",8:"backspace",10:"newline",11:"vertical tab",13:"carriage return",27:"escape",8203:"zero width space",8204:"zero width non-joiner",8205:"zero width joiner",8206:"left-to-right mark",8207:"right-to-left mark",8232:"line separator",8237:"left-to-right override",8238:"right-to-left override",8294:"left-to-right isolate",8295:"right-to-left isolate",8297:"pop directional isolate",8233:"paragraph separator",65279:"zero width no-break space",65532:"object replacement"};let jn=null;function vu(){var n;if(jn==null&&typeof document<"u"&&document.body){let e=document.body.style;jn=((n=e.tabSize)!==null&&n!==void 0?n:e.MozTabSize)!=null}return jn||!1}const tn=D.define({combine(n){let e=Tt(n,{render:null,specialChars:xu,addSpecialChars:null});return(e.replaceTabs=!vu())&&(e.specialChars=new RegExp("	|"+e.specialChars.source,_s)),e.addSpecialChars&&(e.specialChars=new RegExp(e.specialChars.source+"|"+e.addSpecialChars.source,_s)),e}});function Su(n={}){return[tn.of(n),Cu()]}let Eo=null;function Cu(){return Eo||(Eo=ue.fromClass(class{constructor(n){this.view=n,this.decorations=E.none,this.decorationCache=Object.create(null),this.decorator=this.makeDecorator(n.state.facet(tn)),this.decorations=this.decorator.createDeco(n)}makeDecorator(n){return new wu({regexp:n.specialChars,decoration:(e,t,i)=>{let{doc:s}=t.state,r=ce(e[0],0);if(r==9){let o=s.lineAt(i),l=t.state.tabSize,a=Mi(o.text,l,i-o.from);return E.replace({widget:new Tu((l-a%l)*this.view.defaultCharacterWidth)})}return this.decorationCache[r]||(this.decorationCache[r]=E.replace({widget:new Du(n,r)}))},boundary:n.replaceTabs?void 0:/[^]/})}update(n){let e=n.state.facet(tn);n.startState.facet(tn)!=e?(this.decorator=this.makeDecorator(e),this.decorations=this.decorator.createDeco(n.view)):this.decorations=this.decorator.updateDeco(n,this.decorations)}},{decorations:n=>n.decorations}))}const Au="•";function Mu(n){return n>=32?Au:n==10?"␤":String.fromCharCode(9216+n)}class Du extends Ge{constructor(e,t){super(),this.options=e,this.code=t}eq(e){return e.code==this.code}toDOM(e){let t=Mu(this.code),i=e.state.phrase("Control character")+" "+(ku[this.code]||"0x"+this.code.toString(16)),s=this.options.render&&this.options.render(this.code,i,t);if(s)return s;let r=document.createElement("span");return r.textContent=t,r.title=i,r.setAttribute("aria-label",i),r.className="cm-specialChar",r}ignoreEvent(){return!1}}class Tu extends Ge{constructor(e){super(),this.width=e}eq(e){return e.width==this.width}toDOM(){let e=document.createElement("span");return e.textContent="	",e.className="cm-tab",e.style.width=this.width+"px",e}ignoreEvent(){return!1}}class Ou extends Ge{constructor(e){super(),this.content=e}toDOM(){let e=document.createElement("span");return e.className="cm-placeholder",e.style.pointerEvents="none",e.appendChild(typeof this.content=="string"?document.createTextNode(this.content):this.content),typeof this.content=="string"?e.setAttribute("aria-label","placeholder "+this.content):e.setAttribute("aria-hidden","true"),e}ignoreEvent(){return!1}}function Bu(n){return ue.fromClass(class{constructor(e){this.view=e,this.placeholder=E.set([E.widget({widget:new Ou(n),side:1}).range(0)])}get decorations(){return this.view.state.doc.length?E.none:this.placeholder}},{decorations:e=>e.decorations})}const qs=2e3;function Pu(n,e,t){let i=Math.min(e.line,t.line),s=Math.max(e.line,t.line),r=[];if(e.off>qs||t.off>qs||e.col<0||t.col<0){let o=Math.min(e.off,t.off),l=Math.max(e.off,t.off);for(let a=i;a<=s;a++){let h=n.doc.line(a);h.length<=l&&r.push(w.range(h.from+o,h.to+l))}}else{let o=Math.min(e.col,t.col),l=Math.max(e.col,t.col);for(let a=i;a<=s;a++){let h=n.doc.line(a),c=vs(h.text,o,n.tabSize,!0);if(c<0)r.push(w.cursor(h.to));else{let f=vs(h.text,l,n.tabSize);r.push(w.range(h.from+c,h.from+f))}}}return r}function Eu(n,e){let t=n.coordsAtPos(n.viewport.from);return t?Math.round(Math.abs((t.left-e)/n.defaultCharacterWidth)):-1}function Ro(n,e){let t=n.posAtCoords({x:e.clientX,y:e.clientY},!1),i=n.state.doc.lineAt(t),s=t-i.from,r=s>qs?-1:s==i.length?Eu(n,e.clientX):Mi(i.text,n.state.tabSize,t-i.from);return{line:i.number,col:r,off:s}}function Ru(n,e){let t=Ro(n,e),i=n.state.selection;return t?{update(s){if(s.docChanged){let r=s.changes.mapPos(s.startState.doc.line(t.line).from),o=s.state.doc.lineAt(r);t={line:o.number,col:t.col,off:Math.min(t.off,o.length)},i=i.map(s.changes)}},get(s,r,o){let l=Ro(n,s);if(!l)return i;let a=Pu(n.state,t,l);return a.length?o?w.create(a.concat(i.ranges)):w.create(a):i}}:null}function Lu(n){let e=n?.eventFilter||(t=>t.altKey&&t.button==0);return O.mouseSelectionStyle.of((t,i)=>e(i)?Ru(t,i):null)}const Iu={Alt:[18,n=>n.altKey],Control:[17,n=>n.ctrlKey],Shift:[16,n=>n.shiftKey],Meta:[91,n=>n.metaKey]},Nu={style:"cursor: crosshair"};function Vu(n={}){let[e,t]=Iu[n.key||"Alt"],i=ue.fromClass(class{constructor(s){this.view=s,this.isDown=!1}set(s){this.isDown!=s&&(this.isDown=s,this.view.update([]))}},{eventHandlers:{keydown(s){this.set(s.keyCode==e||t(s))},keyup(s){(s.keyCode==e||!t(s))&&this.set(!1)},mousemove(s){this.set(t(s))}}});return[i,O.contentAttributes.of(s=>{var r;return!((r=s.plugin(i))===null||r===void 0)&&r.isDown?Nu:null})]}const Kn="-10000px";class Ya{constructor(e,t,i){this.facet=t,this.createTooltipView=i,this.input=e.state.facet(t),this.tooltips=this.input.filter(s=>s),this.tooltipViews=this.tooltips.map(i)}update(e){var t;let i=e.state.facet(this.facet),s=i.filter(o=>o);if(i===this.input){for(let o of this.tooltipViews)o.update&&o.update(e);return!1}let r=[];for(let o=0;o<s.length;o++){let l=s[o],a=-1;if(l){for(let h=0;h<this.tooltips.length;h++){let c=this.tooltips[h];c&&c.create==l.create&&(a=h)}if(a<0)r[o]=this.createTooltipView(l);else{let h=r[o]=this.tooltipViews[a];h.update&&h.update(e)}}}for(let o of this.tooltipViews)r.indexOf(o)<0&&(o.dom.remove(),(t=o.destroy)===null||t===void 0||t.call(o));return this.input=i,this.tooltips=s,this.tooltipViews=r,!0}}function Fu(n){let{win:e}=n;return{top:0,left:0,bottom:e.innerHeight,right:e.innerWidth}}const Un=D.define({combine:n=>{var e,t,i;return{position:A.ios?"absolute":((e=n.find(s=>s.position))===null||e===void 0?void 0:e.position)||"fixed",parent:((t=n.find(s=>s.parent))===null||t===void 0?void 0:t.parent)||null,tooltipSpace:((i=n.find(s=>s.tooltipSpace))===null||i===void 0?void 0:i.tooltipSpace)||Fu}}}),Xa=ue.fromClass(class{constructor(n){this.view=n,this.inView=!0,this.lastTransaction=0,this.measureTimeout=-1;let e=n.state.facet(Un);this.position=e.position,this.parent=e.parent,this.classes=n.themeClasses,this.createContainer(),this.measureReq={read:this.readMeasure.bind(this),write:this.writeMeasure.bind(this),key:this},this.manager=new Ya(n,ur,t=>this.createTooltip(t)),this.intersectionObserver=typeof IntersectionObserver=="function"?new IntersectionObserver(t=>{Date.now()>this.lastTransaction-50&&t.length>0&&t[t.length-1].intersectionRatio<1&&this.measureSoon()},{threshold:[1]}):null,this.observeIntersection(),n.win.addEventListener("resize",this.measureSoon=this.measureSoon.bind(this)),this.maybeMeasure()}createContainer(){this.parent?(this.container=document.createElement("div"),this.container.style.position="relative",this.container.className=this.view.themeClasses,this.parent.appendChild(this.container)):this.container=this.view.dom}observeIntersection(){if(this.intersectionObserver){this.intersectionObserver.disconnect();for(let n of this.manager.tooltipViews)this.intersectionObserver.observe(n.dom)}}measureSoon(){this.measureTimeout<0&&(this.measureTimeout=setTimeout(()=>{this.measureTimeout=-1,this.maybeMeasure()},50))}update(n){n.transactions.length&&(this.lastTransaction=Date.now());let e=this.manager.update(n);e&&this.observeIntersection();let t=e||n.geometryChanged,i=n.state.facet(Un);if(i.position!=this.position){this.position=i.position;for(let s of this.manager.tooltipViews)s.dom.style.position=this.position;t=!0}if(i.parent!=this.parent){this.parent&&this.container.remove(),this.parent=i.parent,this.createContainer();for(let s of this.manager.tooltipViews)this.container.appendChild(s.dom);t=!0}else this.parent&&this.view.themeClasses!=this.classes&&(this.classes=this.container.className=this.view.themeClasses);t&&this.maybeMeasure()}createTooltip(n){let e=n.create(this.view);if(e.dom.classList.add("cm-tooltip"),n.arrow&&!e.dom.querySelector(".cm-tooltip > .cm-tooltip-arrow")){let t=document.createElement("div");t.className="cm-tooltip-arrow",e.dom.appendChild(t)}return e.dom.style.position=this.position,e.dom.style.top=Kn,this.container.appendChild(e.dom),e.mount&&e.mount(this.view),e}destroy(){var n,e;this.view.win.removeEventListener("resize",this.measureSoon);for(let t of this.manager.tooltipViews)t.dom.remove(),(n=t.destroy)===null||n===void 0||n.call(t);(e=this.intersectionObserver)===null||e===void 0||e.disconnect(),clearTimeout(this.measureTimeout)}readMeasure(){let n=this.view.dom.getBoundingClientRect();return{editor:n,parent:this.parent?this.container.getBoundingClientRect():n,pos:this.manager.tooltips.map((e,t)=>{let i=this.manager.tooltipViews[t];return i.getCoords?i.getCoords(e.pos):this.view.coordsAtPos(e.pos)}),size:this.manager.tooltipViews.map(({dom:e})=>e.getBoundingClientRect()),space:this.view.state.facet(Un).tooltipSpace(this.view)}}writeMeasure(n){let{editor:e,space:t}=n,i=[];for(let s=0;s<this.manager.tooltips.length;s++){let r=this.manager.tooltips[s],o=this.manager.tooltipViews[s],{dom:l}=o,a=n.pos[s],h=n.size[s];if(!a||a.bottom<=Math.max(e.top,t.top)||a.top>=Math.min(e.bottom,t.bottom)||a.right<Math.max(e.left,t.left)-.1||a.left>Math.min(e.right,t.right)+.1){l.style.top=Kn;continue}let c=r.arrow?o.dom.querySelector(".cm-tooltip-arrow"):null,f=c?7:0,u=h.right-h.left,d=h.bottom-h.top,p=o.offset||Wu,y=this.view.textDirection==Y.LTR,g=h.width>t.right-t.left?y?t.left:t.right-h.width:y?Math.min(a.left-(c?14:0)+p.x,t.right-u):Math.max(t.left,a.left-u+(c?14:0)-p.x),b=!!r.above;!r.strictSide&&(b?a.top-(h.bottom-h.top)-p.y<t.top:a.bottom+(h.bottom-h.top)+p.y>t.bottom)&&b==t.bottom-a.bottom>a.top-t.top&&(b=!b);let k=b?a.top-d-f-p.y:a.bottom+f+p.y,v=g+u;if(o.overlap!==!0)for(let S of i)S.left<v&&S.right>g&&S.top<k+d&&S.bottom>k&&(k=b?S.top-d-2-f:S.bottom+f+2);this.position=="absolute"?(l.style.top=k-n.parent.top+"px",l.style.left=g-n.parent.left+"px"):(l.style.top=k+"px",l.style.left=g+"px"),c&&(c.style.left=`${a.left+(y?p.x:-p.x)-(g+14-7)}px`),o.overlap!==!0&&i.push({left:g,top:k,right:v,bottom:k+d}),l.classList.toggle("cm-tooltip-above",b),l.classList.toggle("cm-tooltip-below",!b),o.positioned&&o.positioned()}}maybeMeasure(){if(this.manager.tooltips.length&&(this.view.inView&&this.view.requestMeasure(this.measureReq),this.inView!=this.view.inView&&(this.inView=this.view.inView,!this.inView)))for(let n of this.manager.tooltipViews)n.dom.style.top=Kn}},{eventHandlers:{scroll(){this.maybeMeasure()}}}),Hu=O.baseTheme({".cm-tooltip":{zIndex:100},"&light .cm-tooltip":{border:"1px solid #bbb",backgroundColor:"#f5f5f5"},"&light .cm-tooltip-section:not(:first-child)":{borderTop:"1px solid #bbb"},"&dark .cm-tooltip":{backgroundColor:"#333338",color:"white"},".cm-tooltip-arrow":{height:"7px",width:`${7*2}px`,position:"absolute",zIndex:-1,overflow:"hidden","&:before, &:after":{content:"''",position:"absolute",width:0,height:0,borderLeft:"7px solid transparent",borderRight:"7px solid transparent"},".cm-tooltip-above &":{bottom:"-7px","&:before":{borderTop:"7px solid #bbb"},"&:after":{borderTop:"7px solid #f5f5f5",bottom:"1px"}},".cm-tooltip-below &":{top:"-7px","&:before":{borderBottom:"7px solid #bbb"},"&:after":{borderBottom:"7px solid #f5f5f5",top:"1px"}}},"&dark .cm-tooltip .cm-tooltip-arrow":{"&:before":{borderTopColor:"#333338",borderBottomColor:"#333338"},"&:after":{borderTopColor:"transparent",borderBottomColor:"transparent"}}}),Wu={x:0,y:0},ur=D.define({enables:[Xa,Hu]}),un=D.define();class dr{constructor(e){this.view=e,this.mounted=!1,this.dom=document.createElement("div"),this.dom.classList.add("cm-tooltip-hover"),this.manager=new Ya(e,un,t=>this.createHostedView(t))}static create(e){return new dr(e)}createHostedView(e){let t=e.create(this.view);return t.dom.classList.add("cm-tooltip-section"),this.dom.appendChild(t.dom),this.mounted&&t.mount&&t.mount(this.view),t}mount(e){for(let t of this.manager.tooltipViews)t.mount&&t.mount(e);this.mounted=!0}positioned(){for(let e of this.manager.tooltipViews)e.positioned&&e.positioned()}update(e){this.manager.update(e)}}const zu=ur.compute([un],n=>{let e=n.facet(un).filter(t=>t);return e.length===0?null:{pos:Math.min(...e.map(t=>t.pos)),end:Math.max(...e.filter(t=>t.end!=null).map(t=>t.end)),create:dr.create,above:e[0].above,arrow:e.some(t=>t.arrow)}});class _u{constructor(e,t,i,s,r){this.view=e,this.source=t,this.field=i,this.setHover=s,this.hoverTime=r,this.hoverTimeout=-1,this.restartTimeout=-1,this.pending=null,this.lastMove={x:0,y:0,target:e.dom,time:0},this.checkHover=this.checkHover.bind(this),e.dom.addEventListener("mouseleave",this.mouseleave=this.mouseleave.bind(this)),e.dom.addEventListener("mousemove",this.mousemove=this.mousemove.bind(this))}update(){this.pending&&(this.pending=null,clearTimeout(this.restartTimeout),this.restartTimeout=setTimeout(()=>this.startHover(),20))}get active(){return this.view.state.field(this.field)}checkHover(){if(this.hoverTimeout=-1,this.active)return;let e=Date.now()-this.lastMove.time;e<this.hoverTime?this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime-e):this.startHover()}startHover(){clearTimeout(this.restartTimeout);let{lastMove:e}=this,t=this.view.contentDOM.contains(e.target)?this.view.posAtCoords(e):null;if(t==null)return;let i=this.view.coordsAtPos(t);if(i==null||e.y<i.top||e.y>i.bottom||e.x<i.left-this.view.defaultCharacterWidth||e.x>i.right+this.view.defaultCharacterWidth)return;let s=this.view.bidiSpans(this.view.state.doc.lineAt(t)).find(l=>l.from<=t&&l.to>=t),r=s&&s.dir==Y.RTL?-1:1,o=this.source(this.view,t,e.x<i.left?-r:r);if(o?.then){let l=this.pending={pos:t};o.then(a=>{this.pending==l&&(this.pending=null,a&&this.view.dispatch({effects:this.setHover.of(a)}))},a=>Ee(this.view.state,a,"hover tooltip"))}else o&&this.view.dispatch({effects:this.setHover.of(o)})}mousemove(e){var t;this.lastMove={x:e.clientX,y:e.clientY,target:e.target,time:Date.now()},this.hoverTimeout<0&&(this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime));let i=this.active;if(i&&!qu(this.lastMove.target)||this.pending){let{pos:s}=i||this.pending,r=(t=i?.end)!==null&&t!==void 0?t:s;(s==r?this.view.posAtCoords(this.lastMove)!=s:!ju(this.view,s,r,e.clientX,e.clientY,6))&&(this.view.dispatch({effects:this.setHover.of(null)}),this.pending=null)}}mouseleave(){clearTimeout(this.hoverTimeout),this.hoverTimeout=-1,this.active&&this.view.dispatch({effects:this.setHover.of(null)})}destroy(){clearTimeout(this.hoverTimeout),this.view.dom.removeEventListener("mouseleave",this.mouseleave),this.view.dom.removeEventListener("mousemove",this.mousemove)}}function qu(n){for(let e=n;e;e=e.parentNode)if(e.nodeType==1&&e.classList.contains("cm-tooltip"))return!0;return!1}function ju(n,e,t,i,s,r){let o=document.createRange(),l=n.domAtPos(e),a=n.domAtPos(t);o.setEnd(a.node,a.offset),o.setStart(l.node,l.offset);let h=o.getClientRects();o.detach();for(let c=0;c<h.length;c++){let f=h[c];if(Math.max(f.top-s,s-f.bottom,f.left-i,i-f.right)<=r)return!0}return!1}function Ku(n,e={}){let t=R.define(),i=be.define({create(){return null},update(s,r){if(s&&(e.hideOnChange&&(r.docChanged||r.selection)||e.hideOn&&e.hideOn(r,s)))return null;if(s&&r.docChanged){let o=r.changes.mapPos(s.pos,-1,le.TrackDel);if(o==null)return null;let l=Object.assign(Object.create(null),s);l.pos=o,s.end!=null&&(l.end=r.changes.mapPos(s.end)),s=l}for(let o of r.effects)o.is(t)&&(s=o.value),o.is(Gu)&&(s=null);return s},provide:s=>un.from(s)});return[i,ue.define(s=>new _u(s,n,i,t,e.hoverTime||300)),zu]}function Uu(n,e){let t=n.plugin(Xa);if(!t)return null;let i=t.manager.tooltips.indexOf(e);return i<0?null:t.manager.tooltipViews[i]}const Gu=R.define(),Lo=D.define({combine(n){let e,t;for(let i of n)e=e||i.topContainer,t=t||i.bottomContainer;return{topContainer:e,bottomContainer:t}}});function Ju(n,e){let t=n.plugin(Za),i=t?t.specs.indexOf(e):-1;return i>-1?t.panels[i]:null}const Za=ue.fromClass(class{constructor(n){this.input=n.state.facet(js),this.specs=this.input.filter(t=>t),this.panels=this.specs.map(t=>t(n));let e=n.state.facet(Lo);this.top=new zi(n,!0,e.topContainer),this.bottom=new zi(n,!1,e.bottomContainer),this.top.sync(this.panels.filter(t=>t.top)),this.bottom.sync(this.panels.filter(t=>!t.top));for(let t of this.panels)t.dom.classList.add("cm-panel"),t.mount&&t.mount()}update(n){let e=n.state.facet(Lo);this.top.container!=e.topContainer&&(this.top.sync([]),this.top=new zi(n.view,!0,e.topContainer)),this.bottom.container!=e.bottomContainer&&(this.bottom.sync([]),this.bottom=new zi(n.view,!1,e.bottomContainer)),this.top.syncClasses(),this.bottom.syncClasses();let t=n.state.facet(js);if(t!=this.input){let i=t.filter(a=>a),s=[],r=[],o=[],l=[];for(let a of i){let h=this.specs.indexOf(a),c;h<0?(c=a(n.view),l.push(c)):(c=this.panels[h],c.update&&c.update(n)),s.push(c),(c.top?r:o).push(c)}this.specs=i,this.panels=s,this.top.sync(r),this.bottom.sync(o);for(let a of l)a.dom.classList.add("cm-panel"),a.mount&&a.mount()}else for(let i of this.panels)i.update&&i.update(n)}destroy(){this.top.sync([]),this.bottom.sync([])}},{provide:n=>O.scrollMargins.of(e=>{let t=e.plugin(n);return t&&{top:t.top.scrollMargin(),bottom:t.bottom.scrollMargin()}})});class zi{constructor(e,t,i){this.view=e,this.top=t,this.container=i,this.dom=void 0,this.classes="",this.panels=[],this.syncClasses()}sync(e){for(let t of this.panels)t.destroy&&e.indexOf(t)<0&&t.destroy();this.panels=e,this.syncDOM()}syncDOM(){if(this.panels.length==0){this.dom&&(this.dom.remove(),this.dom=void 0);return}if(!this.dom){this.dom=document.createElement("div"),this.dom.className=this.top?"cm-panels cm-panels-top":"cm-panels cm-panels-bottom",this.dom.style[this.top?"top":"bottom"]="0";let t=this.container||this.view.dom;t.insertBefore(this.dom,this.top?t.firstChild:null)}let e=this.dom.firstChild;for(let t of this.panels)if(t.dom.parentNode==this.dom){for(;e!=t.dom;)e=Io(e);e=e.nextSibling}else this.dom.insertBefore(t.dom,e);for(;e;)e=Io(e)}scrollMargin(){return!this.dom||this.container?0:Math.max(0,this.top?this.dom.getBoundingClientRect().bottom-Math.max(0,this.view.scrollDOM.getBoundingClientRect().top):Math.min(innerHeight,this.view.scrollDOM.getBoundingClientRect().bottom)-this.dom.getBoundingClientRect().top)}syncClasses(){if(!(!this.container||this.classes==this.view.themeClasses)){for(let e of this.classes.split(" "))e&&this.container.classList.remove(e);for(let e of(this.classes=this.view.themeClasses).split(" "))e&&this.container.classList.add(e)}}}function Io(n){let e=n.nextSibling;return n.remove(),e}const js=D.define({enables:Za});class ht extends kt{compare(e){return this==e||this.constructor==e.constructor&&this.eq(e)}eq(e){return!1}destroy(e){}}ht.prototype.elementClass="";ht.prototype.toDOM=void 0;ht.prototype.mapMode=le.TrackBefore;ht.prototype.startSide=ht.prototype.endSide=-1;ht.prototype.point=!0;const Gn=D.define(),$u={class:"",renderEmptyElements:!1,elementStyle:"",markers:()=>H.empty,lineMarker:()=>null,lineMarkerChange:null,initialSpacer:null,updateSpacer:null,domEventHandlers:{}},fi=D.define();function Yu(n){return[Qa(),fi.of(Object.assign(Object.assign({},$u),n))]}const Ks=D.define({combine:n=>n.some(e=>e)});function Qa(n){let e=[Xu];return n&&n.fixed===!1&&e.push(Ks.of(!0)),e}const Xu=ue.fromClass(class{constructor(n){this.view=n,this.prevViewport=n.viewport,this.dom=document.createElement("div"),this.dom.className="cm-gutters",this.dom.setAttribute("aria-hidden","true"),this.dom.style.minHeight=this.view.contentHeight+"px",this.gutters=n.state.facet(fi).map(e=>new Vo(n,e));for(let e of this.gutters)this.dom.appendChild(e.dom);this.fixed=!n.state.facet(Ks),this.fixed&&(this.dom.style.position="sticky"),this.syncGutters(!1),n.scrollDOM.insertBefore(this.dom,n.contentDOM)}update(n){if(this.updateGutters(n)){let e=this.prevViewport,t=n.view.viewport,i=Math.min(e.to,t.to)-Math.max(e.from,t.from);this.syncGutters(i<(t.to-t.from)*.8)}n.geometryChanged&&(this.dom.style.minHeight=this.view.contentHeight+"px"),this.view.state.facet(Ks)!=!this.fixed&&(this.fixed=!this.fixed,this.dom.style.position=this.fixed?"sticky":""),this.prevViewport=n.view.viewport}syncGutters(n){let e=this.dom.nextSibling;n&&this.dom.remove();let t=H.iter(this.view.state.facet(Gn),this.view.viewport.from),i=[],s=this.gutters.map(r=>new Zu(r,this.view.viewport,-this.view.documentPadding.top));for(let r of this.view.viewportLineBlocks){let o;if(Array.isArray(r.type)){for(let l of r.type)if(l.type==W.Text){o=l;break}}else o=r.type==W.Text?r:void 0;if(o){i.length&&(i=[]),eh(t,i,r.from);for(let l of s)l.line(this.view,o,i)}}for(let r of s)r.finish();n&&this.view.scrollDOM.insertBefore(this.dom,e)}updateGutters(n){let e=n.startState.facet(fi),t=n.state.facet(fi),i=n.docChanged||n.heightChanged||n.viewportChanged||!H.eq(n.startState.facet(Gn),n.state.facet(Gn),n.view.viewport.from,n.view.viewport.to);if(e==t)for(let s of this.gutters)s.update(n)&&(i=!0);else{i=!0;let s=[];for(let r of t){let o=e.indexOf(r);o<0?s.push(new Vo(this.view,r)):(this.gutters[o].update(n),s.push(this.gutters[o]))}for(let r of this.gutters)r.dom.remove(),s.indexOf(r)<0&&r.destroy();for(let r of s)this.dom.appendChild(r.dom);this.gutters=s}return i}destroy(){for(let n of this.gutters)n.destroy();this.dom.remove()}},{provide:n=>O.scrollMargins.of(e=>{let t=e.plugin(n);return!t||t.gutters.length==0||!t.fixed?null:e.textDirection==Y.LTR?{left:t.dom.offsetWidth}:{right:t.dom.offsetWidth}})});function No(n){return Array.isArray(n)?n:[n]}function eh(n,e,t){for(;n.value&&n.from<=t;)n.from==t&&e.push(n.value),n.next()}class Zu{constructor(e,t,i){this.gutter=e,this.height=i,this.localMarkers=[],this.i=0,this.cursor=H.iter(e.markers,t.from)}line(e,t,i){this.localMarkers.length&&(this.localMarkers=[]),eh(this.cursor,this.localMarkers,t.from);let s=i.length?this.localMarkers.concat(i):this.localMarkers,r=this.gutter.config.lineMarker(e,t,s);r&&s.unshift(r);let o=this.gutter;if(s.length==0&&!o.config.renderEmptyElements)return;let l=t.top-this.height;if(this.i==o.elements.length){let a=new th(e,t.height,l,s);o.elements.push(a),o.dom.appendChild(a.dom)}else o.elements[this.i].update(e,t.height,l,s);this.height=t.bottom,this.i++}finish(){let e=this.gutter;for(;e.elements.length>this.i;){let t=e.elements.pop();e.dom.removeChild(t.dom),t.destroy()}}}class Vo{constructor(e,t){this.view=e,this.config=t,this.elements=[],this.spacer=null,this.dom=document.createElement("div"),this.dom.className="cm-gutter"+(this.config.class?" "+this.config.class:"");for(let i in t.domEventHandlers)this.dom.addEventListener(i,s=>{let r=e.lineBlockAtHeight(s.clientY-e.documentTop);t.domEventHandlers[i](e,r,s)&&s.preventDefault()});this.markers=No(t.markers(e)),t.initialSpacer&&(this.spacer=new th(e,0,0,[t.initialSpacer(e)]),this.dom.appendChild(this.spacer.dom),this.spacer.dom.style.cssText+="visibility: hidden; pointer-events: none")}update(e){let t=this.markers;if(this.markers=No(this.config.markers(e.view)),this.spacer&&this.config.updateSpacer){let s=this.config.updateSpacer(this.spacer.markers[0],e);s!=this.spacer.markers[0]&&this.spacer.update(e.view,0,0,[s])}let i=e.view.viewport;return!H.eq(this.markers,t,i.from,i.to)||(this.config.lineMarkerChange?this.config.lineMarkerChange(e):!1)}destroy(){for(let e of this.elements)e.destroy()}}class th{constructor(e,t,i,s){this.height=-1,this.above=0,this.markers=[],this.dom=document.createElement("div"),this.dom.className="cm-gutterElement",this.update(e,t,i,s)}update(e,t,i,s){this.height!=t&&(this.dom.style.height=(this.height=t)+"px"),this.above!=i&&(this.dom.style.marginTop=(this.above=i)?i+"px":""),Qu(this.markers,s)||this.setMarkers(e,s)}setMarkers(e,t){let i="cm-gutterElement",s=this.dom.firstChild;for(let r=0,o=0;;){let l=o,a=r<t.length?t[r++]:null,h=!1;if(a){let c=a.elementClass;c&&(i+=" "+c);for(let f=o;f<this.markers.length;f++)if(this.markers[f].compare(a)){l=f,h=!0;break}}else l=this.markers.length;for(;o<l;){let c=this.markers[o++];if(c.toDOM){c.destroy(s);let f=s.nextSibling;s.remove(),s=f}}if(!a)break;a.toDOM&&(h?s=s.nextSibling:this.dom.insertBefore(a.toDOM(e),s)),h&&o++}this.dom.className=i,this.markers=t}destroy(){this.setMarkers(null,[])}}function Qu(n,e){if(n.length!=e.length)return!1;for(let t=0;t<n.length;t++)if(!n[t].compare(e[t]))return!1;return!0}const ed=D.define(),Rt=D.define({combine(n){return Tt(n,{formatNumber:String,domEventHandlers:{}},{domEventHandlers(e,t){let i=Object.assign({},e);for(let s in t){let r=i[s],o=t[s];i[s]=r?(l,a,h)=>r(l,a,h)||o(l,a,h):o}return i}})}});class Jn extends ht{constructor(e){super(),this.number=e}eq(e){return this.number==e.number}toDOM(){return document.createTextNode(this.number)}}function $n(n,e){return n.state.facet(Rt).formatNumber(e,n.state)}const td=fi.compute([Rt],n=>({class:"cm-lineNumbers",renderEmptyElements:!1,markers(e){return e.state.facet(ed)},lineMarker(e,t,i){return i.some(s=>s.toDOM)?null:new Jn($n(e,e.state.doc.lineAt(t.from).number))},lineMarkerChange:e=>e.startState.facet(Rt)!=e.state.facet(Rt),initialSpacer(e){return new Jn($n(e,Fo(e.state.doc.lines)))},updateSpacer(e,t){let i=$n(t.view,Fo(t.view.state.doc.lines));return i==e.number?e:new Jn(i)},domEventHandlers:n.facet(Rt).domEventHandlers}));function id(n={}){return[Rt.of(n),Qa(),td]}function Fo(n){let e=9;for(;e<n;)e=e*10+9;return e}const nd=1024;let sd=0;class Me{constructor(e,t){this.from=e,this.to=t}}class L{constructor(e={}){this.id=sd++,this.perNode=!!e.perNode,this.deserialize=e.deserialize||(()=>{throw new Error("This node type doesn't define a deserialize function")})}add(e){if(this.perNode)throw new RangeError("Can't add per-node props to node types");return typeof e!="function"&&(e=ge.match(e)),t=>{let i=e(t);return i===void 0?null:[this,i]}}}L.closedBy=new L({deserialize:n=>n.split(" ")});L.openedBy=new L({deserialize:n=>n.split(" ")});L.group=new L({deserialize:n=>n.split(" ")});L.contextHash=new L({perNode:!0});L.lookAhead=new L({perNode:!0});L.mounted=new L({perNode:!0});class rd{constructor(e,t,i){this.tree=e,this.overlay=t,this.parser=i}}const od=Object.create(null);class ge{constructor(e,t,i,s=0){this.name=e,this.props=t,this.id=i,this.flags=s}static define(e){let t=e.props&&e.props.length?Object.create(null):od,i=(e.top?1:0)|(e.skipped?2:0)|(e.error?4:0)|(e.name==null?8:0),s=new ge(e.name||"",t,e.id,i);if(e.props){for(let r of e.props)if(Array.isArray(r)||(r=r(s)),r){if(r[0].perNode)throw new RangeError("Can't store a per-node prop on a node type");t[r[0].id]=r[1]}}return s}prop(e){return this.props[e.id]}get isTop(){return(this.flags&1)>0}get isSkipped(){return(this.flags&2)>0}get isError(){return(this.flags&4)>0}get isAnonymous(){return(this.flags&8)>0}is(e){if(typeof e=="string"){if(this.name==e)return!0;let t=this.prop(L.group);return t?t.indexOf(e)>-1:!1}return this.id==e}static match(e){let t=Object.create(null);for(let i in e)for(let s of i.split(" "))t[s]=e[i];return i=>{for(let s=i.prop(L.group),r=-1;r<(s?s.length:0);r++){let o=t[r<0?i.name:s[r]];if(o)return o}}}}ge.none=new ge("",Object.create(null),0,8);class pr{constructor(e){this.types=e;for(let t=0;t<e.length;t++)if(e[t].id!=t)throw new RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...e){let t=[];for(let i of this.types){let s=null;for(let r of e){let o=r(i);o&&(s||(s=Object.assign({},i.props)),s[o[0].id]=o[1])}t.push(s?new ge(i.name,s,i.id,i.flags):i)}return new pr(t)}}const _i=new WeakMap,Ho=new WeakMap;var Z;(function(n){n[n.ExcludeBuffers=1]="ExcludeBuffers",n[n.IncludeAnonymous=2]="IncludeAnonymous",n[n.IgnoreMounts=4]="IgnoreMounts",n[n.IgnoreOverlays=8]="IgnoreOverlays"})(Z||(Z={}));class z{constructor(e,t,i,s,r){if(this.type=e,this.children=t,this.positions=i,this.length=s,this.props=null,r&&r.length){this.props=Object.create(null);for(let[o,l]of r)this.props[typeof o=="number"?o:o.id]=l}}toString(){let e=this.prop(L.mounted);if(e&&!e.overlay)return e.tree.toString();let t="";for(let i of this.children){let s=i.toString();s&&(t&&(t+=","),t+=s)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(t.length?"("+t+")":""):t}cursor(e=0){return new xi(this.topNode,e)}cursorAt(e,t=0,i=0){let s=_i.get(this)||this.topNode,r=new xi(s);return r.moveTo(e,t),_i.set(this,r._tree),r}get topNode(){return new Oe(this,0,0,null)}resolve(e,t=0){let i=jt(_i.get(this)||this.topNode,e,t,!1);return _i.set(this,i),i}resolveInner(e,t=0){let i=jt(Ho.get(this)||this.topNode,e,t,!0);return Ho.set(this,i),i}iterate(e){let{enter:t,leave:i,from:s=0,to:r=this.length}=e;for(let o=this.cursor((e.mode||0)|Z.IncludeAnonymous);;){let l=!1;if(o.from<=r&&o.to>=s&&(o.type.isAnonymous||t(o)!==!1)){if(o.firstChild())continue;l=!0}for(;l&&i&&!o.type.isAnonymous&&i(o),!o.nextSibling();){if(!o.parent())return;l=!0}}}prop(e){return e.perNode?this.props?this.props[e.id]:void 0:this.type.prop(e)}get propValues(){let e=[];if(this.props)for(let t in this.props)e.push([+t,this.props[t]]);return e}balance(e={}){return this.children.length<=8?this:yr(ge.none,this.children,this.positions,0,this.children.length,0,this.length,(t,i,s)=>new z(this.type,t,i,s,this.propValues),e.makeTree||((t,i,s)=>new z(ge.none,t,i,s)))}static build(e){return ad(e)}}z.empty=new z(ge.none,[],[],0);class mr{constructor(e,t){this.buffer=e,this.index=t}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new mr(this.buffer,this.index)}}class Ot{constructor(e,t,i){this.buffer=e,this.length=t,this.set=i}get type(){return ge.none}toString(){let e=[];for(let t=0;t<this.buffer.length;)e.push(this.childString(t)),t=this.buffer[t+3];return e.join(",")}childString(e){let t=this.buffer[e],i=this.buffer[e+3],s=this.set.types[t],r=s.name;if(/\W/.test(r)&&!s.isError&&(r=JSON.stringify(r)),e+=4,i==e)return r;let o=[];for(;e<i;)o.push(this.childString(e)),e=this.buffer[e+3];return r+"("+o.join(",")+")"}findChild(e,t,i,s,r){let{buffer:o}=this,l=-1;for(let a=e;a!=t&&!(ih(r,s,o[a+1],o[a+2])&&(l=a,i>0));a=o[a+3]);return l}slice(e,t,i){let s=this.buffer,r=new Uint16Array(t-e),o=0;for(let l=e,a=0;l<t;){r[a++]=s[l++],r[a++]=s[l++]-i;let h=r[a++]=s[l++]-i;r[a++]=s[l++]-e,o=Math.max(o,h)}return new Ot(r,o,this.set)}}function ih(n,e,t,i){switch(n){case-2:return t<e;case-1:return i>=e&&t<e;case 0:return t<e&&i>e;case 1:return t<=e&&i>e;case 2:return i>e;case 4:return!0}}function nh(n,e){let t=n.childBefore(e);for(;t;){let i=t.lastChild;if(!i||i.to!=t.to)break;i.type.isError&&i.from==i.to?(n=t,t=i.prevSibling):t=i}return n}function jt(n,e,t,i){for(var s;n.from==n.to||(t<1?n.from>=e:n.from>e)||(t>-1?n.to<=e:n.to<e);){let o=!i&&n instanceof Oe&&n.index<0?null:n.parent;if(!o)return n;n=o}let r=i?0:Z.IgnoreOverlays;if(i)for(let o=n,l=o.parent;l;o=l,l=o.parent)o instanceof Oe&&o.index<0&&((s=l.enter(e,t,r))===null||s===void 0?void 0:s.from)!=o.from&&(n=l);for(;;){let o=n.enter(e,t,r);if(!o)return n;n=o}}class Oe{constructor(e,t,i,s){this._tree=e,this.from=t,this.index=i,this._parent=s}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(e,t,i,s,r=0){for(let o=this;;){for(let{children:l,positions:a}=o._tree,h=t>0?l.length:-1;e!=h;e+=t){let c=l[e],f=a[e]+o.from;if(ih(s,i,f,f+c.length)){if(c instanceof Ot){if(r&Z.ExcludeBuffers)continue;let u=c.findChild(0,c.buffer.length,t,i-f,s);if(u>-1)return new _e(new ld(o,c,e,f),null,u)}else if(r&Z.IncludeAnonymous||!c.type.isAnonymous||gr(c)){let u;if(!(r&Z.IgnoreMounts)&&c.props&&(u=c.prop(L.mounted))&&!u.overlay)return new Oe(u.tree,f,e,o);let d=new Oe(c,f,e,o);return r&Z.IncludeAnonymous||!d.type.isAnonymous?d:d.nextChild(t<0?c.children.length-1:0,t,i,s)}}}if(r&Z.IncludeAnonymous||!o.type.isAnonymous||(o.index>=0?e=o.index+t:e=t<0?-1:o._parent._tree.children.length,o=o._parent,!o))return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(e){return this.nextChild(0,1,e,2)}childBefore(e){return this.nextChild(this._tree.children.length-1,-1,e,-2)}enter(e,t,i=0){let s;if(!(i&Z.IgnoreOverlays)&&(s=this._tree.prop(L.mounted))&&s.overlay){let r=e-this.from;for(let{from:o,to:l}of s.overlay)if((t>0?o<=r:o<r)&&(t<0?l>=r:l>r))return new Oe(s.tree,s.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,e,t,i)}nextSignificantParent(){let e=this;for(;e.type.isAnonymous&&e._parent;)e=e._parent;return e}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}cursor(e=0){return new xi(this,e)}get tree(){return this._tree}toTree(){return this._tree}resolve(e,t=0){return jt(this,e,t,!1)}resolveInner(e,t=0){return jt(this,e,t,!0)}enterUnfinishedNodesBefore(e){return nh(this,e)}getChild(e,t=null,i=null){let s=dn(this,e,t,i);return s.length?s[0]:null}getChildren(e,t=null,i=null){return dn(this,e,t,i)}toString(){return this._tree.toString()}get node(){return this}matchContext(e){return pn(this,e)}}function dn(n,e,t,i){let s=n.cursor(),r=[];if(!s.firstChild())return r;if(t!=null){for(;!s.type.is(t);)if(!s.nextSibling())return r}for(;;){if(i!=null&&s.type.is(i))return r;if(s.type.is(e)&&r.push(s.node),!s.nextSibling())return i==null?r:[]}}function pn(n,e,t=e.length-1){for(let i=n.parent;t>=0;i=i.parent){if(!i)return!1;if(!i.type.isAnonymous){if(e[t]&&e[t]!=i.name)return!1;t--}}return!0}class ld{constructor(e,t,i,s){this.parent=e,this.buffer=t,this.index=i,this.start=s}}class _e{get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}constructor(e,t,i){this.context=e,this._parent=t,this.index=i,this.type=e.buffer.set.types[e.buffer.buffer[i]]}child(e,t,i){let{buffer:s}=this.context,r=s.findChild(this.index+4,s.buffer[this.index+3],e,t-this.context.start,i);return r<0?null:new _e(this.context,this,r)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(e){return this.child(1,e,2)}childBefore(e){return this.child(-1,e,-2)}enter(e,t,i=0){if(i&Z.ExcludeBuffers)return null;let{buffer:s}=this.context,r=s.findChild(this.index+4,s.buffer[this.index+3],t>0?1:-1,e-this.context.start,t);return r<0?null:new _e(this.context,this,r)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(e){return this._parent?null:this.context.parent.nextChild(this.context.index+e,e,0,4)}get nextSibling(){let{buffer:e}=this.context,t=e.buffer[this.index+3];return t<(this._parent?e.buffer[this._parent.index+3]:e.buffer.length)?new _e(this.context,this._parent,t):this.externalSibling(1)}get prevSibling(){let{buffer:e}=this.context,t=this._parent?this._parent.index+4:0;return this.index==t?this.externalSibling(-1):new _e(this.context,this._parent,e.findChild(t,this.index,-1,0,4))}cursor(e=0){return new xi(this,e)}get tree(){return null}toTree(){let e=[],t=[],{buffer:i}=this.context,s=this.index+4,r=i.buffer[this.index+3];if(r>s){let o=i.buffer[this.index+1];e.push(i.slice(s,r,o)),t.push(0)}return new z(this.type,e,t,this.to-this.from)}resolve(e,t=0){return jt(this,e,t,!1)}resolveInner(e,t=0){return jt(this,e,t,!0)}enterUnfinishedNodesBefore(e){return nh(this,e)}toString(){return this.context.buffer.childString(this.index)}getChild(e,t=null,i=null){let s=dn(this,e,t,i);return s.length?s[0]:null}getChildren(e,t=null,i=null){return dn(this,e,t,i)}get node(){return this}matchContext(e){return pn(this,e)}}class xi{get name(){return this.type.name}constructor(e,t=0){if(this.mode=t,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,e instanceof Oe)this.yieldNode(e);else{this._tree=e.context.parent,this.buffer=e.context;for(let i=e._parent;i;i=i._parent)this.stack.unshift(i.index);this.bufferNode=e,this.yieldBuf(e.index)}}yieldNode(e){return e?(this._tree=e,this.type=e.type,this.from=e.from,this.to=e.to,!0):!1}yieldBuf(e,t){this.index=e;let{start:i,buffer:s}=this.buffer;return this.type=t||s.set.types[s.buffer[e]],this.from=i+s.buffer[e+1],this.to=i+s.buffer[e+2],!0}yield(e){return e?e instanceof Oe?(this.buffer=null,this.yieldNode(e)):(this.buffer=e.context,this.yieldBuf(e.index,e.type)):!1}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(e,t,i){if(!this.buffer)return this.yield(this._tree.nextChild(e<0?this._tree._tree.children.length-1:0,e,t,i,this.mode));let{buffer:s}=this.buffer,r=s.findChild(this.index+4,s.buffer[this.index+3],e,t-this.buffer.start,i);return r<0?!1:(this.stack.push(this.index),this.yieldBuf(r))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(e){return this.enterChild(1,e,2)}childBefore(e){return this.enterChild(-1,e,-2)}enter(e,t,i=this.mode){return this.buffer?i&Z.ExcludeBuffers?!1:this.enterChild(1,e,t):this.yield(this._tree.enter(e,t,i))}parent(){if(!this.buffer)return this.yieldNode(this.mode&Z.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let e=this.mode&Z.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(e)}sibling(e){if(!this.buffer)return this._tree._parent?this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+e,e,0,4,this.mode)):!1;let{buffer:t}=this.buffer,i=this.stack.length-1;if(e<0){let s=i<0?0:this.stack[i]+4;if(this.index!=s)return this.yieldBuf(t.findChild(s,this.index,-1,0,4))}else{let s=t.buffer[this.index+3];if(s<(i<0?t.buffer.length:t.buffer[this.stack[i]+3]))return this.yieldBuf(s)}return i<0?this.yield(this.buffer.parent.nextChild(this.buffer.index+e,e,0,4,this.mode)):!1}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(e){let t,i,{buffer:s}=this;if(s){if(e>0){if(this.index<s.buffer.buffer.length)return!1}else for(let r=0;r<this.index;r++)if(s.buffer.buffer[r+3]<this.index)return!1;({index:t,parent:i}=s)}else({index:t,_parent:i}=this._tree);for(;i;{index:t,_parent:i}=i)if(t>-1)for(let r=t+e,o=e<0?-1:i._tree.children.length;r!=o;r+=e){let l=i._tree.children[r];if(this.mode&Z.IncludeAnonymous||l instanceof Ot||!l.type.isAnonymous||gr(l))return!1}return!0}move(e,t){if(t&&this.enterChild(e,0,4))return!0;for(;;){if(this.sibling(e))return!0;if(this.atLastNode(e)||!this.parent())return!1}}next(e=!0){return this.move(1,e)}prev(e=!0){return this.move(-1,e)}moveTo(e,t=0){for(;(this.from==this.to||(t<1?this.from>=e:this.from>e)||(t>-1?this.to<=e:this.to<e))&&this.parent(););for(;this.enterChild(1,e,t););return this}get node(){if(!this.buffer)return this._tree;let e=this.bufferNode,t=null,i=0;if(e&&e.context==this.buffer)e:for(let s=this.index,r=this.stack.length;r>=0;){for(let o=e;o;o=o._parent)if(o.index==s){if(s==this.index)return o;t=o,i=r+1;break e}s=this.stack[--r]}for(let s=i;s<this.stack.length;s++)t=new _e(this.buffer,t,this.stack[s]);return this.bufferNode=new _e(this.buffer,t,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(e,t){for(let i=0;;){let s=!1;if(this.type.isAnonymous||e(this)!==!1){if(this.firstChild()){i++;continue}this.type.isAnonymous||(s=!0)}for(;s&&t&&t(this),s=this.type.isAnonymous,!this.nextSibling();){if(!i)return;this.parent(),i--,s=!0}}}matchContext(e){if(!this.buffer)return pn(this.node,e);let{buffer:t}=this.buffer,{types:i}=t.set;for(let s=e.length-1,r=this.stack.length-1;s>=0;r--){if(r<0)return pn(this.node,e,s);let o=i[t.buffer[this.stack[r]]];if(!o.isAnonymous){if(e[s]&&e[s]!=o.name)return!1;s--}}return!0}}function gr(n){return n.children.some(e=>e instanceof Ot||!e.type.isAnonymous||gr(e))}function ad(n){var e;let{buffer:t,nodeSet:i,maxBufferLength:s=nd,reused:r=[],minRepeatType:o=i.types.length}=n,l=Array.isArray(t)?new mr(t,t.length):t,a=i.types,h=0,c=0;function f(S,C,T,B,j){let{id:I,start:P,end:F,size:K}=l,X=c;for(;K<0;)if(l.next(),K==-1){let G=r[I];T.push(G),B.push(P-S);return}else if(K==-3){h=I;return}else if(K==-4){c=I;return}else throw new RangeError(`Unrecognized record size: ${K}`);let M=a[I],U,J,se=P-S;if(F-P<=s&&(J=y(l.pos-C,j))){let G=new Uint16Array(J.size-J.skip),ee=l.pos-J.size,$e=G.length;for(;l.pos>ee;)$e=g(J.start,G,$e);U=new Ot(G,F-J.start,i),se=J.start-S}else{let G=l.pos-K;l.next();let ee=[],$e=[],ut=I>=o?I:-1,Bt=0,Pi=F;for(;l.pos>G;)ut>=0&&l.id==ut&&l.size>=0?(l.end<=Pi-s&&(d(ee,$e,P,Bt,l.end,Pi,ut,X),Bt=ee.length,Pi=l.end),l.next()):f(P,G,ee,$e,ut);if(ut>=0&&Bt>0&&Bt<ee.length&&d(ee,$e,P,Bt,P,Pi,ut,X),ee.reverse(),$e.reverse(),ut>-1&&Bt>0){let Ir=u(M);U=yr(M,ee,$e,0,ee.length,0,F-P,Ir,Ir)}else U=p(M,ee,$e,F-P,X-F)}T.push(U),B.push(se)}function u(S){return(C,T,B)=>{let j=0,I=C.length-1,P,F;if(I>=0&&(P=C[I])instanceof z){if(!I&&P.type==S&&P.length==B)return P;(F=P.prop(L.lookAhead))&&(j=T[I]+P.length+F)}return p(S,C,T,B,j)}}function d(S,C,T,B,j,I,P,F){let K=[],X=[];for(;S.length>B;)K.push(S.pop()),X.push(C.pop()+T-j);S.push(p(i.types[P],K,X,I-j,F-I)),C.push(j-T)}function p(S,C,T,B,j=0,I){if(h){let P=[L.contextHash,h];I=I?[P].concat(I):[P]}if(j>25){let P=[L.lookAhead,j];I=I?[P].concat(I):[P]}return new z(S,C,T,B,I)}function y(S,C){let T=l.fork(),B=0,j=0,I=0,P=T.end-s,F={size:0,start:0,skip:0};e:for(let K=T.pos-S;T.pos>K;){let X=T.size;if(T.id==C&&X>=0){F.size=B,F.start=j,F.skip=I,I+=4,B+=4,T.next();continue}let M=T.pos-X;if(X<0||M<K||T.start<P)break;let U=T.id>=o?4:0,J=T.start;for(T.next();T.pos>M;){if(T.size<0)if(T.size==-3)U+=4;else break e;else T.id>=o&&(U+=4);T.next()}j=J,B+=X,I+=U}return(C<0||B==S)&&(F.size=B,F.start=j,F.skip=I),F.size>4?F:void 0}function g(S,C,T){let{id:B,start:j,end:I,size:P}=l;if(l.next(),P>=0&&B<o){let F=T;if(P>4){let K=l.pos-(P-4);for(;l.pos>K;)T=g(S,C,T)}C[--T]=F,C[--T]=I-S,C[--T]=j-S,C[--T]=B}else P==-3?h=B:P==-4&&(c=B);return T}let b=[],k=[];for(;l.pos>0;)f(n.start||0,n.bufferStart||0,b,k,-1);let v=(e=n.length)!==null&&e!==void 0?e:b.length?k[0]+b[0].length:0;return new z(a[n.topID],b.reverse(),k.reverse(),v)}const Wo=new WeakMap;function nn(n,e){if(!n.isAnonymous||e instanceof Ot||e.type!=n)return 1;let t=Wo.get(e);if(t==null){t=1;for(let i of e.children){if(i.type!=n||!(i instanceof z)){t=1;break}t+=nn(n,i)}Wo.set(e,t)}return t}function yr(n,e,t,i,s,r,o,l,a){let h=0;for(let p=i;p<s;p++)h+=nn(n,e[p]);let c=Math.ceil(h*1.5/8),f=[],u=[];function d(p,y,g,b,k){for(let v=g;v<b;){let S=v,C=y[v],T=nn(n,p[v]);for(v++;v<b;v++){let B=nn(n,p[v]);if(T+B>=c)break;T+=B}if(v==S+1){if(T>c){let B=p[S];d(B.children,B.positions,0,B.children.length,y[S]+k);continue}f.push(p[S])}else{let B=y[v-1]+p[v-1].length-C;f.push(yr(n,p,y,S,v,C,B,null,a))}u.push(C+k-r)}}return d(e,t,i,s,0),(l||a)(f,u,o)}class Yg{constructor(){this.map=new WeakMap}setBuffer(e,t,i){let s=this.map.get(e);s||this.map.set(e,s=new Map),s.set(t,i)}getBuffer(e,t){let i=this.map.get(e);return i&&i.get(t)}set(e,t){e instanceof _e?this.setBuffer(e.context.buffer,e.index,t):e instanceof Oe&&this.map.set(e.tree,t)}get(e){return e instanceof _e?this.getBuffer(e.context.buffer,e.index):e instanceof Oe?this.map.get(e.tree):void 0}cursorSet(e,t){e.buffer?this.setBuffer(e.buffer.buffer,e.index,t):this.map.set(e.tree,t)}cursorGet(e){return e.buffer?this.getBuffer(e.buffer.buffer,e.index):this.map.get(e.tree)}}class Xe{constructor(e,t,i,s,r=!1,o=!1){this.from=e,this.to=t,this.tree=i,this.offset=s,this.open=(r?1:0)|(o?2:0)}get openStart(){return(this.open&1)>0}get openEnd(){return(this.open&2)>0}static addTree(e,t=[],i=!1){let s=[new Xe(0,e.length,e,0,!1,i)];for(let r of t)r.to>e.length&&s.push(r);return s}static applyChanges(e,t,i=128){if(!t.length)return e;let s=[],r=1,o=e.length?e[0]:null;for(let l=0,a=0,h=0;;l++){let c=l<t.length?t[l]:null,f=c?c.fromA:1e9;if(f-a>=i)for(;o&&o.from<f;){let u=o;if(a>=u.from||f<=u.to||h){let d=Math.max(u.from,a)-h,p=Math.min(u.to,f)-h;u=d>=p?null:new Xe(d,p,u.tree,u.offset+h,l>0,!!c)}if(u&&s.push(u),o.to>f)break;o=r<e.length?e[r++]:null}if(!c)break;a=c.toA,h=c.toA-c.toB}return s}}class sh{startParse(e,t,i){return typeof e=="string"&&(e=new hd(e)),i=i?i.length?i.map(s=>new Me(s.from,s.to)):[new Me(0,0)]:[new Me(0,e.length)],this.createParse(e,t||[],i)}parse(e,t,i){let s=this.startParse(e,t,i);for(;;){let r=s.advance();if(r)return r}}}class hd{constructor(e){this.string=e}get length(){return this.string.length}chunk(e){return this.string.slice(e)}get lineChunks(){return!1}read(e,t){return this.string.slice(e,t)}}function Xg(n){return(e,t,i,s)=>new fd(e,n,t,i,s)}class zo{constructor(e,t,i,s,r){this.parser=e,this.parse=t,this.overlay=i,this.target=s,this.ranges=r}}class cd{constructor(e,t,i,s,r,o,l){this.parser=e,this.predicate=t,this.mounts=i,this.index=s,this.start=r,this.target=o,this.prev=l,this.depth=0,this.ranges=[]}}const Us=new L({perNode:!0});class fd{constructor(e,t,i,s,r){this.nest=t,this.input=i,this.fragments=s,this.ranges=r,this.inner=[],this.innerDone=0,this.baseTree=null,this.stoppedAt=null,this.baseParse=e}advance(){if(this.baseParse){let i=this.baseParse.advance();if(!i)return null;if(this.baseParse=null,this.baseTree=i,this.startInner(),this.stoppedAt!=null)for(let s of this.inner)s.parse.stopAt(this.stoppedAt)}if(this.innerDone==this.inner.length){let i=this.baseTree;return this.stoppedAt!=null&&(i=new z(i.type,i.children,i.positions,i.length,i.propValues.concat([[Us,this.stoppedAt]]))),i}let e=this.inner[this.innerDone],t=e.parse.advance();if(t){this.innerDone++;let i=Object.assign(Object.create(null),e.target.props);i[L.mounted.id]=new rd(t,e.overlay,e.parser),e.target.props=i}return null}get parsedPos(){if(this.baseParse)return 0;let e=this.input.length;for(let t=this.innerDone;t<this.inner.length;t++)this.inner[t].ranges[0].from<e&&(e=Math.min(e,this.inner[t].parse.parsedPos));return e}stopAt(e){if(this.stoppedAt=e,this.baseParse)this.baseParse.stopAt(e);else for(let t=this.innerDone;t<this.inner.length;t++)this.inner[t].parse.stopAt(e)}startInner(){let e=new pd(this.fragments),t=null,i=null,s=new xi(new Oe(this.baseTree,this.ranges[0].from,0,null),Z.IncludeAnonymous|Z.IgnoreMounts);e:for(let r,o;this.stoppedAt==null||s.from<this.stoppedAt;){let l=!0,a;if(e.hasNode(s)){if(t){let h=t.mounts.find(c=>c.frag.from<=s.from&&c.frag.to>=s.to&&c.mount.overlay);if(h)for(let c of h.mount.overlay){let f=c.from+h.pos,u=c.to+h.pos;f>=s.from&&u<=s.to&&!t.ranges.some(d=>d.from<u&&d.to>f)&&t.ranges.push({from:f,to:u})}}l=!1}else if(i&&(o=ud(i.ranges,s.from,s.to)))l=o!=2;else if(!s.type.isAnonymous&&s.from<s.to&&(r=this.nest(s,this.input))){s.tree||dd(s);let h=e.findMounts(s.from,r.parser);if(typeof r.overlay=="function")t=new cd(r.parser,r.overlay,h,this.inner.length,s.from,s.tree,t);else{let c=jo(this.ranges,r.overlay||[new Me(s.from,s.to)]);c.length&&this.inner.push(new zo(r.parser,r.parser.startParse(this.input,Ko(h,c),c),r.overlay?r.overlay.map(f=>new Me(f.from-s.from,f.to-s.from)):null,s.tree,c)),r.overlay?c.length&&(i={ranges:c,depth:0,prev:i}):l=!1}}else t&&(a=t.predicate(s))&&(a===!0&&(a=new Me(s.from,s.to)),a.from<a.to&&t.ranges.push(a));if(l&&s.firstChild())t&&t.depth++,i&&i.depth++;else for(;!s.nextSibling();){if(!s.parent())break e;if(t&&!--t.depth){let h=jo(this.ranges,t.ranges);h.length&&this.inner.splice(t.index,0,new zo(t.parser,t.parser.startParse(this.input,Ko(t.mounts,h),h),t.ranges.map(c=>new Me(c.from-t.start,c.to-t.start)),t.target,h)),t=t.prev}i&&!--i.depth&&(i=i.prev)}}}}function ud(n,e,t){for(let i of n){if(i.from>=t)break;if(i.to>e)return i.from<=e&&i.to>=t?2:1}return 0}function _o(n,e,t,i,s,r){if(e<t){let o=n.buffer[e+1];i.push(n.slice(e,t,o)),s.push(o-r)}}function dd(n){let{node:e}=n,t=0;do n.parent(),t++;while(!n.tree);let i=0,s=n.tree,r=0;for(;r=s.positions[i]+n.from,!(r<=e.from&&r+s.children[i].length>=e.to);i++);let o=s.children[i],l=o.buffer;function a(h,c,f,u,d){let p=h;for(;l[p+2]+r<=e.from;)p=l[p+3];let y=[],g=[];_o(o,h,p,y,g,u);let b=l[p+1],k=l[p+2],v=b+r==e.from&&k+r==e.to&&l[p]==e.type.id;return y.push(v?e.toTree():a(p+4,l[p+3],o.set.types[l[p]],b,k-b)),g.push(b-u),_o(o,l[p+3],c,y,g,u),new z(f,y,g,d)}s.children[i]=a(0,l.length,ge.none,0,o.length);for(let h=0;h<=t;h++)n.childAfter(e.from)}class qo{constructor(e,t){this.offset=t,this.done=!1,this.cursor=e.cursor(Z.IncludeAnonymous|Z.IgnoreMounts)}moveTo(e){let{cursor:t}=this,i=e-this.offset;for(;!this.done&&t.from<i;)t.to>=e&&t.enter(i,1,Z.IgnoreOverlays|Z.ExcludeBuffers)||t.next(!1)||(this.done=!0)}hasNode(e){if(this.moveTo(e.from),!this.done&&this.cursor.from+this.offset==e.from&&this.cursor.tree)for(let t=this.cursor.tree;;){if(t==e.tree)return!0;if(t.children.length&&t.positions[0]==0&&t.children[0]instanceof z)t=t.children[0];else break}return!1}}class pd{constructor(e){var t;if(this.fragments=e,this.curTo=0,this.fragI=0,e.length){let i=this.curFrag=e[0];this.curTo=(t=i.tree.prop(Us))!==null&&t!==void 0?t:i.to,this.inner=new qo(i.tree,-i.offset)}else this.curFrag=this.inner=null}hasNode(e){for(;this.curFrag&&e.from>=this.curTo;)this.nextFrag();return this.curFrag&&this.curFrag.from<=e.from&&this.curTo>=e.to&&this.inner.hasNode(e)}nextFrag(){var e;if(this.fragI++,this.fragI==this.fragments.length)this.curFrag=this.inner=null;else{let t=this.curFrag=this.fragments[this.fragI];this.curTo=(e=t.tree.prop(Us))!==null&&e!==void 0?e:t.to,this.inner=new qo(t.tree,-t.offset)}}findMounts(e,t){var i;let s=[];if(this.inner){this.inner.cursor.moveTo(e,1);for(let r=this.inner.cursor.node;r;r=r.parent){let o=(i=r.tree)===null||i===void 0?void 0:i.prop(L.mounted);if(o&&o.parser==t)for(let l=this.fragI;l<this.fragments.length;l++){let a=this.fragments[l];if(a.from>=r.to)break;a.tree==this.curFrag.tree&&s.push({frag:a,pos:r.from-a.offset,mount:o})}}}return s}}function jo(n,e){let t=null,i=e;for(let s=1,r=0;s<n.length;s++){let o=n[s-1].to,l=n[s].from;for(;r<i.length;r++){let a=i[r];if(a.from>=l)break;a.to<=o||(t||(i=t=e.slice()),a.from<o?(t[r]=new Me(a.from,o),a.to>l&&t.splice(r+1,0,new Me(l,a.to))):a.to>l?t[r--]=new Me(l,a.to):t.splice(r--,1))}}return i}function md(n,e,t,i){let s=0,r=0,o=!1,l=!1,a=-1e9,h=[];for(;;){let c=s==n.length?1e9:o?n[s].to:n[s].from,f=r==e.length?1e9:l?e[r].to:e[r].from;if(o!=l){let u=Math.max(a,t),d=Math.min(c,f,i);u<d&&h.push(new Me(u,d))}if(a=Math.min(c,f),a==1e9)break;c==a&&(o?(o=!1,s++):o=!0),f==a&&(l?(l=!1,r++):l=!0)}return h}function Ko(n,e){let t=[];for(let{pos:i,mount:s,frag:r}of n){let o=i+(s.overlay?s.overlay[0].from:0),l=o+s.tree.length,a=Math.max(r.from,o),h=Math.min(r.to,l);if(s.overlay){let c=s.overlay.map(u=>new Me(u.from+i,u.to+i)),f=md(e,c,a,h);for(let u=0,d=a;;u++){let p=u==f.length,y=p?h:f[u].from;if(y>d&&t.push(new Xe(d,y,s.tree,-o,r.from>=d||r.openStart,r.to<=y||r.openEnd)),p)break;d=f[u].to}}else t.push(new Xe(a,h,s.tree,-o,r.from>=o||r.openStart,r.to<=l||r.openEnd))}return t}let gd=0;class He{constructor(e,t,i){this.set=e,this.base=t,this.modified=i,this.id=gd++}static define(e){if(e?.base)throw new Error("Can not derive from a modified tag");let t=new He([],null,[]);if(t.set.push(t),e)for(let i of e.set)t.set.push(i);return t}static defineModifier(){let e=new mn;return t=>t.modified.indexOf(e)>-1?t:mn.get(t.base||t,t.modified.concat(e).sort((i,s)=>i.id-s.id))}}let yd=0;class mn{constructor(){this.instances=[],this.id=yd++}static get(e,t){if(!t.length)return e;let i=t[0].instances.find(l=>l.base==e&&bd(t,l.modified));if(i)return i;let s=[],r=new He(s,e,t);for(let l of t)l.instances.push(r);let o=wd(t);for(let l of e.set)if(!l.modified.length)for(let a of o)s.push(mn.get(l,a));return r}}function bd(n,e){return n.length==e.length&&n.every((t,i)=>t==e[i])}function wd(n){let e=[[]];for(let t=0;t<n.length;t++)for(let i=0,s=e.length;i<s;i++)e.push(e[i].concat(n[t]));return e.sort((t,i)=>i.length-t.length)}function xd(n){let e=Object.create(null);for(let t in n){let i=n[t];Array.isArray(i)||(i=[i]);for(let s of t.split(" "))if(s){let r=[],o=2,l=s;for(let f=0;;){if(l=="..."&&f>0&&f+3==s.length){o=1;break}let u=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(l);if(!u)throw new RangeError("Invalid path: "+s);if(r.push(u[0]=="*"?"":u[0][0]=='"'?JSON.parse(u[0]):u[0]),f+=u[0].length,f==s.length)break;let d=s[f++];if(f==s.length&&d=="!"){o=0;break}if(d!="/")throw new RangeError("Invalid path: "+s);l=s.slice(f)}let a=r.length-1,h=r[a];if(!h)throw new RangeError("Invalid path: "+s);let c=new gn(i,o,a>0?r.slice(0,a):null);e[h]=c.sort(e[h])}}return rh.add(e)}const rh=new L;class gn{constructor(e,t,i,s){this.tags=e,this.mode=t,this.context=i,this.next=s}get opaque(){return this.mode==0}get inherit(){return this.mode==1}sort(e){return!e||e.depth<this.depth?(this.next=e,this):(e.next=this.sort(e.next),e)}get depth(){return this.context?this.context.length:0}}gn.empty=new gn([],2,null);function oh(n,e){let t=Object.create(null);for(let r of n)if(!Array.isArray(r.tag))t[r.tag.id]=r.class;else for(let o of r.tag)t[o.id]=r.class;let{scope:i,all:s=null}=e||{};return{style:r=>{let o=s;for(let l of r)for(let a of l.set){let h=t[a.id];if(h){o=o?o+" "+h:h;break}}return o},scope:i}}function kd(n,e){let t=null;for(let i of n){let s=i.style(e);s&&(t=t?t+" "+s:s)}return t}function vd(n,e,t,i=0,s=n.length){let r=new Sd(i,Array.isArray(e)?e:[e],t);r.highlightRange(n.cursor(),i,s,"",r.highlighters),r.flush(s)}class Sd{constructor(e,t,i){this.at=e,this.highlighters=t,this.span=i,this.class=""}startSpan(e,t){t!=this.class&&(this.flush(e),e>this.at&&(this.at=e),this.class=t)}flush(e){e>this.at&&this.class&&this.span(this.at,e,this.class)}highlightRange(e,t,i,s,r){let{type:o,from:l,to:a}=e;if(l>=i||a<=t)return;o.isTop&&(r=this.highlighters.filter(d=>!d.scope||d.scope(o)));let h=s,c=Cd(e)||gn.empty,f=kd(r,c.tags);if(f&&(h&&(h+=" "),h+=f,c.mode==1&&(s+=(s?" ":"")+f)),this.startSpan(e.from,h),c.opaque)return;let u=e.tree&&e.tree.prop(L.mounted);if(u&&u.overlay){let d=e.node.enter(u.overlay[0].from+l,1),p=this.highlighters.filter(g=>!g.scope||g.scope(u.tree.type)),y=e.firstChild();for(let g=0,b=l;;g++){let k=g<u.overlay.length?u.overlay[g]:null,v=k?k.from+l:a,S=Math.max(t,b),C=Math.min(i,v);if(S<C&&y)for(;e.from<C&&(this.highlightRange(e,S,C,s,r),this.startSpan(Math.min(C,e.to),h),!(e.to>=v||!e.nextSibling())););if(!k||v>i)break;b=k.to+l,b>t&&(this.highlightRange(d.cursor(),Math.max(t,k.from+l),Math.min(i,b),s,p),this.startSpan(b,h))}y&&e.parent()}else if(e.firstChild()){do if(!(e.to<=t)){if(e.from>=i)break;this.highlightRange(e,t,i,s,r),this.startSpan(Math.min(i,e.to),h)}while(e.nextSibling());e.parent()}}}function Cd(n){let e=n.type.prop(rh);for(;e&&e.context&&!n.matchContext(e.context);)e=e.next;return e||null}const x=He.define,qi=x(),Ze=x(),Uo=x(Ze),Go=x(Ze),Qe=x(),ji=x(Qe),Yn=x(Qe),Ve=x(),dt=x(Ve),Ie=x(),Ne=x(),Gs=x(),Qt=x(Gs),Ki=x(),m={comment:qi,lineComment:x(qi),blockComment:x(qi),docComment:x(qi),name:Ze,variableName:x(Ze),typeName:Uo,tagName:x(Uo),propertyName:Go,attributeName:x(Go),className:x(Ze),labelName:x(Ze),namespace:x(Ze),macroName:x(Ze),literal:Qe,string:ji,docString:x(ji),character:x(ji),attributeValue:x(ji),number:Yn,integer:x(Yn),float:x(Yn),bool:x(Qe),regexp:x(Qe),escape:x(Qe),color:x(Qe),url:x(Qe),keyword:Ie,self:x(Ie),null:x(Ie),atom:x(Ie),unit:x(Ie),modifier:x(Ie),operatorKeyword:x(Ie),controlKeyword:x(Ie),definitionKeyword:x(Ie),moduleKeyword:x(Ie),operator:Ne,derefOperator:x(Ne),arithmeticOperator:x(Ne),logicOperator:x(Ne),bitwiseOperator:x(Ne),compareOperator:x(Ne),updateOperator:x(Ne),definitionOperator:x(Ne),typeOperator:x(Ne),controlOperator:x(Ne),punctuation:Gs,separator:x(Gs),bracket:Qt,angleBracket:x(Qt),squareBracket:x(Qt),paren:x(Qt),brace:x(Qt),content:Ve,heading:dt,heading1:x(dt),heading2:x(dt),heading3:x(dt),heading4:x(dt),heading5:x(dt),heading6:x(dt),contentSeparator:x(Ve),list:x(Ve),quote:x(Ve),emphasis:x(Ve),strong:x(Ve),link:x(Ve),monospace:x(Ve),strikethrough:x(Ve),inserted:x(),deleted:x(),changed:x(),invalid:x(),meta:Ki,documentMeta:x(Ki),annotation:x(Ki),processingInstruction:x(Ki),definition:He.defineModifier(),constant:He.defineModifier(),function:He.defineModifier(),standard:He.defineModifier(),local:He.defineModifier(),special:He.defineModifier()};oh([{tag:m.link,class:"tok-link"},{tag:m.heading,class:"tok-heading"},{tag:m.emphasis,class:"tok-emphasis"},{tag:m.strong,class:"tok-strong"},{tag:m.keyword,class:"tok-keyword"},{tag:m.atom,class:"tok-atom"},{tag:m.bool,class:"tok-bool"},{tag:m.url,class:"tok-url"},{tag:m.labelName,class:"tok-labelName"},{tag:m.inserted,class:"tok-inserted"},{tag:m.deleted,class:"tok-deleted"},{tag:m.literal,class:"tok-literal"},{tag:m.string,class:"tok-string"},{tag:m.number,class:"tok-number"},{tag:[m.regexp,m.escape,m.special(m.string)],class:"tok-string2"},{tag:m.variableName,class:"tok-variableName"},{tag:m.local(m.variableName),class:"tok-variableName tok-local"},{tag:m.definition(m.variableName),class:"tok-variableName tok-definition"},{tag:m.special(m.variableName),class:"tok-variableName2"},{tag:m.definition(m.propertyName),class:"tok-propertyName tok-definition"},{tag:m.typeName,class:"tok-typeName"},{tag:m.namespace,class:"tok-namespace"},{tag:m.className,class:"tok-className"},{tag:m.macroName,class:"tok-macroName"},{tag:m.propertyName,class:"tok-propertyName"},{tag:m.operator,class:"tok-operator"},{tag:m.comment,class:"tok-comment"},{tag:m.meta,class:"tok-meta"},{tag:m.invalid,class:"tok-invalid"},{tag:m.punctuation,class:"tok-punctuation"}]);var Xn;const bt=new L;function lh(n){return D.define({combine:n?e=>e.concat(n):void 0})}const Ad=new L;class De{constructor(e,t,i=[],s=""){this.data=e,this.name=s,N.prototype.hasOwnProperty("tree")||Object.defineProperty(N.prototype,"tree",{get(){return ae(this)}}),this.parser=t,this.extension=[ct.of(this),N.languageData.of((r,o,l)=>{let a=Jo(r,o,l),h=a.type.prop(bt);if(!h)return[];let c=r.facet(h),f=a.type.prop(Ad);if(f){let u=a.resolve(o-a.from,l);for(let d of f)if(d.test(u,r)){let p=r.facet(d.facet);return d.type=="replace"?p:p.concat(c)}}return c})].concat(i)}isActiveAt(e,t,i=-1){return Jo(e,t,i).type.prop(bt)==this.data}findRegions(e){let t=e.facet(ct);if(t?.data==this.data)return[{from:0,to:e.doc.length}];if(!t||!t.allowsNesting)return[];let i=[],s=(r,o)=>{if(r.prop(bt)==this.data){i.push({from:o,to:o+r.length});return}let l=r.prop(L.mounted);if(l){if(l.tree.prop(bt)==this.data){if(l.overlay)for(let a of l.overlay)i.push({from:a.from+o,to:a.to+o});else i.push({from:o,to:o+r.length});return}else if(l.overlay){let a=i.length;if(s(l.tree,l.overlay[0].from+o),i.length>a)return}}for(let a=0;a<r.children.length;a++){let h=r.children[a];h instanceof z&&s(h,r.positions[a]+o)}};return s(ae(e),0),i}get allowsNesting(){return!0}}De.setState=R.define();function Jo(n,e,t){let i=n.facet(ct),s=ae(n).topNode;if(!i||i.allowsNesting)for(let r=s;r;r=r.enter(e,t,Z.ExcludeBuffers))r.type.isTop&&(s=r);return s}class Js extends De{constructor(e,t,i){super(e,t,[],i),this.parser=t}static define(e){let t=lh(e.languageData);return new Js(t,e.parser.configure({props:[bt.add(i=>i.isTop?t:void 0)]}),e.name)}configure(e,t){return new Js(this.data,this.parser.configure(e),t||this.name)}get allowsNesting(){return this.parser.hasWrappers()}}function ae(n){let e=n.field(De.state,!1);return e?e.tree:z.empty}class Md{constructor(e){this.doc=e,this.cursorPos=0,this.string="",this.cursor=e.iter()}get length(){return this.doc.length}syncTo(e){return this.string=this.cursor.next(e-this.cursorPos).value,this.cursorPos=e+this.string.length,this.cursorPos-this.string.length}chunk(e){return this.syncTo(e),this.string}get lineChunks(){return!0}read(e,t){let i=this.cursorPos-this.string.length;return e<i||t>=this.cursorPos?this.doc.sliceString(e,t):this.string.slice(e-i,t-i)}}let ei=null;class Kt{constructor(e,t,i=[],s,r,o,l,a){this.parser=e,this.state=t,this.fragments=i,this.tree=s,this.treeLen=r,this.viewport=o,this.skipped=l,this.scheduleOn=a,this.parse=null,this.tempSkipped=[]}static create(e,t,i){return new Kt(e,t,[],z.empty,0,i,[],null)}startParse(){return this.parser.startParse(new Md(this.state.doc),this.fragments)}work(e,t){return t!=null&&t>=this.state.doc.length&&(t=void 0),this.tree!=z.empty&&this.isDone(t??this.state.doc.length)?(this.takeTree(),!0):this.withContext(()=>{var i;if(typeof e=="number"){let s=Date.now()+e;e=()=>Date.now()>s}for(this.parse||(this.parse=this.startParse()),t!=null&&(this.parse.stoppedAt==null||this.parse.stoppedAt>t)&&t<this.state.doc.length&&this.parse.stopAt(t);;){let s=this.parse.advance();if(s)if(this.fragments=this.withoutTempSkipped(Xe.addTree(s,this.fragments,this.parse.stoppedAt!=null)),this.treeLen=(i=this.parse.stoppedAt)!==null&&i!==void 0?i:this.state.doc.length,this.tree=s,this.parse=null,this.treeLen<(t??this.state.doc.length))this.parse=this.startParse();else return!0;if(e())return!1}})}takeTree(){let e,t;this.parse&&(e=this.parse.parsedPos)>=this.treeLen&&((this.parse.stoppedAt==null||this.parse.stoppedAt>e)&&this.parse.stopAt(e),this.withContext(()=>{for(;!(t=this.parse.advance()););}),this.treeLen=e,this.tree=t,this.fragments=this.withoutTempSkipped(Xe.addTree(this.tree,this.fragments,!0)),this.parse=null)}withContext(e){let t=ei;ei=this;try{return e()}finally{ei=t}}withoutTempSkipped(e){for(let t;t=this.tempSkipped.pop();)e=$o(e,t.from,t.to);return e}changes(e,t){let{fragments:i,tree:s,treeLen:r,viewport:o,skipped:l}=this;if(this.takeTree(),!e.empty){let a=[];if(e.iterChangedRanges((h,c,f,u)=>a.push({fromA:h,toA:c,fromB:f,toB:u})),i=Xe.applyChanges(i,a),s=z.empty,r=0,o={from:e.mapPos(o.from,-1),to:e.mapPos(o.to,1)},this.skipped.length){l=[];for(let h of this.skipped){let c=e.mapPos(h.from,1),f=e.mapPos(h.to,-1);c<f&&l.push({from:c,to:f})}}}return new Kt(this.parser,t,i,s,r,o,l,this.scheduleOn)}updateViewport(e){if(this.viewport.from==e.from&&this.viewport.to==e.to)return!1;this.viewport=e;let t=this.skipped.length;for(let i=0;i<this.skipped.length;i++){let{from:s,to:r}=this.skipped[i];s<e.to&&r>e.from&&(this.fragments=$o(this.fragments,s,r),this.skipped.splice(i--,1))}return this.skipped.length>=t?!1:(this.reset(),!0)}reset(){this.parse&&(this.takeTree(),this.parse=null)}skipUntilInView(e,t){this.skipped.push({from:e,to:t})}static getSkippingParser(e){return new class extends sh{createParse(t,i,s){let r=s[0].from,o=s[s.length-1].to;return{parsedPos:r,advance(){let a=ei;if(a){for(let h of s)a.tempSkipped.push(h);e&&(a.scheduleOn=a.scheduleOn?Promise.all([a.scheduleOn,e]):e)}return this.parsedPos=o,new z(ge.none,[],[],o-r)},stoppedAt:null,stopAt(){}}}}}isDone(e){e=Math.min(e,this.state.doc.length);let t=this.fragments;return this.treeLen>=e&&t.length&&t[0].from==0&&t[0].to>=e}static get(){return ei}}function $o(n,e,t){return Xe.applyChanges(n,[{fromA:e,toA:t,fromB:e,toB:t}])}class Ut{constructor(e){this.context=e,this.tree=e.tree}apply(e){if(!e.docChanged&&this.tree==this.context.tree)return this;let t=this.context.changes(e.changes,e.state),i=this.context.treeLen==e.startState.doc.length?void 0:Math.max(e.changes.mapPos(this.context.treeLen),t.viewport.to);return t.work(20,i)||t.takeTree(),new Ut(t)}static init(e){let t=Math.min(3e3,e.doc.length),i=Kt.create(e.facet(ct).parser,e,{from:0,to:t});return i.work(20,t)||i.takeTree(),new Ut(i)}}De.state=be.define({create:Ut.init,update(n,e){for(let t of e.effects)if(t.is(De.setState))return t.value;return e.startState.facet(ct)!=e.state.facet(ct)?Ut.init(e.state):n.apply(e)}});let ah=n=>{let e=setTimeout(()=>n(),500);return()=>clearTimeout(e)};typeof requestIdleCallback<"u"&&(ah=n=>{let e=-1,t=setTimeout(()=>{e=requestIdleCallback(n,{timeout:500-100})},100);return()=>e<0?clearTimeout(t):cancelIdleCallback(e)});const Zn=typeof navigator<"u"&&(!((Xn=navigator.scheduling)===null||Xn===void 0)&&Xn.isInputPending)?()=>navigator.scheduling.isInputPending():null,Dd=ue.fromClass(class{constructor(e){this.view=e,this.working=null,this.workScheduled=0,this.chunkEnd=-1,this.chunkBudget=-1,this.work=this.work.bind(this),this.scheduleWork()}update(e){let t=this.view.state.field(De.state).context;(t.updateViewport(e.view.viewport)||this.view.viewport.to>t.treeLen)&&this.scheduleWork(),e.docChanged&&(this.view.hasFocus&&(this.chunkBudget+=50),this.scheduleWork()),this.checkAsyncSchedule(t)}scheduleWork(){if(this.working)return;let{state:e}=this.view,t=e.field(De.state);(t.tree!=t.context.tree||!t.context.isDone(e.doc.length))&&(this.working=ah(this.work))}work(e){this.working=null;let t=Date.now();if(this.chunkEnd<t&&(this.chunkEnd<0||this.view.hasFocus)&&(this.chunkEnd=t+3e4,this.chunkBudget=3e3),this.chunkBudget<=0)return;let{state:i,viewport:{to:s}}=this.view,r=i.field(De.state);if(r.tree==r.context.tree&&r.context.isDone(s+1e5))return;let o=Date.now()+Math.min(this.chunkBudget,100,e&&!Zn?Math.max(25,e.timeRemaining()-5):1e9),l=r.context.treeLen<s&&i.doc.length>s+1e3,a=r.context.work(()=>Zn&&Zn()||Date.now()>o,s+(l?0:1e5));this.chunkBudget-=Date.now()-t,(a||this.chunkBudget<=0)&&(r.context.takeTree(),this.view.dispatch({effects:De.setState.of(new Ut(r.context))})),this.chunkBudget>0&&!(a&&!l)&&this.scheduleWork(),this.checkAsyncSchedule(r.context)}checkAsyncSchedule(e){e.scheduleOn&&(this.workScheduled++,e.scheduleOn.then(()=>this.scheduleWork()).catch(t=>Ee(this.view.state,t)).then(()=>this.workScheduled--),e.scheduleOn=null)}destroy(){this.working&&this.working()}isWorking(){return!!(this.working||this.workScheduled>0)}},{eventHandlers:{focus(){this.scheduleWork()}}}),ct=D.define({combine(n){return n.length?n[0]:null},enables:n=>[De.state,Dd,O.contentAttributes.compute([n],e=>{let t=e.facet(n);return t&&t.name?{"data-language":t.name}:{}})]});class Qg{constructor(e,t=[]){this.language=e,this.support=t,this.extension=[e,t]}}class hh{constructor(e,t,i,s,r,o=void 0){this.name=e,this.alias=t,this.extensions=i,this.filename=s,this.loadFunc=r,this.support=o,this.loading=null}load(){return this.loading||(this.loading=this.loadFunc().then(e=>this.support=e,e=>{throw this.loading=null,e}))}static of(e){let{load:t,support:i}=e;if(!t){if(!i)throw new RangeError("Must pass either 'load' or 'support' to LanguageDescription.of");t=()=>Promise.resolve(i)}return new hh(e.name,(e.alias||[]).concat(e.name).map(s=>s.toLowerCase()),e.extensions||[],e.filename,t,i)}static matchFilename(e,t){for(let s of e)if(s.filename&&s.filename.test(t))return s;let i=/\.([^.]+)$/.exec(t);if(i){for(let s of e)if(s.extensions.indexOf(i[1])>-1)return s}return null}static matchLanguageName(e,t,i=!0){t=t.toLowerCase();for(let s of e)if(s.alias.some(r=>r==t))return s;if(i)for(let s of e)for(let r of s.alias){let o=t.indexOf(r);if(o>-1&&(r.length>2||!/\w/.test(t[o-1])&&!/\w/.test(t[o+r.length])))return s}return null}}const ch=D.define(),Mn=D.define({combine:n=>{if(!n.length)return"  ";let e=n[0];if(!e||/\S/.test(e)||Array.from(e).some(t=>t!=e[0]))throw new Error("Invalid indent unit: "+JSON.stringify(n[0]));return e}});function Ct(n){let e=n.facet(Mn);return e.charCodeAt(0)==9?n.tabSize*e.length:e.length}function ki(n,e){let t="",i=n.tabSize,s=n.facet(Mn)[0];if(s=="	"){for(;e>=i;)t+="	",e-=i;s=" "}for(let r=0;r<e;r++)t+=s;return t}function br(n,e){n instanceof N&&(n=new Dn(n));for(let i of n.state.facet(ch)){let s=i(n,e);if(s!==void 0)return s}let t=ae(n.state);return t?Od(n,t,e):null}class Dn{constructor(e,t={}){this.state=e,this.options=t,this.unit=Ct(e)}lineAt(e,t=1){let i=this.state.doc.lineAt(e),{simulateBreak:s,simulateDoubleBreak:r}=this.options;return s!=null&&s>=i.from&&s<=i.to?r&&s==e?{text:"",from:e}:(t<0?s<e:s<=e)?{text:i.text.slice(s-i.from),from:s}:{text:i.text.slice(0,s-i.from),from:i.from}:i}textAfterPos(e,t=1){if(this.options.simulateDoubleBreak&&e==this.options.simulateBreak)return"";let{text:i,from:s}=this.lineAt(e,t);return i.slice(e-s,Math.min(i.length,e+100-s))}column(e,t=1){let{text:i,from:s}=this.lineAt(e,t),r=this.countColumn(i,e-s),o=this.options.overrideIndentation?this.options.overrideIndentation(s):-1;return o>-1&&(r+=o-this.countColumn(i,i.search(/\S|$/))),r}countColumn(e,t=e.length){return Mi(e,this.state.tabSize,t)}lineIndent(e,t=1){let{text:i,from:s}=this.lineAt(e,t),r=this.options.overrideIndentation;if(r){let o=r(s);if(o>-1)return o}return this.countColumn(i,i.search(/\S|$/))}get simulatedBreak(){return this.options.simulateBreak||null}}const Td=new L;function Od(n,e,t){return fh(e.resolveInner(t).enterUnfinishedNodesBefore(t),t,n)}function Bd(n){return n.pos==n.options.simulateBreak&&n.options.simulateDoubleBreak}function Pd(n){let e=n.type.prop(Td);if(e)return e;let t=n.firstChild,i;if(t&&(i=t.type.prop(L.closedBy))){let s=n.lastChild,r=s&&i.indexOf(s.name)>-1;return o=>uh(o,!0,1,void 0,r&&!Bd(o)?s.from:void 0)}return n.parent==null?Ed:null}function fh(n,e,t){for(;n;n=n.parent){let i=Pd(n);if(i)return i(wr.create(t,e,n))}return null}function Ed(){return 0}class wr extends Dn{constructor(e,t,i){super(e.state,e.options),this.base=e,this.pos=t,this.node=i}static create(e,t,i){return new wr(e,t,i)}get textAfter(){return this.textAfterPos(this.pos)}get baseIndent(){let e=this.state.doc.lineAt(this.node.from);for(;;){let t=this.node.resolve(e.from);for(;t.parent&&t.parent.from==t.from;)t=t.parent;if(Rd(t,this.node))break;e=this.state.doc.lineAt(t.from)}return this.lineIndent(e.from)}continue(){let e=this.node.parent;return e?fh(e,this.pos,this.base):0}}function Rd(n,e){for(let t=e;t;t=t.parent)if(n==t)return!0;return!1}function Ld(n){let e=n.node,t=e.childAfter(e.from),i=e.lastChild;if(!t)return null;let s=n.options.simulateBreak,r=n.state.doc.lineAt(t.from),o=s==null||s<=r.from?r.to:Math.min(r.to,s);for(let l=t.to;;){let a=e.childAfter(l);if(!a||a==i)return null;if(!a.type.isSkipped)return a.from<o?t:null;l=a.to}}function e0({closing:n,align:e=!0,units:t=1}){return i=>uh(i,e,t,n)}function uh(n,e,t,i,s){let r=n.textAfter,o=r.match(/^\s*/)[0].length,l=i&&r.slice(o,o+i.length)==i||s==n.pos+o,a=e?Ld(n):null;return a?l?n.column(a.from):n.column(a.to):n.baseIndent+(l?0:n.unit*t)}const t0=n=>n.baseIndent;function i0({except:n,units:e=1}={}){return t=>{let i=n&&n.test(t.textAfter);return t.baseIndent+(i?0:e*t.unit)}}const Id=200;function Nd(){return N.transactionFilter.of(n=>{if(!n.docChanged||!n.isUserEvent("input.type")&&!n.isUserEvent("input.complete"))return n;let e=n.startState.languageDataAt("indentOnInput",n.startState.selection.main.head);if(!e.length)return n;let t=n.newDoc,{head:i}=n.newSelection.main,s=t.lineAt(i);if(i>s.from+Id)return n;let r=t.sliceString(s.from,i);if(!e.some(h=>h.test(r)))return n;let{state:o}=n,l=-1,a=[];for(let{head:h}of o.selection.ranges){let c=o.doc.lineAt(h);if(c.from==l)continue;l=c.from;let f=br(o,c.from);if(f==null)continue;let u=/^\s*/.exec(c.text)[0],d=ki(o,f);u!=d&&a.push({from:c.from,to:c.from+u.length,insert:d})}return a.length?[n,{changes:a,sequential:!0}]:n})}const Vd=D.define(),Fd=new L;function n0(n){let e=n.firstChild,t=n.lastChild;return e&&e.to<t.from?{from:e.to,to:t.type.isError?n.to:t.from}:null}function Hd(n,e,t){let i=ae(n);if(i.length<t)return null;let s=i.resolveInner(t,1),r=null;for(let o=s;o;o=o.parent){if(o.to<=t||o.from>t)continue;if(r&&o.from<e)break;let l=o.type.prop(Fd);if(l&&(o.to<i.length-50||i.length==n.doc.length||!Wd(o))){let a=l(o,n);a&&a.from<=t&&a.from>=e&&a.to>t&&(r=a)}}return r}function Wd(n){let e=n.lastChild;return e&&e.to==n.to&&e.type.isError}function yn(n,e,t){for(let i of n.facet(Vd)){let s=i(n,e,t);if(s)return s}return Hd(n,e,t)}function dh(n,e){let t=e.mapPos(n.from,1),i=e.mapPos(n.to,-1);return t>=i?void 0:{from:t,to:i}}const Tn=R.define({map:dh}),Ti=R.define({map:dh});function ph(n){let e=[];for(let{head:t}of n.state.selection.ranges)e.some(i=>i.from<=t&&i.to>=t)||e.push(n.lineBlockAt(t));return e}const At=be.define({create(){return E.none},update(n,e){n=n.map(e.changes);for(let t of e.effects)t.is(Tn)&&!zd(n,t.value.from,t.value.to)?n=n.update({add:[Yo.range(t.value.from,t.value.to)]}):t.is(Ti)&&(n=n.update({filter:(i,s)=>t.value.from!=i||t.value.to!=s,filterFrom:t.value.from,filterTo:t.value.to}));if(e.selection){let t=!1,{head:i}=e.selection.main;n.between(i,i,(s,r)=>{s<i&&r>i&&(t=!0)}),t&&(n=n.update({filterFrom:i,filterTo:i,filter:(s,r)=>r<=i||s>=i}))}return n},provide:n=>O.decorations.from(n),toJSON(n,e){let t=[];return n.between(0,e.doc.length,(i,s)=>{t.push(i,s)}),t},fromJSON(n){if(!Array.isArray(n)||n.length%2)throw new RangeError("Invalid JSON for fold state");let e=[];for(let t=0;t<n.length;){let i=n[t++],s=n[t++];if(typeof i!="number"||typeof s!="number")throw new RangeError("Invalid JSON for fold state");e.push(Yo.range(i,s))}return E.set(e,!0)}});function bn(n,e,t){var i;let s=null;return(i=n.field(At,!1))===null||i===void 0||i.between(e,t,(r,o)=>{(!s||s.from>r)&&(s={from:r,to:o})}),s}function zd(n,e,t){let i=!1;return n.between(e,e,(s,r)=>{s==e&&r==t&&(i=!0)}),i}function mh(n,e){return n.field(At,!1)?e:e.concat(R.appendConfig.of(bh()))}const _d=n=>{for(let e of ph(n)){let t=yn(n.state,e.from,e.to);if(t)return n.dispatch({effects:mh(n.state,[Tn.of(t),gh(n,t)])}),!0}return!1},qd=n=>{if(!n.state.field(At,!1))return!1;let e=[];for(let t of ph(n)){let i=bn(n.state,t.from,t.to);i&&e.push(Ti.of(i),gh(n,i,!1))}return e.length&&n.dispatch({effects:e}),e.length>0};function gh(n,e,t=!0){let i=n.state.doc.lineAt(e.from).number,s=n.state.doc.lineAt(e.to).number;return O.announce.of(`${n.state.phrase(t?"Folded lines":"Unfolded lines")} ${i} ${n.state.phrase("to")} ${s}.`)}const jd=n=>{let{state:e}=n,t=[];for(let i=0;i<e.doc.length;){let s=n.lineBlockAt(i),r=yn(e,s.from,s.to);r&&t.push(Tn.of(r)),i=(r?n.lineBlockAt(r.to):s).to+1}return t.length&&n.dispatch({effects:mh(n.state,t)}),!!t.length},Kd=n=>{let e=n.state.field(At,!1);if(!e||!e.size)return!1;let t=[];return e.between(0,n.state.doc.length,(i,s)=>{t.push(Ti.of({from:i,to:s}))}),n.dispatch({effects:t}),!0},Ud=[{key:"Ctrl-Shift-[",mac:"Cmd-Alt-[",run:_d},{key:"Ctrl-Shift-]",mac:"Cmd-Alt-]",run:qd},{key:"Ctrl-Alt-[",run:jd},{key:"Ctrl-Alt-]",run:Kd}],Gd={placeholderDOM:null,placeholderText:"…"},yh=D.define({combine(n){return Tt(n,Gd)}});function bh(n){let e=[At,Yd];return n&&e.push(yh.of(n)),e}const Yo=E.replace({widget:new class extends Ge{toDOM(n){let{state:e}=n,t=e.facet(yh),i=r=>{let o=n.lineBlockAt(n.posAtDOM(r.target)),l=bn(n.state,o.from,o.to);l&&n.dispatch({effects:Ti.of(l)}),r.preventDefault()};if(t.placeholderDOM)return t.placeholderDOM(n,i);let s=document.createElement("span");return s.textContent=t.placeholderText,s.setAttribute("aria-label",e.phrase("folded code")),s.title=e.phrase("unfold"),s.className="cm-foldPlaceholder",s.onclick=i,s}}}),Jd={openText:"⌄",closedText:"›",markerDOM:null,domEventHandlers:{},foldingChanged:()=>!1};class Qn extends ht{constructor(e,t){super(),this.config=e,this.open=t}eq(e){return this.config==e.config&&this.open==e.open}toDOM(e){if(this.config.markerDOM)return this.config.markerDOM(this.open);let t=document.createElement("span");return t.textContent=this.open?this.config.openText:this.config.closedText,t.title=e.state.phrase(this.open?"Fold line":"Unfold line"),t}}function $d(n={}){let e=Object.assign(Object.assign({},Jd),n),t=new Qn(e,!0),i=new Qn(e,!1),s=ue.fromClass(class{constructor(o){this.from=o.viewport.from,this.markers=this.buildMarkers(o)}update(o){(o.docChanged||o.viewportChanged||o.startState.facet(ct)!=o.state.facet(ct)||o.startState.field(At,!1)!=o.state.field(At,!1)||ae(o.startState)!=ae(o.state)||e.foldingChanged(o))&&(this.markers=this.buildMarkers(o.view))}buildMarkers(o){let l=new vt;for(let a of o.viewportLineBlocks){let h=bn(o.state,a.from,a.to)?i:yn(o.state,a.from,a.to)?t:null;h&&l.add(a.from,a.from,h)}return l.finish()}}),{domEventHandlers:r}=e;return[s,Yu({class:"cm-foldGutter",markers(o){var l;return((l=o.plugin(s))===null||l===void 0?void 0:l.markers)||H.empty},initialSpacer(){return new Qn(e,!1)},domEventHandlers:Object.assign(Object.assign({},r),{click:(o,l,a)=>{if(r.click&&r.click(o,l,a))return!0;let h=bn(o.state,l.from,l.to);if(h)return o.dispatch({effects:Ti.of(h)}),!0;let c=yn(o.state,l.from,l.to);return c?(o.dispatch({effects:Tn.of(c)}),!0):!1}})}),bh()]}const Yd=O.baseTheme({".cm-foldPlaceholder":{backgroundColor:"#eee",border:"1px solid #ddd",color:"#888",borderRadius:".2em",margin:"0 1px",padding:"0 1px",cursor:"pointer"},".cm-foldGutter span":{padding:"0 1px",cursor:"pointer"}});class Jt{constructor(e,t){this.specs=e;let i;function s(l){let a=ot.newName();return(i||(i=Object.create(null)))["."+a]=l,a}const r=typeof t.all=="string"?t.all:t.all?s(t.all):void 0,o=t.scope;this.scope=o instanceof De?l=>l.prop(bt)==o.data:o?l=>l==o:void 0,this.style=oh(e.map(l=>({tag:l.tag,class:l.class||s(Object.assign({},l,{tag:null}))})),{all:r}).style,this.module=i?new ot(i):null,this.themeType=t.themeType}static define(e,t){return new Jt(e,t||{})}}const $s=D.define(),wh=D.define({combine(n){return n.length?[n[0]]:null}});function es(n){let e=n.facet($s);return e.length?e:n.facet(wh)}function xr(n,e){let t=[Zd],i;return n instanceof Jt&&(n.module&&t.push(O.styleModule.of(n.module)),i=n.themeType),e?.fallback?t.push(wh.of(n)):i?t.push($s.computeN([O.darkTheme],s=>s.facet(O.darkTheme)==(i=="dark")?[n]:[])):t.push($s.of(n)),t}class Xd{constructor(e){this.markCache=Object.create(null),this.tree=ae(e.state),this.decorations=this.buildDeco(e,es(e.state))}update(e){let t=ae(e.state),i=es(e.state),s=i!=es(e.startState);t.length<e.view.viewport.to&&!s&&t.type==this.tree.type?this.decorations=this.decorations.map(e.changes):(t!=this.tree||e.viewportChanged||s)&&(this.tree=t,this.decorations=this.buildDeco(e.view,i))}buildDeco(e,t){if(!t||!this.tree.length)return E.none;let i=new vt;for(let{from:s,to:r}of e.visibleRanges)vd(this.tree,t,(o,l,a)=>{i.add(o,l,this.markCache[a]||(this.markCache[a]=E.mark({class:a})))},s,r);return i.finish()}}const Zd=Ai.high(ue.fromClass(Xd,{decorations:n=>n.decorations})),Qd=Jt.define([{tag:m.meta,color:"#404740"},{tag:m.link,textDecoration:"underline"},{tag:m.heading,textDecoration:"underline",fontWeight:"bold"},{tag:m.emphasis,fontStyle:"italic"},{tag:m.strong,fontWeight:"bold"},{tag:m.strikethrough,textDecoration:"line-through"},{tag:m.keyword,color:"#708"},{tag:[m.atom,m.bool,m.url,m.contentSeparator,m.labelName],color:"#219"},{tag:[m.literal,m.inserted],color:"#164"},{tag:[m.string,m.deleted],color:"#a11"},{tag:[m.regexp,m.escape,m.special(m.string)],color:"#e40"},{tag:m.definition(m.variableName),color:"#00f"},{tag:m.local(m.variableName),color:"#30a"},{tag:[m.typeName,m.namespace],color:"#085"},{tag:m.className,color:"#167"},{tag:[m.special(m.variableName),m.macroName],color:"#256"},{tag:m.definition(m.propertyName),color:"#00c"},{tag:m.comment,color:"#940"},{tag:m.invalid,color:"#f00"}]),ep=1e4,tp="()[]{}",ip=new L;function Ys(n,e,t){let i=n.prop(e<0?L.openedBy:L.closedBy);if(i)return i;if(n.name.length==1){let s=t.indexOf(n.name);if(s>-1&&s%2==(e<0?1:0))return[t[s+e]]}return null}function Xs(n){let e=n.type.prop(ip);return e?e(n.node):n}function Lt(n,e,t,i={}){let s=i.maxScanDistance||ep,r=i.brackets||tp,o=ae(n),l=o.resolveInner(e,t);for(let a=l;a;a=a.parent){let h=Ys(a.type,t,r);if(h&&a.from<a.to){let c=Xs(a);if(c&&(t>0?e>=c.from&&e<c.to:e>c.from&&e<=c.to))return np(n,e,t,a,c,h,r)}}return sp(n,e,t,o,l.type,s,r)}function np(n,e,t,i,s,r,o){let l=i.parent,a={from:s.from,to:s.to},h=0,c=l?.cursor();if(c&&(t<0?c.childBefore(i.from):c.childAfter(i.to)))do if(t<0?c.to<=i.from:c.from>=i.to){if(h==0&&r.indexOf(c.type.name)>-1&&c.from<c.to){let f=Xs(c);return{start:a,end:f?{from:f.from,to:f.to}:void 0,matched:!0}}else if(Ys(c.type,t,o))h++;else if(Ys(c.type,-t,o)){if(h==0){let f=Xs(c);return{start:a,end:f&&f.from<f.to?{from:f.from,to:f.to}:void 0,matched:!1}}h--}}while(t<0?c.prevSibling():c.nextSibling());return{start:a,matched:!1}}function sp(n,e,t,i,s,r,o){let l=t<0?n.sliceDoc(e-1,e):n.sliceDoc(e,e+1),a=o.indexOf(l);if(a<0||a%2==0!=t>0)return null;let h={from:t<0?e-1:e,to:t>0?e+1:e},c=n.doc.iterRange(e,t>0?n.doc.length:0),f=0;for(let u=0;!c.next().done&&u<=r;){let d=c.value;t<0&&(u+=d.length);let p=e+u*t;for(let y=t>0?0:d.length-1,g=t>0?d.length:-1;y!=g;y+=t){let b=o.indexOf(d[y]);if(!(b<0||i.resolveInner(p+y,1).type!=s))if(b%2==0==t>0)f++;else{if(f==1)return{start:h,end:{from:p+y,to:p+y+1},matched:b>>1==a>>1};f--}}t>0&&(u+=d.length)}return c.done?{start:h,matched:!1}:null}function Xo(n,e,t,i=0,s=0){e==null&&(e=n.search(/[^\s\u00a0]/),e==-1&&(e=n.length));let r=s;for(let o=i;o<e;o++)n.charCodeAt(o)==9?r+=t-r%t:r++;return r}class xh{constructor(e,t,i,s){this.string=e,this.tabSize=t,this.indentUnit=i,this.overrideIndent=s,this.pos=0,this.start=0,this.lastColumnPos=0,this.lastColumnValue=0}eol(){return this.pos>=this.string.length}sol(){return this.pos==0}peek(){return this.string.charAt(this.pos)||void 0}next(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)}eat(e){let t=this.string.charAt(this.pos),i;if(typeof e=="string"?i=t==e:i=t&&(e instanceof RegExp?e.test(t):e(t)),i)return++this.pos,t}eatWhile(e){let t=this.pos;for(;this.eat(e););return this.pos>t}eatSpace(){let e=this.pos;for(;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e}skipToEnd(){this.pos=this.string.length}skipTo(e){let t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0}backUp(e){this.pos-=e}column(){return this.lastColumnPos<this.start&&(this.lastColumnValue=Xo(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue}indentation(){var e;return(e=this.overrideIndent)!==null&&e!==void 0?e:Xo(this.string,null,this.tabSize)}match(e,t,i){if(typeof e=="string"){let s=o=>i?o.toLowerCase():o,r=this.string.substr(this.pos,e.length);return s(r)==s(e)?(t!==!1&&(this.pos+=e.length),!0):null}else{let s=this.string.slice(this.pos).match(e);return s&&s.index>0?null:(s&&t!==!1&&(this.pos+=s[0].length),s)}}current(){return this.string.slice(this.start,this.pos)}}function rp(n){return{name:n.name||"",token:n.token,blankLine:n.blankLine||(()=>{}),startState:n.startState||(()=>!0),copyState:n.copyState||op,indent:n.indent||(()=>null),languageData:n.languageData||{},tokenTable:n.tokenTable||vr}}function op(n){if(typeof n!="object")return n;let e={};for(let t in n){let i=n[t];e[t]=i instanceof Array?i.slice():i}return e}const Zo=new WeakMap;class It extends De{constructor(e){let t=lh(e.languageData),i=rp(e),s,r=new class extends sh{createParse(o,l,a){return new ap(s,o,l,a)}};super(t,r,[ch.of((o,l)=>this.getIndent(o,l))],e.name),this.topNode=fp(t),s=this,this.streamParser=i,this.stateAfter=new L({perNode:!0}),this.tokenTable=e.tokenTable?new Ch(i.tokenTable):cp}static define(e){return new It(e)}getIndent(e,t){let i=ae(e.state),s=i.resolve(t);for(;s&&s.type!=this.topNode;)s=s.parent;if(!s)return null;let r,{overrideIndentation:o}=e.options;o&&(r=Zo.get(e.state),r!=null&&r<t-1e4&&(r=void 0));let l=kr(this,i,0,s.from,r??t),a,h;if(l?(h=l.state,a=l.pos+1):(h=this.streamParser.startState(e.unit),a=0),t-a>1e4)return null;for(;a<t;){let f=e.state.doc.lineAt(a),u=Math.min(t,f.to);if(f.length){let d=o?o(f.from):-1,p=new xh(f.text,e.state.tabSize,e.unit,d<0?void 0:d);for(;p.pos<u-f.from;)vh(this.streamParser.token,p,h)}else this.streamParser.blankLine(h,e.unit);if(u==t)break;a=f.to+1}let c=e.lineAt(t);return o&&r==null&&Zo.set(e.state,c.from),this.streamParser.indent(h,/^\s*(.*)/.exec(c.text)[1],e)}get allowsNesting(){return!1}}function kr(n,e,t,i,s){let r=t>=i&&t+e.length<=s&&e.prop(n.stateAfter);if(r)return{state:n.streamParser.copyState(r),pos:t+e.length};for(let o=e.children.length-1;o>=0;o--){let l=e.children[o],a=t+e.positions[o],h=l instanceof z&&a<s&&kr(n,l,a,i,s);if(h)return h}return null}function kh(n,e,t,i,s){if(s&&t<=0&&i>=e.length)return e;!s&&e.type==n.topNode&&(s=!0);for(let r=e.children.length-1;r>=0;r--){let o=e.positions[r],l=e.children[r],a;if(o<i&&l instanceof z){if(!(a=kh(n,l,t-o,i-o,s)))break;return s?new z(e.type,e.children.slice(0,r).concat(a),e.positions.slice(0,r+1),o+a.length):a}}return null}function lp(n,e,t,i){for(let s of e){let r=s.from+(s.openStart?25:0),o=s.to-(s.openEnd?25:0),l=r<=t&&o>t&&kr(n,s.tree,0-s.offset,t,o),a;if(l&&(a=kh(n,s.tree,t+s.offset,l.pos+s.offset,!1)))return{state:l.state,tree:a}}return{state:n.streamParser.startState(i?Ct(i):4),tree:z.empty}}class ap{constructor(e,t,i,s){this.lang=e,this.input=t,this.fragments=i,this.ranges=s,this.stoppedAt=null,this.chunks=[],this.chunkPos=[],this.chunk=[],this.chunkReused=void 0,this.rangeIndex=0,this.to=s[s.length-1].to;let r=Kt.get(),o=s[0].from,{state:l,tree:a}=lp(e,i,o,r?.state);this.state=l,this.parsedPos=this.chunkStart=o+a.length;for(let h=0;h<a.children.length;h++)this.chunks.push(a.children[h]),this.chunkPos.push(a.positions[h]);r&&this.parsedPos<r.viewport.from-1e5&&(this.state=this.lang.streamParser.startState(Ct(r.state)),r.skipUntilInView(this.parsedPos,r.viewport.from),this.parsedPos=r.viewport.from),this.moveRangeIndex()}advance(){let e=Kt.get(),t=this.stoppedAt==null?this.to:Math.min(this.to,this.stoppedAt),i=Math.min(t,this.chunkStart+2048);for(e&&(i=Math.min(i,e.viewport.to));this.parsedPos<i;)this.parseLine(e);return this.chunkStart<this.parsedPos&&this.finishChunk(),this.parsedPos>=t?this.finish():e&&this.parsedPos>=e.viewport.to?(e.skipUntilInView(this.parsedPos,t),this.finish()):null}stopAt(e){this.stoppedAt=e}lineAfter(e){let t=this.input.chunk(e);if(this.input.lineChunks)t==`
`&&(t="");else{let i=t.indexOf(`
`);i>-1&&(t=t.slice(0,i))}return e+t.length<=this.to?t:t.slice(0,this.to-e)}nextLine(){let e=this.parsedPos,t=this.lineAfter(e),i=e+t.length;for(let s=this.rangeIndex;;){let r=this.ranges[s].to;if(r>=i||(t=t.slice(0,r-(i-t.length)),s++,s==this.ranges.length))break;let o=this.ranges[s].from,l=this.lineAfter(o);t+=l,i=o+l.length}return{line:t,end:i}}skipGapsTo(e,t,i){for(;;){let s=this.ranges[this.rangeIndex].to,r=e+t;if(i>0?s>r:s>=r)break;let o=this.ranges[++this.rangeIndex].from;t+=o-s}return t}moveRangeIndex(){for(;this.ranges[this.rangeIndex].to<this.parsedPos;)this.rangeIndex++}emitToken(e,t,i,s,r){if(this.ranges.length>1){r=this.skipGapsTo(t,r,1),t+=r;let o=this.chunk.length;r=this.skipGapsTo(i,r,-1),i+=r,s+=this.chunk.length-o}return this.chunk.push(e,t,i,s),r}parseLine(e){let{line:t,end:i}=this.nextLine(),s=0,{streamParser:r}=this.lang,o=new xh(t,e?e.state.tabSize:4,e?Ct(e.state):2);if(o.eol())r.blankLine(this.state,o.indentUnit);else for(;!o.eol();){let l=vh(r.token,o,this.state);if(l&&(s=this.emitToken(this.lang.tokenTable.resolve(l),this.parsedPos+o.start,this.parsedPos+o.pos,4,s)),o.start>1e4)break}this.parsedPos=i,this.moveRangeIndex(),this.parsedPos<this.to&&this.parsedPos++}finishChunk(){let e=z.build({buffer:this.chunk,start:this.chunkStart,length:this.parsedPos-this.chunkStart,nodeSet:hp,topID:0,maxBufferLength:2048,reused:this.chunkReused});e=new z(e.type,e.children,e.positions,e.length,[[this.lang.stateAfter,this.lang.streamParser.copyState(this.state)]]),this.chunks.push(e),this.chunkPos.push(this.chunkStart-this.ranges[0].from),this.chunk=[],this.chunkReused=void 0,this.chunkStart=this.parsedPos}finish(){return new z(this.lang.topNode,this.chunks,this.chunkPos,this.parsedPos-this.ranges[0].from).balance()}}function vh(n,e,t){e.start=e.pos;for(let i=0;i<10;i++){let s=n(e,t);if(e.pos>e.start)return s}throw new Error("Stream parser failed to advance stream.")}const vr=Object.create(null),vi=[ge.none],hp=new pr(vi),Qo=[],Sh=Object.create(null);for(let[n,e]of[["variable","variableName"],["variable-2","variableName.special"],["string-2","string.special"],["def","variableName.definition"],["tag","tagName"],["attribute","attributeName"],["type","typeName"],["builtin","variableName.standard"],["qualifier","modifier"],["error","invalid"],["header","heading"],["property","propertyName"]])Sh[n]=Ah(vr,e);class Ch{constructor(e){this.extra=e,this.table=Object.assign(Object.create(null),Sh)}resolve(e){return e?this.table[e]||(this.table[e]=Ah(this.extra,e)):0}}const cp=new Ch(vr);function ts(n,e){Qo.indexOf(n)>-1||(Qo.push(n),console.warn(e))}function Ah(n,e){let t=null;for(let r of e.split(".")){let o=n[r]||m[r];o?typeof o=="function"?t?t=o(t):ts(r,`Modifier ${r} used at start of tag`):t?ts(r,`Tag ${r} used as modifier`):t=o:ts(r,`Unknown highlighting tag ${r}`)}if(!t)return 0;let i=e.replace(/ /g,"_"),s=ge.define({id:vi.length,name:i,props:[xd({[i]:t})]});return vi.push(s),s.id}function fp(n){let e=ge.define({id:vi.length,name:"Document",props:[bt.add(()=>n)]});return vi.push(e),e}const up=n=>{let e=Cr(n.state);return e.line?dp(n):e.block?mp(n):!1};function Sr(n,e){return({state:t,dispatch:i})=>{if(t.readOnly)return!1;let s=n(e,t);return s?(i(t.update(s)),!0):!1}}const dp=Sr(bp,0),pp=Sr(Mh,0),mp=Sr((n,e)=>Mh(n,e,yp(e)),0);function Cr(n,e=n.selection.main.head){let t=n.languageDataAt("commentTokens",e);return t.length?t[0]:{}}const ti=50;function gp(n,{open:e,close:t},i,s){let r=n.sliceDoc(i-ti,i),o=n.sliceDoc(s,s+ti),l=/\s*$/.exec(r)[0].length,a=/^\s*/.exec(o)[0].length,h=r.length-l;if(r.slice(h-e.length,h)==e&&o.slice(a,a+t.length)==t)return{open:{pos:i-l,margin:l&&1},close:{pos:s+a,margin:a&&1}};let c,f;s-i<=2*ti?c=f=n.sliceDoc(i,s):(c=n.sliceDoc(i,i+ti),f=n.sliceDoc(s-ti,s));let u=/^\s*/.exec(c)[0].length,d=/\s*$/.exec(f)[0].length,p=f.length-d-t.length;return c.slice(u,u+e.length)==e&&f.slice(p,p+t.length)==t?{open:{pos:i+u+e.length,margin:/\s/.test(c.charAt(u+e.length))?1:0},close:{pos:s-d-t.length,margin:/\s/.test(f.charAt(p-1))?1:0}}:null}function yp(n){let e=[];for(let t of n.selection.ranges){let i=n.doc.lineAt(t.from),s=t.to<=i.to?i:n.doc.lineAt(t.to),r=e.length-1;r>=0&&e[r].to>i.from?e[r].to=s.to:e.push({from:i.from,to:s.to})}return e}function Mh(n,e,t=e.selection.ranges){let i=t.map(r=>Cr(e,r.from).block);if(!i.every(r=>r))return null;let s=t.map((r,o)=>gp(e,i[o],r.from,r.to));if(n!=2&&!s.every(r=>r))return{changes:e.changes(t.map((r,o)=>s[o]?[]:[{from:r.from,insert:i[o].open+" "},{from:r.to,insert:" "+i[o].close}]))};if(n!=1&&s.some(r=>r)){let r=[];for(let o=0,l;o<s.length;o++)if(l=s[o]){let a=i[o],{open:h,close:c}=l;r.push({from:h.pos-a.open.length,to:h.pos+h.margin},{from:c.pos-c.margin,to:c.pos+a.close.length})}return{changes:r}}return null}function bp(n,e,t=e.selection.ranges){let i=[],s=-1;for(let{from:r,to:o}of t){let l=i.length,a=1e9;for(let h=r;h<=o;){let c=e.doc.lineAt(h);if(c.from>s&&(r==o||o>c.from)){s=c.from;let f=Cr(e,h).line;if(!f)continue;let u=/^\s*/.exec(c.text)[0].length,d=u==c.length,p=c.text.slice(u,u+f.length)==f?u:-1;u<c.text.length&&u<a&&(a=u),i.push({line:c,comment:p,token:f,indent:u,empty:d,single:!1})}h=c.to+1}if(a<1e9)for(let h=l;h<i.length;h++)i[h].indent<i[h].line.text.length&&(i[h].indent=a);i.length==l+1&&(i[l].single=!0)}if(n!=2&&i.some(r=>r.comment<0&&(!r.empty||r.single))){let r=[];for(let{line:l,token:a,indent:h,empty:c,single:f}of i)(f||!c)&&r.push({from:l.from+h,insert:a+" "});let o=e.changes(r);return{changes:o,selection:e.selection.map(o,1)}}else if(n!=1&&i.some(r=>r.comment>=0)){let r=[];for(let{line:o,comment:l,token:a}of i)if(l>=0){let h=o.from+l,c=h+a.length;o.text[c-o.from]==" "&&c++,r.push({from:h,to:c})}return{changes:r}}return null}const Zs=Dt.define(),wp=Dt.define(),xp=D.define(),Dh=D.define({combine(n){return Tt(n,{minDepth:100,newGroupDelay:500},{minDepth:Math.max,newGroupDelay:Math.min})}});function kp(n){let e=0;return n.iterChangedRanges((t,i)=>e=i),e}const Th=be.define({create(){return qe.empty},update(n,e){let t=e.state.facet(Dh),i=e.annotation(Zs);if(i){let a=e.docChanged?w.single(kp(e.changes)):void 0,h=ye.fromTransaction(e,a),c=i.side,f=c==0?n.undone:n.done;return h?f=wn(f,f.length,t.minDepth,h):f=Ph(f,e.startState.selection),new qe(c==0?i.rest:f,c==0?f:i.rest)}let s=e.annotation(wp);if((s=="full"||s=="before")&&(n=n.isolate()),e.annotation(ie.addToHistory)===!1)return e.changes.empty?n:n.addMapping(e.changes.desc);let r=ye.fromTransaction(e),o=e.annotation(ie.time),l=e.annotation(ie.userEvent);return r?n=n.addChanges(r,o,l,t.newGroupDelay,t.minDepth):e.selection&&(n=n.addSelection(e.startState.selection,o,l,t.newGroupDelay)),(s=="full"||s=="after")&&(n=n.isolate()),n},toJSON(n){return{done:n.done.map(e=>e.toJSON()),undone:n.undone.map(e=>e.toJSON())}},fromJSON(n){return new qe(n.done.map(ye.fromJSON),n.undone.map(ye.fromJSON))}});function vp(n={}){return[Th,Dh.of(n),O.domEventHandlers({beforeinput(e,t){let i=e.inputType=="historyUndo"?Oh:e.inputType=="historyRedo"?Qs:null;return i?(e.preventDefault(),i(t)):!1}})]}function On(n,e){return function({state:t,dispatch:i}){if(!e&&t.readOnly)return!1;let s=t.field(Th,!1);if(!s)return!1;let r=s.pop(n,t,e);return r?(i(r),!0):!1}}const Oh=On(0,!1),Qs=On(1,!1),Sp=On(0,!0),Cp=On(1,!0);class ye{constructor(e,t,i,s,r){this.changes=e,this.effects=t,this.mapped=i,this.startSelection=s,this.selectionsAfter=r}setSelAfter(e){return new ye(this.changes,this.effects,this.mapped,this.startSelection,e)}toJSON(){var e,t,i;return{changes:(e=this.changes)===null||e===void 0?void 0:e.toJSON(),mapped:(t=this.mapped)===null||t===void 0?void 0:t.toJSON(),startSelection:(i=this.startSelection)===null||i===void 0?void 0:i.toJSON(),selectionsAfter:this.selectionsAfter.map(s=>s.toJSON())}}static fromJSON(e){return new ye(e.changes&&te.fromJSON(e.changes),[],e.mapped&&je.fromJSON(e.mapped),e.startSelection&&w.fromJSON(e.startSelection),e.selectionsAfter.map(w.fromJSON))}static fromTransaction(e,t){let i=Te;for(let s of e.startState.facet(xp)){let r=s(e);r.length&&(i=i.concat(r))}return!i.length&&e.changes.empty?null:new ye(e.changes.invert(e.startState.doc),i,void 0,t||e.startState.selection,Te)}static selection(e){return new ye(void 0,Te,void 0,void 0,e)}}function wn(n,e,t,i){let s=e+1>t+20?e-t-1:0,r=n.slice(s,e);return r.push(i),r}function Ap(n,e){let t=[],i=!1;return n.iterChangedRanges((s,r)=>t.push(s,r)),e.iterChangedRanges((s,r,o,l)=>{for(let a=0;a<t.length;){let h=t[a++],c=t[a++];l>=h&&o<=c&&(i=!0)}}),i}function Mp(n,e){return n.ranges.length==e.ranges.length&&n.ranges.filter((t,i)=>t.empty!=e.ranges[i].empty).length===0}function Bh(n,e){return n.length?e.length?n.concat(e):n:e}const Te=[],Dp=200;function Ph(n,e){if(n.length){let t=n[n.length-1],i=t.selectionsAfter.slice(Math.max(0,t.selectionsAfter.length-Dp));return i.length&&i[i.length-1].eq(e)?n:(i.push(e),wn(n,n.length-1,1e9,t.setSelAfter(i)))}else return[ye.selection([e])]}function Tp(n){let e=n[n.length-1],t=n.slice();return t[n.length-1]=e.setSelAfter(e.selectionsAfter.slice(0,e.selectionsAfter.length-1)),t}function is(n,e){if(!n.length)return n;let t=n.length,i=Te;for(;t;){let s=Op(n[t-1],e,i);if(s.changes&&!s.changes.empty||s.effects.length){let r=n.slice(0,t);return r[t-1]=s,r}else e=s.mapped,t--,i=s.selectionsAfter}return i.length?[ye.selection(i)]:Te}function Op(n,e,t){let i=Bh(n.selectionsAfter.length?n.selectionsAfter.map(l=>l.map(e)):Te,t);if(!n.changes)return ye.selection(i);let s=n.changes.map(e),r=e.mapDesc(n.changes,!0),o=n.mapped?n.mapped.composeDesc(r):r;return new ye(s,R.mapEffects(n.effects,e),o,n.startSelection.map(r),i)}const Bp=/^(input\.type|delete)($|\.)/;class qe{constructor(e,t,i=0,s=void 0){this.done=e,this.undone=t,this.prevTime=i,this.prevUserEvent=s}isolate(){return this.prevTime?new qe(this.done,this.undone):this}addChanges(e,t,i,s,r){let o=this.done,l=o[o.length-1];return l&&l.changes&&!l.changes.empty&&e.changes&&(!i||Bp.test(i))&&(!l.selectionsAfter.length&&t-this.prevTime<s&&Ap(l.changes,e.changes)||i=="input.type.compose")?o=wn(o,o.length-1,r,new ye(e.changes.compose(l.changes),Bh(e.effects,l.effects),l.mapped,l.startSelection,Te)):o=wn(o,o.length,r,e),new qe(o,Te,t,i)}addSelection(e,t,i,s){let r=this.done.length?this.done[this.done.length-1].selectionsAfter:Te;return r.length>0&&t-this.prevTime<s&&i==this.prevUserEvent&&i&&/^select($|\.)/.test(i)&&Mp(r[r.length-1],e)?this:new qe(Ph(this.done,e),this.undone,t,i)}addMapping(e){return new qe(is(this.done,e),is(this.undone,e),this.prevTime,this.prevUserEvent)}pop(e,t,i){let s=e==0?this.done:this.undone;if(s.length==0)return null;let r=s[s.length-1];if(i&&r.selectionsAfter.length)return t.update({selection:r.selectionsAfter[r.selectionsAfter.length-1],annotations:Zs.of({side:e,rest:Tp(s)}),userEvent:e==0?"select.undo":"select.redo",scrollIntoView:!0});if(r.changes){let o=s.length==1?Te:s.slice(0,s.length-1);return r.mapped&&(o=is(o,r.mapped)),t.update({changes:r.changes,selection:r.startSelection,effects:r.effects,annotations:Zs.of({side:e,rest:o}),filter:!1,userEvent:e==0?"undo":"redo",scrollIntoView:!0})}else return null}}qe.empty=new qe(Te,Te);const Pp=[{key:"Mod-z",run:Oh,preventDefault:!0},{key:"Mod-y",mac:"Mod-Shift-z",run:Qs,preventDefault:!0},{linux:"Ctrl-Shift-z",run:Qs,preventDefault:!0},{key:"Mod-u",run:Sp,preventDefault:!0},{key:"Alt-u",mac:"Mod-Shift-u",run:Cp,preventDefault:!0}];function $t(n,e){return w.create(n.ranges.map(e),n.mainIndex)}function Je(n,e){return n.update({selection:e,scrollIntoView:!0,userEvent:"select"})}function Re({state:n,dispatch:e},t){let i=$t(n.selection,t);return i.eq(n.selection)?!1:(e(Je(n,i)),!0)}function Bn(n,e){return w.cursor(e?n.to:n.from)}function Eh(n,e){return Re(n,t=>t.empty?n.moveByChar(t,e):Bn(t,e))}function de(n){return n.textDirectionAt(n.state.selection.main.head)==Y.LTR}const Rh=n=>Eh(n,!de(n)),Lh=n=>Eh(n,de(n));function Ih(n,e){return Re(n,t=>t.empty?n.moveByGroup(t,e):Bn(t,e))}const Ep=n=>Ih(n,!de(n)),Rp=n=>Ih(n,de(n));function Lp(n,e,t){if(e.type.prop(t))return!0;let i=e.to-e.from;return i&&(i>2||/[^\s,.;:]/.test(n.sliceDoc(e.from,e.to)))||e.firstChild}function Pn(n,e,t){let i=ae(n).resolveInner(e.head),s=t?L.closedBy:L.openedBy;for(let a=e.head;;){let h=t?i.childAfter(a):i.childBefore(a);if(!h)break;Lp(n,h,s)?i=h:a=t?h.to:h.from}let r=i.type.prop(s),o,l;return r&&(o=t?Lt(n,i.from,1):Lt(n,i.to,-1))&&o.matched?l=t?o.end.to:o.end.from:l=t?i.to:i.from,w.cursor(l,t?-1:1)}const Ip=n=>Re(n,e=>Pn(n.state,e,!de(n))),Np=n=>Re(n,e=>Pn(n.state,e,de(n)));function Nh(n,e){return Re(n,t=>{if(!t.empty)return Bn(t,e);let i=n.moveVertically(t,e);return i.head!=t.head?i:n.moveToLineBoundary(t,e)})}const Vh=n=>Nh(n,!1),Fh=n=>Nh(n,!0);function Hh(n){return Math.max(n.defaultLineHeight,Math.min(n.dom.clientHeight,innerHeight)-5)}function Wh(n,e){let{state:t}=n,i=$t(t.selection,l=>l.empty?n.moveVertically(l,e,Hh(n)):Bn(l,e));if(i.eq(t.selection))return!1;let s=n.coordsAtPos(t.selection.main.head),r=n.scrollDOM.getBoundingClientRect(),o;return s&&s.top>r.top&&s.bottom<r.bottom&&s.top-r.top<=n.scrollDOM.scrollHeight-n.scrollDOM.scrollTop-n.scrollDOM.clientHeight&&(o=O.scrollIntoView(i.main.head,{y:"start",yMargin:s.top-r.top})),n.dispatch(Je(t,i),{effects:o}),!0}const el=n=>Wh(n,!1),er=n=>Wh(n,!0);function ft(n,e,t){let i=n.lineBlockAt(e.head),s=n.moveToLineBoundary(e,t);if(s.head==e.head&&s.head!=(t?i.to:i.from)&&(s=n.moveToLineBoundary(e,t,!1)),!t&&s.head==i.from&&i.length){let r=/^\s*/.exec(n.state.sliceDoc(i.from,Math.min(i.from+100,i.to)))[0].length;r&&e.head!=i.from+r&&(s=w.cursor(i.from+r))}return s}const Vp=n=>Re(n,e=>ft(n,e,!0)),Fp=n=>Re(n,e=>ft(n,e,!1)),Hp=n=>Re(n,e=>ft(n,e,!de(n))),Wp=n=>Re(n,e=>ft(n,e,de(n))),zp=n=>Re(n,e=>w.cursor(n.lineBlockAt(e.head).from,1)),_p=n=>Re(n,e=>w.cursor(n.lineBlockAt(e.head).to,-1));function qp(n,e,t){let i=!1,s=$t(n.selection,r=>{let o=Lt(n,r.head,-1)||Lt(n,r.head,1)||r.head>0&&Lt(n,r.head-1,1)||r.head<n.doc.length&&Lt(n,r.head+1,-1);if(!o||!o.end)return r;i=!0;let l=o.start.from==r.head?o.end.to:o.end.from;return t?w.range(r.anchor,l):w.cursor(l)});return i?(e(Je(n,s)),!0):!1}const jp=({state:n,dispatch:e})=>qp(n,e,!1);function Be(n,e){let t=$t(n.state.selection,i=>{let s=e(i);return w.range(i.anchor,s.head,s.goalColumn)});return t.eq(n.state.selection)?!1:(n.dispatch(Je(n.state,t)),!0)}function zh(n,e){return Be(n,t=>n.moveByChar(t,e))}const _h=n=>zh(n,!de(n)),qh=n=>zh(n,de(n));function jh(n,e){return Be(n,t=>n.moveByGroup(t,e))}const Kp=n=>jh(n,!de(n)),Up=n=>jh(n,de(n)),Gp=n=>Be(n,e=>Pn(n.state,e,!de(n))),Jp=n=>Be(n,e=>Pn(n.state,e,de(n)));function Kh(n,e){return Be(n,t=>n.moveVertically(t,e))}const Uh=n=>Kh(n,!1),Gh=n=>Kh(n,!0);function Jh(n,e){return Be(n,t=>n.moveVertically(t,e,Hh(n)))}const tl=n=>Jh(n,!1),il=n=>Jh(n,!0),$p=n=>Be(n,e=>ft(n,e,!0)),Yp=n=>Be(n,e=>ft(n,e,!1)),Xp=n=>Be(n,e=>ft(n,e,!de(n))),Zp=n=>Be(n,e=>ft(n,e,de(n))),Qp=n=>Be(n,e=>w.cursor(n.lineBlockAt(e.head).from)),em=n=>Be(n,e=>w.cursor(n.lineBlockAt(e.head).to)),nl=({state:n,dispatch:e})=>(e(Je(n,{anchor:0})),!0),sl=({state:n,dispatch:e})=>(e(Je(n,{anchor:n.doc.length})),!0),rl=({state:n,dispatch:e})=>(e(Je(n,{anchor:n.selection.main.anchor,head:0})),!0),ol=({state:n,dispatch:e})=>(e(Je(n,{anchor:n.selection.main.anchor,head:n.doc.length})),!0),tm=({state:n,dispatch:e})=>(e(n.update({selection:{anchor:0,head:n.doc.length},userEvent:"select"})),!0),im=({state:n,dispatch:e})=>{let t=Rn(n).map(({from:i,to:s})=>w.range(i,Math.min(s+1,n.doc.length)));return e(n.update({selection:w.create(t),userEvent:"select"})),!0},nm=({state:n,dispatch:e})=>{let t=$t(n.selection,i=>{var s;let r=ae(n).resolveInner(i.head,1);for(;!(r.from<i.from&&r.to>=i.to||r.to>i.to&&r.from<=i.from||!(!((s=r.parent)===null||s===void 0)&&s.parent));)r=r.parent;return w.range(r.to,r.from)});return e(Je(n,t)),!0},sm=({state:n,dispatch:e})=>{let t=n.selection,i=null;return t.ranges.length>1?i=w.create([t.main]):t.main.empty||(i=w.create([w.cursor(t.main.head)])),i?(e(Je(n,i)),!0):!1};function En(n,e){if(n.state.readOnly)return!1;let t="delete.selection",{state:i}=n,s=i.changeByRange(r=>{let{from:o,to:l}=r;if(o==l){let a=e(o);a<o?(t="delete.backward",a=Ui(n,a,!1)):a>o&&(t="delete.forward",a=Ui(n,a,!0)),o=Math.min(o,a),l=Math.max(l,a)}else o=Ui(n,o,!1),l=Ui(n,l,!0);return o==l?{range:r}:{changes:{from:o,to:l},range:w.cursor(o)}});return s.changes.empty?!1:(n.dispatch(i.update(s,{scrollIntoView:!0,userEvent:t,effects:t=="delete.selection"?O.announce.of(i.phrase("Selection deleted")):void 0})),!0)}function Ui(n,e,t){if(n instanceof O)for(let i of n.state.facet(O.atomicRanges).map(s=>s(n)))i.between(e,e,(s,r)=>{s<e&&r>e&&(e=t?r:s)});return e}const $h=(n,e)=>En(n,t=>{let{state:i}=n,s=i.doc.lineAt(t),r,o;if(!e&&t>s.from&&t<s.from+200&&!/[^ \t]/.test(r=s.text.slice(0,t-s.from))){if(r[r.length-1]=="	")return t-1;let l=Mi(r,i.tabSize),a=l%Ct(i)||Ct(i);for(let h=0;h<a&&r[r.length-1-h]==" ";h++)t--;o=t}else o=ke(s.text,t-s.from,e,e)+s.from,o==t&&s.number!=(e?i.doc.lines:1)&&(o+=e?1:-1);return o}),tr=n=>$h(n,!1),Yh=n=>$h(n,!0),Xh=(n,e)=>En(n,t=>{let i=t,{state:s}=n,r=s.doc.lineAt(i),o=s.charCategorizer(i);for(let l=null;;){if(i==(e?r.to:r.from)){i==t&&r.number!=(e?s.doc.lines:1)&&(i+=e?1:-1);break}let a=ke(r.text,i-r.from,e)+r.from,h=r.text.slice(Math.min(i,a)-r.from,Math.max(i,a)-r.from),c=o(h);if(l!=null&&c!=l)break;(h!=" "||i!=t)&&(l=c),i=a}return i}),Zh=n=>Xh(n,!1),rm=n=>Xh(n,!0),Qh=n=>En(n,e=>{let t=n.lineBlockAt(e).to;return e<t?t:Math.min(n.state.doc.length,e+1)}),om=n=>En(n,e=>{let t=n.lineBlockAt(e).from;return e>t?t:Math.max(0,e-1)}),lm=({state:n,dispatch:e})=>{if(n.readOnly)return!1;let t=n.changeByRange(i=>({changes:{from:i.from,to:i.to,insert:V.of(["",""])},range:w.cursor(i.from)}));return e(n.update(t,{scrollIntoView:!0,userEvent:"input"})),!0},am=({state:n,dispatch:e})=>{if(n.readOnly)return!1;let t=n.changeByRange(i=>{if(!i.empty||i.from==0||i.from==n.doc.length)return{range:i};let s=i.from,r=n.doc.lineAt(s),o=s==r.from?s-1:ke(r.text,s-r.from,!1)+r.from,l=s==r.to?s+1:ke(r.text,s-r.from,!0)+r.from;return{changes:{from:o,to:l,insert:n.doc.slice(s,l).append(n.doc.slice(o,s))},range:w.cursor(l)}});return t.changes.empty?!1:(e(n.update(t,{scrollIntoView:!0,userEvent:"move.character"})),!0)};function Rn(n){let e=[],t=-1;for(let i of n.selection.ranges){let s=n.doc.lineAt(i.from),r=n.doc.lineAt(i.to);if(!i.empty&&i.to==r.from&&(r=n.doc.lineAt(i.to-1)),t>=s.number){let o=e[e.length-1];o.to=r.to,o.ranges.push(i)}else e.push({from:s.from,to:r.to,ranges:[i]});t=r.number+1}return e}function ec(n,e,t){if(n.readOnly)return!1;let i=[],s=[];for(let r of Rn(n)){if(t?r.to==n.doc.length:r.from==0)continue;let o=n.doc.lineAt(t?r.to+1:r.from-1),l=o.length+1;if(t){i.push({from:r.to,to:o.to},{from:r.from,insert:o.text+n.lineBreak});for(let a of r.ranges)s.push(w.range(Math.min(n.doc.length,a.anchor+l),Math.min(n.doc.length,a.head+l)))}else{i.push({from:o.from,to:r.from},{from:r.to,insert:n.lineBreak+o.text});for(let a of r.ranges)s.push(w.range(a.anchor-l,a.head-l))}}return i.length?(e(n.update({changes:i,scrollIntoView:!0,selection:w.create(s,n.selection.mainIndex),userEvent:"move.line"})),!0):!1}const hm=({state:n,dispatch:e})=>ec(n,e,!1),cm=({state:n,dispatch:e})=>ec(n,e,!0);function tc(n,e,t){if(n.readOnly)return!1;let i=[];for(let s of Rn(n))t?i.push({from:s.from,insert:n.doc.slice(s.from,s.to)+n.lineBreak}):i.push({from:s.to,insert:n.lineBreak+n.doc.slice(s.from,s.to)});return e(n.update({changes:i,scrollIntoView:!0,userEvent:"input.copyline"})),!0}const fm=({state:n,dispatch:e})=>tc(n,e,!1),um=({state:n,dispatch:e})=>tc(n,e,!0),dm=n=>{if(n.state.readOnly)return!1;let{state:e}=n,t=e.changes(Rn(e).map(({from:s,to:r})=>(s>0?s--:r<e.doc.length&&r++,{from:s,to:r}))),i=$t(e.selection,s=>n.moveVertically(s,!0)).map(t);return n.dispatch({changes:t,selection:i,scrollIntoView:!0,userEvent:"delete.line"}),!0};function pm(n,e){if(/\(\)|\[\]|\{\}/.test(n.sliceDoc(e-1,e+1)))return{from:e,to:e};let t=ae(n).resolveInner(e),i=t.childBefore(e),s=t.childAfter(e),r;return i&&s&&i.to<=e&&s.from>=e&&(r=i.type.prop(L.closedBy))&&r.indexOf(s.name)>-1&&n.doc.lineAt(i.to).from==n.doc.lineAt(s.from).from?{from:i.to,to:s.from}:null}const mm=ic(!1),gm=ic(!0);function ic(n){return({state:e,dispatch:t})=>{if(e.readOnly)return!1;let i=e.changeByRange(s=>{let{from:r,to:o}=s,l=e.doc.lineAt(r),a=!n&&r==o&&pm(e,r);n&&(r=o=(o<=l.to?l:e.doc.lineAt(o)).to);let h=new Dn(e,{simulateBreak:r,simulateDoubleBreak:!!a}),c=br(h,r);for(c==null&&(c=/^\s*/.exec(e.doc.lineAt(r).text)[0].length);o<l.to&&/\s/.test(l.text[o-l.from]);)o++;a?{from:r,to:o}=a:r>l.from&&r<l.from+100&&!/\S/.test(l.text.slice(0,r))&&(r=l.from);let f=["",ki(e,c)];return a&&f.push(ki(e,h.lineIndent(l.from,-1))),{changes:{from:r,to:o,insert:V.of(f)},range:w.cursor(r+1+f[1].length)}});return t(e.update(i,{scrollIntoView:!0,userEvent:"input"})),!0}}function Ar(n,e){let t=-1;return n.changeByRange(i=>{let s=[];for(let o=i.from;o<=i.to;){let l=n.doc.lineAt(o);l.number>t&&(i.empty||i.to>l.from)&&(e(l,s,i),t=l.number),o=l.to+1}let r=n.changes(s);return{changes:s,range:w.range(r.mapPos(i.anchor,1),r.mapPos(i.head,1))}})}const ym=({state:n,dispatch:e})=>{if(n.readOnly)return!1;let t=Object.create(null),i=new Dn(n,{overrideIndentation:r=>{let o=t[r];return o??-1}}),s=Ar(n,(r,o,l)=>{let a=br(i,r.from);if(a==null)return;/\S/.test(r.text)||(a=0);let h=/^\s*/.exec(r.text)[0],c=ki(n,a);(h!=c||l.from<r.from+h.length)&&(t[r.from]=a,o.push({from:r.from,to:r.from+h.length,insert:c}))});return s.changes.empty||e(n.update(s,{userEvent:"indent"})),!0},nc=({state:n,dispatch:e})=>n.readOnly?!1:(e(n.update(Ar(n,(t,i)=>{i.push({from:t.from,insert:n.facet(Mn)})}),{userEvent:"input.indent"})),!0),sc=({state:n,dispatch:e})=>n.readOnly?!1:(e(n.update(Ar(n,(t,i)=>{let s=/^\s*/.exec(t.text)[0];if(!s)return;let r=Mi(s,n.tabSize),o=0,l=ki(n,Math.max(0,r-Ct(n)));for(;o<s.length&&o<l.length&&s.charCodeAt(o)==l.charCodeAt(o);)o++;i.push({from:t.from+o,to:t.from+s.length,insert:l.slice(o)})}),{userEvent:"delete.dedent"})),!0),bm=[{key:"Ctrl-b",run:Rh,shift:_h,preventDefault:!0},{key:"Ctrl-f",run:Lh,shift:qh},{key:"Ctrl-p",run:Vh,shift:Uh},{key:"Ctrl-n",run:Fh,shift:Gh},{key:"Ctrl-a",run:zp,shift:Qp},{key:"Ctrl-e",run:_p,shift:em},{key:"Ctrl-d",run:Yh},{key:"Ctrl-h",run:tr},{key:"Ctrl-k",run:Qh},{key:"Ctrl-Alt-h",run:Zh},{key:"Ctrl-o",run:lm},{key:"Ctrl-t",run:am},{key:"Ctrl-v",run:er}],wm=[{key:"ArrowLeft",run:Rh,shift:_h,preventDefault:!0},{key:"Mod-ArrowLeft",mac:"Alt-ArrowLeft",run:Ep,shift:Kp,preventDefault:!0},{mac:"Cmd-ArrowLeft",run:Hp,shift:Xp,preventDefault:!0},{key:"ArrowRight",run:Lh,shift:qh,preventDefault:!0},{key:"Mod-ArrowRight",mac:"Alt-ArrowRight",run:Rp,shift:Up,preventDefault:!0},{mac:"Cmd-ArrowRight",run:Wp,shift:Zp,preventDefault:!0},{key:"ArrowUp",run:Vh,shift:Uh,preventDefault:!0},{mac:"Cmd-ArrowUp",run:nl,shift:rl},{mac:"Ctrl-ArrowUp",run:el,shift:tl},{key:"ArrowDown",run:Fh,shift:Gh,preventDefault:!0},{mac:"Cmd-ArrowDown",run:sl,shift:ol},{mac:"Ctrl-ArrowDown",run:er,shift:il},{key:"PageUp",run:el,shift:tl},{key:"PageDown",run:er,shift:il},{key:"Home",run:Fp,shift:Yp,preventDefault:!0},{key:"Mod-Home",run:nl,shift:rl},{key:"End",run:Vp,shift:$p,preventDefault:!0},{key:"Mod-End",run:sl,shift:ol},{key:"Enter",run:mm},{key:"Mod-a",run:tm},{key:"Backspace",run:tr,shift:tr},{key:"Delete",run:Yh},{key:"Mod-Backspace",mac:"Alt-Backspace",run:Zh},{key:"Mod-Delete",mac:"Alt-Delete",run:rm},{mac:"Mod-Backspace",run:om},{mac:"Mod-Delete",run:Qh}].concat(bm.map(n=>({mac:n.key,run:n.run,shift:n.shift}))),xm=[{key:"Alt-ArrowLeft",mac:"Ctrl-ArrowLeft",run:Ip,shift:Gp},{key:"Alt-ArrowRight",mac:"Ctrl-ArrowRight",run:Np,shift:Jp},{key:"Alt-ArrowUp",run:hm},{key:"Shift-Alt-ArrowUp",run:fm},{key:"Alt-ArrowDown",run:cm},{key:"Shift-Alt-ArrowDown",run:um},{key:"Escape",run:sm},{key:"Mod-Enter",run:gm},{key:"Alt-l",mac:"Ctrl-l",run:im},{key:"Mod-i",run:nm,preventDefault:!0},{key:"Mod-[",run:sc},{key:"Mod-]",run:nc},{key:"Mod-Alt-\\",run:ym},{key:"Shift-Mod-k",run:dm},{key:"Shift-Mod-\\",run:jp},{key:"Mod-/",run:up},{key:"Alt-A",run:pp}].concat(wm),km={key:"Tab",run:nc,shift:sc},vm="#2E3235",Fe="#DDDDDD",ui="#B9D2FF",Gi="#b0b0b0",Sm="#e0e0e0",rc="#808080",ns="#000000",Cm="#A54543",oc="#fc6d24",pt="#fda331",ss="#8abeb7",ll="#b5bd68",ii="#6fb3d2",ni="#cc99cc",Am="#6987AF",al=oc,hl="#292d30",Ji=ui+"30",Mm=vm,rs=Fe,Dm="#202325",cl=Fe,Tm=O.theme({"&":{color:Fe,backgroundColor:Mm},".cm-content":{caretColor:cl},".cm-cursor, .cm-dropCursor":{borderLeftColor:cl},"&.cm-focused .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection":{backgroundColor:Dm},".cm-panels":{backgroundColor:hl,color:Gi},".cm-panels.cm-panels-top":{borderBottom:"2px solid black"},".cm-panels.cm-panels-bottom":{borderTop:"2px solid black"},".cm-searchMatch":{backgroundColor:ui,outline:`1px solid ${Gi}`,color:ns},".cm-searchMatch.cm-searchMatch-selected":{backgroundColor:Sm,color:ns},".cm-activeLine":{backgroundColor:Ji},".cm-selectionMatch":{backgroundColor:Ji},"&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket":{outline:`1px solid ${Gi}`},"&.cm-focused .cm-matchingBracket":{backgroundColor:ui,color:ns},".cm-gutters":{borderRight:"1px solid #ffffff10",color:rc,backgroundColor:hl},".cm-activeLineGutter":{backgroundColor:Ji},".cm-foldPlaceholder":{backgroundColor:"transparent",border:"none",color:ui},".cm-tooltip":{border:"none",backgroundColor:rs},".cm-tooltip .cm-tooltip-arrow:before":{borderTopColor:"transparent",borderBottomColor:"transparent"},".cm-tooltip .cm-tooltip-arrow:after":{borderTopColor:rs,borderBottomColor:rs},".cm-tooltip-autocomplete":{"& > ul > li[aria-selected]":{backgroundColor:Ji,color:Gi}}},{dark:!0}),Om=Jt.define([{tag:m.keyword,color:pt},{tag:[m.name,m.deleted,m.character,m.propertyName,m.macroName],color:ll},{tag:[m.variableName],color:ii},{tag:[m.function(m.variableName)],color:pt},{tag:[m.labelName],color:oc},{tag:[m.color,m.constant(m.name),m.standard(m.name)],color:pt},{tag:[m.definition(m.name),m.separator],color:ni},{tag:[m.brace],color:ni},{tag:[m.annotation],color:al},{tag:[m.number,m.changed,m.annotation,m.modifier,m.self,m.namespace],color:pt},{tag:[m.typeName,m.className],color:ii},{tag:[m.operator,m.operatorKeyword],color:ni},{tag:[m.tagName],color:pt},{tag:[m.squareBracket],color:ni},{tag:[m.angleBracket],color:ni},{tag:[m.attributeName],color:ii},{tag:[m.regexp],color:pt},{tag:[m.quote],color:Fe},{tag:[m.string],color:ll},{tag:m.link,color:Am,textDecoration:"underline",textUnderlinePosition:"under"},{tag:[m.url,m.escape,m.special(m.string)],color:ss},{tag:[m.meta],color:Cm},{tag:[m.comment],color:rc,fontStyle:"italic"},{tag:m.monospace,color:Fe},{tag:m.strong,fontWeight:"bold",color:pt},{tag:m.emphasis,fontStyle:"italic",color:ii},{tag:m.strikethrough,textDecoration:"line-through"},{tag:m.heading,fontWeight:"bold",color:Fe},{tag:m.special(m.heading1),fontWeight:"bold",color:Fe},{tag:m.heading1,fontWeight:"bold",color:Fe},{tag:[m.heading2,m.heading3,m.heading4],fontWeight:"bold",color:Fe},{tag:[m.heading5,m.heading6],color:Fe},{tag:[m.atom,m.bool,m.special(m.variableName)],color:ss},{tag:[m.processingInstruction,m.inserted],color:ss},{tag:[m.contentSeparator],color:ii},{tag:m.invalid,color:ui,borderBottom:`1px dotted ${al}`}]),Bm=[Tm,xr(Om)],fl="#2e3440",Mr="#3b4252",ul="#434c5e",$i="#4c566a",dl="#e5e9f0",ir="#eceff4",os="#8fbcbb",pl="#88c0d0",Pm="#81a1c1",Pe="#5e81ac",Em="#bf616a",Et="#d08770",ls="#ebcb8b",ml="#a3be8c",Rm="#b48ead",gl="#d30102",Dr=ir,as=Dr,Lm="#ffffff",hs=Mr,Im=Dr,yl=Mr,Nm=O.theme({"&":{color:fl,backgroundColor:Lm},".cm-content":{caretColor:yl},".cm-cursor, .cm-dropCursor":{borderLeftColor:yl},"&.cm-focused .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection":{backgroundColor:Im},".cm-panels":{backgroundColor:Dr,color:$i},".cm-panels.cm-panels-top":{borderBottom:"2px solid black"},".cm-panels.cm-panels-bottom":{borderTop:"2px solid black"},".cm-searchMatch":{backgroundColor:"#72a1ff59",outline:`1px solid ${$i}`},".cm-searchMatch.cm-searchMatch-selected":{backgroundColor:dl},".cm-activeLine":{backgroundColor:as},".cm-selectionMatch":{backgroundColor:dl},"&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket":{outline:`1px solid ${$i}`},"&.cm-focused .cm-matchingBracket":{backgroundColor:ir},".cm-gutters":{backgroundColor:ir,color:fl,border:"none"},".cm-activeLineGutter":{backgroundColor:as},".cm-foldPlaceholder":{backgroundColor:"transparent",border:"none",color:"#ddd"},".cm-tooltip":{border:"none",backgroundColor:hs},".cm-tooltip .cm-tooltip-arrow:before":{borderTopColor:"transparent",borderBottomColor:"transparent"},".cm-tooltip .cm-tooltip-arrow:after":{borderTopColor:hs,borderBottomColor:hs},".cm-tooltip-autocomplete":{"& > ul > li[aria-selected]":{backgroundColor:as,color:$i}}},{dark:!1}),Vm=Jt.define([{tag:m.keyword,color:Pe},{tag:[m.name,m.deleted,m.character,m.propertyName,m.macroName],color:Et},{tag:[m.variableName],color:Et},{tag:[m.function(m.variableName)],color:Pe},{tag:[m.labelName],color:Pm},{tag:[m.color,m.constant(m.name),m.standard(m.name)],color:Pe},{tag:[m.definition(m.name),m.separator],color:ml},{tag:[m.brace],color:os},{tag:[m.annotation],color:gl},{tag:[m.number,m.changed,m.annotation,m.modifier,m.self,m.namespace],color:pl},{tag:[m.typeName,m.className],color:ls},{tag:[m.operator,m.operatorKeyword],color:ml},{tag:[m.tagName],color:Rm},{tag:[m.squareBracket],color:Em},{tag:[m.angleBracket],color:Et},{tag:[m.attributeName],color:ls},{tag:[m.regexp],color:Pe},{tag:[m.quote],color:Mr},{tag:[m.string],color:Et},{tag:m.link,color:os,textDecoration:"underline",textUnderlinePosition:"under"},{tag:[m.url,m.escape,m.special(m.string)],color:Et},{tag:[m.meta],color:pl},{tag:[m.comment],color:ul,fontStyle:"italic"},{tag:m.strong,fontWeight:"bold",color:Pe},{tag:m.emphasis,fontStyle:"italic",color:Pe},{tag:m.strikethrough,textDecoration:"line-through"},{tag:m.heading,fontWeight:"bold",color:Pe},{tag:m.special(m.heading1),fontWeight:"bold",color:Pe},{tag:m.heading1,fontWeight:"bold",color:Pe},{tag:[m.heading2,m.heading3,m.heading4],fontWeight:"bold",color:Pe},{tag:[m.heading5,m.heading6],color:Pe},{tag:[m.atom,m.bool,m.special(m.variableName)],color:Et},{tag:[m.processingInstruction,m.inserted],color:os},{tag:[m.contentSeparator],color:ls},{tag:m.invalid,color:ul,borderBottom:`1px dotted ${gl}`}]),Fm=[Nm,xr(Vm)];function bl(n){let e=Object.keys(n).join(""),t=/\w/.test(e);return t&&(e=e.replace(/\w/g,"")),`[${t?"\\w":""}${e.replace(/[^\w\s]/g,"\\$&")}]`}function Hm(n){let e=Object.create(null),t=Object.create(null);for(let{label:s}of n){e[s[0]]=!0;for(let r=1;r<s.length;r++)t[s[r]]=!0}let i=bl(e)+bl(t)+"*$";return[new RegExp("^"+i),new RegExp(i)]}function Wm(n){let e=n.map(s=>typeof s=="string"?{label:s}:s),[t,i]=e.every(s=>/^\w+$/.test(s.label))?[/\w*$/,/\w+$/]:Hm(e);return s=>{let r=s.matchBefore(i);return r||s.explicit?{from:r?r.from:s.pos,options:e,validFor:t}:null}}function s0(n,e){return t=>{for(let i=ae(t.state).resolveInner(t.pos,-1);i;i=i.parent)if(n.indexOf(i.name)>-1)return null;return e(t)}}class wl{constructor(e,t,i){this.completion=e,this.source=t,this.match=i}}function nr(n){return n.selection.main.head}function zm(n,e,t,i){return Object.assign(Object.assign({},n.changeByRange(s=>{if(s==n.selection.main)return{changes:{from:t,to:i,insert:e},range:w.cursor(t+e.length)};let r=i-t;return!s.empty||r&&n.sliceDoc(s.from-r,s.from)!=n.sliceDoc(t,i)?{range:s}:{changes:{from:s.from-r,to:s.from,insert:e},range:w.cursor(s.from-r+e.length)}})),{userEvent:"input.complete"})}function lc(n,e){const t=e.completion.apply||e.completion.label;let i=e.source;typeof t=="string"?n.dispatch(zm(n.state,t,i.from,i.to)):t(n,e.completion,i.from,i.to)}const xl=new WeakMap;function _m(n){if(!Array.isArray(n))return n;let e=xl.get(n);return e||xl.set(n,e=Wm(n)),e}class qm{constructor(e){this.pattern=e,this.chars=[],this.folded=[],this.any=[],this.precise=[],this.byWord=[];for(let t=0;t<e.length;){let i=ce(e,t),s=Ce(i);this.chars.push(i);let r=e.slice(t,t+s),o=r.toUpperCase();this.folded.push(ce(o==r?r.toLowerCase():o,0)),t+=s}this.astral=e.length!=this.chars.length}match(e){if(this.pattern.length==0)return[0];if(e.length<this.pattern.length)return null;let{chars:t,folded:i,any:s,precise:r,byWord:o}=this;if(t.length==1){let k=ce(e,0);return k==t[0]?[0,0,Ce(k)]:k==i[0]?[-200,0,Ce(k)]:null}let l=e.indexOf(this.pattern);if(l==0)return[0,0,this.pattern.length];let a=t.length,h=0;if(l<0){for(let k=0,v=Math.min(e.length,200);k<v&&h<a;){let S=ce(e,k);(S==t[h]||S==i[h])&&(s[h++]=k),k+=Ce(S)}if(h<a)return null}let c=0,f=0,u=!1,d=0,p=-1,y=-1,g=/[a-z]/.test(e),b=!0;for(let k=0,v=Math.min(e.length,200),S=0;k<v&&f<a;){let C=ce(e,k);l<0&&(c<a&&C==t[c]&&(r[c++]=k),d<a&&(C==t[d]||C==i[d]?(d==0&&(p=k),y=k+1,d++):d=0));let T,B=C<255?C>=48&&C<=57||C>=97&&C<=122?2:C>=65&&C<=90?1:0:(T=zl(C))!=T.toLowerCase()?1:T!=T.toUpperCase()?2:0;(!k||B==1&&g||S==0&&B!=0)&&(t[f]==C||i[f]==C&&(u=!0)?o[f++]=k:o.length&&(b=!1)),S=B,k+=Ce(C)}return f==a&&o[0]==0&&b?this.result(-100+(u?-200:0),o,e):d==a&&p==0?[-200-e.length,0,y]:l>-1?[-700-e.length,l,l+this.pattern.length]:d==a?[-200+-700-e.length,p,y]:f==a?this.result(-100+(u?-200:0)+-700+(b?0:-1100),o,e):t.length==2?null:this.result((s[0]?-700:0)+-200+-1100,s,e)}result(e,t,i){let s=[e-i.length],r=1;for(let o of t){let l=o+(this.astral?Ce(ce(i,o)):1);r>1&&s[r-1]==o?s[r-1]=l:(s[r++]=o,s[r++]=l)}return s}}const Mt=D.define({combine(n){return Tt(n,{activateOnTyping:!0,selectOnOpen:!0,override:null,closeOnBlur:!0,maxRenderedOptions:100,defaultKeymap:!0,optionClass:()=>"",aboveCursor:!1,icons:!0,addToOptions:[],compareCompletions:(e,t)=>e.label.localeCompare(t.label),interactionDelay:75},{defaultKeymap:(e,t)=>e&&t,closeOnBlur:(e,t)=>e&&t,icons:(e,t)=>e&&t,optionClass:(e,t)=>i=>jm(e(i),t(i)),addToOptions:(e,t)=>e.concat(t)})}});function jm(n,e){return n?e?n+" "+e:n:e}function Km(n){let e=n.addToOptions.slice();return n.icons&&e.push({render(t){let i=document.createElement("div");return i.classList.add("cm-completionIcon"),t.type&&i.classList.add(...t.type.split(/\s+/g).map(s=>"cm-completionIcon-"+s)),i.setAttribute("aria-hidden","true"),i},position:20}),e.push({render(t,i,s){let r=document.createElement("span");r.className="cm-completionLabel";let{label:o}=t,l=0;for(let a=1;a<s.length;){let h=s[a++],c=s[a++];h>l&&r.appendChild(document.createTextNode(o.slice(l,h)));let f=r.appendChild(document.createElement("span"));f.appendChild(document.createTextNode(o.slice(h,c))),f.className="cm-completionMatchedText",l=c}return l<o.length&&r.appendChild(document.createTextNode(o.slice(l))),r},position:50},{render(t){if(!t.detail)return null;let i=document.createElement("span");return i.className="cm-completionDetail",i.textContent=t.detail,i},position:80}),e.sort((t,i)=>t.position-i.position).map(t=>t.render)}function kl(n,e,t){if(n<=t)return{from:0,to:n};if(e<0&&(e=0),e<=n>>1){let s=Math.floor(e/t);return{from:s*t,to:(s+1)*t}}let i=Math.floor((n-e)/t);return{from:n-(i+1)*t,to:n-i*t}}class Um{constructor(e,t){this.view=e,this.stateField=t,this.info=null,this.placeInfo={read:()=>this.measureInfo(),write:l=>this.positionInfo(l),key:this};let i=e.state.field(t),{options:s,selected:r}=i.open,o=e.state.facet(Mt);this.optionContent=Km(o),this.optionClass=o.optionClass,this.range=kl(s.length,r,o.maxRenderedOptions),this.dom=document.createElement("div"),this.dom.className="cm-tooltip-autocomplete",this.dom.addEventListener("mousedown",l=>{for(let a=l.target,h;a&&a!=this.dom;a=a.parentNode)if(a.nodeName=="LI"&&(h=/-(\d+)$/.exec(a.id))&&+h[1]<s.length){lc(e,s[+h[1]]),l.preventDefault();return}}),this.list=this.dom.appendChild(this.createListBox(s,i.id,this.range)),this.list.addEventListener("scroll",()=>{this.info&&this.view.requestMeasure(this.placeInfo)})}mount(){this.updateSel()}update(e){e.state.field(this.stateField)!=e.startState.field(this.stateField)&&this.updateSel()}positioned(){this.info&&this.view.requestMeasure(this.placeInfo)}updateSel(){let e=this.view.state.field(this.stateField),t=e.open;if((t.selected>-1&&t.selected<this.range.from||t.selected>=this.range.to)&&(this.range=kl(t.options.length,t.selected,this.view.state.facet(Mt).maxRenderedOptions),this.list.remove(),this.list=this.dom.appendChild(this.createListBox(t.options,e.id,this.range)),this.list.addEventListener("scroll",()=>{this.info&&this.view.requestMeasure(this.placeInfo)})),this.updateSelectedOption(t.selected)){this.info&&(this.info.remove(),this.info=null);let{completion:i}=t.options[t.selected],{info:s}=i;if(!s)return;let r=typeof s=="string"?document.createTextNode(s):s(i);if(!r)return;"then"in r?r.then(o=>{o&&this.view.state.field(this.stateField,!1)==e&&this.addInfoPane(o)}).catch(o=>Ee(this.view.state,o,"completion info")):this.addInfoPane(r)}}addInfoPane(e){let t=this.info=document.createElement("div");t.className="cm-tooltip cm-completionInfo",t.appendChild(e),this.dom.appendChild(t),this.view.requestMeasure(this.placeInfo)}updateSelectedOption(e){let t=null;for(let i=this.list.firstChild,s=this.range.from;i;i=i.nextSibling,s++)s==e?i.hasAttribute("aria-selected")||(i.setAttribute("aria-selected","true"),t=i):i.hasAttribute("aria-selected")&&i.removeAttribute("aria-selected");return t&&Jm(this.list,t),t}measureInfo(){let e=this.dom.querySelector("[aria-selected]");if(!e||!this.info)return null;let t=this.dom.ownerDocument.defaultView||window,i=this.dom.getBoundingClientRect(),s=this.info.getBoundingClientRect(),r=e.getBoundingClientRect();if(r.top>Math.min(t.innerHeight,i.bottom)-10||r.bottom<Math.max(0,i.top)+10)return null;let o=this.view.textDirection==Y.RTL,l=o,a=!1,h,c="",f="",u=i.left,d=t.innerWidth-i.right;if(l&&u<Math.min(s.width,d)?l=!1:!l&&d<Math.min(s.width,u)&&(l=!0),s.width<=(l?u:d))c=Math.max(0,Math.min(r.top,t.innerHeight-s.height))-i.top+"px",h=Math.min(400,l?u:d)+"px";else{a=!0,h=Math.min(400,(o?i.right:t.innerWidth-i.left)-30)+"px";let p=t.innerHeight-i.bottom;p>=s.height||p>i.top?c=r.bottom-i.top+"px":f=i.bottom-r.top+"px"}return{top:c,bottom:f,maxWidth:h,class:a?o?"left-narrow":"right-narrow":l?"left":"right"}}positionInfo(e){this.info&&(e?(this.info.style.top=e.top,this.info.style.bottom=e.bottom,this.info.style.maxWidth=e.maxWidth,this.info.className="cm-tooltip cm-completionInfo cm-completionInfo-"+e.class):this.info.style.top="-1e6px")}createListBox(e,t,i){const s=document.createElement("ul");s.id=t,s.setAttribute("role","listbox"),s.setAttribute("aria-expanded","true"),s.setAttribute("aria-label",this.view.state.phrase("Completions"));for(let r=i.from;r<i.to;r++){let{completion:o,match:l}=e[r];const a=s.appendChild(document.createElement("li"));a.id=t+"-"+r,a.setAttribute("role","option");let h=this.optionClass(o);h&&(a.className=h);for(let c of this.optionContent){let f=c(o,this.view.state,l);f&&a.appendChild(f)}}return i.from&&s.classList.add("cm-completionListIncompleteTop"),i.to<e.length&&s.classList.add("cm-completionListIncompleteBottom"),s}}function Gm(n){return e=>new Um(e,n)}function Jm(n,e){let t=n.getBoundingClientRect(),i=e.getBoundingClientRect();i.top<t.top?n.scrollTop-=t.top-i.top:i.bottom>t.bottom&&(n.scrollTop+=i.bottom-t.bottom)}function vl(n){return(n.boost||0)*100+(n.apply?10:0)+(n.info?5:0)+(n.type?1:0)}function $m(n,e){let t=[],i=0;for(let l of n)if(l.hasResult())if(l.result.filter===!1){let a=l.result.getMatch;for(let h of l.result.options){let c=[1e9-i++];if(a)for(let f of a(h))c.push(f);t.push(new wl(h,l,c))}}else{let a=new qm(e.sliceDoc(l.from,l.to)),h;for(let c of l.result.options)(h=a.match(c.label))&&(c.boost!=null&&(h[0]+=c.boost),t.push(new wl(c,l,h)))}let s=[],r=null,o=e.facet(Mt).compareCompletions;for(let l of t.sort((a,h)=>h.match[0]-a.match[0]||o(a.completion,h.completion)))!r||r.label!=l.completion.label||r.detail!=l.completion.detail||r.type!=null&&l.completion.type!=null&&r.type!=l.completion.type||r.apply!=l.completion.apply?s.push(l):vl(l.completion)>vl(r)&&(s[s.length-1]=l),r=l.completion;return s}class di{constructor(e,t,i,s,r){this.options=e,this.attrs=t,this.tooltip=i,this.timestamp=s,this.selected=r}setSelected(e,t){return e==this.selected||e>=this.options.length?this:new di(this.options,Sl(t,e),this.tooltip,this.timestamp,e)}static build(e,t,i,s,r){let o=$m(e,t);if(!o.length)return null;let l=t.facet(Mt).selectOnOpen?0:-1;if(s&&s.selected!=l&&s.selected!=-1){let a=s.options[s.selected].completion;for(let h=0;h<o.length;h++)if(o[h].completion==a){l=h;break}}return new di(o,Sl(i,l),{pos:e.reduce((a,h)=>h.hasResult()?Math.min(a,h.from):a,1e8),create:Gm(Oi),above:r.aboveCursor},s?s.timestamp:Date.now(),l)}map(e){return new di(this.options,this.attrs,Object.assign(Object.assign({},this.tooltip),{pos:e.mapPos(this.tooltip.pos)}),this.timestamp,this.selected)}}class xn{constructor(e,t,i){this.active=e,this.id=t,this.open=i}static start(){return new xn(Zm,"cm-ac-"+Math.floor(Math.random()*2e6).toString(36),null)}update(e){let{state:t}=e,i=t.facet(Mt),r=(i.override||t.languageDataAt("autocomplete",nr(t)).map(_m)).map(l=>(this.active.find(h=>h.source==l)||new Ye(l,this.active.some(h=>h.state!=0)?1:0)).update(e,i));r.length==this.active.length&&r.every((l,a)=>l==this.active[a])&&(r=this.active);let o=e.selection||r.some(l=>l.hasResult()&&e.changes.touchesRange(l.from,l.to))||!Ym(r,this.active)?di.build(r,t,this.id,this.open,i):this.open&&e.docChanged?this.open.map(e.changes):this.open;!o&&r.every(l=>l.state!=1)&&r.some(l=>l.hasResult())&&(r=r.map(l=>l.hasResult()?new Ye(l.source,0):l));for(let l of e.effects)l.is(cc)&&(o=o&&o.setSelected(l.value,this.id));return r==this.active&&o==this.open?this:new xn(r,this.id,o)}get tooltip(){return this.open?this.open.tooltip:null}get attrs(){return this.open?this.open.attrs:Xm}}function Ym(n,e){if(n==e)return!0;for(let t=0,i=0;;){for(;t<n.length&&!n[t].hasResult;)t++;for(;i<e.length&&!e[i].hasResult;)i++;let s=t==n.length,r=i==e.length;if(s||r)return s==r;if(n[t++].result!=e[i++].result)return!1}}const Xm={"aria-autocomplete":"list"};function Sl(n,e){let t={"aria-autocomplete":"list","aria-haspopup":"listbox","aria-controls":n};return e>-1&&(t["aria-activedescendant"]=n+"-"+e),t}const Zm=[];function Qm(n){return n.isUserEvent("input.type")?"input":n.isUserEvent("delete.backward")?"delete":null}class Ye{constructor(e,t,i=-1){this.source=e,this.state=t,this.explicitPos=i}hasResult(){return!1}update(e,t){let i=Qm(e),s=this;i?s=s.handleUserEvent(e,i,t):e.docChanged?s=s.handleChange(e):e.selection&&s.state!=0&&(s=new Ye(s.source,0));for(let r of e.effects)if(r.is(ac))s=new Ye(s.source,1,r.value?nr(e.state):-1);else if(r.is(hc))s=new Ye(s.source,0);else if(r.is(eg))for(let o of r.value)o.source==s.source&&(s=o);return s}handleUserEvent(e,t,i){return t=="delete"||!i.activateOnTyping?this.map(e.changes):new Ye(this.source,1)}handleChange(e){return e.changes.touchesRange(nr(e.startState))?new Ye(this.source,0):this.map(e.changes)}map(e){return e.empty||this.explicitPos<0?this:new Ye(this.source,this.state,e.mapPos(this.explicitPos))}}const ac=R.define(),hc=R.define(),eg=R.define({map(n,e){return n.map(t=>t.map(e))}}),cc=R.define(),Oi=be.define({create(){return xn.start()},update(n,e){return n.update(e)},provide:n=>[ur.from(n,e=>e.tooltip),O.contentAttributes.from(n,e=>e.attrs)]});function Yi(n,e="option"){return t=>{let i=t.state.field(Oi,!1);if(!i||!i.open||Date.now()-i.open.timestamp<t.state.facet(Mt).interactionDelay)return!1;let s=1,r;e=="page"&&(r=Uu(t,i.open.tooltip))&&(s=Math.max(2,Math.floor(r.dom.offsetHeight/r.dom.querySelector("li").offsetHeight)-1));let{length:o}=i.open.options,l=i.open.selected>-1?i.open.selected+s*(n?1:-1):n?0:o-1;return l<0?l=e=="page"?0:o-1:l>=o&&(l=e=="page"?o-1:0),t.dispatch({effects:cc.of(l)}),!0}}const tg=n=>{let e=n.state.field(Oi,!1);return n.state.readOnly||!e||!e.open||e.open.selected<0||Date.now()-e.open.timestamp<n.state.facet(Mt).interactionDelay?!1:(lc(n,e.open.options[e.open.selected]),!0)},ig=n=>n.state.field(Oi,!1)?(n.dispatch({effects:ac.of(!0)}),!0):!1,ng=n=>{let e=n.state.field(Oi,!1);return!e||!e.active.some(t=>t.state!=0)?!1:(n.dispatch({effects:hc.of(null)}),!0)},sg=O.baseTheme({".cm-tooltip.cm-tooltip-autocomplete":{"& > ul":{fontFamily:"monospace",whiteSpace:"nowrap",overflow:"hidden auto",maxWidth_fallback:"700px",maxWidth:"min(700px, 95vw)",minWidth:"250px",maxHeight:"10em",listStyle:"none",margin:0,padding:0,"& > li":{overflowX:"hidden",textOverflow:"ellipsis",cursor:"pointer",padding:"1px 3px",lineHeight:1.2}}},"&light .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#17c",color:"white"},"&dark .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#347",color:"white"},".cm-completionListIncompleteTop:before, .cm-completionListIncompleteBottom:after":{content:'"···"',opacity:.5,display:"block",textAlign:"center"},".cm-tooltip.cm-completionInfo":{position:"absolute",padding:"3px 9px",width:"max-content",maxWidth:"400px",boxSizing:"border-box"},".cm-completionInfo.cm-completionInfo-left":{right:"100%"},".cm-completionInfo.cm-completionInfo-right":{left:"100%"},".cm-completionInfo.cm-completionInfo-left-narrow":{right:"30px"},".cm-completionInfo.cm-completionInfo-right-narrow":{left:"30px"},"&light .cm-snippetField":{backgroundColor:"#00000022"},"&dark .cm-snippetField":{backgroundColor:"#ffffff22"},".cm-snippetFieldPosition":{verticalAlign:"text-top",width:0,height:"1.15em",display:"inline-block",margin:"0 -0.7px -.7em",borderLeft:"1.4px dotted #888"},".cm-completionMatchedText":{textDecoration:"underline"},".cm-completionDetail":{marginLeft:"0.5em",fontStyle:"italic"},".cm-completionIcon":{fontSize:"90%",width:".8em",display:"inline-block",textAlign:"center",paddingRight:".6em",opacity:"0.6"},".cm-completionIcon-function, .cm-completionIcon-method":{"&:after":{content:"'ƒ'"}},".cm-completionIcon-class":{"&:after":{content:"'○'"}},".cm-completionIcon-interface":{"&:after":{content:"'◌'"}},".cm-completionIcon-variable":{"&:after":{content:"'𝑥'"}},".cm-completionIcon-constant":{"&:after":{content:"'𝐶'"}},".cm-completionIcon-type":{"&:after":{content:"'𝑡'"}},".cm-completionIcon-enum":{"&:after":{content:"'∪'"}},".cm-completionIcon-property":{"&:after":{content:"'□'"}},".cm-completionIcon-keyword":{"&:after":{content:"'🔑︎'"}},".cm-completionIcon-namespace":{"&:after":{content:"'▢'"}},".cm-completionIcon-text":{"&:after":{content:"'abc'",fontSize:"50%",verticalAlign:"middle"}}});class rg{constructor(e,t,i,s){this.field=e,this.line=t,this.from=i,this.to=s}}class Tr{constructor(e,t,i){this.field=e,this.from=t,this.to=i}map(e){let t=e.mapPos(this.from,-1,le.TrackDel),i=e.mapPos(this.to,1,le.TrackDel);return t==null||i==null?null:new Tr(this.field,t,i)}}class Or{constructor(e,t){this.lines=e,this.fieldPositions=t}instantiate(e,t){let i=[],s=[t],r=e.doc.lineAt(t),o=/^\s*/.exec(r.text)[0];for(let a of this.lines){if(i.length){let h=o,c=/^\t*/.exec(a)[0].length;for(let f=0;f<c;f++)h+=e.facet(Mn);s.push(t+h.length-c),a=h+a.slice(c)}i.push(a),t+=a.length+1}let l=this.fieldPositions.map(a=>new Tr(a.field,s[a.line]+a.from,s[a.line]+a.to));return{text:i,ranges:l}}static parse(e){let t=[],i=[],s=[],r;for(let o of e.split(/\r\n?|\n/)){for(;r=/[#$]\{(?:(\d+)(?::([^}]*))?|([^}]*))\}/.exec(o);){let l=r[1]?+r[1]:null,a=r[2]||r[3]||"",h=-1;for(let c=0;c<t.length;c++)(l!=null?t[c].seq==l:a&&t[c].name==a)&&(h=c);if(h<0){let c=0;for(;c<t.length&&(l==null||t[c].seq!=null&&t[c].seq<l);)c++;t.splice(c,0,{seq:l,name:a}),h=c;for(let f of s)f.field>=h&&f.field++}s.push(new rg(h,i.length,r.index,r.index+a.length)),o=o.slice(0,r.index)+a+o.slice(r.index+r[0].length)}for(let l;l=/([$#])\\{/.exec(o);){o=o.slice(0,l.index)+l[1]+"{"+o.slice(l.index+l[0].length);for(let a of s)a.line==i.length&&a.from>l.index&&(a.from--,a.to--)}i.push(o)}return new Or(i,s)}}let og=E.widget({widget:new class extends Ge{toDOM(){let n=document.createElement("span");return n.className="cm-snippetFieldPosition",n}ignoreEvent(){return!1}}}),lg=E.mark({class:"cm-snippetField"});class Yt{constructor(e,t){this.ranges=e,this.active=t,this.deco=E.set(e.map(i=>(i.from==i.to?og:lg).range(i.from,i.to)))}map(e){let t=[];for(let i of this.ranges){let s=i.map(e);if(!s)return null;t.push(s)}return new Yt(t,this.active)}selectionInsideField(e){return e.ranges.every(t=>this.ranges.some(i=>i.field==this.active&&i.from<=t.from&&i.to>=t.to))}}const Bi=R.define({map(n,e){return n&&n.map(e)}}),ag=R.define(),Si=be.define({create(){return null},update(n,e){for(let t of e.effects){if(t.is(Bi))return t.value;if(t.is(ag)&&n)return new Yt(n.ranges,t.value)}return n&&e.docChanged&&(n=n.map(e.changes)),n&&e.selection&&!n.selectionInsideField(e.selection)&&(n=null),n},provide:n=>O.decorations.from(n,e=>e?e.deco:E.none)});function Br(n,e){return w.create(n.filter(t=>t.field==e).map(t=>w.range(t.from,t.to)))}function hg(n){let e=Or.parse(n);return(t,i,s,r)=>{let{text:o,ranges:l}=e.instantiate(t.state,s),a={changes:{from:s,to:r,insert:V.of(o)},scrollIntoView:!0};if(l.length&&(a.selection=Br(l,0)),l.length>1){let h=new Yt(l,0),c=a.effects=[Bi.of(h)];t.state.field(Si,!1)===void 0&&c.push(R.appendConfig.of([Si,pg,mg,sg]))}t.dispatch(t.state.update(a))}}function fc(n){return({state:e,dispatch:t})=>{let i=e.field(Si,!1);if(!i||n<0&&i.active==0)return!1;let s=i.active+n,r=n>0&&!i.ranges.some(o=>o.field==s+n);return t(e.update({selection:Br(i.ranges,s),effects:Bi.of(r?null:new Yt(i.ranges,s))})),!0}}const cg=({state:n,dispatch:e})=>n.field(Si,!1)?(e(n.update({effects:Bi.of(null)})),!0):!1,fg=fc(1),ug=fc(-1),dg=[{key:"Tab",run:fg,shift:ug},{key:"Escape",run:cg}],Cl=D.define({combine(n){return n.length?n[0]:dg}}),pg=Ai.highest(An.compute([Cl],n=>n.facet(Cl)));function r0(n,e){return Object.assign(Object.assign({},e),{apply:hg(n)})}const mg=O.domEventHandlers({mousedown(n,e){let t=e.state.field(Si,!1),i;if(!t||(i=e.posAtCoords({x:n.clientX,y:n.clientY}))==null)return!1;let s=t.ranges.find(r=>r.from<=i&&r.to>=i);return!s||s.field==t.active?!1:(e.dispatch({selection:Br(t.ranges,s.field),effects:Bi.of(t.ranges.some(r=>r.field>s.field)?new Yt(t.ranges,s.field):null)}),!0)}}),Ci={brackets:["(","[","{","'",'"'],before:")]}:;>",stringPrefixes:[]},wt=R.define({map(n,e){let t=e.mapPos(n,-1,le.TrackAfter);return t??void 0}}),Pr=R.define({map(n,e){return e.mapPos(n)}}),Er=new class extends kt{};Er.startSide=1;Er.endSide=-1;const uc=be.define({create(){return H.empty},update(n,e){if(e.selection){let t=e.state.doc.lineAt(e.selection.main.head).from,i=e.startState.doc.lineAt(e.startState.selection.main.head).from;t!=e.changes.mapPos(i,-1)&&(n=H.empty)}n=n.map(e.changes);for(let t of e.effects)t.is(wt)?n=n.update({add:[Er.range(t.value,t.value+1)]}):t.is(Pr)&&(n=n.update({filter:i=>i!=t.value}));return n}});function gg(){return[bg,uc]}const cs="()[]{}<>";function dc(n){for(let e=0;e<cs.length;e+=2)if(cs.charCodeAt(e)==n)return cs.charAt(e+1);return zl(n<128?n:n+1)}function pc(n,e){return n.languageDataAt("closeBrackets",e)[0]||Ci}const yg=typeof navigator=="object"&&/Android\b/.test(navigator.userAgent),bg=O.inputHandler.of((n,e,t,i)=>{if((yg?n.composing:n.compositionStarted)||n.state.readOnly)return!1;let s=n.state.selection.main;if(i.length>2||i.length==2&&Ce(ce(i,0))==1||e!=s.from||t!=s.to)return!1;let r=kg(n.state,i);return r?(n.dispatch(r),!0):!1}),wg=({state:n,dispatch:e})=>{if(n.readOnly)return!1;let i=pc(n,n.selection.main.head).brackets||Ci.brackets,s=null,r=n.changeByRange(o=>{if(o.empty){let l=vg(n.doc,o.head);for(let a of i)if(a==l&&Ln(n.doc,o.head)==dc(ce(a,0)))return{changes:{from:o.head-a.length,to:o.head+a.length},range:w.cursor(o.head-a.length)}}return{range:s=o}});return s||e(n.update(r,{scrollIntoView:!0,userEvent:"delete.backward"})),!s},xg=[{key:"Backspace",run:wg}];function kg(n,e){let t=pc(n,n.selection.main.head),i=t.brackets||Ci.brackets;for(let s of i){let r=dc(ce(s,0));if(e==s)return r==s?Ag(n,s,i.indexOf(s+s+s)>-1,t):Sg(n,s,r,t.before||Ci.before);if(e==r&&mc(n,n.selection.main.from))return Cg(n,s,r)}return null}function mc(n,e){let t=!1;return n.field(uc).between(0,n.doc.length,i=>{i==e&&(t=!0)}),t}function Ln(n,e){let t=n.sliceString(e,e+2);return t.slice(0,Ce(ce(t,0)))}function vg(n,e){let t=n.sliceString(e-2,e);return Ce(ce(t,0))==t.length?t:t.slice(1)}function Sg(n,e,t,i){let s=null,r=n.changeByRange(o=>{if(!o.empty)return{changes:[{insert:e,from:o.from},{insert:t,from:o.to}],effects:wt.of(o.to+e.length),range:w.range(o.anchor+e.length,o.head+e.length)};let l=Ln(n.doc,o.head);return!l||/\s/.test(l)||i.indexOf(l)>-1?{changes:{insert:e+t,from:o.head},effects:wt.of(o.head+e.length),range:w.cursor(o.head+e.length)}:{range:s=o}});return s?null:n.update(r,{scrollIntoView:!0,userEvent:"input.type"})}function Cg(n,e,t){let i=null,s=n.selection.ranges.map(r=>r.empty&&Ln(n.doc,r.head)==t?w.cursor(r.head+t.length):i=r);return i?null:n.update({selection:w.create(s,n.selection.mainIndex),scrollIntoView:!0,effects:n.selection.ranges.map(({from:r})=>Pr.of(r))})}function Ag(n,e,t,i){let s=i.stringPrefixes||Ci.stringPrefixes,r=null,o=n.changeByRange(l=>{if(!l.empty)return{changes:[{insert:e,from:l.from},{insert:e,from:l.to}],effects:wt.of(l.to+e.length),range:w.range(l.anchor+e.length,l.head+e.length)};let a=l.head,h=Ln(n.doc,a),c;if(h==e){if(Al(n,a))return{changes:{insert:e+e,from:a},effects:wt.of(a+e.length),range:w.cursor(a+e.length)};if(mc(n,a)){let f=t&&n.sliceDoc(a,a+e.length*3)==e+e+e;return{range:w.cursor(a+e.length*(f?3:1)),effects:Pr.of(a)}}}else{if(t&&n.sliceDoc(a-2*e.length,a)==e+e&&(c=Ml(n,a-2*e.length,s))>-1&&Al(n,c))return{changes:{insert:e+e+e+e,from:a},effects:wt.of(a+e.length),range:w.cursor(a+e.length)};if(n.charCategorizer(a)(h)!=Ae.Word&&Ml(n,a,s)>-1&&!Mg(n,a,e,s))return{changes:{insert:e+e,from:a},effects:wt.of(a+e.length),range:w.cursor(a+e.length)}}return{range:r=l}});return r?null:n.update(o,{scrollIntoView:!0,userEvent:"input.type"})}function Al(n,e){let t=ae(n).resolveInner(e+1);return t.parent&&t.from==e}function Mg(n,e,t,i){let s=ae(n).resolveInner(e,-1),r=i.reduce((o,l)=>Math.max(o,l.length),0);for(let o=0;o<5;o++){let l=n.sliceDoc(s.from,Math.min(s.to,s.from+t.length+r)),a=l.indexOf(t);if(!a||a>-1&&i.indexOf(l.slice(0,a))>-1){let c=s.firstChild;for(;c&&c.from==s.from&&c.to-c.from>t.length+a;){if(n.sliceDoc(c.to-t.length,c.to)==t)return!1;c=c.firstChild}return!0}let h=s.to==e&&s.parent;if(!h)break;s=h}return!1}function Ml(n,e,t){let i=n.charCategorizer(e);if(i(n.sliceDoc(e-1,e))!=Ae.Word)return e;for(let s of t){let r=e-s.length;if(n.sliceDoc(r,e)==s&&i(n.sliceDoc(r-1,r))!=Ae.Word)return r}return-1}const Dg=[{key:"Ctrl-Space",run:ig},{key:"Escape",run:ng},{key:"ArrowDown",run:Yi(!0)},{key:"ArrowUp",run:Yi(!1)},{key:"PageDown",run:Yi(!0,"page")},{key:"PageUp",run:Yi(!1,"page")},{key:"Enter",run:tg}];function ze(){var n=arguments[0];typeof n=="string"&&(n=document.createElement(n));var e=1,t=arguments[1];if(t&&typeof t=="object"&&t.nodeType==null&&!Array.isArray(t)){for(var i in t)if(Object.prototype.hasOwnProperty.call(t,i)){var s=t[i];typeof s=="string"?n.setAttribute(i,s):s!=null&&(n[i]=s)}e++}for(;e<arguments.length;e++)gc(n,arguments[e]);return n}function gc(n,e){if(typeof e=="string")n.appendChild(document.createTextNode(e));else if(e!=null)if(e.nodeType!=null)n.appendChild(e);else if(Array.isArray(e))for(var t=0;t<e.length;t++)gc(n,e[t]);else throw new RangeError("Unsupported child node: "+e)}class Tg{constructor(e,t,i){this.from=e,this.to=t,this.diagnostic=i}}class gt{constructor(e,t,i){this.diagnostics=e,this.panel=t,this.selected=i}static init(e,t,i){let s=e,r=i.facet(Nt).markerFilter;r&&(s=r(s));let o=E.set(s.map(l=>l.from==l.to||l.from==l.to-1&&i.doc.lineAt(l.from).to==l.from?E.widget({widget:new Fg(l),diagnostic:l}).range(l.from):E.mark({attributes:{class:"cm-lintRange cm-lintRange-"+l.severity},diagnostic:l}).range(l.from,l.to)),!0);return new gt(o,t,Gt(o))}}function Gt(n,e=null,t=0){let i=null;return n.between(t,1e9,(s,r,{spec:o})=>{if(!(e&&o.diagnostic!=e))return i=new Tg(s,r,o.diagnostic),!1}),i}function Og(n,e){return!!(n.effects.some(t=>t.is(Rr))||n.changes.touchesRange(e.pos))}function yc(n,e){return n.field(ve,!1)?e:e.concat(R.appendConfig.of([ve,O.decorations.compute([ve],t=>{let{selected:i,panel:s}=t.field(ve);return!i||!s||i.from==i.to?E.none:E.set([Pg.range(i.from,i.to)])}),Ku(Eg,{hideOn:Og}),Wg]))}function Bg(n,e){return{effects:yc(n,[Rr.of(e)])}}const Rr=R.define(),Lr=R.define(),bc=R.define(),ve=be.define({create(){return new gt(E.none,null,null)},update(n,e){if(e.docChanged){let t=n.diagnostics.map(e.changes),i=null;if(n.selected){let s=e.changes.mapPos(n.selected.from,1);i=Gt(t,n.selected.diagnostic,s)||Gt(t,null,s)}n=new gt(t,n.panel,i)}for(let t of e.effects)t.is(Rr)?n=gt.init(t.value,n.panel,e.state):t.is(Lr)?n=new gt(n.diagnostics,t.value?In.open:null,n.selected):t.is(bc)&&(n=new gt(n.diagnostics,n.panel,t.value));return n},provide:n=>[js.from(n,e=>e.panel),O.decorations.from(n,e=>e.diagnostics)]}),Pg=E.mark({class:"cm-lintRange cm-lintRange-active"});function Eg(n,e,t){let{diagnostics:i}=n.state.field(ve),s=[],r=2e8,o=0;i.between(e-(t<0?1:0),e+(t>0?1:0),(a,h,{spec:c})=>{e>=a&&e<=h&&(a==h||(e>a||t>0)&&(e<h||t<0))&&(s.push(c.diagnostic),r=Math.min(a,r),o=Math.max(h,o))});let l=n.state.facet(Nt).tooltipFilter;return l&&(s=l(s)),s.length?{pos:r,end:o,above:n.state.doc.lineAt(r).to<o,create(){return{dom:Rg(n,s)}}}:null}function Rg(n,e){return ze("ul",{class:"cm-tooltip-lint"},e.map(t=>xc(n,t,!1)))}const Lg=n=>{let e=n.state.field(ve,!1);(!e||!e.panel)&&n.dispatch({effects:yc(n.state,[Lr.of(!0)])});let t=Ju(n,In.open);return t&&t.dom.querySelector(".cm-panel-lint ul").focus(),!0},Dl=n=>{let e=n.state.field(ve,!1);return!e||!e.panel?!1:(n.dispatch({effects:Lr.of(!1)}),!0)},Ig=n=>{let e=n.state.field(ve,!1);if(!e)return!1;let t=n.state.selection.main,i=e.diagnostics.iter(t.to+1);return!i.value&&(i=e.diagnostics.iter(0),!i.value||i.from==t.from&&i.to==t.to)?!1:(n.dispatch({selection:{anchor:i.from,head:i.to},scrollIntoView:!0}),!0)},Ng=[{key:"Mod-Shift-m",run:Lg},{key:"F8",run:Ig}],Vg=ue.fromClass(class{constructor(n){this.view=n,this.timeout=-1,this.set=!0;let{delay:e}=n.state.facet(Nt);this.lintTime=Date.now()+e,this.run=this.run.bind(this),this.timeout=setTimeout(this.run,e)}run(){let n=Date.now();if(n<this.lintTime-10)setTimeout(this.run,this.lintTime-n);else{this.set=!1;let{state:e}=this.view,{sources:t}=e.facet(Nt);Promise.all(t.map(i=>Promise.resolve(i(this.view)))).then(i=>{let s=i.reduce((r,o)=>r.concat(o));this.view.state.doc==e.doc&&this.view.dispatch(Bg(this.view.state,s))},i=>{Ee(this.view.state,i)})}}update(n){let e=n.state.facet(Nt);(n.docChanged||e!=n.startState.facet(Nt))&&(this.lintTime=Date.now()+e.delay,this.set||(this.set=!0,this.timeout=setTimeout(this.run,e.delay)))}force(){this.set&&(this.lintTime=Date.now(),this.run())}destroy(){clearTimeout(this.timeout)}}),Nt=D.define({combine(n){return Object.assign({sources:n.map(e=>e.source)},Tt(n.map(e=>e.config),{delay:750,markerFilter:null,tooltipFilter:null}))},enables:Vg});function wc(n){let e=[];if(n)e:for(let{name:t}of n){for(let i=0;i<t.length;i++){let s=t[i];if(/[a-zA-Z]/.test(s)&&!e.some(r=>r.toLowerCase()==s.toLowerCase())){e.push(s);continue e}}e.push("")}return e}function xc(n,e,t){var i;let s=t?wc(e.actions):[];return ze("li",{class:"cm-diagnostic cm-diagnostic-"+e.severity},ze("span",{class:"cm-diagnosticText"},e.renderMessage?e.renderMessage():e.message),(i=e.actions)===null||i===void 0?void 0:i.map((r,o)=>{let l=f=>{f.preventDefault();let u=Gt(n.state.field(ve).diagnostics,e);u&&r.apply(n,u.from,u.to)},{name:a}=r,h=s[o]?a.indexOf(s[o]):-1,c=h<0?a:[a.slice(0,h),ze("u",a.slice(h,h+1)),a.slice(h+1)];return ze("button",{type:"button",class:"cm-diagnosticAction",onclick:l,onmousedown:l,"aria-label":` Action: ${a}${h<0?"":` (access key "${s[o]})"`}.`},c)}),e.source&&ze("div",{class:"cm-diagnosticSource"},e.source))}class Fg extends Ge{constructor(e){super(),this.diagnostic=e}eq(e){return e.diagnostic==this.diagnostic}toDOM(){return ze("span",{class:"cm-lintPoint cm-lintPoint-"+this.diagnostic.severity})}}class Tl{constructor(e,t){this.diagnostic=t,this.id="item_"+Math.floor(Math.random()*4294967295).toString(16),this.dom=xc(e,t,!0),this.dom.id=this.id,this.dom.setAttribute("role","option")}}class In{constructor(e){this.view=e,this.items=[];let t=s=>{if(s.keyCode==27)Dl(this.view),this.view.focus();else if(s.keyCode==38||s.keyCode==33)this.moveSelection((this.selectedIndex-1+this.items.length)%this.items.length);else if(s.keyCode==40||s.keyCode==34)this.moveSelection((this.selectedIndex+1)%this.items.length);else if(s.keyCode==36)this.moveSelection(0);else if(s.keyCode==35)this.moveSelection(this.items.length-1);else if(s.keyCode==13)this.view.focus();else if(s.keyCode>=65&&s.keyCode<=90&&this.selectedIndex>=0){let{diagnostic:r}=this.items[this.selectedIndex],o=wc(r.actions);for(let l=0;l<o.length;l++)if(o[l].toUpperCase().charCodeAt(0)==s.keyCode){let a=Gt(this.view.state.field(ve).diagnostics,r);a&&r.actions[l].apply(e,a.from,a.to)}}else return;s.preventDefault()},i=s=>{for(let r=0;r<this.items.length;r++)this.items[r].dom.contains(s.target)&&this.moveSelection(r)};this.list=ze("ul",{tabIndex:0,role:"listbox","aria-label":this.view.state.phrase("Diagnostics"),onkeydown:t,onclick:i}),this.dom=ze("div",{class:"cm-panel-lint"},this.list,ze("button",{type:"button",name:"close","aria-label":this.view.state.phrase("close"),onclick:()=>Dl(this.view)},"×")),this.update()}get selectedIndex(){let e=this.view.state.field(ve).selected;if(!e)return-1;for(let t=0;t<this.items.length;t++)if(this.items[t].diagnostic==e.diagnostic)return t;return-1}update(){let{diagnostics:e,selected:t}=this.view.state.field(ve),i=0,s=!1,r=null;for(e.between(0,this.view.state.doc.length,(o,l,{spec:a})=>{let h=-1,c;for(let f=i;f<this.items.length;f++)if(this.items[f].diagnostic==a.diagnostic){h=f;break}h<0?(c=new Tl(this.view,a.diagnostic),this.items.splice(i,0,c),s=!0):(c=this.items[h],h>i&&(this.items.splice(i,h-i),s=!0)),t&&c.diagnostic==t.diagnostic?c.dom.hasAttribute("aria-selected")||(c.dom.setAttribute("aria-selected","true"),r=c):c.dom.hasAttribute("aria-selected")&&c.dom.removeAttribute("aria-selected"),i++});i<this.items.length&&!(this.items.length==1&&this.items[0].diagnostic.from<0);)s=!0,this.items.pop();this.items.length==0&&(this.items.push(new Tl(this.view,{from:-1,to:-1,severity:"info",message:this.view.state.phrase("No diagnostics")})),s=!0),r?(this.list.setAttribute("aria-activedescendant",r.id),this.view.requestMeasure({key:this,read:()=>({sel:r.dom.getBoundingClientRect(),panel:this.list.getBoundingClientRect()}),write:({sel:o,panel:l})=>{o.top<l.top?this.list.scrollTop-=l.top-o.top:o.bottom>l.bottom&&(this.list.scrollTop+=o.bottom-l.bottom)}})):this.selectedIndex<0&&this.list.removeAttribute("aria-activedescendant"),s&&this.sync()}sync(){let e=this.list.firstChild;function t(){let i=e;e=i.nextSibling,i.remove()}for(let i of this.items)if(i.dom.parentNode==this.list){for(;e!=i.dom;)t();e=i.dom.nextSibling}else this.list.insertBefore(i.dom,e);for(;e;)t()}moveSelection(e){if(this.selectedIndex<0)return;let t=this.view.state.field(ve),i=Gt(t.diagnostics,this.items[e].diagnostic);i&&this.view.dispatch({selection:{anchor:i.from,head:i.to},scrollIntoView:!0,effects:bc.of(i)})}static open(e){return new In(e)}}function Hg(n,e='viewBox="0 0 40 40"'){return`url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" ${e}>${encodeURIComponent(n)}</svg>')`}function fs(n){return Hg(`<path d="m0 2.5 l2 -1.5 l1 0 l2 1.5 l1 0" stroke="${n}" fill="none" stroke-width=".7"/>`,'width="6" height="3"')}const Wg=O.baseTheme({".cm-diagnostic":{padding:"3px 6px 3px 8px",marginLeft:"-1px",display:"block",whiteSpace:"pre-wrap"},".cm-diagnostic-error":{borderLeft:"5px solid #d11"},".cm-diagnostic-warning":{borderLeft:"5px solid orange"},".cm-diagnostic-info":{borderLeft:"5px solid #999"},".cm-diagnosticAction":{font:"inherit",border:"none",padding:"2px 4px",backgroundColor:"#444",color:"white",borderRadius:"3px",marginLeft:"8px"},".cm-diagnosticSource":{fontSize:"70%",opacity:.7},".cm-lintRange":{backgroundPosition:"left bottom",backgroundRepeat:"repeat-x",paddingBottom:"0.7px"},".cm-lintRange-error":{backgroundImage:fs("#d11")},".cm-lintRange-warning":{backgroundImage:fs("orange")},".cm-lintRange-info":{backgroundImage:fs("#999")},".cm-lintRange-active":{backgroundColor:"#ffdd9980"},".cm-tooltip-lint":{padding:0,margin:0},".cm-lintPoint":{position:"relative","&:after":{content:'""',position:"absolute",bottom:0,left:"-2px",borderLeft:"3px solid transparent",borderRight:"3px solid transparent",borderBottom:"4px solid #d11"}},".cm-lintPoint-warning":{"&:after":{borderBottomColor:"orange"}},".cm-lintPoint-info":{"&:after":{borderBottomColor:"#999"}},".cm-panel.cm-panel-lint":{position:"relative","& ul":{maxHeight:"100px",overflowY:"auto","& [aria-selected]":{backgroundColor:"#ddd","& u":{textDecoration:"underline"}},"&:focus [aria-selected]":{background_fallback:"#bdf",backgroundColor:"Highlight",color_fallback:"white",color:"HighlightText"},"& u":{textDecoration:"none"},padding:0,margin:0},"& [name=close]":{position:"absolute",top:"0",right:"2px",background:"inherit",border:"none",font:"inherit",padding:0,margin:0}}}),zg=(()=>[id(),Su(),vp(),$d(),du(),N.allowMultipleSelections.of(!0),Nd(),xr(Qd,{fallback:!0}),gg(),Lu(),Vu(),An.of([...xg,...xm,...Pp,...Ud,...Dg,...Ng])])(),Ol={python:()=>Se(()=>import("./index-d9947285.js"),["./index-d9947285.js","./index-ceec2f2d.js","./index-2519a27e.js","./index-29fa5a20.css"],import.meta.url).then(n=>n.python()),markdown:async()=>{const[n,e]=await Promise.all([Se(()=>import("./index-96c00791.js"),["./index-96c00791.js","./index-6f028ff7.js","./index-ceec2f2d.js","./index-ac1bf2d2.js","./index-2519a27e.js","./index-29fa5a20.css","./index-dbd73d11.js"],import.meta.url),Se(()=>import("./frontmatter-fdf344ec.js"),["./frontmatter-fdf344ec.js","./yaml-95012b83.js","./index-2519a27e.js","./index-29fa5a20.css"],import.meta.url)]);return n.markdown({extensions:[e.frontmatter]})},json:()=>Se(()=>import("./index-7c64fb49.js"),["./index-7c64fb49.js","./index-ceec2f2d.js","./index-2519a27e.js","./index-29fa5a20.css"],import.meta.url).then(n=>n.json()),html:()=>Se(()=>import("./index-6f028ff7.js"),["./index-6f028ff7.js","./index-ceec2f2d.js","./index-ac1bf2d2.js","./index-2519a27e.js","./index-29fa5a20.css","./index-dbd73d11.js"],import.meta.url).then(n=>n.html()),css:()=>Se(()=>import("./index-ac1bf2d2.js"),["./index-ac1bf2d2.js","./index-ceec2f2d.js","./index-2519a27e.js","./index-29fa5a20.css"],import.meta.url).then(n=>n.css()),javascript:()=>Se(()=>import("./index-dbd73d11.js"),["./index-dbd73d11.js","./index-ceec2f2d.js","./index-2519a27e.js","./index-29fa5a20.css"],import.meta.url).then(n=>n.javascript()),typescript:()=>Se(()=>import("./index-dbd73d11.js"),["./index-dbd73d11.js","./index-ceec2f2d.js","./index-2519a27e.js","./index-29fa5a20.css"],import.meta.url).then(n=>n.javascript({typescript:!0})),yaml:()=>Se(()=>import("./yaml-95012b83.js"),[],import.meta.url).then(n=>It.define(n.yaml)),dockerfile:()=>Se(()=>import("./dockerfile-d67bbd50.js"),[],import.meta.url).then(n=>It.define(n.dockerFile)),shell:()=>Se(()=>import("./shell-86dd1d99.js"),[],import.meta.url).then(n=>It.define(n.shell)),r:()=>Se(()=>import("./r-3ca97919.js"),[],import.meta.url).then(n=>It.define(n.r))},_g={py:"python",md:"markdown",js:"javascript",ts:"typescript",sh:"shell"};async function qg(n){const e=Ol[n]||Ol[_g[n]]||void 0;if(e)return e()}function jg(n){let e,t,i;return{c(){e=Vr("div"),t=Vr("div"),et(t,"class",i="codemirror-wrapper "+n[0]+" svelte-1sc8eck"),et(e,"class","wrap svelte-1sc8eck")},m(s,r){Rl(s,e,r),Ll(e,t),n[12](t)},p(s,[r]){r&1&&i!==(i="codemirror-wrapper "+s[0]+" svelte-1sc8eck")&&et(t,"class",i)},i:ai,o:ai,d(s){s&&Il(e),n[12](null)}}}function Kg(n){let e=n.dom.querySelectorAll(".cm-gutterElement");if(e.length===0)return null;for(var t=0;t<e.length;t++){let i=e[t],s=getComputedStyle(i)?.height??"0px";if(s!="0px")return s}return null}function Ug(n,e,t){let{classNames:i=""}=e,{value:s=""}=e,{dark_mode:r}=e,{basic:o=!0}=e,{language:l}=e,{lines:a=5}=e,{extensions:h=[]}=e,{useTab:c=!0}=e,{readonly:f=!1}=e,{placeholder:u=void 0}=e;const d=kc();let p,y,g;async function b(M){const U=await qg(M);t(11,p=U)}function k(M){g&&M!==g.state.doc.toString()&&g.dispatch({changes:{from:0,to:g.state.doc.length,insert:M}})}function v(M){g&&g.requestMeasure({read:C})}function S(){return new O({parent:y,state:I(s)})}function C(M){let U=M.dom.querySelectorAll(".cm-gutter"),J=a+1,se=Kg(M);if(!se)return null;for(var G=0;G<U.length;G++){let ee=U[G];ee.style.minHeight=`calc(${se} * ${J})`}return null}function T(M){if(M.docChanged){const J=M.state.doc.toString();t(2,s=J),d("change",J)}g.requestMeasure({read:C})}function B(){return[...P(o,c,u,f,p),j,...F(),...h]}const j=O.theme({"&":{fontSize:"var(--text-sm)",backgroundColor:"var(--border-color-secondary)"},".cm-content":{paddingTop:"5px",paddingBottom:"5px",color:"var(--body-text-color)",fontFamily:"var(--font-mono)",minHeight:"100%"},".cm-gutters":{marginRight:"1px",borderRight:"1px solid var(--border-color-primary)",backgroundColor:"transparent",color:"var(--body-text-color-subdued)"},".cm-focused":{outline:"none"},".cm-scroller":{height:"auto"},".cm-cursor":{borderLeftColor:"var(--body-text-color)"}});function I(M){return N.create({doc:M??void 0,extensions:B()})}function P(M,U,J,se,G){const ee=[O.editable.of(!se),N.readOnly.of(se)];return M&&ee.push(zg),U&&ee.push(An.of([km])),J&&ee.push(Bu(J)),G&&ee.push(G),ee.push(O.updateListener.of(T)),ee}function F(){const M=[];return r?M.push(Bm):M.push(Fm),M}function K(){g?.dispatch({effects:R.reconfigure.of(B())})}vc(()=>(g=S(),()=>g?.destroy()));function X(M){Sc[M?"unshift":"push"](()=>{y=M,t(1,y)})}return n.$$set=M=>{"classNames"in M&&t(0,i=M.classNames),"value"in M&&t(2,s=M.value),"dark_mode"in M&&t(3,r=M.dark_mode),"basic"in M&&t(4,o=M.basic),"language"in M&&t(5,l=M.language),"lines"in M&&t(6,a=M.lines),"extensions"in M&&t(7,h=M.extensions),"useTab"in M&&t(8,c=M.useTab),"readonly"in M&&t(9,f=M.readonly),"placeholder"in M&&t(10,u=M.placeholder)},n.$$.update=()=>{n.$$.dirty&32&&b(l),n.$$.dirty&2048&&K(),n.$$.dirty&4&&k(s),n.$$.dirty&64&&v()},[i,y,s,r,o,l,a,h,c,f,u,p,X]}class o0 extends Bl{constructor(e){super(),Pl(this,e,Ug,jg,El,{classNames:0,value:2,dark_mode:3,basic:4,language:5,lines:6,extensions:7,useTab:8,readonly:9,placeholder:10})}}export{t0 as A,Ad as B,Jg as C,nd as D,w as E,s0 as F,Wm as G,Z as I,Js as L,pr as N,sh as P,It as S,z as T,o0 as a,n0 as b,Qg as c,e0 as d,ge as e,Fd as f,L as g,He as h,Td as i,ae as j,Ai as k,An as l,De as m,lh as n,bt as o,Xg as p,Vd as q,hh as r,xd as s,m as t,Kt as u,ip as v,O as w,i0 as x,Yg as y,r0 as z};
//# sourceMappingURL=Widgets.svelte_svelte_type_style_lang-66f20987.js.map
