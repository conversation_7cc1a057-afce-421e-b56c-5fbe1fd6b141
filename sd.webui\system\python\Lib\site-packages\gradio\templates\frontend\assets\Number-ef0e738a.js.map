{"version": 3, "file": "Number-ef0e738a.js", "sources": ["../../../../js/number/shared/Number.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { afterUpdate, createEventDispatcher, tick } from \"svelte\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\n\texport let value = 0;\n\texport let minimum: number | undefined = undefined;\n\texport let maximum: number | undefined = undefined;\n\texport let value_is_output = false;\n\texport let disabled = false;\n\texport let label: string;\n\texport let info: string | undefined = undefined;\n\texport let show_label = true;\n\texport let container = true;\n\texport let step: number | null = 1;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: number;\n\t\tsubmit: undefined;\n\t\tblur: undefined;\n\t\tinput: undefined;\n\t\tfocus: undefined;\n\t}>();\n\n\tfunction handle_change(): void {\n\t\tif (!isNaN(value) && value !== null) {\n\t\t\tdispatch(\"change\", value);\n\t\t\tif (!value_is_output) {\n\t\t\t\tdispatch(\"input\");\n\t\t\t}\n\t\t}\n\t}\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\t$: value, handle_change();\n\n\tasync function handle_keypress(e: KeyboardEvent): Promise<void> {\n\t\tawait tick();\n\t\tif (e.key === \"Enter\") {\n\t\t\te.preventDefault();\n\t\t\tdispatch(\"submit\");\n\t\t}\n\t}\n</script>\n\n<label class=\"block\" class:container>\n\t<BlockTitle {show_label} {info}>{label}</BlockTitle>\n\t<input\n\t\ttype=\"number\"\n\t\tbind:value\n\t\tmin={minimum}\n\t\tmax={maximum}\n\t\t{step}\n\t\ton:keypress={handle_keypress}\n\t\ton:blur\n\t\ton:focus\n\t\t{disabled}\n\t/>\n</label>\n\n<style>\n\tlabel:not(.container),\n\tlabel:not(.container) > input {\n\t\theight: 100%;\n\t\tborder: none;\n\t}\n\t.container > input {\n\t\tborder: var(--input-border-width) solid var(--input-border-color);\n\t\tborder-radius: var(--input-radius);\n\t}\n\tinput[type=\"number\"] {\n\t\tdisplay: block;\n\t\tposition: relative;\n\t\toutline: none !important;\n\t\tbox-shadow: var(--input-shadow);\n\t\tbackground: var(--input-background-fill);\n\t\tpadding: var(--input-padding);\n\t\twidth: 100%;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-sm);\n\t}\n\tinput:disabled {\n\t\t-webkit-text-fill-color: var(--body-text-color);\n\t\t-webkit-opacity: 1;\n\t\topacity: 1;\n\t}\n\n\tinput:focus {\n\t\tbox-shadow: var(--input-shadow-focus);\n\t\tborder-color: var(--input-border-color-focus);\n\t}\n\n\tinput::placeholder {\n\t\tcolor: var(--input-placeholder-color);\n\t}\n\n\tinput:out-of-range {\n\t\tborder: var(--input-border-width) solid var(--error-border-color);\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "label_1", "anchor", "append", "input", "value", "$$props", "minimum", "maximum", "value_is_output", "disabled", "label", "info", "show_label", "container", "step", "dispatch", "createEventDispatcher", "handle_change", "afterUpdate", "$$invalidate", "handle_keypress", "e", "tick"], "mappings": "iUA8CkCA,EAAK,CAAA,CAAA,qCAALA,EAAK,CAAA,CAAA,iOAIhCA,EAAO,CAAA,CAAA,YACPA,EAAO,CAAA,CAAA,+HANdC,EAaOC,EAAAC,EAAAC,CAAA,qBAXNC,EAUCF,EAAAG,CAAA,0DAJaN,EAAe,CAAA,CAAA,qLAHvBA,EAAO,CAAA,CAAA,uBACPA,EAAO,CAAA,CAAA,oPA/CF,GAAA,CAAA,MAAAO,EAAQ,CAAC,EAAAC,EACT,CAAA,QAAAC,EAA8B,MAAS,EAAAD,EACvC,CAAA,QAAAE,EAA8B,MAAS,EAAAF,EACvC,CAAA,gBAAAG,EAAkB,EAAK,EAAAH,EACvB,CAAA,SAAAI,EAAW,EAAK,EAAAJ,GAChB,MAAAK,CAAa,EAAAL,EACb,CAAA,KAAAM,EAA2B,MAAS,EAAAN,EACpC,CAAA,WAAAO,EAAa,EAAI,EAAAP,EACjB,CAAA,UAAAQ,EAAY,EAAI,EAAAR,EAChB,CAAA,KAAAS,EAAsB,CAAC,EAAAT,EAE5B,MAAAU,EAAWC,aAQRC,GAAa,CAChB,CAAA,MAAMb,CAAK,GAAKA,IAAU,OAC9BW,EAAS,SAAUX,CAAK,EACnBI,GACJO,EAAS,OAAO,GAInBG,EAAW,IAAA,CACVC,EAAA,GAAAX,EAAkB,EAAK,IAIT,eAAAY,EAAgBC,EAAgB,OACxCC,EAAI,EACND,EAAE,MAAQ,UACbA,EAAE,eAAc,EAChBN,EAAS,QAAQ,ieANTE,EAAa"}