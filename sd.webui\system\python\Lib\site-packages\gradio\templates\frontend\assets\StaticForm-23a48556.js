import{S as c,e as m,s as r,a8 as d,m as h,g,N as u,K as _,h as w,aa as v,ab as b,ac as S,w as q,u as y,k as F}from"./index-2519a27e.js";function k(a){let t,l;const f=a[4].default,s=d(f,a,a[3],null);return{c(){t=h("div"),s&&s.c(),g(t,"class","form svelte-sfqy0y"),u(t,"hidden",!a[0]),_(t,"flex-grow",a[1]),_(t,"min-width",`calc(min(${a[2]}px, 100%))`)},m(e,i){w(e,t,i),s&&s.m(t,null),l=!0},p(e,[i]){s&&s.p&&(!l||i&8)&&v(s,f,e,e[3],l?S(f,e[3],i,null):b(e[3]),null),(!l||i&1)&&u(t,"hidden",!e[0]),i&2&&_(t,"flex-grow",e[1]),i&4&&_(t,"min-width",`calc(min(${e[2]}px, 100%))`)},i(e){l||(q(s,e),l=!0)},o(e){y(s,e),l=!1},d(e){e&&F(t),s&&s.d(e)}}}function C(a,t,l){let{$$slots:f={},$$scope:s}=t,{visible:e=!0}=t,{scale:i=null}=t,{min_width:o=0}=t;return a.$$set=n=>{"visible"in n&&l(0,e=n.visible),"scale"in n&&l(1,i=n.scale),"min_width"in n&&l(2,o=n.min_width),"$$scope"in n&&l(3,s=n.$$scope)},[e,i,o,s,f]}class N extends c{constructor(t){super(),m(this,t,C,k,r,{visible:0,scale:1,min_width:2})}}export{N as S};
//# sourceMappingURL=StaticForm-23a48556.js.map
