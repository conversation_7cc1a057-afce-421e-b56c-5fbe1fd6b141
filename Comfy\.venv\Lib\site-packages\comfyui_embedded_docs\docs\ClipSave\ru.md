Узел Сохранить CLIP предназначен для сохранения моделей текстового кодировщика CLIP в формате SafeTensors. Этот узел является частью расширенных рабочих процессов объединения моделей и обычно используется совместно с узлами CLIPMergeSimple и CLIPMergeAdd. Сохраненные файлы используют формат SafeTensors для обеспечения безопасности и совместимости.

## Входные параметры

| Параметр | Тип данных | Обязательный | Значение по умолчанию | Описание |
|----------|------------|--------------|---------------------|-----------|
| clip | CLIP | Да | - | Модель CLIP для сохранения |
| префикс_названия_файла | STRING | Да | "clip/ComfyUI" | Префикс пути для сохраняемого файла |
| prompt | PROMPT | Скрытый | - | Информация о рабочем процессе (для метаданных) |
| extra_pnginfo | EXTRA_PNGINFO | Скрытый | - | Дополнительная информация PNG (для метаданных) |

## Выходные данные

У этого узла нет определенных типов выходных данных. Он сохраняет обработанные файлы в папку `ComfyUI/output/`.

### Стратегия сохранения нескольких файлов

Узел сохраняет различные компоненты в зависимости от типа модели CLIP:

| Тип префикса | Суффикс файла | Описание |
|--------------|---------------|-----------|
| `clip_l.` | `_clip_l` | Текстовый кодировщик CLIP-L |
| `clip_g.` | `_clip_g` | Текстовый кодировщик CLIP-G |
| Пустой префикс | Без суффикса | Другие компоненты CLIP |

## Примечания по использованию

1. **Расположение файлов**: Все файлы сохраняются в директории `ComfyUI/output/`
2. **Формат файлов**: Модели сохраняются в формате SafeTensors для безопасности
3. **Метаданные**: Включает информацию о рабочем процессе и метаданные PNG, если доступны
4. **Правила именования**: Использует указанный префикс плюс соответствующие суффиксы в зависимости от типа модели
