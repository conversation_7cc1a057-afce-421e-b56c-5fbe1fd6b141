{"last_node_id": 3, "last_link_id": 1, "nodes": [{"id": 1, "type": "VHS_LoadVideo", "pos": {"0": 54, "1": 89}, "size": [260, 460], "flags": {}, "order": 0, "mode": 0, "inputs": [{"name": "meta_batch", "type": "VHS_BatchManager", "link": null}, {"name": "vae", "type": "VAE", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1], "slot_index": 0, "shape": 3}, {"name": "frame_count", "type": "INT", "links": null, "shape": 3}, {"name": "audio", "type": "VHS_AUDIO", "links": null, "shape": 3}, {"name": "video_info", "type": "VHS_VIDEOINFO", "links": null, "shape": 3}], "properties": {"Node name for S&R": "VHS_LoadVideo"}, "widgets_values": {"video": "leader.webm", "force_rate": 8, "force_size": "Disabled", "custom_width": 304, "custom_height": 312, "frame_load_cap": 16, "skip_first_frames": 1, "select_every_nth": 1, "choose video to upload": "image", "videopreview": {"hidden": false, "paused": false, "params": {"frame_load_cap": 16, "skip_first_frames": 1, "force_rate": 8, "filename": "leader.webm", "type": "input", "format": "video/mp4", "force_size": "410.4x?", "select_every_nth": 1}}}}, {"id": 3, "type": "VHS_VideoCombine", "pos": {"0": 629, "1": 222}, "size": [320, 550], "flags": {}, "order": 1, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1}, {"name": "audio", "type": "AUDIO", "link": null}, {"name": "meta_batch", "type": "VHS_BatchManager", "link": null}, {"name": "vae", "type": "VAE", "link": null}], "outputs": [{"name": "Filenames", "type": "VHS_FILENAMES", "links": null, "shape": 3}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 8, "loop_count": 1, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00005.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 8}}}}], "links": [[1, 1, 0, 3, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {}, "version": 0.4, "tests": {"3": [{"type": "video", "key": "nb_read_packets", "value": "32"}], "length": 1}}