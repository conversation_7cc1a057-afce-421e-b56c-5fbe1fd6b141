{"version": 3, "file": "FilePreview-a6a7747b.js", "sources": ["../../../../js/file/shared/utils.ts", "../../../../js/file/shared/FilePreview.svelte"], "sourcesContent": ["import type { FileData } from \"@gradio/upload\";\n\nexport const prettyBytes = (bytes: number): string => {\n\tlet units = [\"B\", \"KB\", \"MB\", \"GB\", \"PB\"];\n\tlet i = 0;\n\twhile (bytes > 1024) {\n\t\tbytes /= 1024;\n\t\ti++;\n\t}\n\tlet unit = units[i];\n\treturn bytes.toFixed(1) + \"&nbsp;\" + unit;\n};\n\nexport const display_file_name = (value: FileData): string => {\n\tvar str: string;\n\tstr = value.orig_name || value.name;\n\tconst max_length = 30;\n\n\tif (str.length > max_length) {\n\t\tconst truncated_name = str.substring(0, max_length);\n\t\tconst file_extension_index = str.lastIndexOf(\".\");\n\t\tif (file_extension_index !== -1) {\n\t\t\tconst file_extension = str.slice(file_extension_index);\n\t\t\treturn `${truncated_name}..${file_extension}`;\n\t\t}\n\t\treturn truncated_name;\n\t}\n\treturn str;\n};\n\nexport const display_file_size = (value: FileData | FileData[]): string => {\n\tvar total_size = 0;\n\tif (Array.isArray(value)) {\n\t\tfor (var file of value) {\n\t\t\tif (file.size !== undefined) total_size += file.size;\n\t\t}\n\t} else {\n\t\ttotal_size = value.size || 0;\n\t}\n\treturn prettyBytes(total_size);\n};\n", "<script lang=\"ts\">\n\timport type { FileData } from \"@gradio/upload\";\n\timport { display_file_name, display_file_size } from \"./utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { _ } from \"svelte-i18n\";\n\n\tconst dispatch = createEventDispatcher<{\n\t\tselect: SelectData;\n\t}>();\n\texport let value: FileData | FileData[];\n\texport let selectable = false;\n\texport let height: number | undefined = undefined;\n</script>\n\n<div\n\tclass=\"file-preview-holder\"\n\tstyle=\"max-height: {typeof height === undefined ? 'auto' : height + 'px'};\"\n>\n\t<table class=\"file-preview\">\n\t\t<tbody>\n\t\t\t{#each Array.isArray(value) ? value : [value] as file, i}\n\t\t\t\t<tr\n\t\t\t\t\tclass=\"file\"\n\t\t\t\t\tclass:selectable\n\t\t\t\t\ton:click={() =>\n\t\t\t\t\t\tdispatch(\"select\", {\n\t\t\t\t\t\t\tvalue: file.orig_name || file.name,\n\t\t\t\t\t\t\tindex: i\n\t\t\t\t\t\t})}\n\t\t\t\t>\n\t\t\t\t\t<td>\n\t\t\t\t\t\t{display_file_name(file)}\n\t\t\t\t\t</td>\n\n\t\t\t\t\t<td class=\"download\">\n\t\t\t\t\t\t{#if file.data}\n\t\t\t\t\t\t\t<a\n\t\t\t\t\t\t\t\thref={file.data}\n\t\t\t\t\t\t\t\ttarget=\"_blank\"\n\t\t\t\t\t\t\t\tdownload={window.__is_colab__\n\t\t\t\t\t\t\t\t\t? null\n\t\t\t\t\t\t\t\t\t: file.orig_name || file.name}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{@html display_file_size(file)}&nbsp;&#8675;\n\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t{$_(\"file.uploading\")}\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</td>\n\t\t\t\t</tr>\n\t\t\t{/each}\n\t\t</tbody>\n\t</table>\n</div>\n\n<style>\n\ttd {\n\t\twidth: 45%;\n\t}\n\n\ttd:last-child {\n\t\twidth: 10%;\n\t\ttext-align: right;\n\t}\n\t.file-preview-holder {\n\t\toverflow-x: auto;\n\t\toverflow-y: auto;\n\t}\n\t.file-preview {\n\t\twidth: var(--size-full);\n\t\tmax-height: var(--size-60);\n\t\toverflow-y: auto;\n\t\tmargin-top: var(--size-1);\n\t\tcolor: var(--body-text-color);\n\t}\n\t.file {\n\t\twidth: var(--size-full);\n\t}\n\n\t.file > * {\n\t\tpadding: var(--size-1) var(--size-2-5);\n\t}\n\n\t.download:hover {\n\t\ttext-decoration: underline;\n\t}\n\t.download > a {\n\t\tcolor: var(--link-text-color);\n\t}\n\n\t.download > a:hover {\n\t\tcolor: var(--link-text-color-hover);\n\t}\n\t.download > a:visited {\n\t\tcolor: var(--link-text-color-visited);\n\t}\n\t.download > a:active {\n\t\tcolor: var(--link-text-color-active);\n\t}\n\t.selectable {\n\t\tcursor: pointer;\n\t}\n\n\ttbody > tr:nth-child(even) {\n\t\tbackground: var(--block-background-fill);\n\t}\n\n\ttbody > tr:nth-child(odd) {\n\t\tbackground: var(--table-odd-background-fill);\n\t}\n</style>\n"], "names": ["prettyBytes", "bytes", "units", "i", "unit", "display_file_name", "value", "str", "max_length", "truncated_name", "file_extension_index", "file_extension", "display_file_size", "total_size", "file", "t_value", "ctx", "dirty", "set_data", "raw_value", "attr", "a", "a_href_value", "a_download_value", "insert", "target", "anchor", "html_tag", "t0_value", "create_if_block", "tr", "append", "td0", "td1", "t0", "set_style", "div", "table", "tbody", "dispatch", "createEventDispatcher", "$$props", "selectable", "height", "click_handler"], "mappings": "kLAEa,MAAAA,EAAeC,GAA0B,CACrD,IAAIC,EAAQ,CAAC,IAAK,KAAM,KAAM,KAAM,IAAI,EACpCC,EAAI,EACR,KAAOF,EAAQ,MACLA,GAAA,KACTE,IAEG,IAAAC,EAAOF,EAAMC,CAAC,EAClB,OAAOF,EAAM,QAAQ,CAAC,EAAI,SAAWG,CACtC,EAEaC,EAAqBC,GAA4B,CACzD,IAAAC,EACEA,EAAAD,EAAM,WAAaA,EAAM,KAC/B,MAAME,EAAa,GAEf,GAAAD,EAAI,OAASC,EAAY,CAC5B,MAAMC,EAAiBF,EAAI,UAAU,EAAGC,CAAU,EAC5CE,EAAuBH,EAAI,YAAY,GAAG,EAChD,GAAIG,IAAyB,GAAI,CAC1B,MAAAC,EAAiBJ,EAAI,MAAMG,CAAoB,EACrD,MAAO,GAAGD,MAAmBE,IAEvB,OAAAF,EAED,OAAAF,CACR,EAEaK,EAAqBN,GAAyC,CAC1E,IAAIO,EAAa,EACb,GAAA,MAAM,QAAQP,CAAK,EACtB,QAASQ,KAAQR,EACZQ,EAAK,OAAS,SAAWD,GAAcC,EAAK,WAGjDD,EAAaP,EAAM,MAAQ,EAE5B,OAAON,EAAYa,CAAU,CAC9B,8ECOQ,IAAAE,EAAAC,KAAG,gBAAgB,EAAA,gDAAnBC,EAAA,GAAAF,KAAAA,EAAAC,KAAG,gBAAgB,EAAA,KAAAE,EAAA,EAAAH,CAAA,wCAHZI,EAAAP,EAAkBI,EAAI,CAAA,CAAA,EAAA,6CAAE,IAChC,QAPOI,EAAAC,EAAA,OAAAC,EAAAN,KAAK,IAAI,yBAELI,EAAAC,EAAA,WAAAE,EAAA,OAAO,aACd,KACAP,EAAK,CAAA,EAAA,WAAaA,KAAK,IAAI,wCAL/BQ,EAQGC,EAAAJ,EAAAK,CAAA,0BADKT,EAAA,GAAAE,KAAAA,EAAAP,EAAkBI,EAAI,CAAA,CAAA,EAAA,KAAAW,EAAA,EAAAR,CAAA,EANvBF,EAAA,GAAAK,KAAAA,EAAAN,KAAK,qBAEDC,EAAA,GAAAM,KAAAA,EAAA,OAAO,aACd,KACAP,EAAK,CAAA,EAAA,WAAaA,KAAK,+DAV3BY,EAAAvB,EAAkBW,EAAI,CAAA,CAAA,EAAA,+BAIlB,OAAAA,KAAK,KAAIa,mQAdhBL,EA4BIC,EAAAK,EAAAJ,CAAA,EAnBHK,EAEID,EAAAE,CAAA,gBAEJD,EAcID,EAAAG,CAAA,4DAjBFhB,EAAA,GAAAW,KAAAA,EAAAvB,EAAkBW,EAAI,CAAA,CAAA,EAAA,KAAAE,EAAAgB,EAAAN,CAAA,yJAXnB,MAAM,QAAQZ,EAAK,CAAA,CAAA,EAAIA,EAAK,CAAA,GAAIA,EAAK,CAAA,CAAA,CAAA,uBAA1C,OAAIb,GAAA,mOAJmBgC,EAAAC,EAAA,aAAA,OAAApB,OAAW,OAAY,OAASA,KAAS,IAAI,UAFzEQ,EAuCKC,EAAAW,EAAAV,CAAA,EAnCJK,EAkCOK,EAAAC,CAAA,EAjCNN,EAgCOM,EAAAC,CAAA,0EA/BC,MAAM,QAAQtB,EAAK,CAAA,CAAA,EAAIA,EAAK,CAAA,GAAIA,EAAK,CAAA,CAAA,CAAA,oBAA1C,OAAIb,GAAA,EAAA,iHAAJ,YAJuBgC,EAAAC,EAAA,aAAA,OAAApB,OAAW,OAAY,OAASA,KAAS,IAAI,4EAVlE,MAAAuB,EAAWC,QAGN,MAAAlC,CAA4B,EAAAmC,EAC5B,CAAA,WAAAC,EAAa,EAAK,EAAAD,EAClB,CAAA,OAAAE,EAA6B,MAAS,EAAAF,EAc5C,MAAAG,EAAA,CAAA9B,EAAAX,IAAAoC,EAAS,SAAQ,CAChB,MAAOzB,EAAK,WAAaA,EAAK,KAC9B,MAAOX"}