{"version": 3, "file": "index-a3e055a1.js", "sources": ["../../../../js/dataframe/static/StaticDataframe.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { afterUpdate } from \"svelte\";\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { Block } from \"@gradio/atoms\";\n\timport Table from \"../shared\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\ttype Headers = string[];\n\ttype Data = (string | number)[][];\n\ttype Datatype = \"str\" | \"markdown\" | \"html\" | \"number\" | \"bool\" | \"date\";\n\n\texport let headers: Headers = [];\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: { data: Data; headers: Headers } = {\n\t\tdata: [[\"\", \"\", \"\"]],\n\t\theaders: [\"1\", \"2\", \"3\"]\n\t};\n\tlet old_value: string = JSON.stringify(value);\n\texport let value_is_output = false;\n\texport let col_count: [number, \"fixed\" | \"dynamic\"];\n\texport let row_count: [number, \"fixed\" | \"dynamic\"];\n\texport let label: string | null = null;\n\texport let wrap: boolean;\n\texport let datatype: Datatype | Datatype[];\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tselect: SelectData;\n\t\tinput: never;\n\t}>;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let height: number | undefined = undefined;\n\n\texport let loading_status: LoadingStatus;\n\n\tfunction handle_change(): void {\n\t\tgradio.dispatch(\"change\");\n\t\tif (!value_is_output) {\n\t\t\tgradio.dispatch(\"input\");\n\t\t}\n\t}\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\t$: {\n\t\tif (JSON.stringify(value) !== old_value) {\n\t\t\told_value = JSON.stringify(value);\n\t\t\thandle_change();\n\t\t}\n\t}\n</script>\n\n<Block\n\t{visible}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\tcontainer={false}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n>\n\t<StatusTracker {...loading_status} />\n\t<Table\n\t\t{label}\n\t\t{row_count}\n\t\t{col_count}\n\t\tvalues={value}\n\t\t{headers}\n\t\ton:change={({ detail }) => {\n\t\t\tvalue = detail;\n\t\t}}\n\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\t{wrap}\n\t\t{datatype}\n\t\t{latex_delimiters}\n\t\teditable={false}\n\t\t{height}\n\t/>\n</Block>\n"], "names": ["ctx", "headers", "$$props", "elem_id", "elem_classes", "visible", "value", "old_value", "value_is_output", "col_count", "row_count", "label", "wrap", "datatype", "scale", "min_width", "gradio", "latex_delimiters", "height", "loading_status", "handle_change", "afterUpdate", "$$invalidate", "detail"], "mappings": "+XAsEoBA,EAAc,EAAA,CAAA,6IAKxBA,EAAK,CAAA,uEASH,mLAdQA,EAAc,EAAA,CAAA,CAAA,CAAA,iHAKxBA,EAAK,CAAA,qVAbL,4CAGE,8CAGK,gYAxDL,QAAAC,EAAO,EAAA,EAAAC,EACP,CAAA,QAAAC,EAAU,EAAE,EAAAD,GACZ,aAAAE,EAAY,EAAA,EAAAF,EACZ,CAAA,QAAAG,EAAU,EAAI,EAAAH,GACd,MAAAI,EAAK,CACf,KAAQ,CAAA,CAAA,GAAI,GAAI,EAAE,CAAA,EAClB,QAAU,CAAA,IAAK,IAAK,GAAG,MAEpBC,EAAoB,KAAK,UAAUD,CAAK,EACjC,CAAA,gBAAAE,EAAkB,EAAK,EAAAN,GACvB,UAAAO,CAAwC,EAAAP,GACxC,UAAAQ,CAAwC,EAAAR,EACxC,CAAA,MAAAS,EAAuB,IAAI,EAAAT,GAC3B,KAAAU,CAAa,EAAAV,GACb,SAAAW,CAA+B,EAAAX,EAC/B,CAAA,MAAAY,EAAuB,IAAI,EAAAZ,EAC3B,CAAA,UAAAa,EAAgC,MAAS,EAAAb,GACzC,OAAAc,CAIT,EAAAd,GACS,iBAAAe,CAIR,EAAAf,EACQ,CAAA,OAAAgB,EAA6B,MAAS,EAAAhB,GAEtC,eAAAiB,CAA6B,EAAAjB,WAE/BkB,GAAa,CACrBJ,EAAO,SAAS,QAAQ,EACnBR,GACJQ,EAAO,SAAS,OAAO,EAGzBK,EAAW,IAAA,CACVC,EAAA,GAAAd,EAAkB,EAAK,cA2BT,OAAAe,KAAM,CACnBD,EAAA,EAAAhB,EAAQiB,CAAM,KAEH,GAAMP,EAAO,SAAS,SAAU,EAAE,MAAM,mpBA3BhD,KAAK,UAAUV,CAAK,IAAMC,IAC7Be,EAAA,GAAAf,EAAY,KAAK,UAAUD,CAAK,CAAA,EAChCc"}