{"version": 3, "file": "index-f34451e3.js", "sources": ["../../../../js/model3D/static/Model3D.svelte", "../../../../js/model3D/static/StaticModel3d.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { FileData } from \"@gradio/upload\";\n\timport { BlockLabel, IconButton } from \"@gradio/atoms\";\n\timport { File, Download } from \"@gradio/icons\";\n\timport { _ } from \"svelte-i18n\";\n\timport { onMount } from \"svelte\";\n\timport * as BABYLON from \"babylonjs\";\n\timport * as BABYLON_LOADERS from \"babylonjs-loaders\";\n\n\texport let value: FileData | null;\n\texport let clearColor: [number, number, number, number] = [0, 0, 0, 0];\n\texport let label = \"\";\n\texport let show_label: boolean;\n\n\tBABYLON_LOADERS.OBJFileLoader.IMPORT_VERTEX_COLORS = true;\n\n\tlet canvas: HTMLCanvasElement;\n\tlet scene: BABYLON.Scene;\n\tlet engine: BABYLON.Engine | null;\n\n\tlet mounted = false;\n\n\tonMount(() => {\n\t\tengine = new BABYLON.Engine(canvas, true);\n\t\twindow.addEventListener(\"resize\", () => {\n\t\t\tengine?.resize();\n\t\t});\n\t\tmounted = true;\n\t});\n\n\t$: ({ data, name } = value || {\n\t\tdata: undefined,\n\t\tname: undefined\n\t});\n\n\t$: canvas && mounted && data != null && name && dispose();\n\n\tfunction dispose(): void {\n\t\tif (scene && !scene.isDisposed) {\n\t\t\tscene.dispose();\n\t\t\tengine?.stopRenderLoop();\n\t\t\tengine?.dispose();\n\t\t\tengine = null;\n\t\t\tengine = new BABYLON.Engine(canvas, true);\n\t\t\twindow.addEventListener(\"resize\", () => {\n\t\t\t\tengine?.resize();\n\t\t\t});\n\t\t}\n\t\taddNewModel();\n\t}\n\n\tfunction addNewModel(): void {\n\t\tscene = new BABYLON.Scene(engine!);\n\t\tscene.createDefaultCameraOrLight();\n\n\t\tscene.clearColor = new BABYLON.Color4(...clearColor);\n\n\t\tengine?.runRenderLoop(() => {\n\t\t\tscene.render();\n\t\t});\n\n\t\tif (!value) return;\n\n\t\tlet url: string;\n\t\tif (value.is_file) {\n\t\t\turl = value.data;\n\t\t} else {\n\t\t\tlet base64_model_content = value.data;\n\t\t\tlet raw_content = BABYLON.Tools.DecodeBase64(base64_model_content);\n\t\t\tlet blob = new Blob([raw_content]);\n\t\t\turl = URL.createObjectURL(blob);\n\t\t}\n\n\t\tBABYLON.SceneLoader.ShowLoadingScreen = false;\n\n\t\tBABYLON.SceneLoader.Append(\n\t\t\t\"\",\n\t\t\turl,\n\t\t\tscene,\n\t\t\t() => {\n\t\t\t\tscene.createDefaultCamera(true, true, true);\n\t\t\t},\n\t\t\tundefined,\n\t\t\tundefined,\n\t\t\t\".\" + value[\"name\"].split(\".\")[1]\n\t\t);\n\t}\n</script>\n\n<BlockLabel {show_label} Icon={File} label={label || $_(\"3D_model.3d_model\")} />\n{#if value}\n\t<div class=\"model3D\">\n\t\t<div class=\"download\">\n\t\t\t<a\n\t\t\t\thref={value.data}\n\t\t\t\ttarget={window.__is_colab__ ? \"_blank\" : null}\n\t\t\t\tdownload={window.__is_colab__ ? null : value.orig_name || value.name}\n\t\t\t>\n\t\t\t\t<IconButton Icon={Download} label={$_(\"common.download\")} />\n\t\t\t</a>\n\t\t</div>\n\n\t\t<canvas bind:this={canvas} />\n\t</div>\n{/if}\n\n<style>\n\t.model3D {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\tcanvas {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: contain;\n\t\toverflow: hidden;\n\t}\n\t.download {\n\t\tposition: absolute;\n\t\ttop: 6px;\n\t\tright: 6px;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { FileData } from \"@gradio/upload\";\n\timport { normalise_file } from \"@gradio/upload\";\n\timport Model3D from \"./Model3D.svelte\";\n\timport { BlockLabel, Block, Empty } from \"@gradio/atoms\";\n\timport { File } from \"@gradio/icons\";\n\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: null | FileData = null;\n\texport let root: string;\n\texport let root_url: null | string;\n\texport let clearColor: [number, number, number, number];\n\texport let loading_status: LoadingStatus;\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\n\tlet _value: null | FileData;\n\t$: _value = normalise_file(value, root, root_url);\n\n\tlet dragging = false;\n</script>\n\n<Block\n\t{visible}\n\tvariant={value === null ? \"dashed\" : \"solid\"}\n\tborder_mode={dragging ? \"focus\" : \"base\"}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n>\n\t<StatusTracker {...loading_status} />\n\n\t{#if value}\n\t\t<Model3D value={_value} {clearColor} {label} {show_label} />\n\t{:else}\n\t\t<!-- Not ideal but some bugs to work out before we can \n\t\t\t\t make this consistent with other components -->\n\n\t\t<BlockLabel {show_label} Icon={File} label={label || \"3D Model\"} />\n\n\t\t<Empty unpadded_box={true} size=\"large\"><File /></Empty>\n\t{/if}\n</Block>\n"], "names": ["Download", "ctx", "attr", "a", "a_href_value", "a_download_value", "insert", "target", "div1", "anchor", "append", "div0", "canvas_1", "dirty", "iconbutton_changes", "current", "File", "create_if_block", "value", "$$props", "clearColor", "label", "show_label", "BABYLON_LOADERS.OBJFileLoader", "canvas", "scene", "engine", "mounted", "onMount", "BABYLON.Engine", "$$invalidate", "dispose", "addNewModel", "BABYLON.Scene", "BABYLON.Color4", "url", "base64_model_content", "raw_content", "BABYLON.Tools", "blob", "BABYLON.SceneLoader", "$$value", "data", "name", "blocklabel_changes", "block_changes", "elem_id", "elem_classes", "visible", "root", "root_url", "loading_status", "container", "scale", "min_width", "_value", "normalise_file"], "mappings": "wlBAkGsBA,GAAiB,MAAAC,KAAG,iBAAiB,8EAJjDC,EAAAC,EAAA,OAAAC,EAAAH,KAAM,IAAI,EACRC,EAAAC,EAAA,SAAA,OAAO,aAAe,SAAW,IAAI,EACnCD,EAAAC,EAAA,WAAAE,EAAA,OAAO,aAAe,KAAOJ,EAAM,CAAA,EAAA,WAAaA,KAAM,IAAI,qHALvEK,EAYKC,EAAAC,EAAAC,CAAA,EAXJC,EAQKF,EAAAG,CAAA,EAPJD,EAMGC,EAAAR,CAAA,qBAGJO,EAA4BF,EAAAI,CAAA,kCAJSC,EAAA,KAAAC,EAAA,MAAAb,KAAG,iBAAiB,cAJjD,CAAAc,GAAAF,EAAA,GAAAT,KAAAA,EAAAH,KAAM,uBAEF,CAAAc,GAAAF,EAAA,GAAAR,KAAAA,EAAA,OAAO,aAAe,KAAOJ,EAAM,CAAA,EAAA,WAAaA,KAAM,2LAPrCe,QAAaf,EAAK,CAAA,GAAIA,EAAE,CAAA,EAAC,mBAAmB,WACtEA,EAAK,CAAA,GAAAgB,EAAAhB,CAAA,oKADkCA,EAAK,CAAA,GAAIA,EAAE,CAAA,EAAC,mBAAmB,aACtEA,EAAK,CAAA,+QAjFE,MAAAiB,CAAsB,EAAAC,EACtB,CAAA,WAAAC,GAAgD,EAAG,EAAG,EAAG,CAAC,CAAA,EAAAD,EAC1D,CAAA,MAAAE,EAAQ,EAAE,EAAAF,GACV,WAAAG,CAAmB,EAAAH,EAE9BI,GAA6B,cAAC,qBAAuB,OAEjDC,EACAC,EACAC,EAEAC,EAAU,GAEdC,EAAO,IAAA,CACNF,MAAaG,EAAAA,OAAeL,EAAQ,EAAI,EACxC,OAAO,iBAAiB,SAAQ,IAAA,CAC/BE,GAAQ,OAAM,IAEfI,EAAA,EAAAH,EAAU,EAAI,aAUNI,GAAO,CACXN,GAAK,CAAKA,EAAM,aACnBA,EAAM,QAAO,EACbC,GAAQ,eAAc,EACtBA,GAAQ,QAAO,EACfA,EAAS,KACTA,MAAaG,EAAAA,OAAeL,EAAQ,EAAI,EACxC,OAAO,iBAAiB,SAAQ,IAAA,CAC/BE,GAAQ,OAAM,KAGhBM,aAGQA,GAAW,IACnBP,EAAY,IAAAQ,QAAcP,CAAO,EACjCD,EAAM,2BAA0B,EAEhCA,EAAM,WAAU,IAAOS,YAAkBd,CAAU,EAEnDM,GAAQ,cAAa,IAAA,CACpBD,EAAM,OAAM,KAGRP,EAAK,WAENiB,EACA,GAAAjB,EAAM,QACTiB,EAAMjB,EAAM,cAERkB,EAAuBlB,EAAM,KAC7BmB,EAAcC,EAAAA,MAAc,aAAaF,CAAoB,EAC7DG,EAAI,IAAO,KAAI,CAAEF,CAAW,CAAA,EAChCF,EAAM,IAAI,gBAAgBI,CAAI,EAG/BC,EAAmB,YAAC,kBAAoB,GAExCA,EAAAA,YAAoB,OACnB,GACAL,EACAV,OAECA,EAAM,oBAAoB,GAAM,GAAM,EAAI,GAE3C,OACA,OACA,IAAMP,EAAM,KAAQ,MAAM,GAAG,EAAE,CAAC,4CAkBdM,EAAMiB,kMAxEpBX,EAAA,EAAA,CAAA,KAAAY,EAAM,KAAAC,CAAS,EAAAzB,GACpB,CAAA,KAAM,OACN,KAAM,MAAA,EAAAwB,GAAAZ,EAAA,EAAAa,CAAA,EAAAb,EAAA,EAAAZ,CAAA,oBAGJM,GAAUG,GAAWe,GAAQ,MAAQC,GAAQZ,EAAO,yMCevBf,EAAa,MAAAf,MAAS,2CAEhC,6LAFuBY,EAAA,KAAA+B,EAAA,MAAA3C,MAAS,0QALrCA,EAAM,EAAA,gIAANA,EAAM,EAAA,qXAHJA,EAAc,CAAA,CAAA,kHAE5BA,EAAK,CAAA,EAAA,yJAFSA,EAAc,CAAA,CAAA,CAAA,CAAA,wUATxB,QAAAA,EAAU,CAAA,IAAA,KAAO,SAAW,oBACH,eACzB,6MAFAY,EAAA,IAAAgC,EAAA,QAAA5C,EAAU,CAAA,IAAA,KAAO,SAAW,wRAtB1B,GAAA,CAAA,QAAA6C,EAAU,EAAE,EAAA3B,GACZ,aAAA4B,EAAY,EAAA,EAAA5B,EACZ,CAAA,QAAA6B,EAAU,EAAI,EAAA7B,EACd,CAAA,MAAAD,EAAyB,IAAI,EAAAC,GAC7B,KAAA8B,CAAY,EAAA9B,GACZ,SAAA+B,CAAuB,EAAA/B,GACvB,WAAAC,CAA4C,EAAAD,GAC5C,eAAAgC,CAA6B,EAAAhC,GAC7B,MAAAE,CAAa,EAAAF,GACb,WAAAG,CAAmB,EAAAH,EACnB,CAAA,UAAAiC,EAAY,EAAI,EAAAjC,EAChB,CAAA,MAAAkC,EAAuB,IAAI,EAAAlC,EAC3B,CAAA,UAAAmC,EAAgC,MAAS,EAAAnC,EAEhDoC,wfACHzB,EAAA,GAAEyB,EAASC,EAAetC,EAAO+B,EAAMC,CAAQ,CAAA"}