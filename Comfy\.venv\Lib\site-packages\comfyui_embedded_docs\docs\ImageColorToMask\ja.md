`ImageColorToMask`ノードは、画像内の指定された色をマスクに変換するために設計されています。画像とターゲットカラーを処理し、指定された色が強調されたマスクを生成します。これにより、色に基づくセグメンテーションやオブジェクトの分離などの操作が容易になります。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `image`   | `IMAGE`     | 'image'パラメータは、処理される入力画像を表します。指定された色に一致する画像の領域をマスクに変換するために重要です。 |
| `color`   | `INT`       | 'color'パラメータは、画像内でマスクに変換されるターゲットカラーを指定します。結果として得られるマスクで強調される特定の色の領域を特定する上で重要な役割を果たします。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `mask`    | `MASK`      | 出力は、指定された色に一致する入力画像の領域を強調するマスクです。このマスクは、セグメンテーションやオブジェクトの分離など、さらなる画像処理タスクに使用できます。 |
