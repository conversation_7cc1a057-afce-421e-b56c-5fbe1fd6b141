#!/usr/bin/env python3
"""
女性の画像生成スクリプト
RTX 5090のGPU性能をテストします
"""

import torch
from diffusers import StableDiffusionPipeline
import os
from datetime import datetime

def main():
    print('=== GPU Stable Diffusion 女性画像生成テスト ===')
    print(f'PyTorch version: {torch.__version__}')
    print(f'CUDA available: {torch.cuda.is_available()}')
    
    if torch.cuda.is_available():
        print(f'GPU: {torch.cuda.get_device_name(0)}')
    else:
        print('GPU: None')
    print()

    # プロンプト設定
    prompt = "a beautiful young woman with long flowing hair, portrait, soft lighting, detailed face, photorealistic, high quality"
    print(f'プロンプト: {prompt}')
    print()

    # 出力ディレクトリの作成
    os.makedirs('outputs', exist_ok=True)

    # デバイスの設定
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f'使用デバイス: {device}')

    try:
        print('モデルを読み込み中...')
        print('注意: 初回実行時はモデルのダウンロードに時間がかかります')
        
        pipe = StableDiffusionPipeline.from_pretrained(
            "runwayml/stable-diffusion-v1-5",
            torch_dtype=torch.float16 if device == "cuda" else torch.float32,
            safety_checker=None,
            requires_safety_checker=False
        )
        pipe = pipe.to(device)
        
        print('画像を生成中...')
        
        # 画像生成
        if device == "cuda":
            with torch.autocast(device):
                image = pipe(
                    prompt,
                    num_inference_steps=30,
                    guidance_scale=7.5,
                    height=512,
                    width=512
                ).images[0]
        else:
            image = pipe(
                prompt,
                num_inference_steps=20,
                guidance_scale=7.5,
                height=512,
                width=512
            ).images[0]
        
        # ファイル名の生成
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"woman_portrait_{timestamp}.png"
        filepath = os.path.join("outputs", filename)
        
        # 画像の保存
        image.save(filepath)
        print(f'✅ 画像を保存しました: {filepath}')
        print(f'📁 ファイルサイズ: {os.path.getsize(filepath) / 1024:.1f} KB')
        
        return filepath
        
    except Exception as e:
        print(f'❌ エラーが発生しました: {e}')
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
