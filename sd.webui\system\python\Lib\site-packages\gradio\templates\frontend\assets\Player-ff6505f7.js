import{S as U,e as D,s as G,f as y,g as n,h as L,j as o,n as g,k as K,m as C,o as O,t as te,F as Y,M as Q,y as ce,N as se,G as Z,p as c,ae as fe,ag as ie,q as de,b as _e,r as pe,u as A,v as me,w as V,x as oe,H as x,B as he,C as ge,a5 as ve,aC as we,E as ue,R as ke}from"./index-2519a27e.js";import{U as ye}from"./Undo-6ef95523.js";/* empty css                                             */function be(a){let e,t;return{c(){e=y("svg"),t=y("path"),n(t,"d","M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"),n(e,"xmlns","http://www.w3.org/2000/svg"),n(e,"width","100%"),n(e,"height","100%"),n(e,"viewBox","0 0 24 24"),n(e,"fill","none"),n(e,"stroke","currentColor"),n(e,"stroke-width","1.5"),n(e,"stroke-linecap","round"),n(e,"stroke-linejoin","round")},m(l,i){L(l,e,i),o(e,t)},p:g,i:g,o:g,d(l){l&&K(e)}}}class je extends U{constructor(e){super(),D(this,e,null,be,G,{})}}function Be(a){let e,t,l;return{c(){e=y("svg"),t=y("rect"),l=y("rect"),n(t,"x","6"),n(t,"y","4"),n(t,"width","4"),n(t,"height","16"),n(l,"x","14"),n(l,"y","4"),n(l,"width","4"),n(l,"height","16"),n(e,"xmlns","http://www.w3.org/2000/svg"),n(e,"width","100%"),n(e,"height","100%"),n(e,"viewBox","0 0 24 24"),n(e,"fill","none"),n(e,"stroke","currentColor"),n(e,"stroke-width","1.5"),n(e,"stroke-linecap","round"),n(e,"stroke-linejoin","round")},m(i,v){L(i,e,v),o(e,t),o(e,l)},p:g,i:g,o:g,d(i){i&&K(e)}}}class Ce extends U{constructor(e){super(),D(this,e,null,Be,G,{})}}function Me(a){let e,t;return{c(){e=y("svg"),t=y("polygon"),n(t,"points","5 3 19 12 5 21 5 3"),n(e,"xmlns","http://www.w3.org/2000/svg"),n(e,"width","100%"),n(e,"height","100%"),n(e,"viewBox","0 0 24 24"),n(e,"fill","none"),n(e,"stroke","currentColor"),n(e,"stroke-width","1.5"),n(e,"stroke-linecap","round"),n(e,"stroke-linejoin","round")},m(l,i){L(l,e,i),o(e,t)},p:g,i:g,o:g,d(l){l&&K(e)}}}class Ne extends U{constructor(e){super(),D(this,e,null,Me,G,{})}}function Ee(a){let e,t,l;return{c(){e=y("svg"),t=y("polygon"),l=y("rect"),n(t,"points","23 7 16 12 23 17 23 7"),n(l,"x","1"),n(l,"y","5"),n(l,"width","15"),n(l,"height","14"),n(l,"rx","2"),n(l,"ry","2"),n(e,"xmlns","http://www.w3.org/2000/svg"),n(e,"width","100%"),n(e,"height","100%"),n(e,"viewBox","0 0 24 24"),n(e,"fill","none"),n(e,"stroke","currentColor"),n(e,"stroke-width","1.5"),n(e,"stroke-linecap","round"),n(e,"stroke-linejoin","round"),n(e,"class","feather feather-video")},m(i,v){L(i,e,v),o(e,t),o(e,l)},p:g,i:g,o:g,d(i){i&&K(e)}}}class Ue extends U{constructor(e){super(),D(this,e,null,Ee,G,{})}}const De=a=>{let e=["B","KB","MB","GB","PB"],t=0;for(;a>1024;)a/=1024,t++;let l=e[t];return a.toFixed(1)+" "+l},Ge=()=>!0;function Pe(a,{autoplay:e}){async function t(){e&&await a.play()}return a.addEventListener("loadeddata",t),{destroy(){a.removeEventListener("loadeddata",t)}}}const{isNaN:Te}=ve;function Fe(a){let e,t;return e=new Ce({}),{c(){Y(e.$$.fragment)},m(l,i){Z(e,l,i),t=!0},i(l){t||(V(e.$$.fragment,l),t=!0)},o(l){A(e.$$.fragment,l),t=!1},d(l){x(e,l)}}}function Re(a){let e,t;return e=new Ne({}),{c(){Y(e.$$.fragment)},m(l,i){Z(e,l,i),t=!0},i(l){t||(V(e.$$.fragment,l),t=!0)},o(l){A(e.$$.fragment,l),t=!1},d(l){x(e,l)}}}function qe(a){let e,t;return e=new ye({}),{c(){Y(e.$$.fragment)},m(l,i){Z(e,l,i),t=!0},i(l){t||(V(e.$$.fragment,l),t=!0)},o(l){A(e.$$.fragment,l),t=!1},d(l){x(e,l)}}}function Ae(a){let e,t,l,i,v,N,P=!1,X,b=!0,w,T,u,p,m,f,h,z,M,F=W(a[5])+"",H,I,R=W(a[6])+"",S,r,d,j,J,B,q,k,$,ne;function le(){cancelAnimationFrame(X),t.paused||(X=we(le),P=!0),a[16].call(t)}const ae=[qe,Re,Fe],E=[];function re(s,_){return s[5]===s[6]?0:s[7]?1:2}return f=re(a),h=E[f]=ae[f](a),q=new je({}),{c(){e=C("div"),t=C("video"),l=C("track"),T=O(),u=C("div"),p=C("div"),m=C("span"),h.c(),z=O(),M=C("span"),H=te(F),I=te(" / "),S=te(R),r=O(),d=C("progress"),J=O(),B=C("div"),Y(q.$$.fragment),n(l,"kind","captions"),Q(l.src,i=a[1])||n(l,"src",i),l.default=!0,Q(t.src,v=a[0])||n(t,"src",v),n(t,"preload","auto"),n(t,"data-testid",N=`${a[4]}-player`),n(t,"class","svelte-w5wajl"),a[6]===void 0&&ce(()=>a[17].call(t)),se(t,"mirror",a[2]),n(m,"role","button"),n(m,"tabindex","0"),n(m,"class","icon svelte-w5wajl"),n(m,"aria-label","play-pause-replay-button"),n(M,"class","time svelte-w5wajl"),d.value=j=a[5]/a[6]||0,n(d,"class","svelte-w5wajl"),n(B,"role","button"),n(B,"tabindex","0"),n(B,"class","icon svelte-w5wajl"),n(B,"aria-label","full-screen"),n(p,"class","inner svelte-w5wajl"),n(u,"class","controls svelte-w5wajl"),n(e,"class","wrap svelte-w5wajl")},m(s,_){L(s,e,_),o(e,t),o(t,l),a[19](t),o(e,T),o(e,u),o(u,p),o(p,m),E[f].m(m,null),o(p,z),o(p,M),o(M,H),o(M,I),o(M,S),o(p,r),o(p,d),o(p,J),o(p,B),Z(q,B,null),k=!0,$||(ne=[c(t,"click",a[10]),c(t,"play",a[14]),c(t,"pause",a[15]),c(t,"ended",a[12]),c(t,"timeupdate",le),c(t,"durationchange",a[17]),c(t,"play",a[18]),c(t,"pause",a[18]),fe(w=Pe.call(null,t,{autoplay:a[3]})),c(m,"click",a[10]),c(m,"keydown",a[10]),c(d,"mousemove",a[9]),c(d,"touchmove",ie(a[9])),c(d,"click",de(ie(a[11]))),c(B,"click",a[13]),c(B,"keypress",a[13])],$=!0)},p(s,[_]){(!k||_&2&&!Q(l.src,i=s[1]))&&n(l,"src",i),(!k||_&1&&!Q(t.src,v=s[0]))&&n(t,"src",v),(!k||_&16&&N!==(N=`${s[4]}-player`))&&n(t,"data-testid",N),!P&&_&32&&!Te(s[5])&&(t.currentTime=s[5]),P=!1,_&128&&b!==(b=s[7])&&t[b?"pause":"play"](),w&&_e(w.update)&&_&8&&w.update.call(null,{autoplay:s[3]}),(!k||_&4)&&se(t,"mirror",s[2]);let ee=f;f=re(s),f!==ee&&(pe(),A(E[ee],1,1,()=>{E[ee]=null}),me(),h=E[f],h||(h=E[f]=ae[f](s),h.c()),V(h,1),h.m(m,null)),(!k||_&32)&&F!==(F=W(s[5])+"")&&oe(H,F),(!k||_&64)&&R!==(R=W(s[6])+"")&&oe(S,R),(!k||_&96&&j!==(j=s[5]/s[6]||0))&&(d.value=j)},i(s){k||(V(h),V(q.$$.fragment,s),k=!0)},o(s){A(h),A(q.$$.fragment,s),k=!1},d(s){s&&K(e),a[19](null),E[f].d(),x(q),$=!1,he(ne)}}}function W(a){if(isNaN(a)||!isFinite(a))return"...";const e=Math.floor(a/60);let t=Math.floor(a%60);return t<10&&(t=`0${t}`),`${e}:${t}`}function Ve(a,e,t){let{src:l}=e,{subtitle:i=null}=e,{mirror:v}=e,{autoplay:N}=e,{label:P="test"}=e;const X=ge();let b=0,w,T=!0,u;function p(r){if(!w)return;if(r.type==="click"){f(r);return}if(r.type!=="touchmove"&&!(r.buttons&1))return;const d=r.type==="touchmove"?r.touches[0].clientX:r.clientX,{left:j,right:J}=r.currentTarget.getBoundingClientRect();t(5,b=w*(d-j)/(J-j))}async function m(){document.fullscreenElement!=u&&(u.currentTime>0&&!u.paused&&!u.ended&&u.readyState>u.HAVE_CURRENT_DATA?u.pause():await u.play())}function f(r){const{left:d,right:j}=r.currentTarget.getBoundingClientRect();t(5,b=w*(r.clientX-d)/(j-d))}function h(){X("stop"),X("end")}function z(){u.requestFullscreen()}function M(r){ue.call(this,a,r)}function F(r){ue.call(this,a,r)}function H(){b=this.currentTime,t(5,b)}function I(){w=this.duration,t(6,w)}function R(){T=this.paused,t(7,T)}function S(r){ke[r?"unshift":"push"](()=>{u=r,t(8,u)})}return a.$$set=r=>{"src"in r&&t(0,l=r.src),"subtitle"in r&&t(1,i=r.subtitle),"mirror"in r&&t(2,v=r.mirror),"autoplay"in r&&t(3,N=r.autoplay),"label"in r&&t(4,P=r.label)},[l,i,v,N,P,b,w,T,u,p,m,f,h,z,M,F,H,I,R,S]}class Le extends U{constructor(e){super(),D(this,e,Ve,Ae,G,{src:0,subtitle:1,mirror:2,autoplay:3,label:4})}}export{Le as P,Ue as V,Ge as a,De as p};
//# sourceMappingURL=Player-ff6505f7.js.map
