# Copyright The Lightning AI team.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Enumerated utilities."""
from __future__ import annotations

from typing import TYPE_CHECKING

from lightning_utilities.core.enums import StrEnum

if TYPE_CHECKING:
    from enum import Enum

    # re-defined because `mypy` infers `StrEnum` as `Any`
    class LightningEnum(StrEnum, Enum):
        ...

else:
    LightningEnum = StrEnum


class _AcceleratorType(LightningEnum):
    """Define Accelerator type by its nature."""

    CPU = "CPU"
    CUDA = "CUDA"
    TPU = "TPU"
    MPS = "MPS"
