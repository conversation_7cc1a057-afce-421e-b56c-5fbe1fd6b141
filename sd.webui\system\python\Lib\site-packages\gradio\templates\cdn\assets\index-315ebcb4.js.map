{"version": 3, "file": "index-315ebcb4.js", "sources": ["../../../../js/tabitem/static/TabItem.svelte", "../../../../js/tabitem/static/StaticTabItem.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { getContext, onMount, createEventDispatcher, tick } from \"svelte\";\n\timport { TABS } from \"@gradio/tabs/static\";\n\timport Column from \"@gradio/column\";\n\timport type { SelectData } from \"@gradio/utils\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let name: string;\n\texport let id: string | number | object = {};\n\n\tconst dispatch = createEventDispatcher<{ select: SelectData }>();\n\n\tconst { register_tab, unregister_tab, selected_tab, selected_tab_index } =\n\t\tgetContext(TABS) as any;\n\n\tlet tab_index = register_tab({ name, id });\n\n\tonMount(() => {\n\t\treturn (): void => unregister_tab({ name, id });\n\t});\n\n\t$: $selected_tab_index === tab_index &&\n\t\ttick().then(() => dispatch(\"select\", { value: name, index: tab_index }));\n</script>\n\n<div\n\tid={elem_id}\n\tclass=\"tabitem {elem_classes.join(' ')}\"\n\tstyle:display={$selected_tab === id ? \"block\" : \"none\"}\n>\n\t<Column>\n\t\t<slot />\n\t</Column>\n</div>\n\n<style>\n\tdiv {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-top: none;\n\t\tborder-bottom-right-radius: var(--container-radius);\n\t\tborder-bottom-left-radius: var(--container-radius);\n\t\tpadding: var(--block-padding);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport TabItem from \"./TabItem.svelte\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let label: string;\n\texport let id: string | number;\n\texport let gradio: Gradio<{\n\t\tselect: SelectData;\n\t}>;\n</script>\n\n<TabItem\n\t{elem_id}\n\t{elem_classes}\n\tname={label}\n\t{id}\n\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n>\n\t<slot />\n</TabItem>\n"], "names": ["ctx", "set_style", "div", "insert", "target", "anchor", "elem_id", "$$props", "elem_classes", "name", "id", "dispatch", "createEventDispatcher", "register_tab", "unregister_tab", "selected_tab", "selected_tab_index", "getContext", "TABS", "tab_index", "onMount", "$selected_tab_index", "tick", "label", "gradio", "select_handler", "detail"], "mappings": "ipBA2BKA,EAAO,CAAA,CAAA,2BACKA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,iBAAA,EACtBC,EAAAC,EAAA,UAAAF,OAAkBA,EAAE,CAAA,EAAG,QAAU,MAAM,UAHvDG,EAQKC,EAAAF,EAAAG,CAAA,yGAPAL,EAAO,CAAA,CAAA,8BACKA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,0CACtBC,EAAAC,EAAA,UAAAF,OAAkBA,EAAE,CAAA,EAAG,QAAU,MAAM,6IAvB3C,CAAA,QAAAM,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,GACZ,KAAAE,CAAY,EAAAF,GACZ,GAAAG,EAAE,EAAA,EAAAH,EAEP,MAAAI,EAAWC,KAET,aAAAC,EAAc,eAAAC,EAAgB,aAAAC,EAAc,mBAAAC,GACnDC,EAAWC,CAAI,wCAEZ,IAAAC,EAAYN,EAAe,CAAA,KAAAJ,EAAM,GAAAC,CAAE,CAAA,EAEvC,OAAAU,EAAO,QACaN,EAAc,CAAG,KAAAL,EAAM,GAAAC,CAAE,CAAA,2MAG1CW,IAAwBF,GAC1BG,IAAO,KAAI,IAAOX,EAAS,UAAY,MAAOF,EAAM,MAAOU,CAAS,CAAA,CAAA,0bCP/DnB,EAAK,CAAA,uMAALA,EAAK,CAAA,iMAZA,CAAA,QAAAM,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,GACZ,MAAAgB,CAAa,EAAAhB,GACb,GAAAG,CAAmB,EAAAH,GACnB,OAAAiB,CAET,EAAAjB,EAQY,MAAAkB,EAAA,CAAA,CAAA,OAAAC,KAAaF,EAAO,SAAS,SAAUE,CAAM"}