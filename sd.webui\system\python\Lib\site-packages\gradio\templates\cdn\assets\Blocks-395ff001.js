const VERSION_RE = new RegExp("3.41.2/", "g");function import_fix(mod, base) {const url =  new URL(mod, base); return import(`https://gradio.s3-us-west-2.amazonaws.com/3.41.2/${url.pathname?.startsWith('/') ? url.pathname.substring(1).replace(VERSION_RE, "") : url.pathname.replace(VERSION_RE, "")}`);}import{n as X,i as sn,a as rn,l as an,c as _n,d as cn,b as fn,S as se,e as re,s as ae,f as de,g as u,h as k,j as p,k as b,m as T,o as M,t as O,p as pe,q as Fe,r as ne,u as C,v as le,w as I,x as H,y as un,z as dn,A as mn,B as pn,C as je,D as Pe,E as ve,F as G,G as W,H as J,I as te,J as zt,K as ue,L as gn,_ as N,M as Ae,N as Le,O as he,P as hn,Q as fe,R as ye,T as He,U as Ge,V as vn,W as bn,X as kn,Y as wn,Z as En,$ as yn,a0 as We,a1 as jn,a2 as An,a3 as On,a4 as Ln,a5 as Tn,a6 as Pn}from"./index-afe51b5b.js";import{c as In,f as Je,B as Ve,a as Rn}from"./Button-b4eb936e.js";function Dn(l,e,t,n){if(!e)return X;const i=l.getBoundingClientRect();if(e.left===i.left&&e.right===i.right&&e.top===i.top&&e.bottom===i.bottom)return X;const{delay:o=0,duration:r=300,easing:s=sn,start:a=rn()+o,end:_=a+r,tick:c=X,css:f}=t(l,{from:e,to:i},n);let d=!0,h=!1,j;function A(){f&&(j=_n(l,0,1,r,o,s,f)),o||(h=!0)}function y(){f&&cn(l,j),d=!1}return an(g=>{if(!h&&g>=a&&(h=!0),h&&g>=_&&(c(1,0),y()),!d)return!1;if(h){const E=g-a,L=0+1*s(E/r);c(L,1-L)}return!0}),A(),c(0,1),y}function Vn(l){const e=getComputedStyle(l);if(e.position!=="absolute"&&e.position!=="fixed"){const{width:t,height:n}=e,i=l.getBoundingClientRect();l.style.position="absolute",l.style.width=t,l.style.height=n,qn(l,i)}}function qn(l,e){const t=l.getBoundingClientRect();if(e.left!==t.left||e.top!==t.top){const n=getComputedStyle(l),i=n.transform==="none"?"":n.transform;l.style.transform=`${i} translate(${e.left-t.left}px, ${e.top-t.top}px)`}}function Cn(l,{from:e,to:t},n={}){const i=getComputedStyle(l),o=i.transform==="none"?"":i.transform,[r,s]=i.transformOrigin.split(" ").map(parseFloat),a=e.left+e.width*r/t.width-(t.left+r),_=e.top+e.height*s/t.height-(t.top+s),{delay:c=0,duration:f=h=>Math.sqrt(h)*120,easing:d=In}=n;return{delay:c,duration:fn(f)?f(Math.sqrt(a*a+_*_)):f,easing:d,css:(h,j)=>{const A=j*a,y=j*_,g=h+j*e.width/t.width,E=h+j*e.height/t.height;return`transform: ${o} translate(${A}px, ${y}px) scale(${g}, ${E});`}}}function Nn(l){let e,t;return{c(){e=de("svg"),t=de("path"),u(t,"stroke-linecap","round"),u(t,"stroke-linejoin","round"),u(t,"d","M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"),u(e,"fill","none"),u(e,"stroke","currentColor"),u(e,"viewBox","0 0 24 24"),u(e,"width","100%"),u(e,"height","100%"),u(e,"xmlns","http://www.w3.org/2000/svg"),u(e,"aria-hidden","true"),u(e,"stroke-width","2"),u(e,"stroke-linecap","round"),u(e,"stroke-linejoin","round")},m(n,i){k(n,e,i),p(e,t)},p:X,i:X,o:X,d(n){n&&b(e)}}}let zn=class extends se{constructor(e){super(),re(this,e,null,Nn,ae,{})}};function Sn(l){let e,t;return{c(){e=de("svg"),t=de("path"),u(t,"stroke-linecap","round"),u(t,"stroke-linejoin","round"),u(t,"d","M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"),u(e,"fill","none"),u(e,"stroke","currentColor"),u(e,"viewBox","0 0 24 24"),u(e,"width","100%"),u(e,"height","100%"),u(e,"xmlns","http://www.w3.org/2000/svg"),u(e,"aria-hidden","true"),u(e,"stroke-width","2"),u(e,"stroke-linecap","round"),u(e,"stroke-linejoin","round")},m(n,i){k(n,e,i),p(e,t)},p:X,i:X,o:X,d(n){n&&b(e)}}}class Mn extends se{constructor(e){super(),re(this,e,null,Sn,ae,{})}}function Bn(l){let e,t;return{c(){e=de("svg"),t=de("path"),u(t,"stroke-linecap","round"),u(t,"stroke-linejoin","round"),u(t,"d","M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"),u(e,"fill","none"),u(e,"stroke","currentColor"),u(e,"stroke-width","2"),u(e,"viewBox","0 0 24 24"),u(e,"width","100%"),u(e,"height","100%"),u(e,"xmlns","http://www.w3.org/2000/svg"),u(e,"aria-hidden","true"),u(e,"stroke-linecap","round"),u(e,"stroke-linejoin","round")},m(n,i){k(n,e,i),p(e,t)},p:X,i:X,o:X,d(n){n&&b(e)}}}class Un extends se{constructor(e){super(),re(this,e,null,Bn,ae,{})}}function Fn(l){let e,t;return e=new zn({}),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Hn(l){let e,t;return e=new Mn({}),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Gn(l){let e,t;return e=new Un({}),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Wn(l){let e,t,n,i,o,r,s,a,_,c,f,d,h,j,A,y,g,E,L,v,w,D,q,F,Q,V,_e,P;const U=[Gn,Hn,Fn],K=[];function me(R,Z){return R[1]==="warning"?0:R[1]==="info"?1:R[1]==="error"?2:-1}return~(n=me(l))&&(i=K[n]=U[n](l)),{c(){e=T("div"),t=T("div"),i&&i.c(),r=M(),s=T("div"),a=T("div"),_=O(l[1]),f=M(),d=T("div"),h=O(l[0]),y=M(),g=T("button"),E=T("span"),E.textContent="×",v=M(),w=T("div"),u(t,"class",o="toast-icon "+l[1]+" svelte-z3l7qj"),u(a,"class",c="toast-title "+l[1]+" svelte-z3l7qj"),u(d,"class",j="toast-text "+l[1]+" svelte-z3l7qj"),u(s,"class",A="toast-details "+l[1]+" svelte-z3l7qj"),u(E,"aria-hidden","true"),u(g,"class",L="toast-close "+l[1]+" svelte-z3l7qj"),u(g,"type","button"),u(g,"aria-label","Close"),u(g,"data-testid","toast-close"),u(w,"class",D="timer "+l[1]+" svelte-z3l7qj"),u(e,"class",q="toast-body "+l[1]+" svelte-z3l7qj"),u(e,"role","alert"),u(e,"data-testid","toast-body")},m(R,Z){k(R,e,Z),p(e,t),~n&&K[n].m(t,null),p(e,r),p(e,s),p(s,a),p(a,_),p(s,f),p(s,d),p(d,h),p(e,y),p(e,g),p(g,E),p(e,v),p(e,w),V=!0,_e||(P=[pe(g,"click",l[2]),pe(e,"click",Fe(l[4])),pe(e,"keydown",Fe(l[5]))],_e=!0)},p(R,[Z]){let ge=n;n=me(R),n!==ge&&(i&&(ne(),C(K[ge],1,1,()=>{K[ge]=null}),le()),~n?(i=K[n],i||(i=K[n]=U[n](R),i.c()),I(i,1),i.m(t,null)):i=null),(!V||Z&2&&o!==(o="toast-icon "+R[1]+" svelte-z3l7qj"))&&u(t,"class",o),(!V||Z&2)&&H(_,R[1]),(!V||Z&2&&c!==(c="toast-title "+R[1]+" svelte-z3l7qj"))&&u(a,"class",c),(!V||Z&1)&&H(h,R[0]),(!V||Z&2&&j!==(j="toast-text "+R[1]+" svelte-z3l7qj"))&&u(d,"class",j),(!V||Z&2&&A!==(A="toast-details "+R[1]+" svelte-z3l7qj"))&&u(s,"class",A),(!V||Z&2&&L!==(L="toast-close "+R[1]+" svelte-z3l7qj"))&&u(g,"class",L),(!V||Z&2&&D!==(D="timer "+R[1]+" svelte-z3l7qj"))&&u(w,"class",D),(!V||Z&2&&q!==(q="toast-body "+R[1]+" svelte-z3l7qj"))&&u(e,"class",q)},i(R){V||(I(i),R&&un(()=>{V&&(Q&&Q.end(1),F=dn(e,Je,{duration:200,delay:100}),F.start())}),V=!0)},o(R){C(i),F&&F.invalidate(),R&&(Q=mn(e,Je,{duration:200})),V=!1},d(R){R&&b(e),~n&&K[n].d(),R&&Q&&Q.end(),_e=!1,pn(P)}}}function Jn(l,e,t){let{message:n=""}=e,{type:i}=e,{id:o}=e;const r=je();function s(){r("close",o)}Pe(()=>{setTimeout(()=>{s()},1e4)});function a(c){ve.call(this,l,c)}function _(c){ve.call(this,l,c)}return l.$$set=c=>{"message"in c&&t(0,n=c.message),"type"in c&&t(1,i=c.type),"id"in c&&t(3,o=c.id)},[n,i,s,o,a,_]}class Qn extends se{constructor(e){super(),re(this,e,Jn,Wn,ae,{message:0,type:1,id:3})}}function Qe(l,e,t){const n=l.slice();return n[2]=e[t].type,n[3]=e[t].message,n[4]=e[t].id,n}function Ke(l,e){let t,n,i,o,r=X,s;return n=new Qn({props:{type:e[2],message:e[3],id:e[4]}}),n.$on("close",e[1]),{key:l,first:null,c(){t=T("div"),G(n.$$.fragment),i=M(),ue(t,"width","100%"),this.first=t},m(a,_){k(a,t,_),W(n,t,null),p(t,i),s=!0},p(a,_){e=a;const c={};_&1&&(c.type=e[2]),_&1&&(c.message=e[3]),_&1&&(c.id=e[4]),n.$set(c)},r(){o=t.getBoundingClientRect()},f(){Vn(t),r()},a(){r(),r=Dn(t,o,Cn,{duration:300})},i(a){s||(I(n.$$.fragment,a),s=!0)},o(a){C(n.$$.fragment,a),s=!1},d(a){a&&b(t),J(n)}}}function Kn(l){let e,t=[],n=new Map,i,o=te(l[0]);const r=s=>s[4];for(let s=0;s<o.length;s+=1){let a=Qe(l,o,s),_=r(a);n.set(_,t[s]=Ke(_,a))}return{c(){e=T("div");for(let s=0;s<t.length;s+=1)t[s].c();u(e,"class","toast-wrap svelte-pu0yf1")},m(s,a){k(s,e,a);for(let _=0;_<t.length;_+=1)t[_]&&t[_].m(e,null);i=!0},p(s,[a]){if(a&1){o=te(s[0]),ne();for(let _=0;_<t.length;_+=1)t[_].r();t=zt(t,a,r,1,s,o,n,e,gn,Ke,null,Qe);for(let _=0;_<t.length;_+=1)t[_].a();le()}},i(s){if(!i){for(let a=0;a<o.length;a+=1)I(t[a]);i=!0}},o(s){for(let a=0;a<t.length;a+=1)C(t[a]);i=!1},d(s){s&&b(e);for(let a=0;a<t.length;a+=1)t[a].d()}}}function Zn(l){l.length>0&&"parentIFrame"in window&&window.parentIFrame?.scrollTo(0,0)}function Xn(l,e,t){let{messages:n=[]}=e;function i(o){ve.call(this,l,o)}return l.$$set=o=>{"messages"in o&&t(0,n=o.messages)},l.$$.update=()=>{l.$$.dirty&1&&Zn(n)},[n,i]}class Yn extends se{constructor(e){super(),re(this,e,Xn,Kn,ae,{messages:0})}}const Ze={accordion:{static:()=>N(()=>import("./index-49e6a619.js"),["assets/index-49e6a619.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/StaticColumn-53350968.js","assets/StaticColumn-2853eb31.css","assets/index-8f1feca1.css"])},annotatedimage:{static:()=>N(()=>import("./index-e57a5c85.js"),["assets/index-e57a5c85.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockLabel-4ae26392.js","assets/Empty-879644c1.js","assets/Image-873e9e43.js","assets/index-f0e43e7d.css"])},audio:{static:()=>N(()=>import("./index-d4182e61.js"),["assets/index-d4182e61.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockLabel-4ae26392.js","assets/IconButton-12dccad1.js","assets/Empty-879644c1.js","assets/ShareButton-34cde537.js","assets/utils-c3e3db58.js","assets/Download-bad6f850.js","assets/utils-921c8c36.js","assets/index-4e2a7646.css"]),interactive:()=>N(()=>import("./index-6caa1e62.js"),["assets/index-6caa1e62.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/UploadText-fa42cf80.js","assets/Upload-eec15a82.js","assets/ModifyUpload-7942a18d.js","assets/IconButton-12dccad1.js","assets/BlockLabel-4ae26392.js","assets/utils-921c8c36.js","assets/index-4a131291.css"])},box:{static:()=>N(()=>import("./index-df349df1.js"),["assets/index-df349df1.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css"])},button:{static:()=>N(()=>import("./index-95a52c64.js"),["assets/index-95a52c64.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css"])},chatbot:{static:()=>N(()=>import("./index-7239f5f3.js"),["assets/index-7239f5f3.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/utils-c3e3db58.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/ShareButton-34cde537.js","assets/IconButton-12dccad1.js","assets/StaticMarkdown-15b756e1.js","assets/StaticMarkdown-44d5594c.css","assets/Copy-df6acdb9.js","assets/BlockLabel-4ae26392.js","assets/index-25f94022.css"])},checkbox:{static:()=>N(()=>import("./index-26d9d973.js"),["assets/index-26d9d973.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Checkbox-60143f81.js","assets/Checkbox-dc375626.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/Info-1e8b7dd5.js"]),interactive:()=>N(()=>import("./index-4478f0a8.js"),["assets/index-4478f0a8.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Checkbox-60143f81.js","assets/Checkbox-dc375626.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/Info-1e8b7dd5.js"])},checkboxgroup:{static:()=>N(()=>import("./index-d5ef9846.js"),["assets/index-d5ef9846.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Checkboxgroup-ee69cded.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockTitle-5b84032d.js","assets/Info-1e8b7dd5.js","assets/Checkboxgroup-e557d23a.css"]),interactive:()=>N(()=>import("./index-be1e2fbc.js"),["assets/index-be1e2fbc.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Checkboxgroup-ee69cded.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockTitle-5b84032d.js","assets/Info-1e8b7dd5.js","assets/Checkboxgroup-e557d23a.css"])},code:{static:()=>N(()=>import("./index-8814b912.js"),["assets/index-8814b912.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Widgets.svelte_svelte_type_style_lang-8f0c3a49.js","assets/Widgets-4ccfb72c.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/Copy-df6acdb9.js","assets/Download-bad6f850.js","assets/BlockLabel-4ae26392.js","assets/Empty-879644c1.js"]),interactive:()=>N(()=>import("./index-b91bc593.js"),["assets/index-b91bc593.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Widgets.svelte_svelte_type_style_lang-8f0c3a49.js","assets/Widgets-4ccfb72c.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockLabel-4ae26392.js"])},colorpicker:{static:()=>N(()=>import("./index-11cec3e5.js"),["assets/index-11cec3e5.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Colorpicker-672f9a35.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockTitle-5b84032d.js","assets/Info-1e8b7dd5.js","assets/Colorpicker-cd311153.css"]),interactive:()=>N(()=>import("./index-7a06afca.js"),["assets/index-7a06afca.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Colorpicker-672f9a35.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockTitle-5b84032d.js","assets/Info-1e8b7dd5.js","assets/Colorpicker-cd311153.css"])},column:{static:()=>N(()=>import("./index-dee103c4.js"),["assets/index-dee103c4.js","assets/StaticColumn-53350968.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/StaticColumn-2853eb31.css"])},dataframe:{static:()=>N(()=>import("./index-12d49dd5.js"),["assets/index-12d49dd5.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/Table-2eea8e55.js","assets/utils-c3e3db58.js","assets/Upload-eec15a82.js","assets/StaticMarkdown-15b756e1.js","assets/StaticMarkdown-44d5594c.css","assets/dsv-576afacd.js","assets/Table-77433dc3.css"]),interactive:()=>N(()=>import("./index-f9fcc037.js"),["assets/index-f9fcc037.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/Table-2eea8e55.js","assets/utils-c3e3db58.js","assets/Upload-eec15a82.js","assets/StaticMarkdown-15b756e1.js","assets/StaticMarkdown-44d5594c.css","assets/dsv-576afacd.js","assets/Table-77433dc3.css"])},dataset:{static:()=>N(()=>import("./index-d5b24dc8.js"),["assets/index-d5b24dc8.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/csv-b0b7514a.js","assets/dsv-576afacd.js","assets/index-77812ee4.css","assets/Player-1e00f554.css"])},dropdown:{static:()=>N(()=>import("./index-b7076ef1.js"),["assets/index-b7076ef1.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Dropdown-6d8e5c74.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockTitle-5b84032d.js","assets/Info-1e8b7dd5.js","assets/Dropdown-1d75348c.css"]),interactive:()=>N(()=>import("./index-c514f50a.js"),["assets/index-c514f50a.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Dropdown-6d8e5c74.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockTitle-5b84032d.js","assets/Info-1e8b7dd5.js","assets/Dropdown-1d75348c.css"])},file:{static:()=>N(()=>import("./index-c878429f.js"),["assets/index-c878429f.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockLabel-4ae26392.js","assets/Empty-879644c1.js","assets/File-b3b00b9b.js","assets/FilePreview-a6a7747b.js","assets/FilePreview-f49dff58.css"]),interactive:()=>N(()=>import("./index-cdf0f3f4.js"),["assets/index-cdf0f3f4.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Upload-eec15a82.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/ModifyUpload-7942a18d.js","assets/IconButton-12dccad1.js","assets/BlockLabel-4ae26392.js","assets/File-b3b00b9b.js","assets/FilePreview-a6a7747b.js","assets/FilePreview-f49dff58.css","assets/UploadText-fa42cf80.js"])},form:{static:()=>N(()=>import("./index-59030f53.js"),["assets/index-59030f53.js","assets/StaticForm-ed3cbc92.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/StaticForm-3812b7f1.css"])},gallery:{static:()=>N(()=>import("./index-347e7c00.js"),["assets/index-347e7c00.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockLabel-4ae26392.js","assets/IconButton-12dccad1.js","assets/Empty-879644c1.js","assets/ShareButton-34cde537.js","assets/utils-c3e3db58.js","assets/ModifyUpload-7942a18d.js","assets/Download-bad6f850.js","assets/Image-873e9e43.js","assets/index-bc19ffad.css"])},group:{static:()=>N(()=>import("./index-080a694a.js"),["assets/index-080a694a.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/index-37519934.css"])},highlightedtext:{static:()=>N(()=>import("./index-cf9b3761.js"),["assets/index-cf9b3761.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/color-0cce4e49.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockLabel-4ae26392.js","assets/Empty-879644c1.js","assets/index-9d08c7d8.css"])},html:{static:()=>N(()=>import("./index-6be8cb77.js"),["assets/index-6be8cb77.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/index-329f8260.css"])},image:{static:()=>N(()=>import("./index-e6b54990.js"),["assets/index-e6b54990.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/utils-c3e3db58.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockLabel-4ae26392.js","assets/IconButton-12dccad1.js","assets/Empty-879644c1.js","assets/ShareButton-34cde537.js","assets/Download-bad6f850.js","assets/Image-873e9e43.js","assets/utils-90f3612b.js","assets/index-f62e764d.css"]),interactive:()=>N(()=>import("./index-b03c53d1.js"),["assets/index-b03c53d1.js","assets/InteractiveImage-f8f367cd.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockLabel-4ae26392.js","assets/Image-873e9e43.js","assets/utils-90f3612b.js","assets/IconButton-12dccad1.js","assets/ModifyUpload-7942a18d.js","assets/Undo-f9729d62.js","assets/Upload-eec15a82.js","assets/UploadText-fa42cf80.js","assets/InteractiveImage-b496c98d.css"])},interpretation:{static:()=>N(()=>import("./index-cd69cc81.js"),["assets/index-cd69cc81.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockTitle-5b84032d.js","assets/Info-1e8b7dd5.js","assets/index-6acaa952.css"]),interactive:()=>N(()=>import("./index-cd69cc81.js"),["assets/index-cd69cc81.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockTitle-5b84032d.js","assets/Info-1e8b7dd5.js","assets/index-6acaa952.css"])},json:{static:()=>N(()=>import("./index-7e0bc3ad.js"),["assets/index-7e0bc3ad.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/Copy-df6acdb9.js","assets/Empty-879644c1.js","assets/BlockLabel-4ae26392.js","assets/index-3ca142e0.css"])},label:{static:()=>N(()=>import("./index-e23092c6.js"),["assets/index-e23092c6.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockLabel-4ae26392.js","assets/Empty-879644c1.js","assets/index-cc2431f4.css"])},markdown:{static:()=>N(()=>import("./index-783dab25.js"),["assets/index-783dab25.js","assets/StaticMarkdown-15b756e1.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/utils-c3e3db58.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/StaticMarkdown-44d5594c.css"])},model3d:{static:()=>N(()=>import("./index-8a70a45b.js"),["assets/index-8a70a45b.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockLabel-4ae26392.js","assets/Empty-879644c1.js","assets/File-b3b00b9b.js","assets/IconButton-12dccad1.js","assets/Download-bad6f850.js","assets/babylonjs.loaders.min-83396955.js","assets/index-d5d74218.css"]),interactive:()=>N(()=>import("./index-dc5f3789.js"),["assets/index-dc5f3789.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/UploadText-fa42cf80.js","assets/Upload-eec15a82.js","assets/ModifyUpload-7942a18d.js","assets/IconButton-12dccad1.js","assets/BlockLabel-4ae26392.js","assets/File-b3b00b9b.js","assets/babylonjs.loaders.min-83396955.js","assets/index-3a72a5f5.css"])},number:{static:()=>N(()=>import("./index-c693bdda.js"),["assets/index-c693bdda.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Number-fee75820.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockTitle-5b84032d.js","assets/Info-1e8b7dd5.js","assets/Number-76c3ee3f.css"]),interactive:()=>N(()=>import("./index-57d06869.js"),["assets/index-57d06869.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Number-fee75820.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockTitle-5b84032d.js","assets/Info-1e8b7dd5.js","assets/Number-76c3ee3f.css"])},plot:{static:()=>N(()=>import("./index-1d5a0c4a.js"),["assets/index-1d5a0c4a.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/color-0cce4e49.js","assets/linear-bcbcf466.js","assets/dsv-576afacd.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/Empty-879644c1.js","assets/BlockLabel-4ae26392.js","assets/index-31d5c487.css"])},radio:{static:()=>N(()=>import("./index-c6cb06f1.js"),["assets/index-c6cb06f1.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Radio-ec6e2741.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockTitle-5b84032d.js","assets/Info-1e8b7dd5.js","assets/Radio-b7554727.css"]),interactive:()=>N(()=>import("./index-0f3b93f7.js"),["assets/index-0f3b93f7.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Radio-ec6e2741.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockTitle-5b84032d.js","assets/Info-1e8b7dd5.js","assets/Radio-b7554727.css"])},row:{static:()=>N(()=>import("./index-92394e9b.js"),["assets/index-92394e9b.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/index-93c91554.css"])},slider:{static:()=>N(()=>import("./index-4d6ef228.js"),["assets/index-4d6ef228.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Range-dee7f93e.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockTitle-5b84032d.js","assets/Info-1e8b7dd5.js","assets/Range-49c152ed.css"]),interactive:()=>N(()=>import("./index-17d65eb8.js"),["assets/index-17d65eb8.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Range-dee7f93e.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockTitle-5b84032d.js","assets/Info-1e8b7dd5.js","assets/Range-49c152ed.css"])},state:{static:()=>N(()=>import("./index-eeb61ab3.js"),["assets/index-eeb61ab3.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css"])},statustracker:{static:()=>N(()=>import("./index-cc2239b9.js"),["assets/index-cc2239b9.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css"])},tabs:{static:()=>N(()=>import("./index-45ca7f07.js"),["assets/index-45ca7f07.js","assets/StaticTabs-5d569f81.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/StaticTabs-42a53876.css"])},tabitem:{static:()=>N(()=>import("./index-315ebcb4.js"),["assets/index-315ebcb4.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/StaticTabs-5d569f81.js","assets/StaticTabs-42a53876.css","assets/StaticColumn-53350968.js","assets/StaticColumn-2853eb31.css","assets/index-d43fcb36.css"])},textbox:{static:()=>N(()=>import("./index-fe24d00c.js"),["assets/index-fe24d00c.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Textbox-3a8fafc1.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockTitle-5b84032d.js","assets/Info-1e8b7dd5.js","assets/Copy-df6acdb9.js","assets/Textbox-dde6f8cc.css"]),interactive:()=>N(()=>import("./index-136057b0.js"),["assets/index-136057b0.js","assets/InteractiveTextbox-66ac855c.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Textbox-3a8fafc1.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockTitle-5b84032d.js","assets/Info-1e8b7dd5.js","assets/Copy-df6acdb9.js","assets/Textbox-dde6f8cc.css"])},timeseries:{static:()=>N(()=>import("./index-0261d910.js"),["assets/index-0261d910.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockLabel-4ae26392.js","assets/Empty-879644c1.js","assets/Chart-63a1564e.js","assets/color-0cce4e49.js","assets/csv-b0b7514a.js","assets/dsv-576afacd.js","assets/linear-bcbcf466.js","assets/Chart-02ddc6a9.css"]),interactive:()=>N(()=>import("./index-3a7e3484.js"),["assets/index-3a7e3484.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Upload-eec15a82.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/ModifyUpload-7942a18d.js","assets/IconButton-12dccad1.js","assets/BlockLabel-4ae26392.js","assets/Chart-63a1564e.js","assets/color-0cce4e49.js","assets/csv-b0b7514a.js","assets/dsv-576afacd.js","assets/linear-bcbcf466.js","assets/Chart-02ddc6a9.css","assets/UploadText-fa42cf80.js","assets/index-69616613.css"])},uploadbutton:{static:()=>N(()=>import("./index-fa9f92d4.js"),["assets/index-fa9f92d4.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/UploadButton-707cc12a.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/UploadButton-03d58ab8.css"]),interactive:()=>N(()=>import("./index-410b2805.js"),["assets/index-410b2805.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/UploadButton-707cc12a.js","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/UploadButton-03d58ab8.css"])},video:{static:()=>N(()=>import("./index-4807f480.js"),["assets/index-4807f480.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/BlockLabel-4ae26392.js","assets/IconButton-12dccad1.js","assets/Empty-879644c1.js","assets/ShareButton-34cde537.js","assets/utils-c3e3db58.js","assets/Download-bad6f850.js","assets/Player-abcfefb3.js","assets/Undo-f9729d62.js","assets/Player-1e00f554.css","assets/index-022688c7.css"]),interactive:()=>N(()=>import("./index-cff5fe52.js"),["assets/index-cff5fe52.js","assets/index-afe51b5b.js","assets/index-29fa5a20.css","assets/Button-b4eb936e.js","assets/Button-620848cb.css","assets/UploadText-fa42cf80.js","assets/Upload-eec15a82.js","assets/ModifyUpload-7942a18d.js","assets/IconButton-12dccad1.js","assets/BlockLabel-4ae26392.js","assets/Player-abcfefb3.js","assets/Undo-f9729d62.js","assets/Player-1e00f554.css","assets/InteractiveImage-f8f367cd.js","assets/Image-873e9e43.js","assets/utils-90f3612b.js","assets/InteractiveImage-b496c98d.css","assets/index-633cd86d.css"])}};function $n(l){let e,t,n,i;return{c(){e=de("svg"),t=de("g"),n=de("path"),i=de("path"),u(n,"d","M3.789,0.09C3.903,-0.024 4.088,-0.024 4.202,0.09L4.817,0.705C4.931,0.819 4.931,1.004 4.817,1.118L1.118,4.817C1.004,4.931 0.819,4.931 0.705,4.817L0.09,4.202C-0.024,4.088 -0.024,3.903 0.09,3.789L3.789,0.09Z"),u(i,"d","M4.825,3.797C4.934,3.907 4.934,4.084 4.825,4.193L4.193,4.825C4.084,4.934 3.907,4.934 3.797,4.825L0.082,1.11C-0.027,1.001 -0.027,0.823 0.082,0.714L0.714,0.082C0.823,-0.027 1.001,-0.027 1.11,0.082L4.825,3.797Z"),u(e,"width","100%"),u(e,"height","100%"),u(e,"viewBox","0 0 5 5"),u(e,"version","1.1"),u(e,"xmlns","http://www.w3.org/2000/svg"),u(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),u(e,"xml:space","preserve"),ue(e,"fill","currentColor"),ue(e,"fill-rule","evenodd"),ue(e,"clip-rule","evenodd"),ue(e,"stroke-linejoin","round"),ue(e,"stroke-miterlimit","2")},m(o,r){k(o,e,r),p(e,t),p(t,n),p(t,i)},p:X,i:X,o:X,d(o){o&&b(e)}}}class St extends se{constructor(e){super(),re(this,e,null,$n,ae,{})}}function xn(l){let e,t,n,i,o,r,s,a,_,c,f,d,h,j,A;return d=new St({}),{c(){e=T("div"),t=T("h1"),t.textContent="API Docs",n=M(),i=T("p"),o=O(`No API Routes found for
		`),r=T("code"),s=O(l[0]),a=M(),_=T("p"),_.innerHTML=`To expose an API endpoint of your app in this page, set the <code>api_name</code>
		parameter of the event listener.
		<br/>
		For more information, visit the
		<a href="https://gradio.app/sharing_your_app/#api-page" target="_blank">API Page guide</a>
		. To hide the API documentation button and this page, set
		<code>show_api=False</code>
		in the
		<code>Blocks.launch()</code>
		method.`,c=M(),f=T("button"),G(d.$$.fragment),u(r,"class","svelte-e1ha0f"),u(i,"class","attention svelte-e1ha0f"),u(e,"class","wrap prose svelte-e1ha0f"),u(f,"class","svelte-e1ha0f")},m(y,g){k(y,e,g),p(e,t),p(e,n),p(e,i),p(i,o),p(i,r),p(r,s),p(e,a),p(e,_),k(y,c,g),k(y,f,g),W(d,f,null),h=!0,j||(A=pe(f,"click",l[2]),j=!0)},p(y,[g]){(!h||g&1)&&H(s,y[0])},i(y){h||(I(d.$$.fragment,y),h=!0)},o(y){C(d.$$.fragment,y),h=!1},d(y){y&&(b(e),b(c),b(f)),J(d),j=!1,A()}}}function el(l,e,t){const n=je();let{root:i}=e;const o=()=>n("close");return l.$$set=r=>{"root"in r&&t(0,i=r.root)},[i,n,o]}class tl extends se{constructor(e){super(),re(this,e,el,xn,ae,{root:0})}}function Te(l,e,t=null){return e===void 0?t==="py"?"None":null:e==="string"||e==="str"?t===null?l:'"'+l+'"':e==="number"?t===null?parseFloat(l):l:e==="boolean"||e=="bool"?t==="py"?(l=String(l),l==="true"?"True":"False"):t==="js"?l:l==="true":e==="List[str]"?(l=JSON.stringify(l),l):t===null?l===""?null:JSON.parse(l):typeof l=="string"?l===""?t==="py"?"None":"null":l:JSON.stringify(l)}const Mt="https://gradio.s3-us-west-2.amazonaws.com/3.41.2/assets/api-logo-5346f193.svg";function Xe(l){let e;return{c(){e=O("s")},m(t,n){k(t,e,n)},d(t){t&&b(e)}}}function nl(l){let e,t,n,i,o,r,s,a,_,c,f,d,h,j,A,y,g,E,L,v=l[1]>1&&Xe();return y=new St({}),{c(){e=T("h2"),t=T("img"),i=M(),o=T("div"),r=O(`API documentation
		`),s=T("div"),a=O(l[0]),_=M(),c=T("span"),f=T("span"),d=O(l[1]),h=O(" API endpoint"),v&&v.c(),j=M(),A=T("button"),G(y.$$.fragment),Ae(t.src,n=Mt)||u(t,"src",n),u(t,"alt",""),u(t,"class","svelte-3n2nxs"),u(s,"class","url svelte-3n2nxs"),u(f,"class","url svelte-3n2nxs"),u(c,"class","counts svelte-3n2nxs"),u(e,"class","svelte-3n2nxs"),u(A,"class","svelte-3n2nxs")},m(w,D){k(w,e,D),p(e,t),p(e,i),p(e,o),p(o,r),p(o,s),p(s,a),p(e,_),p(e,c),p(c,f),p(f,d),p(c,h),v&&v.m(c,null),k(w,j,D),k(w,A,D),W(y,A,null),g=!0,E||(L=pe(A,"click",l[3]),E=!0)},p(w,[D]){(!g||D&1)&&H(a,w[0]),(!g||D&2)&&H(d,w[1]),w[1]>1?v||(v=Xe(),v.c(),v.m(c,null)):v&&(v.d(1),v=null)},i(w){g||(I(y.$$.fragment,w),g=!0)},o(w){C(y.$$.fragment,w),g=!1},d(w){w&&(b(e),b(j),b(A)),v&&v.d(),J(y),E=!1,L()}}}function ll(l,e,t){let{root:n}=e,{api_count:i}=e;const o=je(),r=()=>o("close");return l.$$set=s=>{"root"in s&&t(0,n=s.root),"api_count"in s&&t(1,i=s.api_count)},[n,i,o,r]}class il extends se{constructor(e){super(),re(this,e,ll,nl,ae,{root:0,api_count:1})}}class Ye{#e;#t;constructor(e,t,n,i,o){this.#e=e,this.theme=n,this.version=i,this.#t=t,this.root=o}dispatch(e,t){const n=new CustomEvent("gradio",{bubbles:!0,detail:{data:t,id:this.#e,event:e}});this.#t.dispatchEvent(n)}}function $e(l,e,t){const n=l.slice();return n[4]=e[t].label,n[5]=e[t].type,n[6]=e[t].python_type,n[7]=e[t].component,n[8]=e[t].serializer,n[10]=t,n}function xe(l){let e;return{c(){e=O("(")},m(t,n){k(t,e,n)},d(t){t&&b(e)}}}function ol(l){let e=l[2][l[10]].type+"",t;return{c(){t=O(e)},m(n,i){k(n,t,i)},p(n,i){i&4&&e!==(e=n[2][n[10]].type+"")&&H(t,e)},d(n){n&&b(t)}}}function sl(l){let e=l[6].type+"",t;return{c(){t=O(e)},m(n,i){k(n,t,i)},p(n,i){i&2&&e!==(e=n[6].type+"")&&H(t,e)},d(n){n&&b(t)}}}function et(l){let e;return{c(){e=O(",")},m(t,n){k(t,e,n)},d(t){t&&b(e)}}}function tt(l){let e,t,n,i,o=l[4]+"",r,s,a=l[7]+"",_,c;function f(A,y){return A[3]==="python"?sl:ol}let d=f(l),h=d(l),j=l[1].length>1&&et();return{c(){e=T("div"),t=T("span"),n=O("# "),h.c(),i=O(`
						representing output in '`),r=O(o),s=O("' "),_=O(a),c=O(`
						component`),j&&j.c(),u(t,"class","desc svelte-1c7hj3i"),u(e,"class","svelte-1c7hj3i"),Le(e,"second-level",l[1].length>1)},m(A,y){k(A,e,y),p(e,t),p(t,n),h.m(t,null),p(t,i),p(t,r),p(t,s),p(t,_),p(t,c),j&&j.m(e,null)},p(A,y){d===(d=f(A))&&h?h.p(A,y):(h.d(1),h=d(A),h&&(h.c(),h.m(t,i))),y&2&&o!==(o=A[4]+"")&&H(r,o),y&2&&a!==(a=A[7]+"")&&H(_,a),A[1].length>1?j||(j=et(),j.c(),j.m(e,null)):j&&(j.d(1),j=null),y&2&&Le(e,"second-level",A[1].length>1)},d(A){A&&b(e),h.d(),j&&j.d()}}}function nt(l){let e;return{c(){e=O(")")},m(t,n){k(t,e,n)},d(t){t&&b(e)}}}function lt(l){let e,t,n;return t=new hn({props:{margin:!1}}),{c(){e=T("div"),G(t.$$.fragment),u(e,"class","load-wrap svelte-1c7hj3i")},m(i,o){k(i,e,o),W(t,e,null),n=!0},i(i){n||(I(t.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),n=!1},d(i){i&&b(e),J(t)}}}function rl(l){let e,t,n,i,o,r,s=l[1].length>1&&xe(),a=te(l[1]),_=[];for(let d=0;d<a.length;d+=1)_[d]=tt($e(l,a,d));let c=l[1].length>1&&nt(),f=l[0]&&lt();return{c(){e=T("div"),t=T("div"),s&&s.c(),n=M();for(let d=0;d<_.length;d+=1)_[d].c();i=M(),c&&c.c(),o=M(),f&&f.c(),u(t,"class","svelte-1c7hj3i"),Le(t,"hide",l[0]),u(e,"class","response-wrap svelte-1c7hj3i")},m(d,h){k(d,e,h),p(e,t),s&&s.m(t,null),p(t,n);for(let j=0;j<_.length;j+=1)_[j]&&_[j].m(t,null);p(t,i),c&&c.m(t,null),p(e,o),f&&f.m(e,null),r=!0},p(d,h){if(d[1].length>1?s||(s=xe(),s.c(),s.m(t,n)):s&&(s.d(1),s=null),h&14){a=te(d[1]);let j;for(j=0;j<a.length;j+=1){const A=$e(d,a,j);_[j]?_[j].p(A,h):(_[j]=tt(A),_[j].c(),_[j].m(t,i))}for(;j<_.length;j+=1)_[j].d(1);_.length=a.length}d[1].length>1?c||(c=nt(),c.c(),c.m(t,null)):c&&(c.d(1),c=null),(!r||h&1)&&Le(t,"hide",d[0]),d[0]?f?h&1&&I(f,1):(f=lt(),f.c(),I(f,1),f.m(e,null)):f&&(ne(),C(f,1,1,()=>{f=null}),le())},i(d){r||(I(f),r=!0)},o(d){C(f),r=!1},d(d){d&&b(e),s&&s.d(),he(_,d),c&&c.d(),f&&f.d()}}}function al(l){let e,t,n,i;return n=new Ve({props:{$$slots:{default:[rl]},$$scope:{ctx:l}}}),{c(){e=T("h4"),e.innerHTML=`<div class="toggle-icon svelte-1c7hj3i"><div class="toggle-dot svelte-1c7hj3i"></div></div>
	Return Type(s)`,t=M(),G(n.$$.fragment),u(e,"class","svelte-1c7hj3i")},m(o,r){k(o,e,r),k(o,t,r),W(n,o,r),i=!0},p(o,[r]){const s={};r&2063&&(s.$$scope={dirty:r,ctx:o}),n.$set(s)},i(o){i||(I(n.$$.fragment,o),i=!0)},o(o){C(n.$$.fragment,o),i=!1},d(o){o&&(b(e),b(t)),J(n,o)}}}function _l(l,e,t){let{is_running:n}=e,{endpoint_returns:i}=e,{js_returns:o}=e,{current_language:r}=e;return l.$$set=s=>{"is_running"in s&&t(0,n=s.is_running),"endpoint_returns"in s&&t(1,i=s.endpoint_returns),"js_returns"in s&&t(2,o=s.js_returns),"current_language"in s&&t(3,r=s.current_language)},[n,i,o,r]}class Bt extends se{constructor(e){super(),re(this,e,_l,al,ae,{is_running:0,endpoint_returns:1,js_returns:2,current_language:3})}}function cl(l){let e;return{c(){e=O(l[0])},m(t,n){k(t,e,n)},p(t,n){n&1&&H(e,t[0])},d(t){t&&b(e)}}}function fl(l){let e,t;return e=new Rn({props:{size:"sm",$$slots:{default:[cl]},$$scope:{ctx:l}}}),e.$on("click",l[1]),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},p(n,[i]){const o={};i&9&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function ul(l,e,t){let{code:n}=e,i="copy";function o(){navigator.clipboard.writeText(n),t(0,i="copied!"),setTimeout(()=>{t(0,i="copy")},1500)}return l.$$set=r=>{"code"in r&&t(2,n=r.code)},[i,o,n]}class Ie extends se{constructor(e){super(),re(this,e,ul,fl,ae,{code:2})}}function dl(l){let e,t,n,i,o,r;return t=new Ie({props:{code:ot}}),{c(){e=T("div"),G(t.$$.fragment),n=M(),i=T("div"),o=T("pre"),o.textContent=`$ ${ot}`,u(e,"class","copy svelte-hq8ezf"),u(o,"class","svelte-hq8ezf")},m(s,a){k(s,e,a),W(t,e,null),k(s,n,a),k(s,i,a),p(i,o),r=!0},p:X,i(s){r||(I(t.$$.fragment,s),r=!0)},o(s){C(t.$$.fragment,s),r=!1},d(s){s&&(b(e),b(n),b(i)),J(t)}}}function ml(l){let e,t,n,i,o,r;return t=new Ie({props:{code:it}}),{c(){e=T("div"),G(t.$$.fragment),n=M(),i=T("div"),o=T("pre"),o.textContent=`$ ${it}`,u(e,"class","copy svelte-hq8ezf"),u(o,"class","svelte-hq8ezf")},m(s,a){k(s,e,a),W(t,e,null),k(s,n,a),k(s,i,a),p(i,o),r=!0},p:X,i(s){r||(I(t.$$.fragment,s),r=!0)},o(s){C(t.$$.fragment,s),r=!1},d(s){s&&(b(e),b(n),b(i)),J(t)}}}function pl(l){let e,t,n,i;const o=[ml,dl],r=[];function s(a,_){return a[0]==="python"?0:a[0]==="javascript"?1:-1}return~(t=s(l))&&(n=r[t]=o[t](l)),{c(){e=T("code"),n&&n.c(),u(e,"class","svelte-hq8ezf")},m(a,_){k(a,e,_),~t&&r[t].m(e,null),i=!0},p(a,_){let c=t;t=s(a),t===c?~t&&r[t].p(a,_):(n&&(ne(),C(r[c],1,1,()=>{r[c]=null}),le()),~t?(n=r[t],n?n.p(a,_):(n=r[t]=o[t](a),n.c()),I(n,1),n.m(e,null)):n=null)},i(a){i||(I(n),i=!0)},o(a){C(n),i=!1},d(a){a&&b(e),~t&&r[t].d()}}}function gl(l){let e,t;return e=new Ve({props:{$$slots:{default:[pl]},$$scope:{ctx:l}}}),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},p(n,[i]){const o={};i&3&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}let it="pip install gradio_client",ot="npm i -D @gradio/client";function hl(l,e,t){let{current_language:n}=e;return l.$$set=i=>{"current_language"in i&&t(0,n=i.current_language)},[n]}class vl extends se{constructor(e){super(),re(this,e,hl,gl,ae,{current_language:0})}}function bl(l){let e,t,n,i;return{c(){e=T("h3"),t=O(`fn_index:
		`),n=T("span"),i=O(l[1]),u(n,"class","post svelte-41kcm6"),u(e,"class","svelte-41kcm6")},m(o,r){k(o,e,r),p(e,t),p(e,n),p(n,i)},p(o,r){r&2&&H(i,o[1])},d(o){o&&b(e)}}}function kl(l){let e,t,n,i="/"+l[0],o;return{c(){e=T("h3"),t=O(`api_name:
		`),n=T("span"),o=O(i),u(n,"class","post svelte-41kcm6"),u(e,"class","svelte-41kcm6")},m(r,s){k(r,e,s),p(e,t),p(e,n),p(n,o)},p(r,s){s&1&&i!==(i="/"+r[0])&&H(o,i)},d(r){r&&b(e)}}}function wl(l){let e;function t(o,r){return o[2]?kl:bl}let n=t(l),i=n(l);return{c(){i.c(),e=fe()},m(o,r){i.m(o,r),k(o,e,r)},p(o,[r]){n===(n=t(o))&&i?i.p(o,r):(i.d(1),i=n(o),i&&(i.c(),i.m(e.parentNode,e)))},i:X,o:X,d(o){o&&b(e),i.d(o)}}}function El(l,e,t){let{api_name:n=null}=e,{fn_index:i=null}=e,{named:o}=e;return l.$$set=r=>{"api_name"in r&&t(0,n=r.api_name),"fn_index"in r&&t(1,i=r.fn_index),"named"in r&&t(2,o=r.named)},[n,i,o]}class Ut extends se{constructor(e){super(),re(this,e,El,wl,ae,{api_name:0,fn_index:1,named:2})}}function st(l,e,t){const n=l.slice();return n[14]=e[t].label,n[15]=e[t].type,n[16]=e[t].python_type,n[17]=e[t].component,n[18]=e[t].example_input,n[19]=e[t].serializer,n[21]=t,n}function rt(l,e,t){const n=l.slice();return n[14]=e[t].label,n[15]=e[t].type,n[16]=e[t].python_type,n[17]=e[t].component,n[18]=e[t].example_input,n[19]=e[t].serializer,n[21]=t,n}function at(l,e,t){const n=l.slice();return n[14]=e[t].label,n[15]=e[t].type,n[16]=e[t].python_type,n[17]=e[t].component,n[18]=e[t].example_input,n[19]=e[t].serializer,n[21]=t,n}function yl(l){let e,t;return e=new Ut({props:{named:l[6],fn_index:l[1]}}),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},p(n,i){const o={};i&64&&(o.named=n[6]),i&2&&(o.fn_index=n[1]),e.$set(o)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function jl(l){let e,t;return e=new Ut({props:{named:l[6],api_name:l[0].api_name}}),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},p(n,i){const o={};i&64&&(o.named=n[6]),i&1&&(o.api_name=n[0].api_name),e.$set(o)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Al(l){let e,t,n,i,o,r,s,a,_,c,f,d,h,j,A;t=new Ie({props:{code:l[9]?.innerText}});let y=te(l[11]),g=[];for(let q=0;q<y.length;q+=1)g[q]=_t(rt(l,y,q));function E(q,F){return q[6]?Tl:Ll}let L=E(l),v=L(l),w=te(l[4]),D=[];for(let q=0;q<w.length;q+=1)D[q]=ft(st(l,w,q));return{c(){e=T("div"),G(t.$$.fragment),n=M(),i=T("div"),o=T("pre"),r=O(`import { client } from "@gradio/client";
`);for(let q=0;q<g.length;q+=1)g[q].c();s=O(`
const app = await client(`),a=T("span"),_=O('"'),c=O(l[2]),f=O('"'),d=O(`);
const result = await app.predict(`),v.c(),h=O(", [");for(let q=0;q<D.length;q+=1)D[q].c();j=O(`
	]);

console.log(result.data);
`),u(e,"class","copy svelte-1d98qmk"),u(a,"class","token string svelte-1d98qmk"),u(o,"class","svelte-1d98qmk")},m(q,F){k(q,e,F),W(t,e,null),k(q,n,F),k(q,i,F),p(i,o),p(o,r);for(let Q=0;Q<g.length;Q+=1)g[Q]&&g[Q].m(o,null);p(o,s),p(o,a),p(a,_),p(a,c),p(a,f),p(o,d),v.m(o,null),p(o,h);for(let Q=0;Q<D.length;Q+=1)D[Q]&&D[Q].m(o,null);p(o,j),l[13](i),A=!0},p(q,F){const Q={};if(F&512&&(Q.code=q[9]?.innerText),t.$set(Q),F&2048){y=te(q[11]);let V;for(V=0;V<y.length;V+=1){const _e=rt(q,y,V);g[V]?g[V].p(_e,F):(g[V]=_t(_e),g[V].c(),g[V].m(o,s))}for(;V<g.length;V+=1)g[V].d(1);g.length=y.length}if((!A||F&4)&&H(c,q[2]),L===(L=E(q))&&v?v.p(q,F):(v.d(1),v=L(q),v&&(v.c(),v.m(o,h))),F&1072){w=te(q[4]);let V;for(V=0;V<w.length;V+=1){const _e=st(q,w,V);D[V]?D[V].p(_e,F):(D[V]=ft(_e),D[V].c(),D[V].m(o,j))}for(;V<D.length;V+=1)D[V].d(1);D.length=w.length}},i(q){A||(I(t.$$.fragment,q),A=!0)},o(q){C(t.$$.fragment,q),A=!1},d(q){q&&(b(e),b(n),b(i)),J(t),he(g,q),v.d(),he(D,q),l[13](null)}}}function Ol(l){let e,t,n,i,o,r,s,a,_,c,f,d,h,j;t=new Ie({props:{code:l[8]?.innerText}});let A=te(l[4]),y=[];for(let v=0;v<A.length;v+=1)y[v]=mt(at(l,A,v));function g(v,w){return v[6]?Dl:Rl}let E=g(l),L=E(l);return{c(){e=T("div"),G(t.$$.fragment),n=M(),i=T("div"),o=T("pre"),r=O(`from gradio_client import Client

client = Client(`),s=T("span"),a=O('"'),_=O(l[2]),c=O('"'),f=O(`)
result = client.predict(`);for(let v=0;v<y.length;v+=1)y[v].c();d=O(`
				`),L.c(),h=O(`
)
print(result)`),u(e,"class","copy svelte-1d98qmk"),u(s,"class","token string svelte-1d98qmk"),u(o,"class","svelte-1d98qmk")},m(v,w){k(v,e,w),W(t,e,null),k(v,n,w),k(v,i,w),p(i,o),p(o,r),p(o,s),p(s,a),p(s,_),p(s,c),p(o,f);for(let D=0;D<y.length;D+=1)y[D]&&y[D].m(o,null);p(o,d),L.m(o,null),p(o,h),l[12](i),j=!0},p(v,w){const D={};if(w&256&&(D.code=v[8]?.innerText),t.$set(D),(!j||w&4)&&H(_,v[2]),w&26){A=te(v[4]);let q;for(q=0;q<A.length;q+=1){const F=at(v,A,q);y[q]?y[q].p(F,w):(y[q]=mt(F),y[q].c(),y[q].m(o,d))}for(;q<y.length;q+=1)y[q].d(1);y.length=A.length}E===(E=g(v))&&L?L.p(v,w):(L.d(1),L=E(v),L&&(L.c(),L.m(o,h)))},i(v){j||(I(t.$$.fragment,v),j=!0)},o(v){C(t.$$.fragment,v),j=!1},d(v){v&&(b(e),b(n),b(i)),J(t),he(y,v),L.d(),l[12](null)}}}function _t(l){let e,t,n,i=l[18]+"",o,r,s=l[17]+"",a,_,c,f;return{c(){e=O(`
const response_`),t=O(l[21]),n=O(' = await fetch("'),o=O(i),r=O(`");
const example`),a=O(s),_=O(" = await response_"),c=O(l[21]),f=O(`.blob();
						`)},m(d,h){k(d,e,h),k(d,t,h),k(d,n,h),k(d,o,h),k(d,r,h),k(d,a,h),k(d,_,h),k(d,c,h),k(d,f,h)},p:X,d(d){d&&(b(e),b(t),b(n),b(o),b(r),b(a),b(_),b(c),b(f))}}}function Ll(l){let e;return{c(){e=O(l[1])},m(t,n){k(t,e,n)},p(t,n){n&2&&H(e,t[1])},d(t){t&&b(e)}}}function Tl(l){let e,t=l[0].api_name+"",n,i;return{c(){e=O('"/'),n=O(t),i=O('"')},m(o,r){k(o,e,r),k(o,n,r),k(o,i,r)},p(o,r){r&1&&t!==(t=o[0].api_name+"")&&H(n,t)},d(o){o&&(b(e),b(n),b(i))}}}function Pl(l){let e,t,n=Te(l[18],l[16].type,"js")+"",i,o,r,s,a=l[5][l[21]].type+"",_,c,f,d=l[14]+"",h,j,A=l[17]+"",y,g,E=l[5][l[21]].description&&ct(l);return{c(){e=O(`		
				`),t=T("span"),i=O(n),o=O(", "),r=T("span"),s=O("// "),_=O(a),c=O(" "),E&&E.c(),f=O(" in '"),h=O(d),j=O("' "),y=O(A),g=O(" component"),u(t,"class","example-inputs svelte-1d98qmk"),u(r,"class","desc svelte-1d98qmk")},m(L,v){k(L,e,v),k(L,t,v),p(t,i),k(L,o,v),k(L,r,v),p(r,s),p(r,_),p(r,c),E&&E.m(r,null),p(r,f),p(r,h),p(r,j),p(r,y),p(r,g)},p(L,v){v&16&&n!==(n=Te(L[18],L[16].type,"js")+"")&&H(i,n),v&32&&a!==(a=L[5][L[21]].type+"")&&H(_,a),L[5][L[21]].description?E?E.p(L,v):(E=ct(L),E.c(),E.m(r,f)):E&&(E.d(1),E=null),v&16&&d!==(d=L[14]+"")&&H(h,d),v&16&&A!==(A=L[17]+"")&&H(y,A)},d(L){L&&(b(e),b(t),b(o),b(r)),E&&E.d()}}}function Il(l){let e,t,n,i=l[17]+"",o,r,s,a,_=l[14]+"",c,f,d=l[17]+"",h,j;return{c(){e=O(`
				`),t=T("span"),n=O("example"),o=O(i),r=O(", "),s=T("span"),a=O("	// blob in '"),c=O(_),f=O("' "),h=O(d),j=O(" component"),u(t,"class","example-inputs svelte-1d98qmk"),u(s,"class","desc svelte-1d98qmk")},m(A,y){k(A,e,y),k(A,t,y),p(t,n),p(t,o),k(A,r,y),k(A,s,y),p(s,a),p(s,c),p(s,f),p(s,h),p(s,j)},p(A,y){y&16&&i!==(i=A[17]+"")&&H(o,i),y&16&&_!==(_=A[14]+"")&&H(c,_),y&16&&d!==(d=A[17]+"")&&H(h,d)},d(A){A&&(b(e),b(t),b(r),b(s))}}}function ct(l){let e,t=l[5][l[21]].description+"",n,i;return{c(){e=O("("),n=O(t),i=O(")")},m(o,r){k(o,e,r),k(o,n,r),k(o,i,r)},p(o,r){r&32&&t!==(t=o[5][o[21]].description+"")&&H(n,t)},d(o){o&&(b(e),b(n),b(i))}}}function ft(l){let e,t;function n(r,s){return s&16&&(e=null),e==null&&(e=!!r[10].includes(r[17])),e?Il:Pl}let i=n(l,-1),o=i(l);return{c(){o.c(),t=fe()},m(r,s){o.m(r,s),k(r,t,s)},p(r,s){i===(i=n(r,s))&&o?o.p(r,s):(o.d(1),o=i(r),o&&(o.c(),o.m(t.parentNode,t)))},d(r){r&&b(t),o.d(r)}}}function ut(l){let e;return{c(){e=T("span"),e.textContent="ERROR",u(e,"class","error svelte-1d98qmk")},m(t,n){k(t,e,n)},d(t){t&&b(e)}}}function dt(l){let e,t=l[16].description+"",n,i;return{c(){e=O("("),n=O(t),i=O(`)
								`)},m(o,r){k(o,e,r),k(o,n,r),k(o,i,r)},p(o,r){r&16&&t!==(t=o[16].description+"")&&H(n,t)},d(o){o&&(b(e),b(n),b(i))}}}function mt(l){let e,t,n=Te(l[18],l[16].type,"py")+"",i,o,r,s,a=l[16].type+"",_,c,f,d=l[14]+"",h,j,A=l[17]+"",y,g,E=l[3][l[1]][l[21]]&&ut(),L=l[16].description&&dt(l);return{c(){e=O(`
				`),t=T("span"),i=O(n),o=O(","),E&&E.c(),r=T("span"),s=O("	# "),_=O(a),c=O(" "),L&&L.c(),f=O("in '"),h=O(d),j=O("' "),y=O(A),g=O(" component"),u(t,"class","example-inputs svelte-1d98qmk"),u(r,"class","desc svelte-1d98qmk")},m(v,w){k(v,e,w),k(v,t,w),p(t,i),k(v,o,w),E&&E.m(v,w),k(v,r,w),p(r,s),p(r,_),p(r,c),L&&L.m(r,null),p(r,f),p(r,h),p(r,j),p(r,y),p(r,g)},p(v,w){w&16&&n!==(n=Te(v[18],v[16].type,"py")+"")&&H(i,n),v[3][v[1]][v[21]]?E||(E=ut(),E.c(),E.m(r.parentNode,r)):E&&(E.d(1),E=null),w&16&&a!==(a=v[16].type+"")&&H(_,a),v[16].description?L?L.p(v,w):(L=dt(v),L.c(),L.m(r,f)):L&&(L.d(1),L=null),w&16&&d!==(d=v[14]+"")&&H(h,d),w&16&&A!==(A=v[17]+"")&&H(y,A)},d(v){v&&(b(e),b(t),b(o),b(r)),E&&E.d(v),L&&L.d()}}}function Rl(l){let e,t;return{c(){e=O("fn_index="),t=O(l[1])},m(n,i){k(n,e,i),k(n,t,i)},p(n,i){i&2&&H(t,n[1])},d(n){n&&(b(e),b(t))}}}function Dl(l){let e,t=l[0].api_name+"",n,i;return{c(){e=O('api_name="/'),n=O(t),i=O('"')},m(o,r){k(o,e,r),k(o,n,r),k(o,i,r)},p(o,r){r&1&&t!==(t=o[0].api_name+"")&&H(n,t)},d(o){o&&(b(e),b(n),b(i))}}}function Vl(l){let e,t,n,i;const o=[Ol,Al],r=[];function s(a,_){return a[7]==="python"?0:a[7]==="javascript"?1:-1}return~(t=s(l))&&(n=r[t]=o[t](l)),{c(){e=T("code"),n&&n.c(),u(e,"class","svelte-1d98qmk")},m(a,_){k(a,e,_),~t&&r[t].m(e,null),i=!0},p(a,_){let c=t;t=s(a),t===c?~t&&r[t].p(a,_):(n&&(ne(),C(r[c],1,1,()=>{r[c]=null}),le()),~t?(n=r[t],n?n.p(a,_):(n=r[t]=o[t](a),n.c()),I(n,1),n.m(e,null)):n=null)},i(a){i||(I(n),i=!0)},o(a){C(n),i=!1},d(a){a&&b(e),~t&&r[t].d()}}}function ql(l){let e,t,n,i,o,r;const s=[jl,yl],a=[];function _(c,f){return c[6]?0:1}return t=_(l),n=a[t]=s[t](l),o=new Ve({props:{$$slots:{default:[Vl]},$$scope:{ctx:l}}}),{c(){e=T("div"),n.c(),i=M(),G(o.$$.fragment),u(e,"class","container svelte-1d98qmk")},m(c,f){k(c,e,f),a[t].m(e,null),p(e,i),W(o,e,null),r=!0},p(c,[f]){let d=t;t=_(c),t===d?a[t].p(c,f):(ne(),C(a[d],1,1,()=>{a[d]=null}),le(),n=a[t],n?n.p(c,f):(n=a[t]=s[t](c),n.c()),I(n,1),n.m(e,i));const h={};f&16778239&&(h.$$scope={dirty:f,ctx:c}),o.$set(h)},i(c){r||(I(n),I(o.$$.fragment,c),r=!0)},o(c){C(n),C(o.$$.fragment,c),r=!1},d(c){c&&b(e),a[t].d(),J(o)}}}function Cl(l,e,t){let{dependency:n}=e,{dependency_index:i}=e,{root:o}=e,{dependency_failures:r}=e,{endpoint_parameters:s}=e,{js_parameters:a}=e,{named:_}=e,{current_language:c}=e,f,d,h=["Audio","File","Image","Video"],j=s.filter(g=>h.includes(g.component));function A(g){ye[g?"unshift":"push"](()=>{f=g,t(8,f)})}function y(g){ye[g?"unshift":"push"](()=>{d=g,t(9,d)})}return l.$$set=g=>{"dependency"in g&&t(0,n=g.dependency),"dependency_index"in g&&t(1,i=g.dependency_index),"root"in g&&t(2,o=g.root),"dependency_failures"in g&&t(3,r=g.dependency_failures),"endpoint_parameters"in g&&t(4,s=g.endpoint_parameters),"js_parameters"in g&&t(5,a=g.js_parameters),"named"in g&&t(6,_=g.named),"current_language"in g&&t(7,c=g.current_language)},[n,i,o,r,s,a,_,c,f,d,h,j,A,y]}class Ft extends se{constructor(e){super(),re(this,e,Cl,ql,ae,{dependency:0,dependency_index:1,root:2,dependency_failures:3,endpoint_parameters:4,js_parameters:5,named:6,current_language:7})}}const Nl="https://gradio.s3-us-west-2.amazonaws.com/3.41.2/assets/python-20e39c92.svg",zl="https://gradio.s3-us-west-2.amazonaws.com/3.41.2/assets/javascript-850cf94b.svg";function pt(l,e,t){const n=l.slice();return n[18]=e[t],n[20]=t,n}function gt(l,e,t){const n=l.slice();return n[18]=e[t],n[20]=t,n}function ht(l,e,t){const n=l.slice();return n[22]=e[t][0],n[23]=e[t][1],n}function vt(l){let e,t,n,i,o;const r=[Ml,Sl],s=[];function a(_,c){return c&32&&(e=null),e==null&&(e=!!(Object.keys(_[5].named_endpoints).length+Object.keys(_[5].unnamed_endpoints).length)),e?0:1}return t=a(l,-1),n=s[t]=r[t](l),{c(){n.c(),i=fe()},m(_,c){s[t].m(_,c),k(_,i,c),o=!0},p(_,c){let f=t;t=a(_,c),t===f?s[t].p(_,c):(ne(),C(s[f],1,1,()=>{s[f]=null}),le(),n=s[t],n?n.p(_,c):(n=s[t]=r[t](_),n.c()),I(n,1),n.m(i.parentNode,i))},i(_){o||(I(n),o=!0)},o(_){C(n),o=!1},d(_){_&&b(i),s[t].d(_)}}}function Sl(l){let e,t;return e=new tl({props:{root:l[0]}}),e.$on("close",l[12]),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},p(n,i){const o={};i&1&&(o.root=n[0]),e.$set(o)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Ml(l){let e,t,n,i,o,r,s,a,_,c,f,d=Object.keys(l[5].named_endpoints).length,h,j,A=Object.keys(l[5].unnamed_endpoints).length,y,g;t=new il({props:{root:l[0],api_count:Object.keys(l[5].named_endpoints).length+Object.keys(l[5].unnamed_endpoints).length}}),t.$on("close",l[10]);let E=te(l[7]),L=[];for(let P=0;P<E.length;P+=1)L[P]=bt(ht(l,E,P));c=new vl({props:{current_language:l[2]}});let v=d&&kt(),w=te(l[1]),D=[];for(let P=0;P<w.length;P+=1)D[P]=Et(gt(l,w,P));const q=P=>C(D[P],1,1,()=>{D[P]=null});let F=A&&yt(),Q=te(l[1]),V=[];for(let P=0;P<Q.length;P+=1)V[P]=At(pt(l,Q,P));const _e=P=>C(V[P],1,1,()=>{V[P]=null});return{c(){e=T("div"),G(t.$$.fragment),n=M(),i=T("div"),o=T("div"),o.innerHTML=`<p>Use the <a href="https://gradio.app/docs/#python-client" target="_blank"><code class="library svelte-bdjvpc">gradio_client</code></a>
					Python library or the
					<a href="https://gradio.app/docs/#javascript-client" target="_blank"><code class="library svelte-bdjvpc">@gradio/client</code></a> Javascript package to query the demo via API.</p>`,r=M(),s=T("div"),a=T("div");for(let P=0;P<L.length;P+=1)L[P].c();_=M(),G(c.$$.fragment),f=M(),v&&v.c(),h=M();for(let P=0;P<D.length;P+=1)D[P].c();j=M(),F&&F.c(),y=M();for(let P=0;P<V.length;P+=1)V[P].c();u(e,"class","banner-wrap svelte-bdjvpc"),u(o,"class","client-doc svelte-bdjvpc"),u(a,"class","snippets svelte-bdjvpc"),u(s,"class","endpoint svelte-bdjvpc"),u(i,"class","docs-wrap svelte-bdjvpc")},m(P,U){k(P,e,U),W(t,e,null),k(P,n,U),k(P,i,U),p(i,o),p(i,r),p(i,s),p(s,a);for(let K=0;K<L.length;K+=1)L[K]&&L[K].m(a,null);p(s,_),W(c,s,null),p(s,f),v&&v.m(s,null),p(s,h);for(let K=0;K<D.length;K+=1)D[K]&&D[K].m(s,null);p(s,j),F&&F.m(s,null),p(s,y);for(let K=0;K<V.length;K+=1)V[K]&&V[K].m(s,null);g=!0},p(P,U){const K={};if(U&1&&(K.root=P[0]),U&32&&(K.api_count=Object.keys(P[5].named_endpoints).length+Object.keys(P[5].unnamed_endpoints).length),t.$set(K),U&132){E=te(P[7]);let R;for(R=0;R<E.length;R+=1){const Z=ht(P,E,R);L[R]?L[R].p(Z,U):(L[R]=bt(Z),L[R].c(),L[R].m(a,null))}for(;R<L.length;R+=1)L[R].d(1);L.length=E.length}const me={};if(U&4&&(me.current_language=P[2]),c.$set(me),U&32&&(d=Object.keys(P[5].named_endpoints).length),d?v||(v=kt(),v.c(),v.m(s,h)):v&&(v.d(1),v=null),U&127){w=te(P[1]);let R;for(R=0;R<w.length;R+=1){const Z=gt(P,w,R);D[R]?(D[R].p(Z,U),I(D[R],1)):(D[R]=Et(Z),D[R].c(),I(D[R],1),D[R].m(s,j))}for(ne(),R=w.length;R<D.length;R+=1)q(R);le()}if(U&32&&(A=Object.keys(P[5].unnamed_endpoints).length),A?F||(F=yt(),F.c(),F.m(s,y)):F&&(F.d(1),F=null),U&127){Q=te(P[1]);let R;for(R=0;R<Q.length;R+=1){const Z=pt(P,Q,R);V[R]?(V[R].p(Z,U),I(V[R],1)):(V[R]=At(Z),V[R].c(),I(V[R],1),V[R].m(s,null))}for(ne(),R=Q.length;R<V.length;R+=1)_e(R);le()}},i(P){if(!g){I(t.$$.fragment,P),I(c.$$.fragment,P);for(let U=0;U<w.length;U+=1)I(D[U]);for(let U=0;U<Q.length;U+=1)I(V[U]);g=!0}},o(P){C(t.$$.fragment,P),C(c.$$.fragment,P),D=D.filter(Boolean);for(let U=0;U<D.length;U+=1)C(D[U]);V=V.filter(Boolean);for(let U=0;U<V.length;U+=1)C(V[U]);g=!1},d(P){P&&(b(e),b(n),b(i)),J(t),he(L,P),J(c),v&&v.d(),he(D,P),F&&F.d(),he(V,P)}}}function bt(l){let e,t,n,i,o=l[22]+"",r,s,a,_,c;function f(){return l[11](l[22])}return{c(){e=T("li"),t=T("img"),i=M(),r=O(o),s=M(),Ae(t.src,n=l[23])||u(t,"src",n),u(t,"alt",""),u(t,"class","svelte-bdjvpc"),u(e,"class",a="snippet "+(l[2]===l[22]?"current-lang":"inactive-lang")+" svelte-bdjvpc")},m(d,h){k(d,e,h),p(e,t),p(e,i),p(e,r),p(e,s),_||(c=pe(e,"click",f),_=!0)},p(d,h){l=d,h&4&&a!==(a="snippet "+(l[2]===l[22]?"current-lang":"inactive-lang")+" svelte-bdjvpc")&&u(e,"class",a)},d(d){d&&b(e),_=!1,c()}}}function kt(l){let e;return{c(){e=T("h2"),e.textContent="Named Endpoints",u(e,"class","header svelte-bdjvpc")},m(t,n){k(t,e,n)},d(t){t&&b(e)}}}function wt(l){let e,t,n,i,o;return t=new Ft({props:{named:!0,endpoint_parameters:l[5].named_endpoints["/"+l[18].api_name].parameters,js_parameters:l[6].named_endpoints["/"+l[18].api_name].parameters,dependency:l[18],dependency_index:l[20],current_language:l[2],root:l[0],dependency_failures:l[4]}}),i=new Bt({props:{endpoint_returns:l[5].named_endpoints["/"+l[18].api_name].returns,js_returns:l[6].named_endpoints["/"+l[18].api_name].returns,is_running:l[3],current_language:l[2]}}),{c(){e=T("div"),G(t.$$.fragment),n=M(),G(i.$$.fragment),u(e,"class","endpoint-container svelte-bdjvpc")},m(r,s){k(r,e,s),W(t,e,null),p(e,n),W(i,e,null),o=!0},p(r,s){const a={};s&34&&(a.endpoint_parameters=r[5].named_endpoints["/"+r[18].api_name].parameters),s&66&&(a.js_parameters=r[6].named_endpoints["/"+r[18].api_name].parameters),s&2&&(a.dependency=r[18]),s&4&&(a.current_language=r[2]),s&1&&(a.root=r[0]),s&16&&(a.dependency_failures=r[4]),t.$set(a);const _={};s&34&&(_.endpoint_returns=r[5].named_endpoints["/"+r[18].api_name].returns),s&66&&(_.js_returns=r[6].named_endpoints["/"+r[18].api_name].returns),s&8&&(_.is_running=r[3]),s&4&&(_.current_language=r[2]),i.$set(_)},i(r){o||(I(t.$$.fragment,r),I(i.$$.fragment,r),o=!0)},o(r){C(t.$$.fragment,r),C(i.$$.fragment,r),o=!1},d(r){r&&b(e),J(t),J(i)}}}function Et(l){let e,t,n=l[18].api_name&&wt(l);return{c(){n&&n.c(),e=fe()},m(i,o){n&&n.m(i,o),k(i,e,o),t=!0},p(i,o){i[18].api_name?n?(n.p(i,o),o&2&&I(n,1)):(n=wt(i),n.c(),I(n,1),n.m(e.parentNode,e)):n&&(ne(),C(n,1,1,()=>{n=null}),le())},i(i){t||(I(n),t=!0)},o(i){C(n),t=!1},d(i){i&&b(e),n&&n.d(i)}}}function yt(l){let e;return{c(){e=T("h2"),e.textContent="Unnamed Endpoints",u(e,"class","header svelte-bdjvpc")},m(t,n){k(t,e,n)},d(t){t&&b(e)}}}function jt(l){let e,t,n,i,o,r;return t=new Ft({props:{named:!1,endpoint_parameters:l[5].unnamed_endpoints[l[20]].parameters,js_parameters:l[6].unnamed_endpoints[l[20]].parameters,dependency:l[18],dependency_index:l[20],current_language:l[2],root:l[0],dependency_failures:l[4]}}),i=new Bt({props:{endpoint_returns:l[5].unnamed_endpoints[l[20]].returns,js_returns:l[6].unnamed_endpoints[l[20]].returns,is_running:l[3],current_language:l[2]}}),{c(){e=T("div"),G(t.$$.fragment),n=M(),G(i.$$.fragment),o=M(),u(e,"class","endpoint-container svelte-bdjvpc")},m(s,a){k(s,e,a),W(t,e,null),p(e,n),W(i,e,null),p(e,o),r=!0},p(s,a){const _={};a&32&&(_.endpoint_parameters=s[5].unnamed_endpoints[s[20]].parameters),a&64&&(_.js_parameters=s[6].unnamed_endpoints[s[20]].parameters),a&2&&(_.dependency=s[18]),a&4&&(_.current_language=s[2]),a&1&&(_.root=s[0]),a&16&&(_.dependency_failures=s[4]),t.$set(_);const c={};a&32&&(c.endpoint_returns=s[5].unnamed_endpoints[s[20]].returns),a&64&&(c.js_returns=s[6].unnamed_endpoints[s[20]].returns),a&8&&(c.is_running=s[3]),a&4&&(c.current_language=s[2]),i.$set(c)},i(s){r||(I(t.$$.fragment,s),I(i.$$.fragment,s),r=!0)},o(s){C(t.$$.fragment,s),C(i.$$.fragment,s),r=!1},d(s){s&&b(e),J(t),J(i)}}}function At(l){let e,t,n=l[5].unnamed_endpoints[l[20]]&&jt(l);return{c(){n&&n.c(),e=fe()},m(i,o){n&&n.m(i,o),k(i,e,o),t=!0},p(i,o){i[5].unnamed_endpoints[i[20]]?n?(n.p(i,o),o&32&&I(n,1)):(n=jt(i),n.c(),I(n,1),n.m(e.parentNode,e)):n&&(ne(),C(n,1,1,()=>{n=null}),le())},i(i){t||(I(n),t=!0)},o(i){C(n),t=!1},d(i){i&&b(e),n&&n.d(i)}}}function Bl(l){let e,t,n=l[5]&&vt(l);return{c(){n&&n.c(),e=fe()},m(i,o){n&&n.m(i,o),k(i,e,o),t=!0},p(i,[o]){i[5]?n?(n.p(i,o),o&32&&I(n,1)):(n=vt(i),n.c(),I(n,1),n.m(e.parentNode,e)):n&&(ne(),C(n,1,1,()=>{n=null}),le())},i(i){t||(I(n),t=!0)},o(i){C(n),t=!1},d(i){i&&b(e),n&&n.d(i)}}}function Ul(l,e,t){let{instance_map:n}=e,{dependencies:i}=e,{root:o}=e,{app:r}=e;o===""&&(o=location.protocol+"//"+location.host+location.pathname),o.endsWith("/")||(o+="/");let s="python";const a=[["python",Nl],["javascript",zl]];let _=!1;i.map(E=>E.inputs.map(L=>{let v=n[L].documentation?.example_data;return v===void 0?v="":typeof v=="object"&&(v=JSON.stringify(v)),v})),i.map(E=>new Array(E.outputs.length));let c=i.map(E=>new Array(E.inputs.length).fill(!1));async function f(){return await(await fetch(o+"info")).json()}async function d(){return await r.view_api()}let h,j;f().then(E=>t(5,h=E)),d().then(E=>t(6,j=E)),Pe(()=>(document.body.style.overflow="hidden","parentIFrame"in window&&window.parentIFrame?.scrollTo(0,0),()=>{document.body.style.overflow="auto"}));function A(E){ve.call(this,l,E)}const y=E=>t(2,s=E);function g(E){ve.call(this,l,E)}return l.$$set=E=>{"instance_map"in E&&t(8,n=E.instance_map),"dependencies"in E&&t(1,i=E.dependencies),"root"in E&&t(0,o=E.root),"app"in E&&t(9,r=E.app)},[o,i,s,_,c,h,j,a,n,r,A,y,g]}class Fl extends se{constructor(e){super(),re(this,e,Ul,Bl,ae,{instance_map:8,dependencies:1,root:0,app:9})}}function Ot(l,e,t){const n=l.slice();return n[9]=e[t].component,n[18]=e[t].id,n[2]=e[t].props,n[19]=e[t].children,n[20]=e[t].has_modes,n}function Lt(l){let e=[],t=new Map,n,i,o=te(l[1]);const r=s=>s[18];for(let s=0;s<o.length;s+=1){let a=Ot(l,o,s),_=r(a);t.set(_,e[s]=Tt(_,a))}return{c(){for(let s=0;s<e.length;s+=1)e[s].c();n=fe()},m(s,a){for(let _=0;_<e.length;_+=1)e[_]&&e[_].m(s,a);k(s,n,a),i=!0},p(s,a){a&235&&(o=te(s[1]),ne(),e=zt(e,a,r,1,s,o,t,n.parentNode,yn,Tt,n,Ot),le())},i(s){if(!i){for(let a=0;a<o.length;a+=1)I(e[a]);i=!0}},o(s){for(let a=0;a<e.length;a+=1)C(e[a]);i=!1},d(s){s&&b(n);for(let a=0;a<e.length;a+=1)e[a].d(s)}}}function Tt(l,e){let t,n,i;return n=new Ht({props:{component:e[9],target:e[6],id:e[18],props:e[2],root:e[3],instance_map:e[0],children:e[19],dynamic_ids:e[5],has_modes:e[20],theme_mode:e[7]}}),n.$on("destroy",e[12]),n.$on("mount",e[13]),{key:l,first:null,c(){t=fe(),G(n.$$.fragment),this.first=t},m(o,r){k(o,t,r),W(n,o,r),i=!0},p(o,r){e=o;const s={};r&2&&(s.component=e[9]),r&64&&(s.target=e[6]),r&2&&(s.id=e[18]),r&2&&(s.props=e[2]),r&8&&(s.root=e[3]),r&1&&(s.instance_map=e[0]),r&2&&(s.children=e[19]),r&32&&(s.dynamic_ids=e[5]),r&2&&(s.has_modes=e[20]),r&128&&(s.theme_mode=e[7]),n.$set(s)},i(o){i||(I(n.$$.fragment,o),i=!0)},o(o){C(n.$$.fragment,o),i=!1},d(o){o&&b(t),J(n,o)}}}function Hl(l){let e,t,n=l[1]&&l[1].length&&Lt(l);return{c(){n&&n.c(),e=fe()},m(i,o){n&&n.m(i,o),k(i,e,o),t=!0},p(i,o){i[1]&&i[1].length?n?(n.p(i,o),o&2&&I(n,1)):(n=Lt(i),n.c(),I(n,1),n.m(e.parentNode,e)):n&&(ne(),C(n,1,1,()=>{n=null}),le())},i(i){t||(I(n),t=!0)},o(i){C(n),t=!1},d(i){i&&b(e),n&&n.d(i)}}}function Gl(l){let e,t,n,i;const o=[{elem_id:"elem_id"in l[2]&&l[2].elem_id||`component-${l[4]}`},{elem_classes:"elem_classes"in l[2]&&l[2].elem_classes||[]},{target:l[6]},l[2],{theme_mode:l[7]},{root:l[3]},{gradio:new Ye(l[4],l[6],l[7],l[8],l[3])}];function r(_){l[15](_)}var s=l[9];function a(_){let c={$$slots:{default:[Hl]},$$scope:{ctx:_}};for(let f=0;f<o.length;f+=1)c=En(c,o[f]);return _[0][_[4]].props.value!==void 0&&(c.value=_[0][_[4]].props.value),{props:c}}return s&&(e=He(s,a(l)),l[14](e),ye.push(()=>Ge(e,"value",r)),e.$on("prop_change",l[10])),{c(){e&&G(e.$$.fragment),n=fe()},m(_,c){e&&W(e,_,c),k(_,n,c),i=!0},p(_,[c]){const f=c&476?vn(o,[c&20&&{elem_id:"elem_id"in _[2]&&_[2].elem_id||`component-${_[4]}`},c&4&&{elem_classes:"elem_classes"in _[2]&&_[2].elem_classes||[]},c&64&&{target:_[6]},c&4&&bn(_[2]),c&128&&{theme_mode:_[7]},c&8&&{root:_[3]},c&472&&{gradio:new Ye(_[4],_[6],_[7],_[8],_[3])}]):{};if(c&8388843&&(f.$$scope={dirty:c,ctx:_}),!t&&c&17&&(t=!0,f.value=_[0][_[4]].props.value,kn(()=>t=!1)),c&512&&s!==(s=_[9])){if(e){ne();const d=e;C(d.$$.fragment,1,0,()=>{J(d,1)}),le()}s?(e=He(s,a(_)),_[14](e),ye.push(()=>Ge(e,"value",r)),e.$on("prop_change",_[10]),G(e.$$.fragment),I(e.$$.fragment,1),W(e,n.parentNode,n)):e=null}else s&&e.$set(f)},i(_){i||(e&&I(e.$$.fragment,_),i=!0)},o(_){e&&C(e.$$.fragment,_),i=!1},d(_){_&&b(n),l[14](null),e&&J(e,_)}}}function Wl(l,e,t){let{root:n}=e,{component:i}=e,{instance_map:o}=e,{id:r}=e,{props:s}=e,{children:a}=e,{dynamic_ids:_}=e,{parent:c=null}=e,{target:f}=e,{theme_mode:d}=e,{version:h}=e;const j=je();let A=[];Pe(()=>{j("mount",r);for(const w of A)j("mount",w.id);return()=>{j("destroy",r);for(const w of A)j("mount",w.id)}}),wn("BLOCK_KEY",c);function y(w){for(const D in w.detail)t(0,o[r].props[D]=w.detail[D],o)}function g(w){ve.call(this,l,w)}function E(w){ve.call(this,l,w)}function L(w){ye[w?"unshift":"push"](()=>{o[r].instance=w,t(0,o)})}function v(w){l.$$.not_equal(o[r].props.value,w)&&(o[r].props.value=w,t(0,o))}return l.$$set=w=>{"root"in w&&t(3,n=w.root),"component"in w&&t(9,i=w.component),"instance_map"in w&&t(0,o=w.instance_map),"id"in w&&t(4,r=w.id),"props"in w&&t(2,s=w.props),"children"in w&&t(1,a=w.children),"dynamic_ids"in w&&t(5,_=w.dynamic_ids),"parent"in w&&t(11,c=w.parent),"target"in w&&t(6,f=w.target),"theme_mode"in w&&t(7,d=w.theme_mode),"version"in w&&t(8,h=w.version)},l.$$.update=()=>{l.$$.dirty&3&&t(1,a=a&&a.filter(w=>{const D=o[w.id].type!=="statustracker";return D||A.push(w),D})),l.$$.dirty&19&&o[r].type==="form"&&(a?.every(w=>!w.props.visible)?t(2,s.visible=!1,s):t(2,s.visible=!0,s))},[o,a,s,n,r,_,f,d,h,i,y,c,g,E,L,v]}class Ht extends se{constructor(e){super(),re(this,e,Wl,Gl,ae,{root:3,component:9,instance_map:0,id:4,props:2,children:1,dynamic_ids:5,parent:11,target:6,theme_mode:7,version:8})}}function Jl(l){let e,t;return e=new Ht({props:{component:l[0].component,id:l[0].id,props:l[0].props,children:l[0].children,dynamic_ids:l[1],instance_map:l[2],root:l[3],target:l[4],theme_mode:l[5],version:l[6]}}),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},p(n,[i]){const o={};i&1&&(o.component=n[0].component),i&1&&(o.id=n[0].id),i&1&&(o.props=n[0].props),i&1&&(o.children=n[0].children),i&2&&(o.dynamic_ids=n[1]),i&4&&(o.instance_map=n[2]),i&8&&(o.root=n[3]),i&16&&(o.target=n[4]),i&32&&(o.theme_mode=n[5]),i&64&&(o.version=n[6]),e.$set(o)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Ql(l,e,t){let{rootNode:n}=e,{dynamic_ids:i}=e,{instance_map:o}=e,{root:r}=e,{target:s}=e,{theme_mode:a}=e,{version:_}=e;const c=je();return Pe(()=>{c("mount")}),l.$$set=f=>{"rootNode"in f&&t(0,n=f.rootNode),"dynamic_ids"in f&&t(1,i=f.dynamic_ids),"instance_map"in f&&t(2,o=f.instance_map),"root"in f&&t(3,r=f.root),"target"in f&&t(4,s=f.target),"theme_mode"in f&&t(5,a=f.theme_mode),"version"in f&&t(6,_=f.version)},[n,i,o,r,s,a,_]}class Kl extends se{constructor(e){super(),re(this,e,Ql,Jl,ae,{rootNode:0,dynamic_ids:1,instance_map:2,root:3,target:4,theme_mode:5,version:6})}}const Zl="https://gradio.s3-us-west-2.amazonaws.com/3.41.2/assets/logo-0a070fcf.svg";const{document:Ee}=Tn;function Pt(l){return Ee.title=l[3],{c:X,m:X,d:X}}function It(l){let e,t,n,i;return{c(){e=T("script"),e.innerHTML="",n=M(),i=T("script"),i.textContent=`window.dataLayer = window.dataLayer || [];
			function gtag() {
				dataLayer.push(arguments);
			}
			gtag("js", new Date());
			gtag("config", "UA-156449732-1");`,e.async=!0,e.defer=!0,Ae(e.src,t="https://www.googletagmanager.com/gtag/js?id=UA-156449732-1")||u(e,"src",t)},m(o,r){k(o,e,r),k(o,n,r),k(o,i,r)},d(o){o&&(b(e),b(n),b(i))}}}function Rt(l){let e,t;return e=new Kl({props:{rootNode:l[13],dynamic_ids:l[19],instance_map:l[20],root:l[1],target:l[5],theme_mode:l[10],version:l[12]}}),e.$on("mount",l[22]),e.$on("destroy",l[30]),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},p(n,i){const o={};i[0]&8192&&(o.rootNode=n[13]),i[0]&2&&(o.root=n[1]),i[0]&32&&(o.target=n[5]),i[0]&1024&&(o.theme_mode=n[10]),i[0]&4096&&(o.version=n[12]),e.$set(o)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Dt(l){let e,t,n,i=l[16]("common.built_with_gradio")+"",o,r,s,a,_,c=l[6]&&Vt(l);return{c(){e=T("footer"),c&&c.c(),t=M(),n=T("a"),o=O(i),r=M(),s=T("img"),Ae(s.src,a=Zl)||u(s,"src",a),u(s,"alt",_=l[16]("common.logo")),u(s,"class","svelte-1ax1toq"),u(n,"href","https://gradio.app"),u(n,"class","built-with svelte-1ax1toq"),u(n,"target","_blank"),u(n,"rel","noreferrer"),u(e,"class","svelte-1ax1toq")},m(f,d){k(f,e,d),c&&c.m(e,null),p(e,t),p(e,n),p(n,o),p(n,r),p(n,s)},p(f,d){f[6]?c?c.p(f,d):(c=Vt(f),c.c(),c.m(e,t)):c&&(c.d(1),c=null),d[0]&65536&&i!==(i=f[16]("common.built_with_gradio")+"")&&H(o,i),d[0]&65536&&_!==(_=f[16]("common.logo"))&&u(s,"alt",_)},d(f){f&&b(e),c&&c.d()}}}function Vt(l){let e,t=l[16]("errors.use_via_api")+"",n,i,o,r,s,a,_,c,f;return{c(){e=T("button"),n=O(t),i=M(),o=T("img"),a=M(),_=T("div"),_.textContent="·",Ae(o.src,r=Mt)||u(o,"src",r),u(o,"alt",s=l[16]("common.logo")),u(o,"class","svelte-1ax1toq"),u(e,"class","show-api svelte-1ax1toq"),u(_,"class","svelte-1ax1toq")},m(d,h){k(d,e,h),p(e,n),p(e,i),p(e,o),k(d,a,h),k(d,_,h),c||(f=pe(e,"click",l[31]),c=!0)},p(d,h){h[0]&65536&&t!==(t=d[16]("errors.use_via_api")+"")&&H(n,t),h[0]&65536&&s!==(s=d[16]("common.logo"))&&u(o,"alt",s)},d(d){d&&(b(e),b(a),b(_)),c=!1,f()}}}function qt(l){let e,t,n,i,o,r,s,a;return o=new Fl({props:{instance_map:l[20],dependencies:l[2],root:l[1],app:l[11]}}),o.$on("close",l[33]),{c(){e=T("div"),t=T("div"),n=M(),i=T("div"),G(o.$$.fragment),u(t,"class","backdrop svelte-1ax1toq"),u(i,"class","api-docs-wrap svelte-1ax1toq"),u(e,"class","api-docs svelte-1ax1toq")},m(_,c){k(_,e,c),p(e,t),p(e,n),p(e,i),W(o,i,null),r=!0,s||(a=pe(t,"click",l[32]),s=!0)},p(_,c){const f={};c[0]&4&&(f.dependencies=_[2]),c[0]&2&&(f.root=_[1]),c[0]&2048&&(f.app=_[11]),o.$set(f)},i(_){r||(I(o.$$.fragment,_),r=!0)},o(_){C(o.$$.fragment,_),r=!1},d(_){_&&b(e),J(o),s=!1,a()}}}function Ct(l){let e,t;return e=new Yn({props:{messages:l[15]}}),e.$on("close",l[21]),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},p(n,i){const o={};i[0]&32768&&(o.messages=n[15]),e.$set(o)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Xl(l){let e,t,n,i,o,r,s,a,_,c,f=l[8]&&Pt(l),d=l[4]&&It(),h=l[0]&&Rt(l),j=l[7]&&Dt(l),A=l[14]&&l[0]&&qt(l),y=l[15]&&Ct(l);return{c(){f&&f.c(),e=fe(),d&&d.c(),t=fe(),n=M(),i=T("div"),o=T("div"),h&&h.c(),r=M(),j&&j.c(),s=M(),A&&A.c(),a=M(),y&&y.c(),_=fe(),u(o,"class","contain"),ue(o,"flex-grow",l[9]?"1":"auto"),u(i,"class","wrap svelte-1ax1toq"),ue(i,"min-height",l[9]?"100%":"auto")},m(g,E){f&&f.m(Ee.head,null),p(Ee.head,e),d&&d.m(Ee.head,null),p(Ee.head,t),k(g,n,E),k(g,i,E),p(i,o),h&&h.m(o,null),p(i,r),j&&j.m(i,null),k(g,s,E),A&&A.m(g,E),k(g,a,E),y&&y.m(g,E),k(g,_,E),c=!0},p(g,E){g[8]?f||(f=Pt(g),f.c(),f.m(e.parentNode,e)):f&&(f.d(1),f=null),g[4]?d||(d=It(),d.c(),d.m(t.parentNode,t)):d&&(d.d(1),d=null),g[0]?h?(h.p(g,E),E[0]&1&&I(h,1)):(h=Rt(g),h.c(),I(h,1),h.m(o,null)):h&&(ne(),C(h,1,1,()=>{h=null}),le()),E[0]&512&&ue(o,"flex-grow",g[9]?"1":"auto"),g[7]?j?j.p(g,E):(j=Dt(g),j.c(),j.m(i,null)):j&&(j.d(1),j=null),E[0]&512&&ue(i,"min-height",g[9]?"100%":"auto"),g[14]&&g[0]?A?(A.p(g,E),E[0]&16385&&I(A,1)):(A=qt(g),A.c(),I(A,1),A.m(a.parentNode,a)):A&&(ne(),C(A,1,1,()=>{A=null}),le()),g[15]?y?(y.p(g,E),E[0]&32768&&I(y,1)):(y=Ct(g),y.c(),I(y,1),y.m(_.parentNode,_)):y&&(ne(),C(y,1,1,()=>{y=null}),le())},i(g){c||(I(h),I(A),I(y),c=!0)},o(g){C(h),C(A),C(y),c=!1},d(g){g&&(b(n),b(i),b(s),b(a),b(_)),f&&f.d(g),b(e),d&&d.d(g),b(t),h&&h.d(),j&&j.d(),A&&A.d(g),y&&y.d(g)}}}const Yl=/^'([^]+)'$/,$l=15,xl=10;function Nt(l,e,t){for(const n of t)for(const i of n[e])if(i===l)return!0;return!1}function ei(l){return Array.isArray(l)&&l.length===0||l===""||l===0||!l}function ti(l){return"detail"in l}function ni(l,e,t){let n,i;We(l,jn,m=>t(16,i=m)),An();let{root:o}=e,{components:r}=e,{layout:s}=e,{dependencies:a}=e,{title:_="Gradio"}=e,{analytics_enabled:c=!1}=e,{target:f}=e,{autoscroll:d}=e,{show_api:h=!0}=e,{show_footer:j=!0}=e,{control_page_title:A=!1}=e,{app_mode:y}=e,{theme_mode:g}=e,{app:E}=e,{space_id:L}=e,{version:v}=e,w=On();We(l,w,m=>t(29,n=m));let D={id:s.id,type:"column",props:{mode:"static"},has_modes:!1,instance:{},component:{}};r.push(D);const q=Object.getPrototypeOf(async function(){}).constructor;a.forEach(m=>{if(m.js){const S=m.backend_fn?m.inputs.length===1:m.outputs.length===1;try{m.frontend_fn=new q("__fn_args",`let result = await (${m.js})(...__fn_args);
					return (${S} && !Array.isArray(result)) ? [result] : result;`)}catch(z){console.error("Could not parse custom js method."),console.error(z)}}});let Q=new URLSearchParams(window.location.search).get("view")==="api"&&h;function V(m){t(14,Q=m);let S=new URLSearchParams(window.location.search);m?S.set("view","api"):S.delete("view"),history.replaceState(null,"","?"+S.toString())}const _e=new Set;for(const m of r){const{id:S,props:z}=m;(Nt(S,"inputs",a)||!Nt(S,"outputs",a)&&ei(z?.value))&&_e.add(S)}let P=r.reduce((m,S)=>(m[S.id]=S,m),{});async function U(m,S){try{const z=await Ze[m][S]();return{name:m,component:z}}catch(z){if(S==="interactive")try{const B=await Ze[m].static();return{name:m,component:B}}catch(B){throw console.error(`failed to load: ${m}`),console.error(B),B}else throw console.error(`failed to load: ${m}`),console.error(z),z}}const K=new Set,me=new Map,R=new Map;async function Z(m){let S=P[m.id];const z=(await me.get(`${S.type}_${R.get(m.id)||"static"}`)).component;S.component=z.default,m.children&&(S.children=m.children.map(B=>P[B.id]),await Promise.all(m.children.map(B=>Z(B))))}r.forEach(m=>{m.props.interactive===!1?m.props.mode="static":m.props.interactive===!0||_e.has(m.id)?m.props.mode="interactive":m.props.mode="static",R.set(m.id,m.props.mode);const S=U(m.type,m.props.mode);K.add(S),me.set(`${m.type}_${m.props.mode}`,S)});let{ready:ge=!1}=e,{render_complete:Re=!1}=e;Promise.all(Array.from(K)).then(()=>{Z(s).then(async()=>{t(0,ge=!0)}).catch(m=>{console.error(m)})});async function Gt(m,S){let z=S==="dynamic"?"interactive":S;if(m.props.mode===z)return;m.props.mode=z;const B=U(m.type,m.props.mode);K.add(B),me.set(`${m.type}_${m.props.mode}`,B),B.then(ee=>{m.component=ee.component.default,t(13,D)})}function qe(m,S){const z=a[S].outputs;m?.forEach((B,ee)=>{const ce=P[z[ee]];if(ce.props.value_is_output=!0,typeof B=="object"&&B!==null&&B.__type__==="update")for(const[x,Y]of Object.entries(B))x!=="__type__"&&(x==="mode"&&Gt(ce,Y),ce.props[x]=Y);else ce.props.value=B}),t(13,D)}let Ce=new Map;function Ne(m,S,z){m?.props||(m.props={}),m.props[S]=z,t(13,D)}let ze=[],ie=[];function be(m,S,z){return{message:m,fn_index:S,type:z,id:++Wt}}let Wt=-1,De=!1;document.addEventListener("visibilitychange",function(){document.visibilityState==="hidden"&&(De=!0)});const Jt=i("blocks.long_requests_queue"),Qt=i("blocks.connection_can_break"),Kt=i("blocks.lost_connection"),Se=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);let Me=!1,Be=!1;async function we(m,S=null){let z=a[m];const B=w.get_status_for_fn(m);if(t(15,ie=ie.filter(({fn_index:x})=>x!==m)),z.cancels&&await Promise.all(z.cancels.map(async x=>{const Y=Ce.get(x);return Y?.cancel(),Y})),B==="pending"||B==="generating")return;let ee={fn_index:m,data:z.inputs.map(x=>P[x].props.value),event_data:z.collects_event_data?S:null};z.frontend_fn?z.frontend_fn(ee.data.concat(z.outputs.map(x=>P[x].props.value))).then(x=>{z.backend_fn?(ee.data=x,ce()):qe(x,m)}):z.backend_fn&&ce();function ce(){const x=E.submit(ee.fn_index,ee.data,ee.event_data).on("data",({data:Y,fn_index:$})=>{qe(Y,$)}).on("status",({fn_index:Y,...$})=>{if(w.update({...$,status:$.stage,progress:$.progress_data,fn_index:Y}),!Me&&L!==null&&$.position!==void 0&&$.position>=2&&$.eta!==void 0&&$.eta>$l&&(Me=!0,t(15,ie=[be(Jt,Y,"warning"),...ie])),!Be&&Se&&$.eta!==void 0&&$.eta>xl&&(Be=!0,t(15,ie=[be(Qt,Y,"warning"),...ie])),$.stage==="complete"&&(a.map(async(oe,Oe)=>{oe.trigger_after===Y&&we(Oe)}),x.destroy()),$.broken&&Se&&De)window.setTimeout(()=>{t(15,ie=[be(Kt,Y,"error"),...ie])},0),we(m,S),De=!1;else if($.stage==="error"){if($.message){const oe=$.message.replace(Yl,(Oe,on)=>on);t(15,ie=[be(oe,Y,"error"),...ie])}a.map(async(oe,Oe)=>{oe.trigger_after===Y&&!oe.trigger_only_on_success&&we(Oe)}),x.destroy()}}).on("log",({log:Y,fn_index:$,level:oe})=>{t(15,ie=[be(Y,$,oe),...ie])});Ce.set(m,x)}}function Zt(m,S){if(L===null)return;const z=new URL(`https://huggingface.co/spaces/${L}/discussions/new`);m!==void 0&&m.length>0&&z.searchParams.set("title",m),z.searchParams.set("description",S),window.open(z.toString(),"_blank")}function Xt(m){const S=m.detail;t(15,ie=ie.filter(z=>z.id!==S))}const Yt=m=>!!(m&&new URL(m,location.href).origin!==location.origin);async function $t(){await Pn();for(var m=f.getElementsByTagName("a"),S=0;S<m.length;S++){const z=m[S].getAttribute("target"),B=m[S].getAttribute("href");Yt(B)&&z!=="_blank"&&m[S].setAttribute("target","_blank")}a.forEach((z,B)=>{z.targets.length===0&&z.trigger==="load"&&we(B)}),a.forEach((z,B)=>{let{targets:ee,trigger:ce,inputs:x,outputs:Y}=z;ee.map(oe=>[oe,P[oe]]).forEach(([oe])=>{ke[oe]||(ke[oe]={}),ke[oe]?.[ce]?ke[oe][ce].push(B):ke[oe][ce]=[B]})}),f.addEventListener("gradio",z=>{if(!ti(z))throw new Error("not a custom event");const{id:B,event:ee,data:ce}=z.detail;if(ee==="share"){const{title:Y,description:$}=ce;Zt(Y,$)}else ee==="error"&&t(15,ie=[be(ce,-1,"error"),...ie]);ke[B]?.[ee]?.forEach(Y=>{we(Y,ce)})}),t(24,Re=!0)}function Ue(m){ze=ze.map(S=>S.filter(z=>z!==m))}a.forEach((m,S)=>{w.register(S,m.inputs,m.outputs)});function xt(m){for(const z in m){let B=m[z],ee=a[B.fn_index];B.scroll_to_output=ee.scroll_to_output,B.show_progress=ee.show_progress,Ne(P[z],"loading_status",B)}const S=w.get_inputs_to_update();for(const[z,B]of S)Ne(P[z],"pending",B==="pending")}const ke={},en=({detail:m})=>Ue(m),tn=()=>{V(!Q)},nn=()=>{V(!1)},ln=()=>{V(!1)};return l.$$set=m=>{"root"in m&&t(1,o=m.root),"components"in m&&t(25,r=m.components),"layout"in m&&t(26,s=m.layout),"dependencies"in m&&t(2,a=m.dependencies),"title"in m&&t(3,_=m.title),"analytics_enabled"in m&&t(4,c=m.analytics_enabled),"target"in m&&t(5,f=m.target),"autoscroll"in m&&t(27,d=m.autoscroll),"show_api"in m&&t(6,h=m.show_api),"show_footer"in m&&t(7,j=m.show_footer),"control_page_title"in m&&t(8,A=m.control_page_title),"app_mode"in m&&t(9,y=m.app_mode),"theme_mode"in m&&t(10,g=m.theme_mode),"app"in m&&t(11,E=m.app),"space_id"in m&&t(28,L=m.space_id),"version"in m&&t(12,v=m.version),"ready"in m&&t(0,ge=m.ready),"render_complete"in m&&t(24,Re=m.render_complete)},l.$$.update=()=>{l.$$.dirty[0]&134217728&&Ln.update(m=>({...m,autoscroll:d})),l.$$.dirty[0]&536870912&&xt(n)},[ge,o,a,_,c,f,h,j,A,y,g,E,v,D,Q,ie,i,w,V,_e,P,Xt,$t,Ue,Re,r,s,d,L,n,en,tn,nn,ln]}class li extends se{constructor(e){super(),re(this,e,ni,Xl,ae,{root:1,components:25,layout:26,dependencies:2,title:3,analytics_enabled:4,target:5,autoscroll:27,show_api:6,show_footer:7,control_page_title:8,app_mode:9,theme_mode:10,app:11,space_id:28,version:12,ready:0,render_complete:24},null,[-1,-1])}}const ri=Object.freeze(Object.defineProperty({__proto__:null,default:li},Symbol.toStringTag,{value:"Module"}));export{ri as B,Yn as T};
//# sourceMappingURL=Blocks-395ff001.js.map
