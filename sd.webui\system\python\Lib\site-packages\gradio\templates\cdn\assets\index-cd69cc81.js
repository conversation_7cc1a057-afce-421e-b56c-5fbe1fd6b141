import{S as M,e as j,s as G,I,m as b,F as T,o as y,g as u,h as v,G as A,j as h,w as q,u as N,k,H as B,O as D,t as C,x as R,f as E,N as F,M as U,al as _e,R as V,Q as se,r as oe,v as ae,T as W,V as ge,W as he,Z as me}from"./index-afe51b5b.js";import"./Button-b4eb936e.js";import{B as z}from"./BlockTitle-5b84032d.js";import"./Info-1e8b7dd5.js";const w=i=>{var e=null;return i<0?e=[52,152,219]:e=[231,76,60],be(de(Math.abs(i),[255,255,255],e))},de=(i,e,t)=>{i>1&&(i=1),i=Math.sqrt(i);var n=[0,0,0],s;for(s=0;s<3;s++)n[s]=Math.round(e[s]*(1-i)+t[s]*i);return n},be=i=>"rgb("+i[0]+", "+i[1]+", "+i[2]+")";function Z(i,e,t,n,s){var o=n/s,c=e/t,l=0,r=0,f=i?o>c:o<c;return f?(l=e,r=l/o):(r=t,l=r*o),{width:l,height:r,x:(e-l)/2,y:(t-r)/2}}function x(i,e,t){const n=i.slice();return n[2]=e[t],n}function ve(i){let e;return{c(){e=C(i[1])},m(t,n){v(t,e,n)},p(t,n){n&2&&R(e,t[1])},d(t){t&&k(e)}}}function J(i){let e,t=i[2][0]+"",n,s,o;return{c(){e=b("div"),n=C(t),s=y(),u(e,"class","item svelte-x6nxfm"),u(e,"style",o="background-color: "+w(i[2][1]))},m(c,l){v(c,e,l),h(e,n),h(e,s)},p(c,l){l&1&&t!==(t=c[2][0]+"")&&R(n,t),l&1&&o!==(o="background-color: "+w(c[2][1]))&&u(e,"style",o)},d(c){c&&k(e)}}}function ke(i){let e,t,n,s,o;t=new z({props:{$$slots:{default:[ve]},$$scope:{ctx:i}}});let c=I(i[0]),l=[];for(let r=0;r<c.length;r+=1)l[r]=J(x(i,c,r));return{c(){e=b("div"),T(t.$$.fragment),n=y(),s=b("div");for(let r=0;r<l.length;r+=1)l[r].c();u(s,"class","range svelte-x6nxfm"),u(e,"class","input-number svelte-x6nxfm")},m(r,f){v(r,e,f),A(t,e,null),h(e,n),h(e,s);for(let a=0;a<l.length;a+=1)l[a]&&l[a].m(s,null);o=!0},p(r,[f]){const a={};if(f&34&&(a.$$scope={dirty:f,ctx:r}),t.$set(a),f&1){c=I(r[0]);let _;for(_=0;_<c.length;_+=1){const g=x(r,c,_);l[_]?l[_].p(g,f):(l[_]=J(g),l[_].c(),l[_].m(s,null))}for(;_<l.length;_+=1)l[_].d(1);l.length=c.length}},i(r){o||(q(t.$$.fragment,r),o=!0)},o(r){N(t.$$.fragment,r),o=!1},d(r){r&&k(e),B(t),D(l,r)}}}function pe(i,e,t){let{interpretation:n}=e,{label:s=""}=e;return i.$$set=o=>{"interpretation"in o&&t(0,n=o.interpretation),"label"in o&&t(1,s=o.label)},[n,s]}class we extends M{constructor(e){super(),j(this,e,pe,ke,G,{interpretation:0,label:1})}}function K(i,e,t){const n=i.slice();return n[3]=e[t],n[5]=t,n}function ye(i){let e;return{c(){e=C(i[2])},m(t,n){v(t,e,n)},p(t,n){n&4&&R(e,t[2])},d(t){t&&k(e)}}}function L(i){let e,t=i[3]+"",n,s,o;return{c(){e=b("li"),n=C(t),s=y(),u(e,"class","dropdown-item svelte-1cqwepf"),u(e,"style",o="background-color: "+w(i[0][i[5]]))},m(c,l){v(c,e,l),h(e,n),h(e,s)},p(c,l){l&2&&t!==(t=c[3]+"")&&R(n,t),l&1&&o!==(o="background-color: "+w(c[0][c[5]]))&&u(e,"style",o)},d(c){c&&k(e)}}}function Se(i){let e,t,n,s,o;t=new z({props:{$$slots:{default:[ye]},$$scope:{ctx:i}}});let c=I(i[1]),l=[];for(let r=0;r<c.length;r+=1)l[r]=L(K(i,c,r));return{c(){e=b("div"),T(t.$$.fragment),n=y(),s=b("ul");for(let r=0;r<l.length;r+=1)l[r].c();u(s,"class","dropdown-menu svelte-1cqwepf")},m(r,f){v(r,e,f),A(t,e,null),h(e,n),h(e,s);for(let a=0;a<l.length;a+=1)l[a]&&l[a].m(s,null);o=!0},p(r,[f]){const a={};if(f&68&&(a.$$scope={dirty:f,ctx:r}),t.$set(a),f&3){c=I(r[1]);let _;for(_=0;_<c.length;_+=1){const g=K(r,c,_);l[_]?l[_].p(g,f):(l[_]=L(g),l[_].c(),l[_].m(s,null))}for(;_<l.length;_+=1)l[_].d(1);l.length=c.length}},i(r){o||(q(t.$$.fragment,r),o=!0)},o(r){N(t.$$.fragment,r),o=!1},d(r){r&&k(e),B(t),D(l,r)}}}function Ce(i,e,t){let{interpretation:n}=e,{choices:s}=e,{label:o=""}=e;return i.$$set=c=>{"interpretation"in c&&t(0,n=c.interpretation),"choices"in c&&t(1,s=c.choices),"label"in c&&t(2,o=c.label)},[n,s,o]}class Re extends M{constructor(e){super(),j(this,e,Ce,Se,G,{interpretation:0,choices:1,label:2})}}function qe(i){let e;return{c(){e=C(i[0])},m(t,n){v(t,e,n)},p(t,n){n&1&&R(e,t[0])},d(t){t&&k(e)}}}function Ie(i){let e,t,n,s,o,c,l,r,f,a,_,g,m;return t=new z({props:{$$slots:{default:[qe]},$$scope:{ctx:i}}}),{c(){e=b("div"),T(t.$$.fragment),n=y(),s=b("button"),o=b("div"),l=y(),r=b("div"),f=E("svg"),a=E("line"),_=E("line"),u(o,"class","checkbox svelte-1nw19ca"),u(o,"style",c="background-color: "+w(i[2][0])),u(a,"x1","-7.5"),u(a,"y1","0"),u(a,"x2","-2.5"),u(a,"y2","5"),u(a,"stroke","black"),u(a,"stroke-width","4"),u(a,"stroke-linecap","round"),u(_,"x1","-2.5"),u(_,"y1","5"),u(_,"x2","7.5"),u(_,"y2","-7.5"),u(_,"stroke","black"),u(_,"stroke-width","4"),u(_,"stroke-linecap","round"),u(f,"viewBox","-10 -10 20 20"),u(f,"class","svelte-1nw19ca"),u(r,"class","checkbox svelte-1nw19ca"),u(r,"style",g="background-color: "+w(i[2][1])),u(s,"class","checkbox-item svelte-1nw19ca"),F(s,"selected",i[1]),u(e,"class","input-checkbox svelte-1nw19ca")},m(d,p){v(d,e,p),A(t,e,null),h(e,n),h(e,s),h(s,o),h(s,l),h(s,r),h(r,f),h(f,a),h(f,_),m=!0},p(d,[p]){const S={};p&9&&(S.$$scope={dirty:p,ctx:d}),t.$set(S),(!m||p&4&&c!==(c="background-color: "+w(d[2][0])))&&u(o,"style",c),(!m||p&4&&g!==(g="background-color: "+w(d[2][1])))&&u(r,"style",g),(!m||p&2)&&F(s,"selected",d[1])},i(d){m||(q(t.$$.fragment,d),m=!0)},o(d){N(t.$$.fragment,d),m=!1},d(d){d&&k(e),B(t)}}}function Ne(i,e,t){let{label:n=""}=e,{original:s}=e,{interpretation:o}=e;return i.$$set=c=>{"label"in c&&t(0,n=c.label),"original"in c&&t(1,s=c.original),"interpretation"in c&&t(2,o=c.interpretation)},[n,s,o]}class Te extends M{constructor(e){super(),j(this,e,Ne,Ie,G,{label:0,original:1,interpretation:2})}}function P(i,e,t){const n=i.slice();return n[4]=e[t],n[6]=t,n}function Ae(i){let e;return{c(){e=C(i[3])},m(t,n){v(t,e,n)},p(t,n){n&8&&R(e,t[3])},d(t){t&&k(e)}}}function X(i){let e,t,n,s,o,c,l,r,f,a,_=i[4]+"",g,m;return{c(){e=b("button"),t=b("div"),s=y(),o=b("div"),c=E("svg"),l=E("line"),r=E("line"),a=y(),g=C(_),m=y(),u(t,"class","checkbox svelte-1cbhr6k"),u(t,"style",n="background-color: "+w(i[1][i[6]][0])),u(l,"x1","-7.5"),u(l,"y1","0"),u(l,"x2","-2.5"),u(l,"y2","5"),u(l,"stroke","black"),u(l,"stroke-width","4"),u(l,"stroke-linecap","round"),u(r,"x1","-2.5"),u(r,"y1","5"),u(r,"x2","7.5"),u(r,"y2","-7.5"),u(r,"stroke","black"),u(r,"stroke-width","4"),u(r,"stroke-linecap","round"),u(c,"viewBox","-10 -10 20 20"),u(c,"class","svelte-1cbhr6k"),u(o,"class","checkbox svelte-1cbhr6k"),u(o,"style",f="background-color: "+w(i[1][i[6]][1])),u(e,"class","checkbox-item svelte-1cbhr6k"),F(e,"selected",i[0].includes(i[4]))},m(d,p){v(d,e,p),h(e,t),h(e,s),h(e,o),h(o,c),h(c,l),h(c,r),h(e,a),h(e,g),h(e,m)},p(d,p){p&2&&n!==(n="background-color: "+w(d[1][d[6]][0]))&&u(t,"style",n),p&2&&f!==(f="background-color: "+w(d[1][d[6]][1]))&&u(o,"style",f),p&4&&_!==(_=d[4]+"")&&R(g,_),p&5&&F(e,"selected",d[0].includes(d[4]))},d(d){d&&k(e)}}}function Be(i){let e,t,n,s;t=new z({props:{$$slots:{default:[Ae]},$$scope:{ctx:i}}});let o=I(i[2]),c=[];for(let l=0;l<o.length;l+=1)c[l]=X(P(i,o,l));return{c(){e=b("div"),T(t.$$.fragment),n=y();for(let l=0;l<c.length;l+=1)c[l].c();u(e,"class","input-checkbox-group svelte-1cbhr6k")},m(l,r){v(l,e,r),A(t,e,null),h(e,n);for(let f=0;f<c.length;f+=1)c[f]&&c[f].m(e,null);s=!0},p(l,[r]){const f={};if(r&136&&(f.$$scope={dirty:r,ctx:l}),t.$set(f),r&7){o=I(l[2]);let a;for(a=0;a<o.length;a+=1){const _=P(l,o,a);c[a]?c[a].p(_,r):(c[a]=X(_),c[a].c(),c[a].m(e,null))}for(;a<c.length;a+=1)c[a].d(1);c.length=o.length}},i(l){s||(q(t.$$.fragment,l),s=!0)},o(l){N(t.$$.fragment,l),s=!1},d(l){l&&k(e),B(t),D(c,l)}}}function Me(i,e,t){let{original:n}=e,{interpretation:s}=e,{choices:o}=e,{label:c=""}=e;return i.$$set=l=>{"original"in l&&t(0,n=l.original),"interpretation"in l&&t(1,s=l.interpretation),"choices"in l&&t(2,o=l.choices),"label"in l&&t(3,c=l.label)},[n,s,o,c]}class je extends M{constructor(e){super(),j(this,e,Me,Be,G,{original:0,interpretation:1,choices:2,label:3})}}function Y(i,e,t){const n=i.slice();return n[6]=e[t],n}function Ge(i){let e;return{c(){e=C(i[5])},m(t,n){v(t,e,n)},p(t,n){n&32&&R(e,t[5])},d(t){t&&k(e)}}}function $(i){let e,t;return{c(){e=b("div"),u(e,"style",t="background-color: "+w(i[6])),u(e,"class","svelte-1sxprr7")},m(n,s){v(n,e,s)},p(n,s){s&2&&t!==(t="background-color: "+w(n[6]))&&u(e,"style",t)},d(n){n&&k(e)}}}function ze(i){let e,t,n,s,o,c,l,r,f,a;t=new z({props:{$$slots:{default:[Ge]},$$scope:{ctx:i}}});let _=I(i[1]),g=[];for(let m=0;m<_.length;m+=1)g[m]=$(Y(i,_,m));return{c(){e=b("div"),T(t.$$.fragment),n=y(),s=b("input"),o=y(),c=b("div");for(let m=0;m<g.length;m+=1)g[m].c();l=y(),r=b("div"),f=C(i[0]),u(s,"type","range"),s.disabled=!0,u(s,"min",i[2]),u(s,"max",i[3]),u(s,"step",i[4]),u(s,"class","svelte-1sxprr7"),u(c,"class","range svelte-1sxprr7"),u(r,"class","original svelte-1sxprr7"),u(e,"class","input-slider svelte-1sxprr7")},m(m,d){v(m,e,d),A(t,e,null),h(e,n),h(e,s),h(e,o),h(e,c);for(let p=0;p<g.length;p+=1)g[p]&&g[p].m(c,null);h(e,l),h(e,r),h(r,f),a=!0},p(m,[d]){const p={};if(d&544&&(p.$$scope={dirty:d,ctx:m}),t.$set(p),(!a||d&4)&&u(s,"min",m[2]),(!a||d&8)&&u(s,"max",m[3]),(!a||d&16)&&u(s,"step",m[4]),d&2){_=I(m[1]);let S;for(S=0;S<_.length;S+=1){const O=Y(m,_,S);g[S]?g[S].p(O,d):(g[S]=$(O),g[S].c(),g[S].m(c,null))}for(;S<g.length;S+=1)g[S].d(1);g.length=_.length}(!a||d&1)&&R(f,m[0])},i(m){a||(q(t.$$.fragment,m),a=!0)},o(m){N(t.$$.fragment,m),a=!1},d(m){m&&k(e),B(t),D(g,m)}}}function De(i,e,t){let{original:n}=e,{interpretation:s}=e,{minimum:o}=e,{maximum:c}=e,{step:l}=e,{label:r=""}=e;return i.$$set=f=>{"original"in f&&t(0,n=f.original),"interpretation"in f&&t(1,s=f.interpretation),"minimum"in f&&t(2,o=f.minimum),"maximum"in f&&t(3,c=f.maximum),"step"in f&&t(4,l=f.step),"label"in f&&t(5,r=f.label)},[n,s,o,c,l,r]}class Ee extends M{constructor(e){super(),j(this,e,De,ze,G,{original:0,interpretation:1,minimum:2,maximum:3,step:4,label:5})}}function ee(i,e,t){const n=i.slice();return n[4]=e[t],n[6]=t,n}function Fe(i){let e;return{c(){e=C(i[3])},m(t,n){v(t,e,n)},p(t,n){n&8&&R(e,t[3])},d(t){t&&k(e)}}}function te(i){let e,t,n,s,o=i[4]+"",c,l;return{c(){e=b("button"),t=b("div"),s=y(),c=C(o),l=y(),u(t,"class","radio-circle svelte-1nekfre"),u(t,"style",n="background-color: "+w(i[1][i[6]])),u(e,"class","radio-item svelte-1nekfre"),F(e,"selected",i[0]===i[4])},m(r,f){v(r,e,f),h(e,t),h(e,s),h(e,c),h(e,l)},p(r,f){f&2&&n!==(n="background-color: "+w(r[1][r[6]]))&&u(t,"style",n),f&4&&o!==(o=r[4]+"")&&R(c,o),f&5&&F(e,"selected",r[0]===r[4])},d(r){r&&k(e)}}}function Oe(i){let e,t,n,s;t=new z({props:{$$slots:{default:[Fe]},$$scope:{ctx:i}}});let o=I(i[2]),c=[];for(let l=0;l<o.length;l+=1)c[l]=te(ee(i,o,l));return{c(){e=b("div"),T(t.$$.fragment),n=y();for(let l=0;l<c.length;l+=1)c[l].c();u(e,"class","input-radio svelte-1nekfre")},m(l,r){v(l,e,r),A(t,e,null),h(e,n);for(let f=0;f<c.length;f+=1)c[f]&&c[f].m(e,null);s=!0},p(l,[r]){const f={};if(r&136&&(f.$$scope={dirty:r,ctx:l}),t.$set(f),r&7){o=I(l[2]);let a;for(a=0;a<o.length;a+=1){const _=ee(l,o,a);c[a]?c[a].p(_,r):(c[a]=te(_),c[a].c(),c[a].m(e,null))}for(;a<c.length;a+=1)c[a].d(1);c.length=o.length}},i(l){s||(q(t.$$.fragment,l),s=!0)},o(l){N(t.$$.fragment,l),s=!1},d(l){l&&k(e),B(t),D(c,l)}}}function He(i,e,t){let{original:n}=e,{interpretation:s}=e,{choices:o}=e,{label:c=""}=e;return i.$$set=l=>{"original"in l&&t(0,n=l.original),"interpretation"in l&&t(1,s=l.interpretation),"choices"in l&&t(2,o=l.choices),"label"in l&&t(3,c=l.label)},[n,s,o,c]}class Qe extends M{constructor(e){super(),j(this,e,He,Oe,G,{original:0,interpretation:1,choices:2,label:3})}}function Ue(i){let e;return{c(){e=C(i[1])},m(t,n){v(t,e,n)},p(t,n){n&2&&R(e,t[1])},d(t){t&&k(e)}}}function Ve(i){let e,t,n,s,o,c,l,r,f,a;return t=new z({props:{$$slots:{default:[Ue]},$$scope:{ctx:i}}}),{c(){e=b("div"),T(t.$$.fragment),n=y(),s=b("div"),o=b("div"),c=b("canvas"),l=y(),r=b("img"),u(o,"class","interpretation svelte-h0dntu"),U(r.src,f=i[0])||u(r,"src",f),u(r,"class","svelte-h0dntu"),u(s,"class","image-preview svelte-h0dntu"),u(e,"class","input-image")},m(_,g){v(_,e,g),A(t,e,null),h(e,n),h(e,s),h(s,o),h(o,c),i[6](c),h(s,l),h(s,r),i[7](r),a=!0},p(_,[g]){const m={};g&514&&(m.$$scope={dirty:g,ctx:_}),t.$set(m),(!a||g&1&&!U(r.src,f=_[0]))&&u(r,"src",f)},i(_){a||(q(t.$$.fragment,_),a=!0)},o(_){N(t.$$.fragment,_),a=!1},d(_){_&&k(e),B(t),i[6](null),i[7](null)}}}function We(i,e,t){let{original:n}=e,{interpretation:s}=e,{shape:o}=e,{label:c=""}=e,l,r;function f(g,m,d,p){var S=d/g[0].length,O=p/g.length,H=0;g.forEach(function(fe){var Q=0;fe.forEach(function(ue){m.fillStyle=w(ue),m.fillRect(Q*S,H*O,S,O),Q++}),H++})}_e(()=>{let g=Z(!0,r.width,r.height,r.naturalWidth,r.naturalHeight);o&&(g=Z(!0,g.width,g.height,o[0],o[1]));let m=g.width,d=g.height;l.setAttribute("height",`${d}`),l.setAttribute("width",`${m}`),f(s,l.getContext("2d"),m,d)});function a(g){V[g?"unshift":"push"](()=>{l=g,t(2,l)})}function _(g){V[g?"unshift":"push"](()=>{r=g,t(3,r)})}return i.$$set=g=>{"original"in g&&t(0,n=g.original),"interpretation"in g&&t(4,s=g.interpretation),"shape"in g&&t(5,o=g.shape),"label"in g&&t(1,c=g.label)},[n,c,l,r,s,o,a,_]}class Ze extends M{constructor(e){super(),j(this,e,We,Ve,G,{original:0,interpretation:4,shape:5,label:1})}}function le(i,e,t){const n=i.slice();return n[2]=e[t],n}function xe(i){let e;return{c(){e=C(i[1])},m(t,n){v(t,e,n)},p(t,n){n&2&&R(e,t[1])},d(t){t&&k(e)}}}function ne(i){let e,t;return{c(){e=b("div"),u(e,"class","item svelte-13lmfcp"),u(e,"style",t="background-color: "+w(i[2]))},m(n,s){v(n,e,s)},p(n,s){s&1&&t!==(t="background-color: "+w(n[2]))&&u(e,"style",t)},d(n){n&&k(e)}}}function Je(i){let e,t,n,s,o;t=new z({props:{$$slots:{default:[xe]},$$scope:{ctx:i}}});let c=I(i[0]),l=[];for(let r=0;r<c.length;r+=1)l[r]=ne(le(i,c,r));return{c(){e=b("div"),T(t.$$.fragment),n=y(),s=b("div");for(let r=0;r<l.length;r+=1)l[r].c();u(s,"class","range svelte-13lmfcp")},m(r,f){v(r,e,f),A(t,e,null),h(e,n),h(e,s);for(let a=0;a<l.length;a+=1)l[a]&&l[a].m(s,null);o=!0},p(r,[f]){const a={};if(f&34&&(a.$$scope={dirty:f,ctx:r}),t.$set(a),f&1){c=I(r[0]);let _;for(_=0;_<c.length;_+=1){const g=le(r,c,_);l[_]?l[_].p(g,f):(l[_]=ne(g),l[_].c(),l[_].m(s,null))}for(;_<l.length;_+=1)l[_].d(1);l.length=c.length}},i(r){o||(q(t.$$.fragment,r),o=!0)},o(r){N(t.$$.fragment,r),o=!1},d(r){r&&k(e),B(t),D(l,r)}}}function Ke(i,e,t){let{interpretation:n}=e,{label:s=""}=e;return i.$$set=o=>{"interpretation"in o&&t(0,n=o.interpretation),"label"in o&&t(1,s=o.label)},[n,s]}class Le extends M{constructor(e){super(),j(this,e,Ke,Je,G,{interpretation:0,label:1})}}function ie(i,e,t){const n=i.slice();return n[2]=e[t][0],n[3]=e[t][1],n}function Pe(i){let e;return{c(){e=C(i[0])},m(t,n){v(t,e,n)},p(t,n){n&1&&R(e,t[0])},d(t){t&&k(e)}}}function re(i){let e,t=i[2]+"",n,s,o;return{c(){e=b("span"),n=C(t),s=y(),u(e,"class","text-span svelte-15c0u2m"),u(e,"style",o="background-color: "+w(i[3]))},m(c,l){v(c,e,l),h(e,n),h(e,s)},p(c,l){l&2&&t!==(t=c[2]+"")&&R(n,t),l&2&&o!==(o="background-color: "+w(c[3]))&&u(e,"style",o)},d(c){c&&k(e)}}}function Xe(i){let e,t,n,s;t=new z({props:{$$slots:{default:[Pe]},$$scope:{ctx:i}}});let o=I(i[1]),c=[];for(let l=0;l<o.length;l+=1)c[l]=re(ie(i,o,l));return{c(){e=b("div"),T(t.$$.fragment),n=y();for(let l=0;l<c.length;l+=1)c[l].c();u(e,"class","input-text svelte-15c0u2m")},m(l,r){v(l,e,r),A(t,e,null),h(e,n);for(let f=0;f<c.length;f+=1)c[f]&&c[f].m(e,null);s=!0},p(l,[r]){const f={};if(r&65&&(f.$$scope={dirty:r,ctx:l}),t.$set(f),r&2){o=I(l[1]);let a;for(a=0;a<o.length;a+=1){const _=ie(l,o,a);c[a]?c[a].p(_,r):(c[a]=re(_),c[a].c(),c[a].m(e,null))}for(;a<c.length;a+=1)c[a].d(1);c.length=o.length}},i(l){s||(q(t.$$.fragment,l),s=!0)},o(l){N(t.$$.fragment,l),s=!1},d(l){l&&k(e),B(t),D(c,l)}}}function Ye(i,e,t){let{label:n=""}=e,{interpretation:s}=e;return i.$$set=o=>{"label"in o&&t(0,n=o.label),"interpretation"in o&&t(1,s=o.interpretation)},[n,s]}class $e extends M{constructor(e){super(),j(this,e,Ye,Xe,G,{label:0,interpretation:1})}}const et={audio:Le,dropdown:Re,checkbox:Te,checkboxgroup:je,number:we,slider:Ee,radio:Qe,image:Ze,textbox:$e};function ce(i){let e,t,n;const s=[i[0],{original:i[1].original},{interpretation:i[1].interpretation}];var o=i[2];function c(l){let r={};for(let f=0;f<s.length;f+=1)r=me(r,s[f]);return{props:r}}return o&&(e=W(o,c())),{c(){e&&T(e.$$.fragment),t=se()},m(l,r){e&&A(e,l,r),v(l,t,r),n=!0},p(l,r){const f=r&3?ge(s,[r&1&&he(l[0]),r&2&&{original:l[1].original},r&2&&{interpretation:l[1].interpretation}]):{};if(r&4&&o!==(o=l[2])){if(e){oe();const a=e;N(a.$$.fragment,1,0,()=>{B(a,1)}),ae()}o?(e=W(o,c()),T(e.$$.fragment),q(e.$$.fragment,1),A(e,t.parentNode,t)):e=null}else o&&e.$set(f)},i(l){n||(e&&q(e.$$.fragment,l),n=!0)},o(l){e&&N(e.$$.fragment,l),n=!1},d(l){l&&k(t),e&&B(e,l)}}}function tt(i){let e,t,n=i[1]&&ce(i);return{c(){n&&n.c(),e=se()},m(s,o){n&&n.m(s,o),v(s,e,o),t=!0},p(s,[o]){s[1]?n?(n.p(s,o),o&2&&q(n,1)):(n=ce(s),n.c(),q(n,1),n.m(e.parentNode,e)):n&&(oe(),N(n,1,1,()=>{n=null}),ae())},i(s){t||(q(n),t=!0)},o(s){N(n),t=!1},d(s){s&&k(e),n&&n.d(s)}}}function lt(i,e,t){let n,{component:s}=e,{component_props:o}=e,{value:c}=e;return i.$$set=l=>{"component"in l&&t(3,s=l.component),"component_props"in l&&t(0,o=l.component_props),"value"in l&&t(1,c=l.value)},i.$$.update=()=>{i.$$.dirty&8&&t(2,n=et[s])},[o,c,n,s]}class nt extends M{constructor(e){super(),j(this,e,lt,tt,G,{component:3,component_props:0,value:1})}}const ot=nt;export{ot as default};
//# sourceMappingURL=index-cd69cc81.js.map
