import{S as D,e as E,s as F,m as v,F as G,o as C,g as u,h as k,j as w,G as H,an as p,p as g,ax as R,w as T,u as U,k as B,H as z,B as A,C as I,al as J,t as K,x as L}from"./index-afe51b5b.js";import"./Button-b4eb936e.js";import{B as N}from"./BlockTitle-5b84032d.js";function O(e){let s;return{c(){s=K(e[5])},m(l,f){k(l,s,f)},p(l,f){f&32&&L(s,l[5])},d(l){l&&B(s)}}}function P(e){let s,l,f,o,b,a,d,t,_,h,r;return o=new N({props:{show_label:e[7],info:e[6],$$slots:{default:[O]},$$scope:{ctx:e}}}),{c(){s=v("div"),l=v("div"),f=v("label"),G(o.$$.fragment),b=C(),a=v("input"),d=C(),t=v("input"),u(f,"for",e[8]),u(a,"data-testid","number-input"),u(a,"type","number"),u(a,"min",e[1]),u(a,"max",e[2]),u(a,"step",e[3]),a.disabled=e[4],u(a,"class","svelte-1cl284s"),u(l,"class","head svelte-1cl284s"),u(s,"class","wrap svelte-1cl284s"),u(t,"type","range"),u(t,"id",e[8]),u(t,"name","cowbell"),u(t,"min",e[1]),u(t,"max",e[2]),u(t,"step",e[3]),t.disabled=e[4],u(t,"class","svelte-1cl284s")},m(n,i){k(n,s,i),w(s,l),w(l,f),H(o,f,null),w(l,b),w(l,a),p(a,e[0]),k(n,d,i),k(n,t,i),p(t,e[0]),_=!0,h||(r=[g(a,"input",e[12]),g(a,"blur",e[10]),g(a,"pointerup",e[9]),g(t,"change",e[13]),g(t,"input",e[13]),g(t,"pointerup",e[9])],h=!0)},p(n,[i]){const c={};i&128&&(c.show_label=n[7]),i&64&&(c.info=n[6]),i&65568&&(c.$$scope={dirty:i,ctx:n}),o.$set(c),(!_||i&2)&&u(a,"min",n[1]),(!_||i&4)&&u(a,"max",n[2]),(!_||i&8)&&u(a,"step",n[3]),(!_||i&16)&&(a.disabled=n[4]),i&1&&R(a.value)!==n[0]&&p(a,n[0]),(!_||i&2)&&u(t,"min",n[1]),(!_||i&4)&&u(t,"max",n[2]),(!_||i&8)&&u(t,"step",n[3]),(!_||i&16)&&(t.disabled=n[4]),i&1&&p(t,n[0])},i(n){_||(T(o.$$.fragment,n),_=!0)},o(n){U(o.$$.fragment,n),_=!1},d(n){n&&(B(s),B(d),B(t)),z(o),h=!1,A(r)}}}let Q=0;function V(e,s,l){let{value:f=0}=s,{value_is_output:o=!1}=s,{minimum:b=0}=s,{maximum:a=100}=s,{step:d=1}=s,{disabled:t=!1}=s,{label:_}=s,{info:h=void 0}=s,{show_label:r}=s;const n=`range_id_${Q++}`,i=I();function c(){i("change",f),o||i("input")}J(()=>{l(11,o=!1)});function M(m){i("release",f)}function S(){i("release",f),l(0,f=Math.min(Math.max(f,b),a))}function j(){f=R(this.value),l(0,f)}function q(){f=R(this.value),l(0,f)}return e.$$set=m=>{"value"in m&&l(0,f=m.value),"value_is_output"in m&&l(11,o=m.value_is_output),"minimum"in m&&l(1,b=m.minimum),"maximum"in m&&l(2,a=m.maximum),"step"in m&&l(3,d=m.step),"disabled"in m&&l(4,t=m.disabled),"label"in m&&l(5,_=m.label),"info"in m&&l(6,h=m.info),"show_label"in m&&l(7,r=m.show_label)},e.$$.update=()=>{e.$$.dirty&1&&c()},[f,b,a,d,t,_,h,r,n,M,S,o,j,q]}class Z extends D{constructor(s){super(),E(this,s,V,P,F,{value:0,value_is_output:11,minimum:1,maximum:2,step:3,disabled:4,label:5,info:6,show_label:7})}}export{Z as R};
//# sourceMappingURL=Range-dee7f93e.js.map
