{"version": 3, "file": "index-a024de33.js", "sources": ["../../../../js/gallery/static/utils.ts", "../../../../js/gallery/static/Gallery.svelte", "../../../../js/gallery/static/StaticGallery.svelte"], "sourcesContent": ["import { uploadToHuggingFace } from \"@gradio/utils\";\nimport type { FileData } from \"@gradio/upload\";\n\nexport async function format_gallery_for_sharing(\n\tvalue: [FileData, string | null][] | null\n): Promise<string> {\n\tif (!value) return \"\";\n\tlet urls = await Promise.all(\n\t\tvalue.map(async ([image, _]) => {\n\t\t\tif (image === null) return \"\";\n\t\t\treturn await uploadToHuggingFace(image.data, \"url\");\n\t\t})\n\t);\n\n\treturn `<div style=\"display: flex; flex-wrap: wrap; gap: 16px\">${urls\n\t\t.map((url) => `<img src=\"${url}\" style=\"height: 400px\" />`)\n\t\t.join(\"\")}</div>`;\n}\n", "<script lang=\"ts\">\n\timport { BlockLabel, Empty, ShareButton } from \"@gradio/atoms\";\n\timport { ModifyUpload } from \"@gradio/upload\";\n\timport type { SelectData } from \"@gradio/utils\";\n\n\timport { createEventDispatcher } from \"svelte\";\n\timport { tick } from \"svelte\";\n\timport { _ } from \"svelte-i18n\";\n\n\timport { Download, Image } from \"@gradio/icons\";\n\timport type { FileData } from \"@gradio/upload\";\n\timport { normalise_file } from \"@gradio/upload\";\n\timport { format_gallery_for_sharing } from \"./utils\";\n\timport { IconButton } from \"@gradio/atoms\";\n\n\texport let show_label = true;\n\texport let label: string;\n\texport let root = \"\";\n\texport let root_url: null | string = null;\n\texport let value: (FileData | string | [FileData | string, string])[] | null =\n\t\tnull;\n\texport let grid_cols: number | number[] | undefined = [2];\n\texport let grid_rows: number | number[] | undefined = undefined;\n\texport let height: number | \"auto\" = \"auto\";\n\texport let preview: boolean;\n\texport let allow_preview = true;\n\texport let object_fit: \"contain\" | \"cover\" | \"fill\" | \"none\" | \"scale-down\" =\n\t\t\"cover\";\n\texport let show_share_button = false;\n\texport let show_download_button = false;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tselect: SelectData;\n\t}>();\n\n\t// tracks whether the value of the gallery was reset\n\tlet was_reset = true;\n\n\t$: was_reset = value == null || value.length == 0 ? true : was_reset;\n\n\tlet _value: [FileData, string | null][] | null = null;\n\t$: _value =\n\t\tvalue === null\n\t\t\t? null\n\t\t\t: value.map((img) =>\n\t\t\t\t\tArray.isArray(img)\n\t\t\t\t\t\t? [normalise_file(img[0], root, root_url) as FileData, img[1]]\n\t\t\t\t\t\t: [normalise_file(img, root, root_url) as FileData, null]\n\t\t\t  );\n\n\tlet prevValue: (FileData | string | [FileData | string, string])[] | null =\n\t\tvalue;\n\tlet selected_image = preview && value?.length ? 0 : null;\n\tlet old_selected_image: number | null = selected_image;\n\n\t$: if (prevValue !== value) {\n\t\t// When value is falsy (clear button or first load),\n\t\t// preview determines the selected image\n\t\tif (was_reset) {\n\t\t\tselected_image = preview && value?.length ? 0 : null;\n\t\t\twas_reset = false;\n\t\t\t// Otherwise we keep the selected_image the same if the\n\t\t\t// gallery has at least as many elements as it did before\n\t\t} else {\n\t\t\tselected_image =\n\t\t\t\tselected_image !== null &&\n\t\t\t\tvalue !== null &&\n\t\t\t\tselected_image < value.length\n\t\t\t\t\t? selected_image\n\t\t\t\t\t: null;\n\t\t}\n\t\tprevValue = value;\n\t}\n\n\t$: previous =\n\t\t((selected_image ?? 0) + (_value?.length ?? 0) - 1) % (_value?.length ?? 0);\n\t$: next = ((selected_image ?? 0) + 1) % (_value?.length ?? 0);\n\n\tfunction handle_preview_click(event: MouseEvent): void {\n\t\tconst element = event.target as HTMLElement;\n\t\tconst x = event.clientX;\n\t\tconst width = element.offsetWidth;\n\t\tconst centerX = width / 2;\n\n\t\tif (x < centerX) {\n\t\t\tselected_image = previous;\n\t\t} else {\n\t\t\tselected_image = next;\n\t\t}\n\t}\n\n\tfunction on_keydown(e: KeyboardEvent): void {\n\t\tswitch (e.code) {\n\t\t\tcase \"Escape\":\n\t\t\t\te.preventDefault();\n\t\t\t\tselected_image = null;\n\t\t\t\tbreak;\n\t\t\tcase \"ArrowLeft\":\n\t\t\t\te.preventDefault();\n\t\t\t\tselected_image = previous;\n\t\t\t\tbreak;\n\t\t\tcase \"ArrowRight\":\n\t\t\t\te.preventDefault();\n\t\t\t\tselected_image = next;\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tbreak;\n\t\t}\n\t}\n\n\tfunction isFileData(obj: any): obj is FileData {\n\t\treturn typeof obj === \"object\" && obj !== null && \"data\" in obj;\n\t}\n\n\tfunction getHrefValue(selected: any): string {\n\t\tif (isFileData(selected)) {\n\t\t\treturn selected.data;\n\t\t} else if (typeof selected === \"string\") {\n\t\t\treturn selected;\n\t\t}\n\t\treturn \"\";\n\t}\n\n\t$: {\n\t\tif (selected_image !== old_selected_image) {\n\t\t\told_selected_image = selected_image;\n\t\t\tif (selected_image !== null) {\n\t\t\t\tdispatch(\"select\", {\n\t\t\t\t\tindex: selected_image,\n\t\t\t\t\tvalue: _value?.[selected_image][1]\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n\n\t$: if (allow_preview) {\n\t\tscroll_to_img(selected_image);\n\t}\n\n\tlet el: HTMLButtonElement[] = [];\n\tlet container_element: HTMLDivElement;\n\n\tasync function scroll_to_img(index: number | null): Promise<void> {\n\t\tif (typeof index !== \"number\") return;\n\t\tawait tick();\n\n\t\tel[index].focus();\n\n\t\tconst { left: container_left, width: container_width } =\n\t\t\tcontainer_element.getBoundingClientRect();\n\t\tconst { left, width } = el[index].getBoundingClientRect();\n\n\t\tconst relative_left = left - container_left;\n\n\t\tconst pos =\n\t\t\trelative_left +\n\t\t\twidth / 2 -\n\t\t\tcontainer_width / 2 +\n\t\t\tcontainer_element.scrollLeft;\n\n\t\tcontainer_element?.scrollTo({\n\t\t\tleft: pos < 0 ? 0 : pos,\n\t\t\tbehavior: \"smooth\"\n\t\t});\n\t}\n\n\tlet client_height = 0;\n\tlet window_height = 0;\n</script>\n\n<svelte:window bind:innerHeight={window_height} />\n\n{#if show_label}\n\t<BlockLabel {show_label} Icon={Image} label={label || \"Gallery\"} />\n{/if}\n{#if value === null || _value === null || _value.length === 0}\n\t<Empty unpadded_box={true} size=\"large\"><Image /></Empty>\n{:else}\n\t{#if selected_image !== null && allow_preview}\n\t\t<!-- svelte-ignore a11y-no-static-element-interactions -->\n\t\t<div on:keydown={on_keydown} class=\"preview\">\n\t\t\t<div class=\"icon-buttons\">\n\t\t\t\t{#if show_download_button}\n\t\t\t\t\t<a\n\t\t\t\t\t\thref={getHrefValue(value[selected_image])}\n\t\t\t\t\t\ttarget={window.__is_colab__ ? \"_blank\" : null}\n\t\t\t\t\t\tdownload=\"image\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<IconButton Icon={Download} label={$_(\"common.download\")} />\n\t\t\t\t\t</a>\n\t\t\t\t{/if}\n\n\t\t\t\t<ModifyUpload\n\t\t\t\t\tabsolute={false}\n\t\t\t\t\ton:clear={() => (selected_image = null)}\n\t\t\t\t/>\n\t\t\t</div>\n\t\t\t<!-- svelte-ignore a11y-click-events-have-key-events -->\n\t\t\t<!-- svelte-ignore a11y-no-noninteractive-element-interactions -->\n\t\t\t<img\n\t\t\t\tdata-testid=\"detailed-image\"\n\t\t\t\ton:click={(event) => handle_preview_click(event)}\n\t\t\t\tsrc={_value[selected_image][0].data}\n\t\t\t\talt={_value[selected_image][1] || \"\"}\n\t\t\t\ttitle={_value[selected_image][1] || null}\n\t\t\t\tclass:with-caption={!!_value[selected_image][1]}\n\t\t\t\tstyle=\"height: calc(100% - {_value[selected_image][1]\n\t\t\t\t\t? '80px'\n\t\t\t\t\t: '60px'})\"\n\t\t\t/>\n\t\t\t{#if _value[selected_image][1]}\n\t\t\t\t<div class=\"caption\">\n\t\t\t\t\t{_value[selected_image][1]}\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t\t<div\n\t\t\t\tbind:this={container_element}\n\t\t\t\tclass=\"thumbnails scroll-hide\"\n\t\t\t\tdata-testid=\"container_el\"\n\t\t\t>\n\t\t\t\t{#each _value as image, i}\n\t\t\t\t\t<button\n\t\t\t\t\t\tbind:this={el[i]}\n\t\t\t\t\t\ton:click={() => (selected_image = i)}\n\t\t\t\t\t\tclass=\"thumbnail-item thumbnail-small\"\n\t\t\t\t\t\tclass:selected={selected_image === i}\n\t\t\t\t\t>\n\t\t\t\t\t\t<img\n\t\t\t\t\t\t\tsrc={image[0].data}\n\t\t\t\t\t\t\ttitle={image[1] || null}\n\t\t\t\t\t\t\talt={image[1] || null}\n\t\t\t\t\t\t/>\n\t\t\t\t\t</button>\n\t\t\t\t{/each}\n\t\t\t</div>\n\t\t</div>\n\t{/if}\n\n\t<div\n\t\tbind:clientHeight={client_height}\n\t\tclass=\"grid-wrap\"\n\t\tclass:fixed-height={!height || height == \"auto\"}\n\t>\n\t\t<div\n\t\t\tclass=\"grid-container\"\n\t\t\tstyle=\"--grid-cols:{grid_cols}; --grid-rows:{grid_rows}; --object-fit: {object_fit}; height: {height};\"\n\t\t\tclass:pt-6={show_label}\n\t\t>\n\t\t\t{#if show_share_button}\n\t\t\t\t<div class=\"icon-button\">\n\t\t\t\t\t<ShareButton\n\t\t\t\t\t\ton:share\n\t\t\t\t\t\ton:error\n\t\t\t\t\t\tvalue={_value}\n\t\t\t\t\t\tformatter={format_gallery_for_sharing}\n\t\t\t\t\t/>\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t\t{#each _value as [image, caption], i}\n\t\t\t\t<button\n\t\t\t\t\tclass=\"thumbnail-item thumbnail-lg\"\n\t\t\t\t\tclass:selected={selected_image === i}\n\t\t\t\t\ton:click={() => (selected_image = i)}\n\t\t\t\t>\n\t\t\t\t\t<img\n\t\t\t\t\t\talt={caption || \"\"}\n\t\t\t\t\t\tsrc={typeof image === \"string\" ? image : image.data}\n\t\t\t\t\t/>\n\t\t\t\t\t{#if caption}\n\t\t\t\t\t\t<div class=\"caption-label\">\n\t\t\t\t\t\t\t{caption}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t</button>\n\t\t\t{/each}\n\t\t</div>\n\t</div>\n{/if}\n\n<style lang=\"postcss\">\n\t.preview {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: 0px;\n\t\tright: 0px;\n\t\tbottom: 0px;\n\t\tleft: 0px;\n\t\tflex-direction: column;\n\t\tz-index: var(--layer-2);\n\t\tbackdrop-filter: blur(8px);\n\t\tbackground: var(--background-fill-primary);\n\t\theight: var(--size-full);\n\t}\n\n\t.fixed-height {\n\t\tmin-height: var(--size-80);\n\t\tmax-height: 55vh;\n\t}\n\n\t@media (--screen-xl) {\n\t\t.fixed-height {\n\t\t\tmin-height: 450px;\n\t\t}\n\t}\n\n\t.preview img {\n\t\twidth: var(--size-full);\n\t\theight: calc(var(--size-full) - 60px);\n\t\tobject-fit: contain;\n\t}\n\n\t.preview img.with-caption {\n\t\theight: calc(var(--size-full) - 80px);\n\t}\n\n\t.caption {\n\t\tpadding: var(--size-2) var(--size-3);\n\t\toverflow: hidden;\n\t\tcolor: var(--block-label-text-color);\n\t\tfont-weight: var(--weight-semibold);\n\t\ttext-align: center;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t}\n\n\t.thumbnails {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tgap: var(--spacing-lg);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-14);\n\t\toverflow-x: scroll;\n\t}\n\n\t.thumbnail-item {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\tbox-shadow:\n\t\t\t0 0 0 2px var(--ring-color),\n\t\t\tvar(--shadow-drop);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--button-small-radius);\n\t\tbackground: var(--background-fill-secondary);\n\t\taspect-ratio: var(--ratio-square);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\toverflow: clip;\n\t}\n\n\t.thumbnail-item:hover {\n\t\t--ring-color: var(--color-accent);\n\t\tfilter: brightness(1.1);\n\t}\n\n\t.thumbnail-item.selected {\n\t\t--ring-color: var(--color-accent);\n\t}\n\n\t.thumbnail-small {\n\t\tflex: none;\n\t\ttransform: scale(0.9);\n\t\ttransition: 0.075s;\n\t\twidth: var(--size-9);\n\t\theight: var(--size-9);\n\t}\n\n\t.thumbnail-small.selected {\n\t\t--ring-color: var(--color-accent);\n\t\ttransform: scale(1);\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.thumbnail-small > img {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\toverflow: hidden;\n\t\tobject-fit: var(--object-fit);\n\t}\n\n\t.grid-wrap {\n\t\tposition: relative;\n\t\tpadding: var(--size-2);\n\t\theight: var(--size-full);\n\t\toverflow-y: scroll;\n\t}\n\n\t.grid-container {\n\t\tdisplay: grid;\n\t\tposition: relative;\n\t\tgrid-template-rows: repeat(var(--grid-rows), minmax(100px, 1fr));\n\t\tgrid-template-columns: repeat(var(--grid-cols), minmax(100px, 1fr));\n\t\tgrid-auto-rows: minmax(100px, 1fr);\n\t\tgap: var(--spacing-lg);\n\t}\n\n\t.thumbnail-lg > img {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\toverflow: hidden;\n\t\tobject-fit: var(--object-fit);\n\t}\n\n\t.thumbnail-lg:hover .caption-label {\n\t\topacity: 0.5;\n\t}\n\n\t.caption-label {\n\t\tposition: absolute;\n\t\tright: var(--block-label-margin);\n\t\tbottom: var(--block-label-margin);\n\t\tz-index: var(--layer-1);\n\t\tborder-top: 1px solid var(--border-color-primary);\n\t\tborder-left: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--block-label-radius);\n\t\tbackground: var(--background-fill-secondary);\n\t\tpadding: var(--block-label-padding);\n\t\tmax-width: 80%;\n\t\toverflow: hidden;\n\t\tfont-size: var(--block-label-text-size);\n\t\ttext-align: left;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t}\n\n\t.icon-button {\n\t\tposition: absolute;\n\t\ttop: 0px;\n\t\tright: 0px;\n\t\tz-index: var(--layer-1);\n\t}\n\n\t.icon-buttons {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tright: 0;\n\t}\n\n\t.icon-buttons a {\n\t\tmargin: var(--size-1) 0;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Gradio, ShareData, SelectData } from \"@gradio/utils\";\n\timport { Block } from \"@gradio/atoms\";\n\timport Gallery from \"./Gallery.svelte\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { FileData } from \"@gradio/upload\";\n\n\texport let loading_status: LoadingStatus;\n\texport let show_label: boolean;\n\texport let label: string;\n\texport let root: string;\n\texport let root_url: null | string;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: (FileData | string | [FileData | string, string])[] | null =\n\t\tnull;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let grid_cols: number | number[] | undefined = [2];\n\texport let grid_rows: number | number[] | undefined = undefined;\n\texport let height: number | \"auto\" = \"auto\";\n\texport let preview: boolean;\n\texport let allow_preview = true;\n\texport let object_fit: \"contain\" | \"cover\" | \"fill\" | \"none\" | \"scale-down\" =\n\t\t\"cover\";\n\texport let show_share_button = false;\n\texport let show_download_button = false;\n\texport let gradio: Gradio<{\n\t\tselect: SelectData;\n\t\tshare: ShareData;\n\t\terror: string;\n\t}>;\n</script>\n\n<Block\n\t{visible}\n\tvariant=\"solid\"\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n\theight={typeof height === \"number\" ? height : undefined}\n>\n\t<StatusTracker {...loading_status} />\n\t<Gallery\n\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\ton:share={(e) => gradio.dispatch(\"share\", e.detail)}\n\t\ton:error={(e) => gradio.dispatch(\"error\", e.detail)}\n\t\t{label}\n\t\t{value}\n\t\t{show_label}\n\t\t{root}\n\t\t{root_url}\n\t\t{grid_cols}\n\t\t{grid_rows}\n\t\t{height}\n\t\t{preview}\n\t\t{object_fit}\n\t\t{allow_preview}\n\t\t{show_share_button}\n\t\t{show_download_button}\n\t/>\n</Block>\n"], "names": ["format_gallery_for_sharing", "value", "image", "_", "uploadToHuggingFace", "url", "Image", "ctx", "dirty", "blocklabel_changes", "create_if_block_3", "create_if_block_2", "i", "insert", "target", "div1", "anchor", "append", "div0", "create_if_block_5", "create_if_block_4", "src_url_equal", "img", "img_src_value", "attr", "img_alt_value", "img_title_value", "div2", "current", "Download", "getHrefValue", "a", "iconbutton_changes", "div", "set_data", "t", "t_value", "toggle_class", "button", "create_if_block_1", "create_if_block_6", "isFileData", "obj", "selected", "show_label", "$$props", "label", "root", "root_url", "grid_cols", "grid_rows", "height", "preview", "allow_preview", "object_fit", "show_share_button", "show_download_button", "dispatch", "createEventDispatcher", "was_reset", "_value", "prevValue", "selected_image", "old_selected_image", "handle_preview_click", "event", "element", "x", "centerX", "$$invalidate", "previous", "next", "on_keydown", "e", "el", "container_element", "scroll_to_img", "index", "tick", "container_left", "container_width", "left", "width", "pos", "client_height", "window_height", "clear_handler", "$$value", "click_handler_1", "click_handler_2", "normalise_file", "block_changes", "loading_status", "elem_id", "elem_classes", "visible", "container", "scale", "min_width", "gradio"], "mappings": "iuBAGA,eAAsBA,GACrBC,EACkB,CAClB,OAAKA,EAQE,2DAPI,MAAM,QAAQ,IACxBA,EAAM,IAAI,MAAO,CAACC,EAAOC,CAAC,IACrBD,IAAU,KAAa,GACpB,MAAME,GAAoBF,EAAM,KAAM,KAAK,CAClD,CAAA,GAIA,IAAKG,GAAQ,aAAaA,6BAA+B,EACzD,KAAK,EAAE,UAVU,EAWpB,oPC4JgCC,GAAc,MAAAC,MAAS,wGAATC,EAAA,CAAA,EAAA,IAAAC,EAAA,MAAAF,MAAS,sIAKjDA,EAAc,EAAA,IAAK,MAAQA,EAAa,CAAA,GAAAG,GAAAH,CAAA,IAsEtCA,EAAiB,CAAA,GAAAI,GAAAJ,CAAA,MAUfA,EAAM,EAAA,CAAA,uBAAX,OAAIK,GAAA,wLAbcL,EAAS,CAAA,CAAA,oBAAgBA,EAAS,CAAA,CAAA,qBAAkBA,EAAU,CAAA,CAAA,eAAYA,EAAM,CAAA,CAAA,aACxFA,EAAU,CAAA,CAAA,oFALFA,EAAM,CAAA,GAAIA,EAAM,CAAA,GAAI,MAAM,+BAHhDM,EAsCKC,EAAAC,EAAAC,CAAA,EAjCJC,EAgCKF,EAAAG,CAAA,+GAjGDX,EAAc,EAAA,IAAK,MAAQA,EAAa,CAAA,+GAsEtCA,EAAiB,CAAA,kHAUfA,EAAM,EAAA,CAAA,oBAAX,OAAIK,GAAA,EAAA,mHAAJ,uCAbkBL,EAAS,CAAA,CAAA,mCAAgBA,EAAS,CAAA,CAAA,qCAAkBA,EAAU,CAAA,CAAA,8BAAYA,EAAM,CAAA,CAAA,2BACxFA,EAAU,CAAA,CAAA,qCALFA,EAAM,CAAA,GAAIA,EAAM,CAAA,GAAI,MAAM,mKAjE3B,6SAMbA,EAAoB,CAAA,GAAAY,GAAAZ,CAAA,4BAWd,EAAK,CAAA,CAAA,6BAiBZA,EAAM,EAAA,EAACA,EAAc,EAAA,CAAA,EAAE,CAAC,GAAAa,GAAAb,CAAA,MAUrBA,EAAM,EAAA,CAAA,uBAAX,OAAIK,GAAA,2PAlBDS,EAAAC,EAAA,IAAAC,EAAAhB,EAAO,EAAA,EAAAA,EAAgB,EAAA,CAAA,EAAA,CAAC,EAAE,IAAI,GAAAiB,EAAAF,EAAA,MAAAC,CAAA,EAC9BC,EAAAF,EAAA,MAAAG,EAAAlB,EAAO,EAAA,EAAAA,EAAgB,EAAA,CAAA,EAAA,CAAC,GAAK,EAAE,EAC7BiB,EAAAF,EAAA,QAAAI,EAAAnB,EAAO,EAAA,EAAAA,EAAgB,EAAA,CAAA,EAAA,CAAC,GAAK,IAAI,+BAEZA,EAAM,EAAA,EAACA,EAAc,EAAA,CAAA,EAAE,CAAC,EACjD,OACA,QAAM,GAAA,qDAHaA,EAAM,EAAA,EAACA,EAAc,EAAA,CAAA,EAAE,CAAC,CAAA,uIAzBhDM,EAuDKC,EAAAa,EAAAX,CAAA,EAtDJC,EAeKU,EAAAT,CAAA,2CAGLD,EAUCU,EAAAL,CAAA,+BAMDL,EAmBKU,EAAAZ,CAAA,yGAtDWR,EAAU,EAAA,CAAA,oBAEpBA,EAAoB,CAAA,oGAoBpB,CAAAqB,GAAApB,EAAA,CAAA,EAAA,MAAA,CAAAa,EAAAC,EAAA,IAAAC,EAAAhB,EAAO,EAAA,EAAAA,EAAgB,EAAA,CAAA,EAAA,CAAC,EAAE,IAAI,kBAC9B,CAAAqB,GAAApB,EAAA,CAAA,EAAA,MAAAiB,KAAAA,EAAAlB,EAAO,EAAA,EAAAA,EAAgB,EAAA,CAAA,EAAA,CAAC,GAAK,oBAC3B,CAAAqB,GAAApB,EAAA,CAAA,EAAA,MAAAkB,KAAAA,EAAAnB,EAAO,EAAA,EAAAA,EAAgB,EAAA,CAAA,EAAA,CAAC,GAAK,qEAERA,EAAM,EAAA,EAACA,EAAc,EAAA,CAAA,EAAE,CAAC,EACjD,OACA,QAAM,GAAA,wCAHaA,EAAM,EAAA,EAACA,EAAc,EAAA,CAAA,EAAE,CAAC,CAAA,EAK1CA,EAAM,EAAA,EAACA,EAAc,EAAA,CAAA,EAAE,CAAC,wEAUrBA,EAAM,EAAA,CAAA,oBAAX,OAAIK,GAAA,EAAA,mHAAJ,iNAhCkBiB,GAAiB,MAAAtB,MAAG,iBAAiB,iDAJjDuB,GAAavB,EAAK,CAAA,EAACA,EAAc,EAAA,CAAA,CAAA,CAAA,EAC/BiB,EAAAO,EAAA,SAAA,OAAO,aAAe,SAAW,IAAI,gEAF9ClB,EAMGC,EAAAiB,EAAAf,CAAA,sCADiCR,EAAA,CAAA,EAAA,QAAAwB,EAAA,MAAAzB,MAAG,iBAAiB,oCAJjDuB,GAAavB,EAAK,CAAA,EAACA,EAAc,EAAA,CAAA,CAAA,gIA4BvCA,EAAM,EAAA,EAACA,EAAc,EAAA,CAAA,EAAE,CAAC,EAAA,iFAD1BM,EAEKC,EAAAmB,EAAAjB,CAAA,mCADHT,EAAM,EAAA,EAACA,EAAc,EAAA,CAAA,EAAE,CAAC,EAAA,KAAA2B,GAAAC,EAAAC,CAAA,6LAgBlB7B,EAAK,EAAA,EAAC,CAAC,EAAE,IAAI,GAAAiB,EAAAF,EAAA,MAAAC,CAAA,gBACXhB,EAAK,EAAA,EAAC,CAAC,GAAK,IAAI,cAClBA,EAAK,EAAA,EAAC,CAAC,GAAK,IAAI,6FALN8B,EAAAC,EAAA,WAAA/B,QAAmBA,EAAC,EAAA,CAAA,UAJrCM,EAWQC,EAAAwB,EAAAtB,CAAA,EALPC,EAICqB,EAAAhB,CAAA,0EAHKf,EAAK,EAAA,EAAC,CAAC,EAAE,IAAI,kCACXA,EAAK,EAAA,EAAC,CAAC,GAAK,wCACdA,EAAK,EAAA,EAAC,CAAC,GAAK,4DALF8B,EAAAC,EAAA,WAAA/B,QAAmBA,EAAC,EAAA,CAAA,sFA4B7BA,EAAM,EAAA,YACFP,mIALba,EAOKC,EAAAmB,EAAAjB,CAAA,0DAHIT,EAAM,EAAA,0HAiBXA,EAAO,EAAA,EAAA,uFADTM,EAEKC,EAAAmB,EAAAjB,CAAA,mCADHT,EAAO,EAAA,EAAA,KAAA2B,GAAAC,EAAAC,CAAA,uDAFL7B,EAAO,EAAA,GAAAgC,GAAAhC,CAAA,4FAHNiB,EAAAF,EAAA,MAAAG,EAAAlB,OAAW,EAAE,EACNc,EAAAC,EAAA,IAAAC,EAAA,OAAAhB,OAAU,SAAWA,EAAK,EAAA,EAAGA,MAAM,IAAI,GAAAiB,EAAAF,EAAA,MAAAC,CAAA,0FALpCc,EAAAC,EAAA,WAAA/B,QAAmBA,EAAC,EAAA,CAAA,UAFrCM,EAcQC,EAAAwB,EAAAtB,CAAA,EATPC,EAGCqB,EAAAhB,CAAA,sEAFKd,EAAA,CAAA,EAAA,MAAAiB,KAAAA,EAAAlB,OAAW,kBACJC,EAAA,CAAA,EAAA,MAAA,CAAAa,EAAAC,EAAA,IAAAC,EAAA,OAAAhB,OAAU,SAAWA,EAAK,EAAA,EAAGA,MAAM,IAAI,gBAE/CA,EAAO,EAAA,oEAPI8B,EAAAC,EAAA,WAAA/B,QAAmBA,EAAC,EAAA,CAAA,4PAzFpCA,EAAU,CAAA,GAAAiC,GAAAjC,CAAA,8CAGVA,EAAK,CAAA,IAAK,MAAQA,EAAM,EAAA,IAAK,MAAQA,EAAM,EAAA,EAAC,SAAW,EAAC,kKAHxDA,EAAU,CAAA,0VA9DL,SAAAkC,GAAWC,EAAQ,eACbA,GAAQ,UAAYA,IAAQ,MAAQ,SAAUA,EAGpD,SAAAZ,GAAaa,EAAa,CAC9B,OAAAF,GAAWE,CAAQ,EACfA,EAAS,KACC,OAAAA,GAAa,SACvBA,EAED,sDAzGG,GAAA,CAAA,WAAAC,EAAa,EAAI,EAAAC,GACjB,MAAAC,CAAa,EAAAD,EACb,CAAA,KAAAE,EAAO,EAAE,EAAAF,EACT,CAAA,SAAAG,EAA0B,IAAI,EAAAH,EAC9B,CAAA,MAAA5C,EACV,IAAI,EAAA4C,EACM,CAAA,UAAAI,GAA4C,CAAC,CAAA,EAAAJ,EAC7C,CAAA,UAAAK,EAA2C,MAAS,EAAAL,EACpD,CAAA,OAAAM,EAA0B,MAAM,EAAAN,GAChC,QAAAO,CAAgB,EAAAP,EAChB,CAAA,cAAAQ,EAAgB,EAAI,EAAAR,EACpB,CAAA,WAAAS,EACV,OAAO,EAAAT,EACG,CAAA,kBAAAU,EAAoB,EAAK,EAAAV,EACzB,CAAA,qBAAAW,EAAuB,EAAK,EAAAX,EAEjC,MAAAY,EAAWC,KAKb,IAAAC,EAAY,GAIZC,EAA6C,KAU7CC,EACH5D,EACG6D,EAAiBV,GAAWnD,GAAO,OAAS,EAAI,KAChD8D,EAAoCD,EAyB/B,SAAAE,EAAqBC,EAAiB,OACxCC,EAAUD,EAAM,OAChBE,EAAIF,EAAM,QAEVG,EADQF,EAAQ,YACE,EAEpBC,EAAIC,EACPC,EAAA,GAAAP,EAAiBQ,CAAQ,EAEzBD,EAAA,GAAAP,EAAiBS,CAAI,EAId,SAAAC,EAAWC,EAAgB,CAC3B,OAAAA,EAAE,KAAI,KACR,SACJA,EAAE,eAAc,EAChBJ,EAAA,GAAAP,EAAiB,IAAI,YAEjB,YACJW,EAAE,eAAc,EAChBJ,EAAA,GAAAP,EAAiBQ,CAAQ,YAErB,aACJG,EAAE,eAAc,EAChBJ,EAAA,GAAAP,EAAiBS,CAAI,aAoCpBG,EAAE,CAAA,EACFC,EAEW,eAAAC,GAAcC,EAAoB,CACrC,GAAA,OAAAA,GAAU,SAAQ,aACvBC,GAAI,EAEVJ,EAAGG,CAAK,EAAE,cAEF,KAAME,EAAgB,MAAOC,GACpCL,EAAkB,wBACX,CAAA,KAAAM,EAAM,MAAAC,CAAK,EAAKR,EAAGG,CAAK,EAAE,wBAI5BM,GAFgBF,EAAOF,EAI5BG,EAAQ,EACRF,EAAkB,EAClBL,EAAkB,WAEnBA,GAAmB,SAAQ,CAC1B,KAAMQ,GAAM,EAAI,EAAIA,GACpB,SAAU,WAIR,IAAAC,EAAgB,EAChBC,EAAgB,uCA2BC,MAAAC,GAAA,IAAAjB,EAAA,GAAAP,EAAiB,IAAI,KAO5BG,GAAUD,EAAqBC,CAAK,+CAqBlCS,EAAG9D,CAAC,EAAA2E,YACE,MAAAC,GAAA5E,GAAAyD,EAAA,GAAAP,EAAiBlD,CAAC,6CAP1B+D,EAAiBY,8EA8CV,MAAAE,GAAA7E,GAAAyD,EAAA,GAAAP,EAAiBlD,CAAC,gBAvBnBwE,EAAa,KAAA,ojBAzM9Bf,EAAA,GAAAV,EAAY1D,GAAS,MAAQA,EAAM,QAAU,EAAI,GAAO0D,CAAS,yBAGjEU,EAAA,GAAAT,EACF3D,IAAU,KACP,KACAA,EAAM,IAAKqB,GACX,MAAM,QAAQA,CAAG,GACboE,GAAepE,EAAI,CAAC,EAAGyB,EAAMC,CAAQ,EAAe1B,EAAI,CAAC,CAAA,EACzD,CAAAoE,GAAepE,EAAKyB,EAAMC,CAAQ,EAAe,IAAI,CAAA,CAAA,0BAQtDa,IAAc5D,IAGhB0D,QACHG,EAAiBV,GAAWnD,GAAO,OAAS,EAAI,IAAI,EACpDoE,EAAA,GAAAV,EAAY,EAAK,QAIjBG,EACCA,IAAmB,MACnB7D,IAAU,MACV6D,EAAiB7D,EAAM,OACpB6D,EACA,MAELO,EAAA,GAAAR,EAAY5D,CAAK,wBAGfqE,IACAR,GAAkB,IAAMF,GAAQ,QAAU,GAAK,IAAMA,GAAQ,QAAU,yBACvEW,IAAST,GAAkB,GAAK,IAAMF,GAAQ,QAAU,4BAgDtDE,IAAmBC,IACtBM,EAAA,GAAAN,EAAqBD,CAAc,EAC/BA,IAAmB,MACtBL,EAAS,SAAQ,CAChB,MAAOK,EACP,MAAOF,IAASE,CAAc,EAAE,CAAC,yBAM9BT,GACNuB,GAAcd,CAAc,6WCvFVvD,EAAc,CAAA,CAAA,seAAdA,EAAc,CAAA,CAAA,CAAA,CAAA,umBATxB,4FAMO,GACD,OAAA,OAAAA,OAAW,SAAWA,EAAM,EAAA,EAAG,2QAA/BC,EAAA,QAAAmF,EAAA,OAAA,OAAApF,OAAW,SAAWA,EAAM,EAAA,EAAG,iKAvCnC,eAAAqF,CAA6B,EAAA/C,GAC7B,WAAAD,CAAmB,EAAAC,GACnB,MAAAC,CAAa,EAAAD,GACb,KAAAE,CAAY,EAAAF,GACZ,SAAAG,CAAuB,EAAAH,EACvB,CAAA,QAAAgD,EAAU,EAAE,EAAAhD,GACZ,aAAAiD,EAAY,EAAA,EAAAjD,EACZ,CAAA,QAAAkD,EAAU,EAAI,EAAAlD,EACd,CAAA,MAAA5C,EACV,IAAI,EAAA4C,EACM,CAAA,UAAAmD,EAAY,EAAI,EAAAnD,EAChB,CAAA,MAAAoD,EAAuB,IAAI,EAAApD,EAC3B,CAAA,UAAAqD,EAAgC,MAAS,EAAArD,EACzC,CAAA,UAAAI,GAA4C,CAAC,CAAA,EAAAJ,EAC7C,CAAA,UAAAK,EAA2C,MAAS,EAAAL,EACpD,CAAA,OAAAM,EAA0B,MAAM,EAAAN,GAChC,QAAAO,CAAgB,EAAAP,EAChB,CAAA,cAAAQ,EAAgB,EAAI,EAAAR,EACpB,CAAA,WAAAS,EACV,OAAO,EAAAT,EACG,CAAA,kBAAAU,EAAoB,EAAK,EAAAV,EACzB,CAAA,qBAAAW,EAAuB,EAAK,EAAAX,GAC5B,OAAAsD,CAIT,EAAAtD,UAiBW4B,GAAM0B,EAAO,SAAS,SAAU1B,EAAE,MAAM,IACzCA,GAAM0B,EAAO,SAAS,QAAS1B,EAAE,MAAM,IACvCA,GAAM0B,EAAO,SAAS,QAAS1B,EAAE,MAAM"}