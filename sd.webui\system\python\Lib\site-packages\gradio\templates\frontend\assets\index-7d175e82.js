import{S as q,e as C,s as F,F as h,G as d,w as g,u as w,H as b,al as G,Z as H,ad as U,o as V,h as W,V as Z,W as z,k as A}from"./index-2519a27e.js";import{B as E}from"./Button-748313a7.js";import{T as K}from"./Table-be00801a.js";import"./utils-c3e3db58.js";import"./Upload-b0a38490.js";import"./StaticMarkdown-e77fff97.js";import"./dsv-576afacd.js";function L(n){let t,l,i,s;const _=[n[15]];let m={};for(let a=0;a<_.length;a+=1)m=H(m,_[a]);return t=new U({props:m}),i=new K({props:{label:n[9],row_count:n[8],col_count:n[7],values:n[0],headers:n[1],editable:!0,wrap:n[10],datatype:n[11],latex_delimiters:n[5],height:n[6]}}),i.$on("change",n[18]),i.$on("select",n[19]),{c(){h(t.$$.fragment),l=V(),h(i.$$.fragment)},m(a,f){d(t,a,f),W(a,l,f),d(i,a,f),s=!0},p(a,f){const o=f&32768?Z(_,[z(a[15])]):{};t.$set(o);const u={};f&512&&(u.label=a[9]),f&256&&(u.row_count=a[8]),f&128&&(u.col_count=a[7]),f&1&&(u.values=a[0]),f&2&&(u.headers=a[1]),f&1024&&(u.wrap=a[10]),f&2048&&(u.datatype=a[11]),f&32&&(u.latex_delimiters=a[5]),f&64&&(u.height=a[6]),i.$set(u)},i(a){s||(g(t.$$.fragment,a),g(i.$$.fragment,a),s=!0)},o(a){w(t.$$.fragment,a),w(i.$$.fragment,a),s=!1},d(a){a&&A(l),b(t,a),b(i,a)}}}function M(n){let t,l;return t=new E({props:{visible:n[4],padding:!1,elem_id:n[2],elem_classes:n[3],container:!1,scale:n[12],min_width:n[13],allow_overflow:!1,$$slots:{default:[L]},$$scope:{ctx:n}}}),{c(){h(t.$$.fragment)},m(i,s){d(t,i,s),l=!0},p(i,[s]){const _={};s&16&&(_.visible=i[4]),s&4&&(_.elem_id=i[2]),s&8&&(_.elem_classes=i[3]),s&4096&&(_.scale=i[12]),s&8192&&(_.min_width=i[13]),s&2150371&&(_.$$scope={dirty:s,ctx:i}),t.$set(_)},i(i){l||(g(t.$$.fragment,i),l=!0)},o(i){w(t.$$.fragment,i),l=!1},d(i){b(t,i)}}}function P(n,t,l){let{headers:i=[]}=t,{elem_id:s=""}=t,{elem_classes:_=[]}=t,{visible:m=!0}=t,{value:a={data:[["","",""]],headers:["1","2","3"]}}=t,{latex_delimiters:f}=t,{height:o=void 0}=t,u=JSON.stringify(a),{value_is_output:r=!1}=t,{col_count:k}=t,{row_count:S}=t,{label:v=null}=t,{wrap:J}=t,{datatype:N}=t,{scale:O=null}=t,{min_width:B=void 0}=t,{gradio:c}=t,{loading_status:D}=t;function I(){c.dispatch("change"),r||c.dispatch("input")}G(()=>{l(16,r=!1)});const T=({detail:e})=>{l(0,a=e)},j=e=>c.dispatch("select",e.detail);return n.$$set=e=>{"headers"in e&&l(1,i=e.headers),"elem_id"in e&&l(2,s=e.elem_id),"elem_classes"in e&&l(3,_=e.elem_classes),"visible"in e&&l(4,m=e.visible),"value"in e&&l(0,a=e.value),"latex_delimiters"in e&&l(5,f=e.latex_delimiters),"height"in e&&l(6,o=e.height),"value_is_output"in e&&l(16,r=e.value_is_output),"col_count"in e&&l(7,k=e.col_count),"row_count"in e&&l(8,S=e.row_count),"label"in e&&l(9,v=e.label),"wrap"in e&&l(10,J=e.wrap),"datatype"in e&&l(11,N=e.datatype),"scale"in e&&l(12,O=e.scale),"min_width"in e&&l(13,B=e.min_width),"gradio"in e&&l(14,c=e.gradio),"loading_status"in e&&l(15,D=e.loading_status)},n.$$.update=()=>{n.$$.dirty&131073&&JSON.stringify(a)!==u&&(l(17,u=JSON.stringify(a)),I())},[a,i,s,_,m,f,o,k,S,v,J,N,O,B,c,D,r,u,T,j]}class Q extends q{constructor(t){super(),C(this,t,P,M,F,{headers:1,elem_id:2,elem_classes:3,visible:4,value:0,latex_delimiters:5,height:6,value_is_output:16,col_count:7,row_count:8,label:9,wrap:10,datatype:11,scale:12,min_width:13,gradio:14,loading_status:15})}}const ee=Q;export{ee as default};
//# sourceMappingURL=index-7d175e82.js.map
