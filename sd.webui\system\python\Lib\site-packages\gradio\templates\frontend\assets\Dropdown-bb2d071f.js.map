{"version": 3, "file": "Dropdown-bb2d071f.js", "sources": ["../../../../js/icons/src/DropdownArrow.svelte", "../../../../js/icons/src/Remove.svelte", "../../../../js/dropdown/shared/DropdownOptions.svelte", "../../../../js/dropdown/shared/Dropdown.svelte"], "sourcesContent": ["<svg\n\tclass=\"dropdown-arrow\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"18\"\n\theight=\"18\"\n\tviewBox=\"0 0 18 18\"\n>\n\t<path d=\"M5 8l4 4 4-4z\" />\n</svg>\n\n<style>\n\t.dropdown-arrow {\n\t\tfill: var(--body-text-color);\n\t\tmargin-right: var(--size-2);\n\t\twidth: var(--size-5);\n\t}\n</style>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"16\"\n\theight=\"16\"\n\tviewBox=\"0 0 24 24\"\n>\n\t<path\n\t\td=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\timport { fly } from \"svelte/transition\";\n\timport { createEventDispatcher } from \"svelte\";\n\texport let value: string | string[] | undefined = undefined;\n\texport let filtered: string[];\n\texport let showOptions = false;\n\texport let activeOption: string | null;\n\texport let disabled = false;\n\n\tlet distance_from_top: number;\n\tlet distance_from_bottom: number;\n\tlet input_height: number;\n\tlet input_width: number;\n\tlet refElement: HTMLDivElement;\n\tlet listElement: HTMLUListElement;\n\tlet top: string | null, bottom: string | null, max_height: number;\n\tlet innerHeight: number;\n\n\tfunction calculate_window_distance(): void {\n\t\tconst { top: ref_top, bottom: ref_bottom } =\n\t\t\trefElement.getBoundingClientRect();\n\t\tdistance_from_top = ref_top;\n\t\tdistance_from_bottom = innerHeight - ref_bottom;\n\t}\n\n\tlet scroll_timeout: NodeJS.Timeout | null = null;\n\tfunction scroll_listener(): void {\n\t\tif (!showOptions) return;\n\t\tif (scroll_timeout !== null) {\n\t\t\tclearTimeout(scroll_timeout);\n\t\t}\n\n\t\tscroll_timeout = setTimeout(() => {\n\t\t\tcalculate_window_distance();\n\t\t\tscroll_timeout = null;\n\t\t}, 10);\n\t}\n\n\t$: {\n\t\tif (showOptions && refElement) {\n\t\t\tif (listElement && typeof value === \"string\") {\n\t\t\t\tlet elements = listElement.querySelectorAll(\"li\");\n\t\t\t\tfor (const element of Array.from(elements)) {\n\t\t\t\t\tif (element.getAttribute(\"data-value\") === value) {\n\t\t\t\t\t\tlistElement?.scrollTo?.(0, (element as HTMLLIElement).offsetTop);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tcalculate_window_distance();\n\t\t\tconst rect = refElement.parentElement?.getBoundingClientRect();\n\t\t\tinput_height = rect?.height || 0;\n\t\t\tinput_width = rect?.width || 0;\n\t\t}\n\t\tif (distance_from_bottom > distance_from_top) {\n\t\t\ttop = `${distance_from_top}px`;\n\t\t\tmax_height = distance_from_bottom;\n\t\t\tbottom = null;\n\t\t} else {\n\t\t\tbottom = `${distance_from_bottom + input_height}px`;\n\t\t\tmax_height = distance_from_top - input_height;\n\t\t\ttop = null;\n\t\t}\n\t}\n\n\tconst dispatch = createEventDispatcher();\n\t$: _value = Array.isArray(value) ? value : [value];\n</script>\n\n<svelte:window on:scroll={scroll_listener} bind:innerHeight />\n\n<div class=\"reference\" bind:this={refElement} />\n{#if showOptions && !disabled}\n\t<!-- TODO: fix-->\n\t<!-- svelte-ignore a11y-no-noninteractive-element-interactions -->\n\t<ul\n\t\tclass=\"options\"\n\t\ttransition:fly={{ duration: 200, y: 5 }}\n\t\ton:mousedown|preventDefault={(e) => dispatch(\"change\", e)}\n\t\tstyle:top\n\t\tstyle:bottom\n\t\tstyle:max-height={`calc(${max_height}px - var(--window-padding))`}\n\t\tstyle:width={input_width + \"px\"}\n\t\tbind:this={listElement}\n\t>\n\t\t{#each filtered as choice}\n\t\t\t<!-- TODO: fix-->\n\t\t\t<!-- svelte-ignore a11y-no-noninteractive-element-to-interactive-role -->\n\t\t\t<li\n\t\t\t\tclass=\"item\"\n\t\t\t\trole=\"button\"\n\t\t\t\tclass:selected={_value.includes(choice)}\n\t\t\t\tclass:active={activeOption === choice}\n\t\t\t\tclass:bg-gray-100={activeOption === choice}\n\t\t\t\tclass:dark:bg-gray-600={activeOption === choice}\n\t\t\t\tdata-value={choice}\n\t\t\t\taria-label={choice}\n\t\t\t\tdata-testid=\"dropdown-option\"\n\t\t\t>\n\t\t\t\t<span class:hide={!_value.includes(choice)} class=\"inner-item\">\n\t\t\t\t\t✓\n\t\t\t\t</span>\n\t\t\t\t{choice}\n\t\t\t</li>\n\t\t{/each}\n\t</ul>\n{/if}\n\n<style>\n\t.options {\n\t\t--window-padding: var(--size-8);\n\t\tposition: fixed;\n\t\tz-index: var(--layer-top);\n\t\tmargin-left: 0;\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tborder-radius: var(--container-radius);\n\t\tbackground: var(--background-fill-primary);\n\t\tmin-width: fit-content;\n\t\tmax-width: inherit;\n\t\toverflow: auto;\n\t\tcolor: var(--body-text-color);\n\t\tlist-style: none;\n\t}\n\n\t.item {\n\t\tdisplay: flex;\n\t\tcursor: pointer;\n\t\tpadding: var(--size-2);\n\t}\n\n\t.item:hover,\n\t.active {\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.inner-item {\n\t\tpadding-right: var(--size-1);\n\t}\n\n\t.hide {\n\t\tvisibility: hidden;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport DropdownOptions from \"./DropdownOptions.svelte\";\n\timport { createEventDispatcher, afterUpdate } from \"svelte\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\timport { Remove, DropdownArrow } from \"@gradio/icons\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let label: string;\n\texport let info: string | undefined = undefined;\n\texport let value: string | string[] | undefined;\n\tlet old_value = Array.isArray(value) ? value.slice() : value;\n\texport let value_is_output = false;\n\texport let multiselect = false;\n\texport let max_choices: number;\n\texport let choices: string[];\n\texport let disabled = false;\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let allow_custom_value = false;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: string | string[] | undefined;\n\t\tinput: undefined;\n\t\tselect: SelectData;\n\t\tblur: undefined;\n\t\tfocus: undefined;\n\t}>();\n\n\tlet inputValue: string | undefined,\n\t\tactiveOption: string | null,\n\t\tshowOptions = false,\n\t\tfilterInput: HTMLElement;\n\n\t$: if (typeof value === \"string\" || value === null) {\n\t\tinputValue = value;\n\t}\n\n\tlet old_choices: string[] = [];\n\tlet filtered: string[] = [];\n\n\t$: old_choices, inputValue, handle_filter();\n\n\tfunction handle_filter(): void {\n\t\tif (choices !== old_choices || typeof inputValue === \"string\") {\n\t\t\told_choices = choices;\n\t\t\tfiltered = choices.filter((o) =>\n\t\t\t\tinputValue ? o.toLowerCase().includes(inputValue.toLowerCase()) : o\n\t\t\t);\n\t\t}\n\t}\n\t$: if (!activeOption || !filtered.includes(activeOption)) {\n\t\tactiveOption = filtered.length ? filtered[0] : null;\n\t}\n\n\tfunction handle_change(): void {\n\t\tdispatch(\"change\", value);\n\t\tif (!value_is_output) {\n\t\t\tdispatch(\"input\");\n\t\t}\n\t}\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\n\t$: {\n\t\tif (JSON.stringify(value) != JSON.stringify(old_value)) {\n\t\t\told_value = Array.isArray(value) ? value.slice() : value;\n\t\t\thandle_change();\n\t\t}\n\t}\n\n\tfunction add(option: string): void {\n\t\tvalue = value as string[];\n\t\tif (!max_choices || value.length < max_choices) {\n\t\t\tvalue.push(option);\n\t\t\tdispatch(\"select\", {\n\t\t\t\tindex: choices.indexOf(option),\n\t\t\t\tvalue: option,\n\t\t\t\tselected: true\n\t\t\t});\n\t\t}\n\t\tvalue = value;\n\t}\n\n\tfunction remove(option: string): void {\n\t\tif (!disabled) {\n\t\t\tvalue = value as string[];\n\t\t\tvalue = value.filter((v: string) => v !== option);\n\t\t}\n\t\tdispatch(\"select\", {\n\t\t\tindex: choices.indexOf(option),\n\t\t\tvalue: option,\n\t\t\tselected: false\n\t\t});\n\t}\n\n\tfunction remove_all(e: any): void {\n\t\tvalue = [];\n\t\tinputValue = \"\";\n\t\te.preventDefault();\n\t}\n\n\tfunction handle_blur(e: FocusEvent): void {\n\t\tif (multiselect) {\n\t\t\tinputValue = \"\";\n\t\t} else if (!allow_custom_value) {\n\t\t\tif (value !== inputValue) {\n\t\t\t\tif (typeof value === \"string\" && inputValue == \"\") {\n\t\t\t\t\tinputValue = value;\n\t\t\t\t} else {\n\t\t\t\t\tvalue = undefined;\n\t\t\t\t\tinputValue = \"\";\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tshowOptions = false;\n\t\tdispatch(\"blur\");\n\t}\n\n\tfunction handle_focus(e: FocusEvent): void {\n\t\tdispatch(\"focus\");\n\t\tshowOptions = true;\n\t\tfiltered = choices;\n\t}\n\n\tfunction handleOptionMousedown(e: any): void {\n\t\tconst option = e.detail.target.dataset.value;\n\t\tif (allow_custom_value) {\n\t\t\tinputValue = option;\n\t\t}\n\n\t\tif (option !== undefined) {\n\t\t\tif (multiselect) {\n\t\t\t\tif (value?.includes(option)) {\n\t\t\t\t\tremove(option);\n\t\t\t\t} else {\n\t\t\t\t\tadd(option);\n\t\t\t\t}\n\t\t\t\tinputValue = \"\";\n\t\t\t} else {\n\t\t\t\tvalue = option;\n\t\t\t\tinputValue = option;\n\t\t\t\tshowOptions = false;\n\t\t\t\tdispatch(\"select\", {\n\t\t\t\t\tindex: choices.indexOf(option),\n\t\t\t\t\tvalue: option,\n\t\t\t\t\tselected: true\n\t\t\t\t});\n\t\t\t\tfilterInput.blur();\n\t\t\t}\n\t\t}\n\t}\n\n\t// eslint-disable-next-line complexity\n\tfunction handleKeydown(e: any): void {\n\t\tif (e.key === \"Enter\" && activeOption != undefined) {\n\t\t\tif (!multiselect) {\n\t\t\t\tif (value !== activeOption) {\n\t\t\t\t\tvalue = activeOption;\n\t\t\t\t\tdispatch(\"select\", {\n\t\t\t\t\t\tindex: choices.indexOf(value),\n\t\t\t\t\t\tvalue: value,\n\t\t\t\t\t\tselected: true\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tinputValue = activeOption;\n\t\t\t\tshowOptions = false;\n\t\t\t\tfilterInput.blur();\n\t\t\t} else if (multiselect && Array.isArray(value)) {\n\t\t\t\tvalue.includes(activeOption) ? remove(activeOption) : add(activeOption);\n\t\t\t\tinputValue = \"\";\n\t\t\t}\n\t\t} else {\n\t\t\tshowOptions = true;\n\t\t\tif (e.key === \"ArrowUp\" || e.key === \"ArrowDown\") {\n\t\t\t\tif (activeOption === null) {\n\t\t\t\t\tactiveOption = filtered[0];\n\t\t\t\t}\n\t\t\t\tconst increment = e.key === \"ArrowUp\" ? -1 : 1;\n\t\t\t\tconst calcIndex = filtered.indexOf(activeOption) + increment;\n\t\t\t\tactiveOption =\n\t\t\t\t\tcalcIndex < 0\n\t\t\t\t\t\t? filtered[filtered.length - 1]\n\t\t\t\t\t\t: calcIndex === filtered.length\n\t\t\t\t\t\t? filtered[0]\n\t\t\t\t\t\t: filtered[calcIndex];\n\t\t\t\te.preventDefault();\n\t\t\t} else if (e.key === \"Escape\") {\n\t\t\t\tshowOptions = false;\n\t\t\t} else if (e.key === \"Backspace\") {\n\t\t\t\tif (\n\t\t\t\t\tmultiselect &&\n\t\t\t\t\t(!inputValue || inputValue === \"\") &&\n\t\t\t\t\tArray.isArray(value) &&\n\t\t\t\t\tvalue.length > 0\n\t\t\t\t) {\n\t\t\t\t\tremove(value[value.length - 1]);\n\t\t\t\t\tinputValue = \"\";\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tshowOptions = true;\n\t\t\t}\n\t\t}\n\t}\n\n\t$: {\n\t\tif (JSON.stringify(value) != JSON.stringify(old_value)) {\n\t\t\tdispatch(\"change\", value);\n\t\t\told_value = Array.isArray(value) ? value.slice() : value;\n\t\t}\n\t}\n</script>\n\n<label class:container>\n\t<BlockTitle {show_label} {info}>{label}</BlockTitle>\n\n\t<div class=\"wrap\">\n\t\t<div class=\"wrap-inner\" class:showOptions>\n\t\t\t{#if multiselect && Array.isArray(value)}\n\t\t\t\t{#each value as s}\n\t\t\t\t\t<!-- TODO: fix -->\n\t\t\t\t\t<!-- svelte-ignore a11y-click-events-have-key-events -->\n\t\t\t\t\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t\t\t\t\t<div on:click|preventDefault={() => remove(s)} class=\"token\">\n\t\t\t\t\t\t<span>{s}</span>\n\t\t\t\t\t\t{#if !disabled}\n\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\tclass:hidden={disabled}\n\t\t\t\t\t\t\t\tclass=\"token-remove\"\n\t\t\t\t\t\t\t\ttitle={$_(\"common.remove\") + \" \" + s}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<Remove />\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</div>\n\t\t\t\t{/each}\n\t\t\t{/if}\n\t\t\t<div class=\"secondary-wrap\">\n\t\t\t\t<input\n\t\t\t\t\tclass=\"border-none\"\n\t\t\t\t\tclass:subdued={value !== inputValue && !allow_custom_value}\n\t\t\t\t\t{disabled}\n\t\t\t\t\tautocomplete=\"off\"\n\t\t\t\t\tbind:value={inputValue}\n\t\t\t\t\tbind:this={filterInput}\n\t\t\t\t\ton:keydown={handleKeydown}\n\t\t\t\t\ton:keyup={() => {\n\t\t\t\t\t\tif (allow_custom_value) {\n\t\t\t\t\t\t\tvalue = inputValue;\n\t\t\t\t\t\t}\n\t\t\t\t\t}}\n\t\t\t\t\ton:blur={handle_blur}\n\t\t\t\t\ton:focus={handle_focus}\n\t\t\t\t/>\n\t\t\t\t<!-- TODO: fix -->\n\t\t\t\t<!-- svelte-ignore a11y-click-events-have-key-events -->\n\t\t\t\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t\t\t\t<div\n\t\t\t\t\tclass:hide={!multiselect || !value?.length || disabled}\n\t\t\t\t\tclass=\"token-remove remove-all\"\n\t\t\t\t\ttitle={$_(\"common.clear\")}\n\t\t\t\t\ton:click={remove_all}\n\t\t\t\t>\n\t\t\t\t\t<Remove />\n\t\t\t\t</div>\n\t\t\t\t<DropdownArrow />\n\t\t\t</div>\n\t\t</div>\n\t\t<DropdownOptions\n\t\t\tbind:value\n\t\t\t{showOptions}\n\t\t\t{filtered}\n\t\t\t{activeOption}\n\t\t\t{disabled}\n\t\t\ton:change={handleOptionMousedown}\n\t\t/>\n\t</div>\n</label>\n\n<style>\n\tlabel:not(.container),\n\tlabel:not(.container) .wrap,\n\tlabel:not(.container) .wrap-inner,\n\tlabel:not(.container) .secondary-wrap,\n\tlabel:not(.container) .token,\n\tlabel:not(.container) input {\n\t\theight: 100%;\n\t}\n\t.container .wrap {\n\t\tbox-shadow: var(--input-shadow);\n\t\tborder: var(--input-border-width) solid var(--border-color-primary);\n\t}\n\n\t.wrap {\n\t\tposition: relative;\n\t\tborder-radius: var(--input-radius);\n\t\tbackground: var(--input-background-fill);\n\t}\n\n\t.wrap:focus-within {\n\t\tbox-shadow: var(--input-shadow-focus);\n\t\tborder-color: var(--input-border-color-focus);\n\t}\n\n\t.wrap-inner {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-wrap: wrap;\n\t\talign-items: center;\n\t\tgap: var(--checkbox-label-gap);\n\t\tpadding: var(--checkbox-label-padding);\n\t}\n\n\t.token {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\ttransition: var(--button-transition);\n\t\tcursor: pointer;\n\t\tbox-shadow: var(--checkbox-label-shadow);\n\t\tborder: var(--checkbox-label-border-width) solid\n\t\t\tvar(--checkbox-label-border-color);\n\t\tborder-radius: var(--button-small-radius);\n\t\tbackground: var(--checkbox-label-background-fill);\n\t\tpadding: var(--checkbox-label-padding);\n\t\tcolor: var(--checkbox-label-text-color);\n\t\tfont-weight: var(--checkbox-label-text-weight);\n\t\tfont-size: var(--checkbox-label-text-size);\n\t\tline-height: var(--line-md);\n\t}\n\n\t.token > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\t.token-remove {\n\t\tfill: var(--body-text-color);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tcursor: pointer;\n\t\tborder: var(--checkbox-border-width) solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--background-fill-primary);\n\t\tpadding: var(--size-0-5);\n\t\twidth: 18px;\n\t\theight: 18px;\n\t}\n\n\t.secondary-wrap {\n\t\tdisplay: flex;\n\t\tflex: 1 1 0%;\n\t\talign-items: center;\n\t\tborder: none;\n\t\tmin-width: min-content;\n\t}\n\n\tinput {\n\t\tmargin: var(--spacing-sm);\n\t\toutline: none;\n\t\tborder: none;\n\t\tbackground: inherit;\n\t\twidth: var(--size-full);\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t}\n\n\tinput:disabled {\n\t\t-webkit-text-fill-color: var(--body-text-color);\n\t\t-webkit-opacity: 1;\n\t\topacity: 1;\n\t\tcursor: not-allowed;\n\t}\n\n\t.remove-all {\n\t\tmargin-left: var(--size-1);\n\t\twidth: 20px;\n\t\theight: 20px;\n\t}\n\n\t.hide {\n\t\tdisplay: none;\n\t}\n\n\t.subdued {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "ctx", "set_style", "ul", "i", "ul_transition", "create_bidirectional_transition", "fly", "toggle_class", "li", "span", "set_data", "t2", "t2_value", "if_block", "create_if_block", "div", "value", "$$props", "filtered", "showOptions", "activeOption", "disabled", "distance_from_top", "distance_from_bottom", "input_height", "input_width", "refElement", "listElement", "top", "bottom", "max_height", "innerHeight", "calculate_window_distance", "ref_top", "ref_bottom", "$$invalidate", "scroll_timeout", "scroll_listener", "dispatch", "createEventDispatcher", "$$value", "mousedown_handler", "e", "elements", "element", "rect", "_value", "each_blocks", "attr", "div_title_value", "current", "dirty", "create_if_block_1", "t0", "t0_value", "show_if", "div0", "div0_title_value", "label_1", "div3", "div2", "div1", "input", "label", "info", "old_value", "value_is_output", "multiselect", "max_choices", "choices", "show_label", "container", "allow_custom_value", "inputValue", "filterInput", "old_choices", "handle_filter", "o", "handle_change", "afterUpdate", "add", "option", "remove", "v", "remove_all", "handle_blur", "handle_focus", "handleOptionMousedown", "handleKeydown", "increment", "calcIndex", "click_handler", "s"], "mappings": "yoBAAAA,EAQKC,EAAAC,EAAAC,CAAA,EADJC,EAAyBF,EAAAG,CAAA,4XCP1BL,EASKC,EAAAC,EAAAC,CAAA,EAHJC,EAECF,EAAAG,CAAA,0LC6EOC,EAAQ,CAAA,CAAA,uBAAb,OAAI,GAAA,iLAJoBA,EAAU,CAAA,8BAAA,EACvBC,EAAAC,EAAA,QAAAF,KAAc,IAAI,UAPhCN,EA8BIC,EAAAO,EAAAL,CAAA,+HApBIG,EAAQ,CAAA,CAAA,oBAAb,OAAIG,GAAA,EAAA,mHAAJ,wFAJwBH,EAAU,CAAA,8BAAA,QACvBC,EAAAC,EAAA,QAAAF,KAAc,IAAI,2BALbI,IAAAA,EAAAC,GAAAH,EAAAI,GAAA,CAAA,SAAU,IAAK,EAAG,CAAC,EAAA,EAAA,+BAAnBF,IAAAA,EAAAC,GAAAH,EAAAI,GAAA,CAAA,SAAU,IAAK,EAAG,CAAC,EAAA,EAAA,wGAyBlCN,EAAM,EAAA,EAAA,uIAHYA,EAAM,EAAA,EAAC,SAASA,EAAM,EAAA,CAAA,CAAA,6EAJ7BA,EAAM,EAAA,CAAA,qBACNA,EAAM,EAAA,CAAA,sDALFA,EAAM,EAAA,EAAC,SAASA,EAAM,EAAA,CAAA,CAAA,EACxBO,EAAAC,EAAA,SAAAR,OAAiBA,EAAM,EAAA,CAAA,EAClBO,EAAAC,EAAA,cAAAR,OAAiBA,EAAM,EAAA,CAAA,EAClBO,EAAAC,EAAA,mBAAAR,OAAiBA,EAAM,EAAA,CAAA,UANhDN,EAeIC,EAAAa,EAAAX,CAAA,EAJHC,EAEMU,EAAAC,CAAA,mDAFaT,EAAM,EAAA,EAAC,SAASA,EAAM,EAAA,CAAA,CAAA,cAGxCA,EAAM,EAAA,EAAA,KAAAU,GAAAC,EAAAC,CAAA,cAPKZ,EAAM,EAAA,oCACNA,EAAM,EAAA,+CALFA,EAAM,EAAA,EAAC,SAASA,EAAM,EAAA,CAAA,CAAA,OACxBO,EAAAC,EAAA,SAAAR,OAAiBA,EAAM,EAAA,CAAA,OAClBO,EAAAC,EAAA,cAAAR,OAAiBA,EAAM,EAAA,CAAA,OAClBO,EAAAC,EAAA,mBAAAR,OAAiBA,EAAM,EAAA,CAAA,yDAtB9C,IAAAa,EAAAb,OAAgBA,EAAQ,CAAA,GAAAc,GAAAd,CAAA,+EAD7BN,EAA+CC,EAAAoB,EAAAlB,CAAA,kEAFrBG,EAAe,EAAA,CAAA,4CAGpCA,OAAgBA,EAAQ,CAAA,uNArEjB,CAAA,MAAAgB,EAAuC,MAAS,EAAAC,GAChD,SAAAC,CAAkB,EAAAD,EAClB,CAAA,YAAAE,EAAc,EAAK,EAAAF,GACnB,aAAAG,CAA2B,EAAAH,EAC3B,CAAA,SAAAI,EAAW,EAAK,EAAAJ,EAEvBK,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAAoBC,EAAuBC,EAC3CC,WAEKC,GAAyB,OACzB,IAAKC,EAAS,OAAQC,GAC7BR,EAAW,wBACZS,EAAA,GAAAb,EAAoBW,CAAO,OAC3BV,EAAuBQ,EAAcG,CAAU,EAG5C,IAAAE,EAAwC,cACnCC,GAAe,CAClBlB,IACDiB,IAAmB,MACtB,aAAaA,CAAc,EAG5BA,EAAiB,gBAChBJ,IACAI,EAAiB,MACf,KA8BE,MAAAE,EAAWC,sFAMgBb,EAAUc,WAOZ,MAAAC,EAAAC,GAAMJ,EAAS,SAAUI,CAAC,2CAK7Cf,EAAWa,wPA7CtB,CACI,GAAArB,GAAeO,EAAU,IACxBC,GAAW,OAAWX,GAAU,SAAQ,CACvC,IAAA2B,EAAWhB,EAAY,iBAAiB,IAAI,EACrC,UAAAiB,KAAW,MAAM,KAAKD,CAAQ,EACpC,GAAAC,EAAQ,aAAa,YAAY,IAAM5B,EAAK,CAC/CW,GAAa,WAAW,EAAIiB,EAA0B,SAAS,SAKlEZ,IACM,MAAAa,EAAOnB,EAAW,eAAe,sBAAqB,EAC5DS,EAAA,GAAAX,EAAeqB,GAAM,QAAU,CAAC,EAChCV,EAAA,EAAAV,EAAcoB,GAAM,OAAS,CAAC,EAE3BtB,EAAuBD,GAC1Ba,EAAA,EAAAP,KAASN,KAAiB,EAC1Ba,EAAA,EAAAL,EAAaP,CAAoB,EACjCY,EAAA,EAAAN,EAAS,IAAI,QAEbA,EAAM,GAAMN,EAAuBC,KAAY,MAC/CM,EAAaR,EAAoBE,CAAY,EAC7CW,EAAA,EAAAP,EAAM,IAAI,qBAKXO,EAAA,GAAEW,EAAS,MAAM,QAAQ9B,CAAK,EAAIA,EAAK,CAAIA,CAAK,CAAA,0QCqJhBhB,EAAK,CAAA,CAAA,wCAALA,EAAK,CAAA,CAAA,6CAK5BA,EAAK,CAAA,CAAA,uBAAV,OAAIG,GAAA,kNAACH,EAAK,CAAA,CAAA,oBAAV,OAAI,GAAA,EAAA,mHAAJ,OAAI,EAAA+C,EAAA,OAAA,GAAA,0CAAJ,OAAI5C,GAAA,sOAUK6C,EAAAjC,EAAA,QAAAkC,EAAAjD,EAAG,EAAA,EAAA,eAAe,EAAI,IAAMA,EAAC,EAAA,CAAA,eAFtBA,EAAQ,CAAA,CAAA,UADvBN,EAMKC,EAAAoB,EAAAlB,CAAA,4BAHG,CAAAqD,GAAAC,EAAA,CAAA,EAAA,MAAAF,KAAAA,EAAAjD,EAAG,EAAA,EAAA,eAAe,EAAI,IAAMA,EAAC,EAAA,gDAFtBA,EAAQ,CAAA,CAAA,iHAHjBA,EAAC,EAAA,EAAA,kBACFA,EAAQ,CAAA,GAAAoD,GAAApD,CAAA,4KAFfN,EAWKC,EAAAoB,EAAAlB,CAAA,EAVJC,EAAeiB,EAAAN,CAAA,2GAART,EAAC,EAAA,EAAA,KAAAU,GAAA2C,EAAAC,CAAA,EACFtD,EAAQ,CAAA,uMAPZuD,EAAAvD,EAAe,CAAA,GAAA,MAAM,QAAQA,EAAK,CAAA,CAAA,kWAwD5BA,EAAqB,EAAA,CAAA,0RAlCfA,EAAK,CAAA,IAAKA,EAAU,CAAA,GAAA,CAAKA,EAAkB,CAAA,CAAA,wDAoBnDgD,EAAAQ,EAAA,QAAAC,EAAAzD,MAAG,cAAc,CAAA,EAFXO,EAAAiD,EAAA,OAAA,CAAAxD,EAAgB,CAAA,GAAA,CAAAA,EAAO,CAAA,GAAA,QAAUA,EAAQ,CAAA,CAAA,gNA7C3DN,EAgEOC,EAAA+D,EAAA7D,CAAA,qBA7DNC,EA4DK4D,EAAAC,CAAA,EA3DJ7D,EAkDK6D,EAAAC,CAAA,wBA9BJ9D,EA6BK8D,EAAAC,CAAA,EA5BJ/D,EAeC+D,EAAAC,CAAA,OAVY9D,EAAU,CAAA,CAAA,kBAcvBF,EAOK+D,EAAAL,CAAA,iGAnBQxD,EAAa,EAAA,CAAA,gCAMhBA,EAAW,EAAA,CAAA,cACVA,EAAY,EAAA,CAAA,cASZA,EAAU,EAAA,CAAA,qIA3CjBmD,EAAA,CAAA,EAAA,IAAAI,EAAAvD,EAAe,CAAA,GAAA,MAAM,QAAQA,EAAK,CAAA,CAAA,0JAyBzBA,EAAU,CAAA,QAAVA,EAAU,CAAA,CAAA,gCAHPA,EAAK,CAAA,IAAKA,EAAU,CAAA,GAAA,CAAKA,EAAkB,CAAA,CAAA,GAoBnD,CAAAkD,GAAAC,EAAA,CAAA,EAAA,MAAAM,KAAAA,EAAAzD,MAAG,cAAc,mCAFXO,EAAAiD,EAAA,OAAA,CAAAxD,EAAgB,CAAA,GAAA,CAAAA,EAAO,CAAA,GAAA,QAAUA,EAAQ,CAAA,CAAA,ikBA3P/C,MAAA+D,CAAa,EAAA9C,EACb,CAAA,KAAA+C,EAA2B,MAAS,EAAA/C,GACpC,MAAAD,CAAoC,EAAAC,EAC3CgD,EAAY,MAAM,QAAQjD,CAAK,EAAIA,EAAM,MAAK,EAAKA,EAC5C,CAAA,gBAAAkD,EAAkB,EAAK,EAAAjD,EACvB,CAAA,YAAAkD,EAAc,EAAK,EAAAlD,GACnB,YAAAmD,CAAmB,EAAAnD,GACnB,QAAAoD,CAAiB,EAAApD,EACjB,CAAA,SAAAI,EAAW,EAAK,EAAAJ,GAChB,WAAAqD,CAAmB,EAAArD,EACnB,CAAA,UAAAsD,EAAY,EAAI,EAAAtD,EAChB,CAAA,mBAAAuD,EAAqB,EAAK,EAAAvD,EAE/B,MAAAqB,EAAWC,KAQb,IAAAkC,EACHrD,EACAD,EAAc,GACduD,EAMGC,EAAW,CAAA,EACXzD,EAAQ,CAAA,WAIH0D,GAAa,EACjBP,IAAYM,GAAsB,OAAAF,GAAe,YACpDtC,EAAA,GAAAwC,EAAcN,CAAO,EACrBlC,EAAA,GAAAjB,EAAWmD,EAAQ,OAAQQ,GAC1BJ,EAAaI,EAAE,YAAW,EAAG,SAASJ,EAAW,YAAW,CAAA,EAAMI,CAAC,CAAA,YAQ7DC,GAAa,CACrBxC,EAAS,SAAUtB,CAAK,EACnBkD,GACJ5B,EAAS,OAAO,EAGlByC,GAAW,IAAA,CACV5C,EAAA,GAAA+B,EAAkB,EAAK,IAUf,SAAAc,EAAIC,EAAc,SAErB,CAAAb,GAAepD,EAAM,OAASoD,KAClCpD,EAAM,KAAKiE,CAAM,EACjB3C,EAAS,SAAQ,CAChB,MAAO+B,EAAQ,QAAQY,CAAM,EAC7B,MAAOA,EACP,SAAU,aAMJ,SAAAC,EAAOD,EAAc,CACxB5D,eAEJL,EAAQA,EAAM,OAAQmE,GAAcA,IAAMF,CAAM,CAAA,GAEjD3C,EAAS,SAAQ,CAChB,MAAO+B,EAAQ,QAAQY,CAAM,EAC7B,MAAOA,EACP,SAAU,KAIH,SAAAG,EAAW1C,EAAM,KACzB1B,EAAK,CAAA,CAAA,EACLmB,EAAA,EAAAsC,EAAa,EAAE,EACf/B,EAAE,eAAc,EAGR,SAAA2C,EAAY3C,EAAa,CAC7ByB,EACHhC,EAAA,EAAAsC,EAAa,EAAE,EACJD,GACPxD,IAAUyD,IACF,OAAAzD,GAAU,UAAYyD,GAAc,GAC9CtC,EAAA,EAAAsC,EAAazD,CAAK,GAElBmB,EAAA,EAAAnB,EAAQ,MAAS,EACjBmB,EAAA,EAAAsC,EAAa,EAAE,IAIlBtC,EAAA,GAAAhB,EAAc,EAAK,EACnBmB,EAAS,MAAM,EAGP,SAAAgD,EAAa5C,EAAa,CAClCJ,EAAS,OAAO,EAChBH,EAAA,GAAAhB,EAAc,EAAI,EAClBgB,EAAA,GAAAjB,EAAWmD,CAAO,EAGV,SAAAkB,GAAsB7C,EAAM,OAC9BuC,EAASvC,EAAE,OAAO,OAAO,QAAQ,MACnC8B,GACHrC,EAAA,EAAAsC,EAAaQ,CAAM,EAGhBA,IAAW,SACVd,GACCnD,GAAO,SAASiE,CAAM,EACzBC,EAAOD,CAAM,EAEbD,EAAIC,CAAM,EAEX9C,EAAA,EAAAsC,EAAa,EAAE,IAEftC,EAAA,EAAAnB,EAAQiE,CAAM,EACd9C,EAAA,EAAAsC,EAAaQ,CAAM,EACnB9C,EAAA,GAAAhB,EAAc,EAAK,EACnBmB,EAAS,SAAQ,CAChB,MAAO+B,EAAQ,QAAQY,CAAM,EAC7B,MAAOA,EACP,SAAU,KAEXP,EAAY,KAAI,IAMV,SAAAc,GAAc9C,EAAM,CACxB,GAAAA,EAAE,MAAQ,SAAWtB,GAAgB,KACnC+C,EAYMA,GAAe,MAAM,QAAQnD,CAAK,IAC5CA,EAAM,SAASI,CAAY,EAAI8D,EAAO9D,CAAY,EAAI4D,EAAI5D,CAAY,EACtEe,EAAA,EAAAsC,EAAa,EAAE,IAbXzD,IAAUI,IACbe,EAAA,EAAAnB,EAAQI,CAAY,EACpBkB,EAAS,SAAQ,CAChB,MAAO+B,EAAQ,QAAQrD,CAAK,EACrB,MAAAA,EACP,SAAU,MAGZmB,EAAA,EAAAsC,EAAarD,CAAY,EACzBe,EAAA,GAAAhB,EAAc,EAAK,EACnBuD,EAAY,KAAI,WAMjBvC,EAAA,GAAAhB,EAAc,EAAI,EACduB,EAAE,MAAQ,WAAaA,EAAE,MAAQ,YAAW,CAC3CtB,IAAiB,UACpBA,EAAeF,EAAS,CAAC,CAAA,QAEpBuE,EAAY/C,EAAE,MAAQ,UAAS,GAAQ,EACvCgD,GAAYxE,EAAS,QAAQE,CAAY,EAAIqE,MACnDrE,EACCsE,GAAY,EACTxE,EAASA,EAAS,OAAS,CAAC,EAC5BwE,KAAcxE,EAAS,OACvBA,EAAS,CAAC,EACVA,EAASwE,EAAS,CAAA,EACtBhD,EAAE,eAAc,OACNA,EAAE,MAAQ,SACpBP,EAAA,GAAAhB,EAAc,EAAK,EACTuB,EAAE,MAAQ,YAEnByB,KACEM,GAAcA,IAAe,KAC/B,MAAM,QAAQzD,CAAK,GACnBA,EAAM,OAAS,IAEfkE,EAAOlE,EAAMA,EAAM,OAAS,CAAC,CAAA,EAC7BmB,EAAA,EAAAsC,EAAa,EAAE,GAGhBtC,EAAA,GAAAhB,EAAc,EAAI,EAuBmB,MAAAwE,GAAAC,GAAAV,EAAOU,CAAC,gBAoBhCnB,EAAU,KAAA,8DACXC,EAAWlC,0BAGjBgC,GACHrC,EAAA,EAAAnB,EAAQyD,CAAU,kfAvNVzD,GAAU,UAAYA,IAAU,OAC7CmB,EAAA,EAAAsC,EAAazD,CAAK,0BAMS4D,EAAa,wBAUjCxD,GAAY,CAAKF,EAAS,SAASE,CAAY,QACtDA,EAAeF,EAAS,OAASA,EAAS,CAAC,EAAI,IAAI,yBAc/C,KAAK,UAAUF,CAAK,GAAK,KAAK,UAAUiD,CAAS,SACpDA,EAAY,MAAM,QAAQjD,CAAK,EAAIA,EAAM,QAAUA,CAAK,EACxD8D,4BA2IG,KAAK,UAAU9D,CAAK,GAAK,KAAK,UAAUiD,CAAS,IACpD3B,EAAS,SAAUtB,CAAK,OACxBiD,EAAY,MAAM,QAAQjD,CAAK,EAAIA,EAAM,QAAUA,CAAK"}