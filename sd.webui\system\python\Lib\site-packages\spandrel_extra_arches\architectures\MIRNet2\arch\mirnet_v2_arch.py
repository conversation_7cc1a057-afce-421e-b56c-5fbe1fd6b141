## Learning Enriched Features for Fast Image Restoration and Enhancement
## <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON>
## IEEE Transactions on Pattern Analysis and Machine Intelligence (TPAMI)
## https://www.waqaszamir.com/publication/zamir-2022-mirnetv2/

# --- Imports --- #
from __future__ import annotations

from typing import Literal

import numpy as np
import torch
import torch.nn as nn

from spandrel.util import store_hyperparameters


##########################################################################
##---------- Selective Kernel Feature Fusion (SKFF) ----------
class SKFF(nn.Module):
    def __init__(self, in_channels, height=3, reduction=8, bias=False):
        super().__init__()

        self.height = height
        d = max(int(in_channels / reduction), 4)

        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.conv_du = nn.Sequential(
            nn.Conv2d(in_channels, d, 1, padding=0, bias=bias), nn.LeakyReLU(0.2)
        )

        self.fcs = nn.ModuleList([])
        for _ in range(self.height):
            self.fcs.append(
                nn.Conv2d(d, in_channels, kernel_size=1, stride=1, bias=bias)
            )

        self.softmax = nn.Softmax(dim=1)

    def forward(self, inp_feats):
        batch_size = inp_feats[0].shape[0]
        n_feats = inp_feats[0].shape[1]

        inp_feats = torch.cat(inp_feats, dim=1)
        inp_feats = inp_feats.view(
            batch_size, self.height, n_feats, inp_feats.shape[2], inp_feats.shape[3]
        )

        feats_U = torch.sum(inp_feats, dim=1)
        feats_S = self.avg_pool(feats_U)
        feats_Z = self.conv_du(feats_S)

        attention_vectors = [fc(feats_Z) for fc in self.fcs]
        attention_vectors = torch.cat(attention_vectors, dim=1)
        attention_vectors = attention_vectors.view(
            batch_size, self.height, n_feats, 1, 1
        )
        # stx()
        attention_vectors = self.softmax(attention_vectors)

        feats_V = torch.sum(inp_feats * attention_vectors, dim=1)

        return feats_V


class ContextBlock(nn.Module):
    def __init__(self, n_feat, bias=False):
        super().__init__()

        self.conv_mask = nn.Conv2d(n_feat, 1, kernel_size=1, bias=bias)
        self.softmax = nn.Softmax(dim=2)

        self.channel_add_conv = nn.Sequential(
            nn.Conv2d(n_feat, n_feat, kernel_size=1, bias=bias),
            nn.LeakyReLU(0.2),
            nn.Conv2d(n_feat, n_feat, kernel_size=1, bias=bias),
        )

    def modeling(self, x):
        batch, channel, height, width = x.size()
        input_x = x
        # [N, C, H * W]
        input_x = input_x.view(batch, channel, height * width)
        # [N, 1, C, H * W]
        input_x = input_x.unsqueeze(1)
        # [N, 1, H, W]
        context_mask = self.conv_mask(x)
        # [N, 1, H * W]
        context_mask = context_mask.view(batch, 1, height * width)
        # [N, 1, H * W]
        context_mask = self.softmax(context_mask)
        # [N, 1, H * W, 1]
        context_mask = context_mask.unsqueeze(3)
        # [N, 1, C, 1]
        context = torch.matmul(input_x, context_mask)
        # [N, C, 1, 1]
        context = context.view(batch, channel, 1, 1)

        return context

    def forward(self, x):
        # [N, C, 1, 1]
        context = self.modeling(x)

        # [N, C, 1, 1]
        channel_add_term = self.channel_add_conv(context)
        x = x + channel_add_term

        return x


##########################################################################
### --------- Residual Context Block (RCB) ----------
class RCB(nn.Module):
    def __init__(self, n_feat, bias=False, groups=1):
        super().__init__()

        act = nn.LeakyReLU(0.2)

        self.body = nn.Sequential(
            nn.Conv2d(
                n_feat,
                n_feat,
                kernel_size=3,
                stride=1,
                padding=1,
                bias=bias,
                groups=groups,
            ),
            act,
            nn.Conv2d(
                n_feat,
                n_feat,
                kernel_size=3,
                stride=1,
                padding=1,
                bias=bias,
                groups=groups,
            ),
        )

        self.act = act

        self.gcnet = ContextBlock(n_feat, bias=bias)

    def forward(self, x):
        res = self.body(x)
        res = self.act(self.gcnet(res))
        res += x
        return res


##########################################################################
##---------- Resizing Modules ----------
class Down(nn.Module):
    def __init__(self, in_channels, chan_factor, bias=False):
        super().__init__()

        self.bot = nn.Sequential(
            nn.AvgPool2d(2, ceil_mode=True, count_include_pad=False),
            nn.Conv2d(
                in_channels,
                int(in_channels * chan_factor),
                1,
                stride=1,
                padding=0,
                bias=bias,
            ),
        )

    def forward(self, x):
        return self.bot(x)


class DownSample(nn.Module):
    def __init__(self, in_channels, scale_factor, chan_factor=2, kernel_size=3):
        super().__init__()
        self.scale_factor = int(np.log2(scale_factor))

        modules_body = []
        for _ in range(self.scale_factor):
            modules_body.append(Down(in_channels, chan_factor))
            in_channels = int(in_channels * chan_factor)

        self.body = nn.Sequential(*modules_body)

    def forward(self, x):
        x = self.body(x)
        return x


class Up(nn.Module):
    def __init__(self, in_channels, chan_factor, bias=False):
        super().__init__()

        self.bot = nn.Sequential(
            nn.Conv2d(
                in_channels,
                int(in_channels // chan_factor),
                1,
                stride=1,
                padding=0,
                bias=bias,
            ),
            nn.Upsample(scale_factor=2, mode="bilinear", align_corners=bias),
        )

    def forward(self, x):
        return self.bot(x)


class UpSample(nn.Module):
    def __init__(self, in_channels, scale_factor, chan_factor=2, kernel_size=3):
        super().__init__()
        self.scale_factor = int(np.log2(scale_factor))

        modules_body = []
        for _ in range(self.scale_factor):
            modules_body.append(Up(in_channels, chan_factor))
            in_channels = int(in_channels // chan_factor)

        self.body = nn.Sequential(*modules_body)

    def forward(self, x):
        x = self.body(x)
        return x


##########################################################################
##---------- Multi-Scale Resiudal Block (MRB) ----------
class MRB(nn.Module):
    def __init__(self, n_feat, chan_factor, bias, groups):
        super().__init__()

        self.dau_top = RCB(int(n_feat * chan_factor**0), bias=bias, groups=groups)
        self.dau_mid = RCB(int(n_feat * chan_factor**1), bias=bias, groups=groups)
        self.dau_bot = RCB(int(n_feat * chan_factor**2), bias=bias, groups=groups)

        self.down2 = DownSample(int((chan_factor**0) * n_feat), 2, chan_factor)
        self.down4 = nn.Sequential(
            DownSample(int((chan_factor**0) * n_feat), 2, chan_factor),
            DownSample(int((chan_factor**1) * n_feat), 2, chan_factor),
        )

        self.up21_1 = UpSample(int((chan_factor**1) * n_feat), 2, chan_factor)
        self.up21_2 = UpSample(int((chan_factor**1) * n_feat), 2, chan_factor)
        self.up32_1 = UpSample(int((chan_factor**2) * n_feat), 2, chan_factor)
        self.up32_2 = UpSample(int((chan_factor**2) * n_feat), 2, chan_factor)

        self.conv_out = nn.Conv2d(n_feat, n_feat, kernel_size=1, padding=0, bias=bias)

        # only two inputs for SKFF
        self.skff_top = SKFF(int(n_feat * chan_factor**0), 2)
        self.skff_mid = SKFF(int(n_feat * chan_factor**1), 2)

    def forward(self, x):
        x_top = x.clone()
        x_mid = self.down2(x_top)
        x_bot = self.down4(x_top)

        x_top = self.dau_top(x_top)
        x_mid = self.dau_mid(x_mid)
        x_bot = self.dau_bot(x_bot)

        x_mid = self.skff_mid([x_mid, self.up32_1(x_bot)])
        x_top = self.skff_top([x_top, self.up21_1(x_mid)])

        x_top = self.dau_top(x_top)
        x_mid = self.dau_mid(x_mid)
        x_bot = self.dau_bot(x_bot)

        x_mid = self.skff_mid([x_mid, self.up32_2(x_bot)])
        x_top = self.skff_top([x_top, self.up21_2(x_mid)])

        out = self.conv_out(x_top)
        out = out + x

        return out


##########################################################################
##---------- Recursive Residual Group (RRG) ----------
class RRG(nn.Module):
    def __init__(
        self, n_feat: int, n_MRB: int, chan_factor: float, bias=False, groups=1
    ):
        super().__init__()
        modules_body: list[nn.Module] = [
            MRB(n_feat, chan_factor, bias, groups) for _ in range(n_MRB)
        ]
        modules_body.append(
            nn.Conv2d(n_feat, n_feat, kernel_size=3, stride=1, padding=1, bias=bias)
        )
        self.body = nn.Sequential(*modules_body)

    def forward(self, x):
        res = self.body(x)
        res += x
        return res


##########################################################################
##---------- MIRNet  -----------------------
@store_hyperparameters()
class MIRNet_v2(nn.Module):
    hyperparameters = {}

    def __init__(
        self,
        inp_channels=3,
        out_channels=3,
        n_feat=80,
        chan_factor=1.5,
        n_MRB=2,
        bias=False,
        task: Literal["defocus_deblurring"] | None = None,
    ):
        super().__init__()

        self.task = task

        self.conv_in = nn.Conv2d(
            inp_channels, n_feat, kernel_size=3, padding=1, bias=bias
        )

        modules_body = []

        modules_body.append(RRG(n_feat, n_MRB, chan_factor, bias, groups=1))
        modules_body.append(RRG(n_feat, n_MRB, chan_factor, bias, groups=2))
        modules_body.append(RRG(n_feat, n_MRB, chan_factor, bias, groups=4))
        modules_body.append(RRG(n_feat, n_MRB, chan_factor, bias, groups=4))

        self.body = nn.Sequential(*modules_body)
        self.conv_out = nn.Conv2d(
            n_feat, out_channels, kernel_size=3, padding=1, bias=bias
        )

    def forward(self, inp_img):
        shallow_feats = self.conv_in(inp_img)
        deep_feats = self.body(shallow_feats)

        if self.task == "defocus_deblurring":
            deep_feats += shallow_feats
            out_img = self.conv_out(deep_feats)

        else:
            out_img = self.conv_out(deep_feats)
            out_img += inp_img

        return out_img
