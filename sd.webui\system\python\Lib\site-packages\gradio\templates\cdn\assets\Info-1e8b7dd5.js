import{S as i,e as r,s as u,a8 as f,m as _,g as c,h as p,aa as m,ab as d,ac as $,w as g,u as v,k as h}from"./index-afe51b5b.js";import"./Button-b4eb936e.js";function b(n){let s,a;const l=n[1].default,e=f(l,n,n[0],null);return{c(){s=_("div"),e&&e.c(),c(s,"class","svelte-e8n7p6")},m(t,o){p(t,s,o),e&&e.m(s,null),a=!0},p(t,[o]){e&&e.p&&(!a||o&1)&&m(e,l,t,t[0],a?$(l,t[0],o,null):d(t[0]),null)},i(t){a||(g(e,t),a=!0)},o(t){v(e,t),a=!1},d(t){t&&h(s),e&&e.d(t)}}}function I(n,s,a){let{$$slots:l={},$$scope:e}=s;return n.$$set=t=>{"$$scope"in t&&a(0,e=t.$$scope)},[e,l]}class q extends i{constructor(s){super(),r(this,s,I,b,u,{})}}export{q as I};
//# sourceMappingURL=Info-1e8b7dd5.js.map
