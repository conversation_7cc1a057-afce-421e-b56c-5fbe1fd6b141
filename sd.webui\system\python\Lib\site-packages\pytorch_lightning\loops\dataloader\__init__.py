# Copyright The Lightning team.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from pytorch_lightning.loops.dataloader.dataloader_loop import DataLoaderLoop  # noqa: F401
from pytorch_lightning.loops.dataloader.evaluation_loop import EvaluationLoop  # noqa: F401
from pytorch_lightning.loops.dataloader.prediction_loop import PredictionLoop  # noqa: F401
