import{S as r,e as _,s as h,m as c,F as d,o as w,t as g,g as b,N as o,h as I,j as m,G as k,x as q,w as v,u as B,k as S,H as j}from"./index-afe51b5b.js";import"./Button-b4eb936e.js";function C(s){let e,n,i,u,f,t;return i=new s[1]({}),{c(){e=c("div"),n=c("span"),d(i.$$.fragment),u=w(),f=g(s[0]),b(n,"class","svelte-1mwvhlq"),b(e,"data-testid","block-label"),b(e,"class","svelte-1mwvhlq"),o(e,"hide",!s[2]),o(e,"sr-only",!s[2]),o(e,"float",s[4]),o(e,"hide-label",s[3])},m(a,l){I(a,e,l),m(e,n),k(i,n,null),m(e,u),m(e,f),t=!0},p(a,[l]){(!t||l&1)&&q(f,a[0]),(!t||l&4)&&o(e,"hide",!a[2]),(!t||l&4)&&o(e,"sr-only",!a[2]),(!t||l&16)&&o(e,"float",a[4]),(!t||l&8)&&o(e,"hide-label",a[3])},i(a){t||(v(i.$$.fragment,a),t=!0)},o(a){B(i.$$.fragment,a),t=!1},d(a){a&&S(e),j(i)}}}function F(s,e,n){let{label:i=null}=e,{Icon:u}=e,{show_label:f=!0}=e,{disable:t=!1}=e,{float:a=!0}=e;return s.$$set=l=>{"label"in l&&n(0,i=l.label),"Icon"in l&&n(1,u=l.Icon),"show_label"in l&&n(2,f=l.show_label),"disable"in l&&n(3,t=l.disable),"float"in l&&n(4,a=l.float)},[i,u,f,t,a]}class L extends r{constructor(e){super(),_(this,e,F,C,h,{label:0,Icon:1,show_label:2,disable:3,float:4})}}export{L as B};
//# sourceMappingURL=BlockLabel-4ae26392.js.map
