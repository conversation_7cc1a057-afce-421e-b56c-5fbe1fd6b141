{"version": 3, "file": "index-3739d845.js", "sources": ["../../../../js/dropdown/static/StaticDropdown.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport Dropdown from \"../shared\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\texport let label = \"Dropdown\";\n\texport let info: string | undefined = undefined;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: string | string[];\n\texport let value_is_output = false;\n\texport let multiselect = false;\n\texport let max_choices: number;\n\texport let choices: string[];\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let allow_custom_value = false;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tinput: never;\n\t\tselect: SelectData;\n\t\tblur: never;\n\t\tfocus: never;\n\t}>;\n\n\tif (multiselect && !value) {\n\t\tvalue = [];\n\t} else if (!value) {\n\t\tvalue = \"\";\n\t}\n</script>\n\n<Block\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\tpadding={container}\n\tallow_overflow={false}\n\t{scale}\n\t{min_width}\n>\n\t<StatusTracker {...loading_status} />\n\n\t<Dropdown\n\t\tbind:value\n\t\tbind:value_is_output\n\t\t{choices}\n\t\t{multiselect}\n\t\t{max_choices}\n\t\t{label}\n\t\t{info}\n\t\t{show_label}\n\t\t{allow_custom_value}\n\t\t{container}\n\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\ton:input={() => gradio.dispatch(\"input\")}\n\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\ton:blur={() => gradio.dispatch(\"blur\")}\n\t\ton:focus={() => gradio.dispatch(\"focus\")}\n\t\tdisabled\n\t/>\n</Block>\n"], "names": ["ctx", "label", "$$props", "info", "elem_id", "elem_classes", "visible", "value", "value_is_output", "multiselect", "max_choices", "choices", "show_label", "container", "scale", "min_width", "loading_status", "allow_custom_value", "gradio"], "mappings": "uVA+CoBA,EAAc,EAAA,CAAA,ooBAAdA,EAAc,EAAA,CAAA,CAAA,CAAA,4jBALxBA,EAAS,EAAA,iBACF,sOADPA,EAAS,EAAA,2MAnCP,GAAA,CAAA,MAAAC,EAAQ,UAAU,EAAAC,EAClB,CAAA,KAAAC,EAA2B,MAAS,EAAAD,EACpC,CAAA,QAAAE,EAAU,EAAE,EAAAF,GACZ,aAAAG,EAAY,EAAA,EAAAH,EACZ,CAAA,QAAAI,EAAU,EAAI,EAAAJ,GACd,MAAAK,CAAwB,EAAAL,EACxB,CAAA,gBAAAM,EAAkB,EAAK,EAAAN,EACvB,CAAA,YAAAO,EAAc,EAAK,EAAAP,GACnB,YAAAQ,CAAmB,EAAAR,GACnB,QAAAS,CAAiB,EAAAT,GACjB,WAAAU,CAAmB,EAAAV,EACnB,CAAA,UAAAW,EAAY,EAAI,EAAAX,EAChB,CAAA,MAAAY,EAAuB,IAAI,EAAAZ,EAC3B,CAAA,UAAAa,EAAgC,MAAS,EAAAb,GACzC,eAAAc,CAA6B,EAAAd,EAC7B,CAAA,mBAAAe,EAAqB,EAAK,EAAAf,GAC1B,OAAAgB,CAMT,EAAAhB,EAEEO,IAAgBF,EACnBA,EAAK,CAAA,EACMA,IACXA,EAAQ,kEA0BSW,EAAO,SAAS,QAAQ,QACzBA,EAAO,SAAS,OAAO,IAC3B,GAAMA,EAAO,SAAS,SAAU,EAAE,MAAM,QACrCA,EAAO,SAAS,MAAM,QACrBA,EAAO,SAAS,OAAO"}