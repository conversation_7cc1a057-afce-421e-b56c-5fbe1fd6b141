import{S as h,e as p,s as c,f as a,g as e,h as u,j as i,n as o,k as g}from"./index-2519a27e.js";function v(l){let t,s;return{c(){t=a("svg"),s=a("polyline"),e(s,"points","20 6 9 17 4 12"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","100%"),e(t,"height","100%"),e(t,"viewBox","0 0 24 24"),e(t,"fill","none"),e(t,"stroke","currentColor"),e(t,"stroke-width","3"),e(t,"stroke-linecap","round"),e(t,"stroke-linejoin","round")},m(n,r){u(n,t,r),i(t,s)},p:o,i:o,o,d(n){n&&g(t)}}}class m extends h{constructor(t){super(),p(this,t,null,v,c,{})}}function w(l){let t,s,n;return{c(){t=a("svg"),s=a("path"),n=a("path"),e(s,"fill","currentColor"),e(s,"d","M28 10v18H10V10h18m0-2H10a2 2 0 0 0-2 2v18a2 2 0 0 0 2 2h18a2 2 0 0 0 2-2V10a2 2 0 0 0-2-2Z"),e(n,"fill","currentColor"),e(n,"d","M4 18H2V4a2 2 0 0 1 2-2h14v2H4Z"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","100%"),e(t,"height","100%"),e(t,"viewBox","0 0 32 32")},m(r,d){u(r,t,d),i(t,s),i(t,n)},p:o,i:o,o,d(r){r&&g(t)}}}class x extends h{constructor(t){super(),p(this,t,null,w,c,{})}}export{x as C,m as a};
//# sourceMappingURL=Copy-754acc5f.js.map
