{"version": 3, "file": "Checkboxgroup-73d55393.js", "sources": ["../../../../js/checkboxgroup/shared/Checkboxgroup.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher, afterUpdate } from \"svelte\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\timport type { SelectData } from \"@gradio/utils\";\n\n\texport let value: (string | number)[] = [];\n\tlet old_value: (string | number)[] = value.slice();\n\texport let value_is_output = false;\n\texport let choices: [string, number][];\n\texport let disabled = false;\n\texport let label: string;\n\texport let info: string | undefined = undefined;\n\texport let show_label: boolean;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: (string | number)[];\n\t\tinput: undefined;\n\t\tselect: SelectData;\n\t}>();\n\n\tfunction toggleChoice(choice: string | number): void {\n\t\tif (value.includes(choice)) {\n\t\t\tvalue.splice(value.indexOf(choice), 1);\n\t\t} else {\n\t\t\tvalue.push(choice);\n\t\t}\n\t\tvalue = value;\n\t}\n\n\tfunction handle_change(): void {\n\t\tdispatch(\"change\", value);\n\t\tif (!value_is_output) {\n\t\t\tdispatch(\"input\");\n\t\t}\n\t}\n\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\n\t$: {\n\t\tif (JSON.stringify(value) !== JSON.stringify(old_value)) {\n\t\t\told_value = value.slice();\n\t\t\thandle_change();\n\t\t}\n\t}\n</script>\n\n<BlockTitle {show_label} {info}>{label}</BlockTitle>\n\n<div class=\"wrap\" data-testid=\"checkbox-group\">\n\t{#each choices as choice, i}\n\t\t<label class:disabled class:selected={value.includes(choice[1])}>\n\t\t\t<input\n\t\t\t\t{disabled}\n\t\t\t\ton:change={() => toggleChoice(choice[1])}\n\t\t\t\ton:input={(evt) =>\n\t\t\t\t\tdispatch(\"select\", {\n\t\t\t\t\t\tindex: i,\n\t\t\t\t\t\tvalue: choice[1],\n\t\t\t\t\t\tselected: evt.currentTarget.checked\n\t\t\t\t\t})}\n\t\t\t\tchecked={value.includes(choice[1])}\n\t\t\t\ttype=\"checkbox\"\n\t\t\t\tname=\"test\"\n\t\t\t/>\n\t\t\t<span class=\"ml-2\">{choice[0]}</span>\n\t\t</label>\n\t{/each}\n</div>\n\n<style>\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: var(--checkbox-label-gap);\n\t}\n\tlabel {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\ttransition: var(--button-transition);\n\t\tcursor: pointer;\n\t\tbox-shadow: var(--checkbox-label-shadow);\n\t\tborder: var(--checkbox-label-border-width) solid\n\t\t\tvar(--checkbox-label-border-color);\n\t\tborder-radius: var(--button-small-radius);\n\t\tbackground: var(--checkbox-label-background-fill);\n\t\tpadding: var(--checkbox-label-padding);\n\t\tcolor: var(--checkbox-label-text-color);\n\t\tfont-weight: var(--checkbox-label-text-weight);\n\t\tfont-size: var(--checkbox-label-text-size);\n\t\tline-height: var(--line-md);\n\t}\n\n\tlabel:hover {\n\t\tbackground: var(--checkbox-label-background-fill-hover);\n\t}\n\tlabel:focus {\n\t\tbackground: var(--checkbox-label-background-fill-focus);\n\t}\n\tlabel.selected {\n\t\tbackground: var(--checkbox-label-background-fill-selected);\n\t\tcolor: var(--checkbox-label-text-color-selected);\n\t}\n\n\tlabel > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\tinput {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\tbox-shadow: var(--checkbox-shadow);\n\t\tborder: var(--checkbox-border-width) solid var(--checkbox-border-color);\n\t\tborder-radius: var(--checkbox-border-radius);\n\t\tbackground-color: var(--checkbox-background-color);\n\t\tline-height: var(--line-sm);\n\t}\n\n\tinput:checked,\n\tinput:checked:hover,\n\tinput:checked:focus {\n\t\tborder-color: var(--checkbox-border-color-selected);\n\t\tbackground-image: var(--checkbox-check);\n\t\tbackground-color: var(--checkbox-background-color-selected);\n\t}\n\n\tinput:hover {\n\t\tborder-color: var(--checkbox-border-color-hover);\n\t\tbackground-color: var(--checkbox-background-color-hover);\n\t}\n\n\tinput:focus {\n\t\tborder-color: var(--checkbox-border-color-focus);\n\t\tbackground-color: var(--checkbox-background-color-focus);\n\t}\n\n\tinput[disabled],\n\t.disabled {\n\t\tcursor: not-allowed;\n\t}\n</style>\n"], "names": ["ctx", "t1_value", "input", "input_checked_value", "toggle_class", "label_1", "insert", "target", "anchor", "append", "span", "dirty", "set_data", "t1", "i", "div", "value", "$$props", "old_value", "value_is_output", "choices", "disabled", "label", "info", "show_label", "dispatch", "createEventDispatcher", "toggleChoice", "choice", "handle_change", "afterUpdate", "$$invalidate", "evt"], "mappings": "gXAgDiCA,EAAK,CAAA,CAAA,oCAALA,EAAK,CAAA,CAAA,8CAkBfC,EAAAD,MAAO,CAAC,EAAA,gLAJlBE,EAAA,QAAAC,EAAAH,EAAM,CAAA,EAAA,SAASA,MAAO,CAAC,CAAA,gKAVII,EAAAC,EAAA,WAAAL,EAAM,CAAA,EAAA,SAASA,MAAO,CAAC,CAAA,CAAA,UAA7DM,EAeOC,EAAAF,EAAAG,CAAA,EAdNC,EAYCJ,EAAAH,CAAA,SACDO,EAAoCJ,EAAAK,CAAA,gGAJ1BC,EAAA,GAAAR,KAAAA,EAAAH,EAAM,CAAA,EAAA,SAASA,MAAO,CAAC,CAAA,kBAIbW,EAAA,GAAAV,KAAAA,EAAAD,MAAO,CAAC,EAAA,KAAAY,EAAAC,EAAAZ,CAAA,iCAdSG,EAAAC,EAAA,WAAAL,EAAM,CAAA,EAAA,SAASA,MAAO,CAAC,CAAA,CAAA,iJADvDA,EAAO,CAAA,CAAA,uBAAZ,OAAIc,GAAA,sMADPR,EAmBKC,EAAAQ,EAAAP,CAAA,wLAlBGR,EAAO,CAAA,CAAA,oBAAZ,OAAIc,GAAA,EAAA,iHAAJ,oIA9CS,MAAAE,EAAK,EAAA,EAAAC,EACZC,EAAiCF,EAAM,QAChC,CAAA,gBAAAG,EAAkB,EAAK,EAAAF,GACvB,QAAAG,CAA2B,EAAAH,EAC3B,CAAA,SAAAI,EAAW,EAAK,EAAAJ,GAChB,MAAAK,CAAa,EAAAL,EACb,CAAA,KAAAM,EAA2B,MAAS,EAAAN,GACpC,WAAAO,CAAmB,EAAAP,EAExB,MAAAQ,EAAWC,IAMR,SAAAC,EAAaC,EAAuB,CACxCZ,EAAM,SAASY,CAAM,EACxBZ,EAAM,OAAOA,EAAM,QAAQY,CAAM,EAAG,CAAC,EAErCZ,EAAM,KAAKY,CAAM,kBAKVC,GAAa,CACrBJ,EAAS,SAAUT,CAAK,EACnBG,GACJM,EAAS,OAAO,EAIlBK,EAAW,IAAA,CACVC,EAAA,EAAAZ,EAAkB,EAAK,eAkBJQ,EAAaC,EAAO,CAAC,CAAA,SAC3BI,IACVP,EAAS,SAAQ,CAChB,MAAO,EACP,MAAOG,EAAO,CAAC,EACf,SAAUI,EAAI,cAAc,ySAnB5B,KAAK,UAAUhB,CAAK,IAAM,KAAK,UAAUE,CAAS,QACrDA,EAAYF,EAAM,MAAK,CAAA,EACvBa"}