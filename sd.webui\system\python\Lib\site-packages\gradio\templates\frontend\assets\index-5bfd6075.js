import{S as z,e as A,s as D,F as v,G as k,w as S,u as N,H as B,a0 as E,a1 as I,Z as J,ad as K,R as q,U as C,o as L,h as M,V as O,W as P,X as F,k as Q}from"./index-2519a27e.js";import{N as T}from"./Number-ef0e738a.js";import{B as Y}from"./Button-748313a7.js";import"./BlockTitle-de7b2d6e.js";import"./Info-02b862eb.js";function y(l){let i,a,u,s,m,f;const b=[l[13]];let c={};for(let n=0;n<b.length;n+=1)c=J(c,b[n]);i=new K({props:c});function d(n){l[16](n)}function g(n){l[17](n)}let r={label:l[2],info:l[3],show_label:l[10],minimum:l[11],maximum:l[12],step:l[14],container:l[7],disabled:!0};return l[0]!==void 0&&(r.value=l[0]),l[1]!==void 0&&(r.value_is_output=l[1]),u=new T({props:r}),q.push(()=>C(u,"value",d)),q.push(()=>C(u,"value_is_output",g)),u.$on("change",l[18]),u.$on("input",l[19]),u.$on("submit",l[20]),u.$on("blur",l[21]),u.$on("focus",l[22]),{c(){v(i.$$.fragment),a=L(),v(u.$$.fragment)},m(n,t){k(i,n,t),M(n,a,t),k(u,n,t),f=!0},p(n,t){const h=t&8192?O(b,[P(n[13])]):{};i.$set(h);const _={};t&4&&(_.label=n[2]),t&8&&(_.info=n[3]),t&1024&&(_.show_label=n[10]),t&2048&&(_.minimum=n[11]),t&4096&&(_.maximum=n[12]),t&16384&&(_.step=n[14]),t&128&&(_.container=n[7]),!s&&t&1&&(s=!0,_.value=n[0],F(()=>s=!1)),!m&&t&2&&(m=!0,_.value_is_output=n[1],F(()=>m=!1)),u.$set(_)},i(n){f||(S(i.$$.fragment,n),S(u.$$.fragment,n),f=!0)},o(n){N(i.$$.fragment,n),N(u.$$.fragment,n),f=!1},d(n){n&&Q(a),B(i,n),B(u,n)}}}function p(l){let i,a;return i=new Y({props:{visible:l[6],elem_id:l[4],elem_classes:l[5],padding:l[7],allow_overflow:!1,scale:l[8],min_width:l[9],$$slots:{default:[y]},$$scope:{ctx:l}}}),{c(){v(i.$$.fragment)},m(u,s){k(i,u,s),a=!0},p(u,[s]){const m={};s&64&&(m.visible=u[6]),s&16&&(m.elem_id=u[4]),s&32&&(m.elem_classes=u[5]),s&128&&(m.padding=u[7]),s&256&&(m.scale=u[8]),s&512&&(m.min_width=u[9]),s&16841871&&(m.$$scope={dirty:s,ctx:u}),i.$set(m)},i(u){a||(S(i.$$.fragment,u),a=!0)},o(u){N(i.$$.fragment,u),a=!1},d(u){B(i,u)}}}function x(l,i,a){let u;E(l,I,e=>a(23,u=e));let{label:s=u("number.number")}=i,{info:m=void 0}=i,{elem_id:f=""}=i,{elem_classes:b=[]}=i,{visible:c=!0}=i,{container:d=!0}=i,{scale:g=null}=i,{min_width:r=void 0}=i,{value:n=0}=i,{show_label:t}=i,{minimum:h=void 0}=i,{maximum:_=void 0}=i,{loading_status:X}=i,{value_is_output:w=!1}=i,{step:j=null}=i,{gradio:o}=i;function G(e){n=e,a(0,n)}function H(e){w=e,a(1,w)}const R=()=>o.dispatch("change"),U=()=>o.dispatch("input"),V=()=>o.dispatch("submit"),W=()=>o.dispatch("blur"),Z=()=>o.dispatch("focus");return l.$$set=e=>{"label"in e&&a(2,s=e.label),"info"in e&&a(3,m=e.info),"elem_id"in e&&a(4,f=e.elem_id),"elem_classes"in e&&a(5,b=e.elem_classes),"visible"in e&&a(6,c=e.visible),"container"in e&&a(7,d=e.container),"scale"in e&&a(8,g=e.scale),"min_width"in e&&a(9,r=e.min_width),"value"in e&&a(0,n=e.value),"show_label"in e&&a(10,t=e.show_label),"minimum"in e&&a(11,h=e.minimum),"maximum"in e&&a(12,_=e.maximum),"loading_status"in e&&a(13,X=e.loading_status),"value_is_output"in e&&a(1,w=e.value_is_output),"step"in e&&a(14,j=e.step),"gradio"in e&&a(15,o=e.gradio)},[n,w,s,m,f,b,c,d,g,r,t,h,_,X,j,o,G,H,R,U,V,W,Z]}class $ extends z{constructor(i){super(),A(this,i,x,p,D,{label:2,info:3,elem_id:4,elem_classes:5,visible:6,container:7,scale:8,min_width:9,value:0,show_label:10,minimum:11,maximum:12,loading_status:13,value_is_output:1,step:14,gradio:15})}}const ae=$;export{ae as default};
//# sourceMappingURL=index-5bfd6075.js.map
