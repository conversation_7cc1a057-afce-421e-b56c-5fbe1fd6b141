import{S as f,e as p,s as h,f as u,g as c,h as d,j as w,n as m,k as v,F as S,G as b,w as A,u as y,H as C,a0 as Z,a1 as k,C as x}from"./index-2519a27e.js";import{I as B}from"./IconButton-f5e53901.js";import{S as I}from"./utils-c3e3db58.js";function M(o){let e,n;return{c(){e=u("svg"),n=u("path"),c(n,"d","M23,20a5,5,0,0,0-3.89,1.89L11.8,17.32a4.46,4.46,0,0,0,0-2.64l7.31-4.57A5,5,0,1,0,18,7a4.79,4.79,0,0,0,.2,1.32l-7.31,4.57a5,5,0,1,0,0,6.22l7.31,4.57A4.79,4.79,0,0,0,18,25a5,5,0,1,0,5-5ZM23,4a3,3,0,1,1-3,3A3,3,0,0,1,23,4ZM7,19a3,3,0,1,1,3-3A3,3,0,0,1,7,19Zm16,9a3,3,0,1,1,3-3A3,3,0,0,1,23,28Z"),c(n,"fill","currentColor"),c(e,"id","icon"),c(e,"xmlns","http://www.w3.org/2000/svg"),c(e,"viewBox","0 0 32 32")},m(t,r){d(t,e,r),w(e,n)},p:m,i:m,o:m,d(t){t&&v(e)}}}class E extends f{constructor(e){super(),p(this,e,null,M,h,{})}}function j(o){let e,n;return e=new B({props:{Icon:E,label:o[3]("common.share"),pending:o[2]}}),e.$on("click",o[5]),{c(){S(e.$$.fragment)},m(t,r){b(e,t,r),n=!0},p(t,[r]){const s={};r&8&&(s.label=t[3]("common.share")),r&4&&(s.pending=t[2]),e.$set(s)},i(t){n||(A(e.$$.fragment,t),n=!0)},o(t){y(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function q(o,e,n){let t;Z(o,k,a=>n(3,t=a));const r=x();let{formatter:s}=e,{value:i}=e,l=!1;const g=async()=>{try{n(2,l=!0);const a=await s(i);r("share",{description:a})}catch(a){console.error(a);let _=a instanceof I?a.message:"Share failed.";r("error",_)}finally{n(2,l=!1)}};return o.$$set=a=>{"formatter"in a&&n(0,s=a.formatter),"value"in a&&n(1,i=a.value)},[s,i,l,t,r,g]}class H extends f{constructor(e){super(),p(this,e,q,j,h,{formatter:0,value:1})}}export{H as S};
//# sourceMappingURL=ShareButton-33bb4648.js.map
