{"version": 3, "file": "Upload-b0a38490.js", "sources": ["../../../../js/upload/src/Upload.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport type { FileData } from \"./types\";\n\timport { blobToBase64 } from \"./utils\";\n\n\texport let filetype: string | null = null;\n\texport let include_file_metadata = true;\n\texport let dragging = false;\n\texport let boundedheight = true;\n\texport let center = true;\n\texport let flex = true;\n\texport let file_count = \"single\";\n\texport let disable_click = false;\n\texport let parse_to_data_url = true;\n\n\tlet hidden_upload: HTMLInputElement;\n\n\tconst dispatch = createEventDispatcher();\n\n\tfunction updateDragging(): void {\n\t\tdragging = !dragging;\n\t}\n\n\tfunction openFileUpload(): void {\n\t\tif (disable_click) return;\n\t\thidden_upload.value = \"\";\n\t\thidden_upload.click();\n\t}\n\n\tasync function loadFiles(files: FileList): Promise<void> {\n\t\tlet _files: File[] = Array.from(files);\n\t\tif (!files.length || !window.FileReader) {\n\t\t\treturn;\n\t\t}\n\t\tif (file_count === \"single\") {\n\t\t\t_files = [files[0]];\n\t\t}\n\n\t\tif (include_file_metadata) {\n\t\t\tvar file_metadata: { name: string; size: number }[] = _files.map((f) => ({\n\t\t\t\tname: f.name,\n\t\t\t\tsize: f.size\n\t\t\t}));\n\t\t}\n\t\tvar load_file_data = [];\n\t\tvar file_data: string[] | File[] = [];\n\t\tif (parse_to_data_url) {\n\t\t\tfile_data = await Promise.all(_files.map((f) => blobToBase64(f)));\n\t\t} else {\n\t\t\tfile_data = _files;\n\t\t}\n\t\tif (include_file_metadata) {\n\t\t\tif (parse_to_data_url) {\n\t\t\t\tload_file_data = file_data.map((data, i) => ({\n\t\t\t\t\tdata,\n\t\t\t\t\t...file_metadata[i]\n\t\t\t\t}));\n\t\t\t} else {\n\t\t\t\tload_file_data = file_data.map((data, i) => ({\n\t\t\t\t\tdata: \"\",\n\t\t\t\t\tblob: data,\n\t\t\t\t\t...file_metadata[i]\n\t\t\t\t}));\n\t\t\t}\n\t\t} else {\n\t\t\tload_file_data = file_data;\n\t\t}\n\t\tdispatch(\n\t\t\t\"load\",\n\t\t\tfile_count === \"single\" ? load_file_data[0] : load_file_data\n\t\t);\n\t}\n\n\tasync function loadFilesFromUpload(e: Event): Promise<void> {\n\t\tconst target = e.target as HTMLInputElement;\n\n\t\tif (!target.files) return;\n\t\tawait loadFiles(target.files);\n\t}\n\n\tasync function loadFilesFromDrop(e: DragEvent): Promise<void> {\n\t\tdragging = false;\n\t\tif (!e.dataTransfer?.files) return;\n\t\tawait loadFiles(e.dataTransfer.files);\n\t}\n</script>\n\n<!-- TODO: fix -->\n<!-- svelte-ignore a11y-click-events-have-key-events -->\n<!-- svelte-ignore a11y-no-static-element-interactions -->\n<div\n\tclass:center\n\tclass:boundedheight\n\tclass:flex\n\ton:drag|preventDefault|stopPropagation\n\ton:dragstart|preventDefault|stopPropagation\n\ton:dragend|preventDefault|stopPropagation\n\ton:dragover|preventDefault|stopPropagation\n\ton:dragenter|preventDefault|stopPropagation\n\ton:dragleave|preventDefault|stopPropagation\n\ton:drop|preventDefault|stopPropagation\n\ton:click={openFileUpload}\n\ton:drop={loadFilesFromDrop}\n\ton:dragenter={updateDragging}\n\ton:dragleave={updateDragging}\n>\n\t<slot />\n\t<input\n\t\ttype=\"file\"\n\t\tbind:this={hidden_upload}\n\t\ton:change={loadFilesFromUpload}\n\t\taccept={filetype}\n\t\tmultiple={file_count === \"multiple\" || undefined}\n\t\twebkitdirectory={file_count === \"directory\" || undefined}\n\t\tmozdirectory={file_count === \"directory\" || undefined}\n\t/>\n</div>\n\n<style>\n\tdiv {\n\t\tcursor: pointer;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t.center {\n\t\ttext-align: center;\n\t}\n\t.flex {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\n\tinput {\n\t\tdisplay: none;\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "div", "anchor", "append", "input", "filetype", "$$props", "include_file_metadata", "dragging", "boundedheight", "center", "flex", "file_count", "disable_click", "parse_to_data_url", "hidden_upload", "dispatch", "createEventDispatcher", "updateDragging", "$$invalidate", "openFileUpload", "loadFiles", "files", "_files", "file_metadata", "f", "load_file_data", "file_data", "blobToBase64", "data", "i", "loadFilesFromUpload", "loadFilesFromDrop", "$$value"], "mappings": "uZA+GUA,EAAQ,CAAA,CAAA,eACNA,EAAU,CAAA,IAAK,YAAc,+BACtBA,EAAU,CAAA,IAAK,aAAe,MAAS,uBAC1CA,EAAU,CAAA,IAAK,aAAe,MAAS,oIAxBvDC,EA0BKC,EAAAC,EAAAC,CAAA,wBATJC,EAQCF,EAAAG,CAAA,oCALWN,EAAmB,CAAA,CAAA,4MATrBA,EAAc,CAAA,CAAA,aACfA,EAAiB,CAAA,CAAA,kBACZA,EAAc,CAAA,CAAA,kBACdA,EAAc,CAAA,CAAA,mHAOnBA,EAAQ,CAAA,CAAA,oBACNA,EAAU,CAAA,IAAK,YAAc,2CACtBA,EAAU,CAAA,IAAK,aAAe,qDACjCA,EAAU,CAAA,IAAK,aAAe,4QA7GlC,CAAA,SAAAO,EAA0B,IAAI,EAAAC,EAC9B,CAAA,sBAAAC,EAAwB,EAAI,EAAAD,EAC5B,CAAA,SAAAE,EAAW,EAAK,EAAAF,EAChB,CAAA,cAAAG,EAAgB,EAAI,EAAAH,EACpB,CAAA,OAAAI,EAAS,EAAI,EAAAJ,EACb,CAAA,KAAAK,EAAO,EAAI,EAAAL,EACX,CAAA,WAAAM,EAAa,QAAQ,EAAAN,EACrB,CAAA,cAAAO,EAAgB,EAAK,EAAAP,EACrB,CAAA,kBAAAQ,EAAoB,EAAI,EAAAR,EAE/BS,EAEE,MAAAC,EAAWC,cAERC,GAAc,CACtBC,EAAA,GAAAX,GAAYA,CAAQ,WAGZY,GAAc,CAClBP,QACJE,EAAc,MAAQ,GAAEA,CAAA,EACxBA,EAAc,MAAK,GAGL,eAAAM,EAAUC,EAAe,CACnC,IAAAC,EAAiB,MAAM,KAAKD,CAAK,EAChC,GAAA,GAAAA,EAAM,QAAW,CAAA,OAAO,gBAGzBV,IAAe,WAClBW,EAAM,CAAID,EAAM,CAAC,CAAA,GAGdf,EACC,IAAAiB,EAAkDD,EAAO,IAAKE,IACjE,CAAA,KAAMA,EAAE,KACR,KAAMA,EAAE,IAAA,EAAA,MAGNC,EAAc,CAAA,EACdC,EAAS,CAAA,EACTb,EACHa,EAAkB,MAAA,QAAQ,IAAIJ,EAAO,IAAKE,GAAMG,GAAaH,CAAC,CAAA,CAAA,EAE9DE,EAAYJ,EAEThB,EACCO,EACHY,EAAiBC,EAAU,IAAK,CAAAE,EAAMC,KACrC,CAAA,KAAAD,EACG,GAAAL,EAAcM,CAAC,CAAA,EAAA,EAGnBJ,EAAiBC,EAAU,IAAK,CAAAE,EAAMC,KAAC,CACtC,KAAM,GACN,KAAMD,EACH,GAAAL,EAAcM,CAAC,KAIpBJ,EAAiBC,EAElBX,EACC,OACAJ,IAAe,SAAWc,EAAe,CAAC,EAAIA,CAAc,GAI/C,eAAAK,EAAoB,EAAQ,OACpC/B,EAAS,EAAE,OAEZA,EAAO,aACNqB,EAAUrB,EAAO,KAAK,EAGd,eAAAgC,EAAkB,EAAY,CAC5Cb,EAAA,GAAAX,EAAW,EAAK,EACX,EAAE,cAAc,OACf,MAAAa,EAAU,EAAE,aAAa,KAAK,qQA0BzBN,EAAakB"}