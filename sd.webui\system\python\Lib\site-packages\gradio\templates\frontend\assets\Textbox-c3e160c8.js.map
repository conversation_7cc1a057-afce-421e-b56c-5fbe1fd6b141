{"version": 3, "file": "Textbox-c3e160c8.js", "sources": ["../../../../js/textbox/shared/Textbox.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { afterUpdate, createEventDispatcher, tick } from \"svelte\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\timport { Copy, Check } from \"@gradio/icons\";\n\timport { fade } from \"svelte/transition\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport type { ActionReturn } from \"svelte/action\";\n\n\texport let value = \"\";\n\texport let value_is_output = false;\n\texport let lines = 1;\n\texport let placeholder = \"Type here...\";\n\texport let label: string;\n\texport let info: string | undefined = undefined;\n\texport let disabled = false;\n\texport let show_label = true;\n\texport let container = true;\n\texport let max_lines: number;\n\texport let type: \"text\" | \"password\" | \"email\" = \"text\";\n\texport let show_copy_button = false;\n\texport let rtl = false;\n\texport let autofocus = false;\n\texport let text_align: \"left\" | \"right\" | undefined = undefined;\n\n\tlet el: HTMLTextAreaElement | HTMLInputElement;\n\tlet copied = false;\n\tlet timer: NodeJS.Timeout;\n\n\t$: value, el && lines !== max_lines && resize({ target: el });\n\n\t$: if (value === null) value = \"\";\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: string;\n\t\tsubmit: undefined;\n\t\tblur: undefined;\n\t\tselect: SelectData;\n\t\tinput: undefined;\n\t\tfocus: undefined;\n\t}>();\n\n\tfunction handle_change(): void {\n\t\tdispatch(\"change\", value);\n\t\tif (!value_is_output) {\n\t\t\tdispatch(\"input\");\n\t\t}\n\t}\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\t$: value, handle_change();\n\n\tasync function handle_copy(): Promise<void> {\n\t\tif (\"clipboard\" in navigator) {\n\t\t\tawait navigator.clipboard.writeText(value);\n\t\t\tcopy_feedback();\n\t\t}\n\t}\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 1000);\n\t}\n\n\tfunction handle_select(event: Event): void {\n\t\tconst target: HTMLTextAreaElement | HTMLInputElement = event.target as\n\t\t\t| HTMLTextAreaElement\n\t\t\t| HTMLInputElement;\n\t\tconst text = target.value;\n\t\tconst index: [number, number] = [\n\t\t\ttarget.selectionStart as number,\n\t\t\ttarget.selectionEnd as number\n\t\t];\n\t\tdispatch(\"select\", { value: text.substring(...index), index: index });\n\t}\n\n\tasync function handle_keypress(e: KeyboardEvent): Promise<void> {\n\t\tawait tick();\n\t\tif (e.key === \"Enter\" && e.shiftKey && lines > 1) {\n\t\t\te.preventDefault();\n\t\t\tdispatch(\"submit\");\n\t\t} else if (\n\t\t\te.key === \"Enter\" &&\n\t\t\t!e.shiftKey &&\n\t\t\tlines === 1 &&\n\t\t\tmax_lines >= 1\n\t\t) {\n\t\t\te.preventDefault();\n\t\t\tdispatch(\"submit\");\n\t\t}\n\t}\n\n\tasync function resize(\n\t\tevent: Event | { target: HTMLTextAreaElement | HTMLInputElement }\n\t): Promise<void> {\n\t\tawait tick();\n\t\tif (lines === max_lines || !container) return;\n\n\t\tlet max =\n\t\t\tmax_lines === undefined\n\t\t\t\t? false\n\t\t\t\t: max_lines === undefined // default\n\t\t\t\t? 21 * 11\n\t\t\t\t: 21 * (max_lines + 1);\n\t\tlet min = 21 * (lines + 1);\n\n\t\tconst target = event.target as HTMLTextAreaElement;\n\t\ttarget.style.height = \"1px\";\n\n\t\tlet scroll_height;\n\t\tif (max && target.scrollHeight > max) {\n\t\t\tscroll_height = max;\n\t\t} else if (target.scrollHeight < min) {\n\t\t\tscroll_height = min;\n\t\t} else {\n\t\t\tscroll_height = target.scrollHeight;\n\t\t}\n\n\t\ttarget.style.height = `${scroll_height}px`;\n\t}\n\n\tfunction text_area_resize(\n\t\t_el: HTMLTextAreaElement,\n\t\t_value: string\n\t): ActionReturn | undefined {\n\t\tif (lines === max_lines) return;\n\t\t_el.style.overflowY = \"scroll\";\n\t\t_el.addEventListener(\"input\", resize);\n\n\t\tif (!_value.trim()) return;\n\t\tresize({ target: _el });\n\n\t\treturn {\n\t\t\tdestroy: () => _el.removeEventListener(\"input\", resize)\n\t\t};\n\t}\n</script>\n\n<!-- svelte-ignore a11y-autofocus -->\n<label class:container>\n\t<BlockTitle {show_label} {info}>{label}</BlockTitle>\n\n\t{#if lines === 1 && max_lines === 1}\n\t\t{#if type === \"text\"}\n\t\t\t<input\n\t\t\t\tdata-testid=\"textbox\"\n\t\t\t\ttype=\"text\"\n\t\t\t\tclass=\"scroll-hide\"\n\t\t\t\tdir={rtl ? \"rtl\" : \"ltr\"}\n\t\t\t\tbind:value\n\t\t\t\tbind:this={el}\n\t\t\t\t{placeholder}\n\t\t\t\t{disabled}\n\t\t\t\t{autofocus}\n\t\t\t\ton:keypress={handle_keypress}\n\t\t\t\ton:blur\n\t\t\t\ton:select={handle_select}\n\t\t\t\ton:focus\n\t\t\t\tstyle={text_align ? \"text-align: \" + text_align : \"\"}\n\t\t\t/>\n\t\t{:else if type === \"password\"}\n\t\t\t<input\n\t\t\t\tdata-testid=\"password\"\n\t\t\t\ttype=\"password\"\n\t\t\t\tclass=\"scroll-hide\"\n\t\t\t\tbind:value\n\t\t\t\tbind:this={el}\n\t\t\t\t{placeholder}\n\t\t\t\t{disabled}\n\t\t\t\t{autofocus}\n\t\t\t\ton:keypress={handle_keypress}\n\t\t\t\ton:blur\n\t\t\t\ton:select={handle_select}\n\t\t\t\ton:focus\n\t\t\t\tautocomplete=\"\"\n\t\t\t/>\n\t\t{:else if type === \"email\"}\n\t\t\t<input\n\t\t\t\tdata-testid=\"textbox\"\n\t\t\t\ttype=\"email\"\n\t\t\t\tclass=\"scroll-hide\"\n\t\t\t\tbind:value\n\t\t\t\tbind:this={el}\n\t\t\t\t{placeholder}\n\t\t\t\t{disabled}\n\t\t\t\t{autofocus}\n\t\t\t\ton:keypress={handle_keypress}\n\t\t\t\ton:blur\n\t\t\t\ton:select={handle_select}\n\t\t\t\ton:focus\n\t\t\t\tautocomplete=\"email\"\n\t\t\t/>\n\t\t{/if}\n\t{:else}\n\t\t{#if show_label && show_copy_button}\n\t\t\t{#if copied}\n\t\t\t\t<button in:fade={{ duration: 300 }}><Check /></button>\n\t\t\t{:else}\n\t\t\t\t<button on:click={handle_copy} class=\"copy-text\"><Copy /></button>\n\t\t\t{/if}\n\t\t{/if}\n\t\t<textarea\n\t\t\tdata-testid=\"textbox\"\n\t\t\tuse:text_area_resize={value}\n\t\t\tclass=\"scroll-hide\"\n\t\t\tdir={rtl ? \"rtl\" : \"ltr\"}\n\t\t\tbind:value\n\t\t\tbind:this={el}\n\t\t\t{placeholder}\n\t\t\trows={lines}\n\t\t\t{disabled}\n\t\t\t{autofocus}\n\t\t\ton:keypress={handle_keypress}\n\t\t\ton:blur\n\t\t\ton:select={handle_select}\n\t\t\ton:focus\n\t\t\tstyle={text_align ? \"text-align: \" + text_align : \"\"}\n\t\t/>\n\t{/if}\n</label>\n\n<style>\n\tlabel {\n\t\tdisplay: block;\n\t\twidth: 100%;\n\t}\n\n\tinput,\n\ttextarea {\n\t\tdisplay: block;\n\t\tposition: relative;\n\t\toutline: none !important;\n\t\tbox-shadow: var(--input-shadow);\n\t\tbackground: var(--input-background-fill);\n\t\tpadding: var(--input-padding);\n\t\twidth: 100%;\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--input-text-weight);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-sm);\n\t\tborder: none;\n\t}\n\tlabel:not(.container),\n\tlabel:not(.container) > input,\n\tlabel:not(.container) > textarea {\n\t\theight: 100%;\n\t}\n\t.container > input,\n\t.container > textarea {\n\t\tborder: var(--input-border-width) solid var(--input-border-color);\n\t\tborder-radius: var(--input-radius);\n\t}\n\tinput:disabled,\n\ttextarea:disabled {\n\t\t-webkit-text-fill-color: var(--body-text-color);\n\t\t-webkit-opacity: 1;\n\t\topacity: 1;\n\t}\n\n\tinput:focus,\n\ttextarea:focus {\n\t\tbox-shadow: var(--input-shadow-focus);\n\t\tborder-color: var(--input-border-color-focus);\n\t}\n\n\tinput::placeholder,\n\ttextarea::placeholder {\n\t\tcolor: var(--input-placeholder-color);\n\t}\n\tbutton {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: var(--block-label-margin);\n\t\tright: var(--block-label-margin);\n\t\talign-items: center;\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--color-border-primary);\n\t\tborder-top: none;\n\t\tborder-right: none;\n\t\tborder-radius: var(--block-label-right-radius);\n\t\tbackground: var(--block-label-background-fill);\n\t\tpadding: 5px;\n\t\twidth: 22px;\n\t\theight: 22px;\n\t\toverflow: hidden;\n\t\tcolor: var(--block-label-color);\n\t\tfont: var(--font-sans);\n\t\tfont-size: var(--button-small-text-size);\n\t}\n</style>\n"], "names": ["ctx", "if_block", "create_if_block_4", "insert", "target", "textarea", "anchor", "create_if_block_1", "create_if_block_2", "create_if_block_3", "button", "button_intro", "create_in_transition", "fade", "input", "label_1", "value", "$$props", "value_is_output", "lines", "placeholder", "label", "info", "disabled", "show_label", "container", "max_lines", "type", "show_copy_button", "rtl", "autofocus", "text_align", "el", "copied", "timer", "dispatch", "createEventDispatcher", "handle_change", "afterUpdate", "$$invalidate", "handle_copy", "copy_feedback", "handle_select", "event", "text", "index", "handle_keypress", "e", "tick", "resize", "max", "min", "scroll_height", "text_area_resize", "_el", "_value", "$$value"], "mappings": "sbA+IkCA,EAAK,CAAA,CAAA,wCAALA,EAAK,CAAA,CAAA,qDAsDhCC,EAAAD,MAAcA,EAAgB,EAAA,GAAAE,EAAAF,CAAA,+HAW7BA,EAAG,EAAA,EAAG,MAAQ,KAAK,qCAIlBA,EAAK,CAAA,CAAA,kDAOJA,EAAU,EAAA,EAAG,eAAiBA,EAAU,EAAA,EAAG,EAAE,+BAfrDG,EAgBCC,EAAAC,EAAAC,CAAA,yEAdsBN,EAAK,CAAA,CAAA,CAAA,oCASdA,EAAe,EAAA,CAAA,iCAEjBA,EAAa,EAAA,CAAA,oCApBpBA,MAAcA,EAAgB,EAAA,qIAW7BA,EAAG,EAAA,EAAG,MAAQ,qFAIbA,EAAK,CAAA,CAAA,+FAOJA,EAAU,EAAA,EAAG,eAAiBA,EAAU,EAAA,EAAG,iEAb5BA,EAAK,CAAA,CAAA,qJA5DvB,GAAAA,OAAS,OAAM,OAAAO,GAiBV,GAAAP,OAAS,WAAU,OAAAQ,GAgBnB,GAAAR,OAAS,QAAO,OAAAS,6QAmBpBT,EAAM,EAAA,EAAA,2aAGVG,EAAiEC,EAAAM,EAAAJ,CAAA,qCAA/CN,EAAW,EAAA,CAAA,oOAF7BG,EAAqDC,EAAAM,EAAAJ,CAAA,mEAAlCK,EAAAC,GAAAF,EAAAG,GAAA,CAAA,SAAU,GAAG,CAAA,kTAnBjCV,EAcCC,EAAAU,EAAAR,CAAA,+EALaN,EAAe,EAAA,CAAA,iCAEjBA,EAAa,EAAA,CAAA,+aA3BzBG,EAcCC,EAAAU,EAAAR,CAAA,+EALaN,EAAe,EAAA,CAAA,iCAEjBA,EAAa,EAAA,CAAA,iWAxBnBA,EAAG,EAAA,EAAG,MAAQ,KAAK,0EAUjBA,EAAU,EAAA,EAAG,eAAiBA,EAAU,EAAA,EAAG,EAAE,UAdrDG,EAeCC,EAAAU,EAAAR,CAAA,+EALaN,EAAe,EAAA,CAAA,iCAEjBA,EAAa,EAAA,CAAA,sDARnBA,EAAG,EAAA,EAAG,MAAQ,iIAUZA,EAAU,EAAA,EAAG,eAAiBA,EAAU,EAAA,EAAG,qPAhBhD,OAAAA,EAAU,CAAA,IAAA,GAAKA,OAAc,EAAC,4IAHpCG,EAgFOC,EAAAW,EAAAT,CAAA,8cAtNK,GAAA,CAAA,MAAAU,EAAQ,EAAE,EAAAC,EACV,CAAA,gBAAAC,EAAkB,EAAK,EAAAD,EACvB,CAAA,MAAAE,EAAQ,CAAC,EAAAF,EACT,CAAA,YAAAG,EAAc,cAAc,EAAAH,GAC5B,MAAAI,CAAa,EAAAJ,EACb,CAAA,KAAAK,EAA2B,MAAS,EAAAL,EACpC,CAAA,SAAAM,EAAW,EAAK,EAAAN,EAChB,CAAA,WAAAO,EAAa,EAAI,EAAAP,EACjB,CAAA,UAAAQ,EAAY,EAAI,EAAAR,GAChB,UAAAS,CAAiB,EAAAT,EACjB,CAAA,KAAAU,EAAsC,MAAM,EAAAV,EAC5C,CAAA,iBAAAW,EAAmB,EAAK,EAAAX,EACxB,CAAA,IAAAY,EAAM,EAAK,EAAAZ,EACX,CAAA,UAAAa,EAAY,EAAK,EAAAb,EACjB,CAAA,WAAAc,EAA2C,MAAS,EAAAd,EAE3De,EACAC,EAAS,GACTC,EAME,MAAAC,EAAWC,cASRC,GAAa,CACrBF,EAAS,SAAUnB,CAAK,EACnBE,GACJiB,EAAS,OAAO,EAGlBG,GAAW,IAAA,CACVC,EAAA,GAAArB,EAAkB,EAAK,mBAITsB,GAAW,CACrB,cAAe,YACZ,MAAA,UAAU,UAAU,UAAUxB,CAAK,EACzCyB,cAIOA,GAAa,CACrBF,EAAA,GAAAN,EAAS,EAAI,EACTC,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,gBACPK,EAAA,GAAAN,EAAS,EAAK,GACZ,KAGK,SAAAS,EAAcC,EAAY,OAC5BvC,EAAiDuC,EAAM,OAGvDC,EAAOxC,EAAO,MACdyC,GACLzC,EAAO,eACPA,EAAO,YAAA,EAER+B,EAAS,SAAQ,CAAI,MAAOS,EAAK,UAAS,GAAIC,CAAK,EAAU,MAAAA,CAAK,CAAA,EAGpD,eAAAC,GAAgBC,EAAgB,OACxCC,EAAI,GACND,EAAE,MAAQ,SAAWA,EAAE,UAAY5B,EAAQ,GAI9C4B,EAAE,MAAQ,UACTA,EAAE,UACH5B,IAAU,GACVO,GAAa,KAEbqB,EAAE,eAAc,EAChBZ,EAAS,QAAQ,GAIJ,eAAAc,EACdN,EAAiE,UAE3DK,EAAI,EACN7B,IAAUO,GAAS,CAAKD,EAAS,WAEjCyB,EACHxB,IAAc,OACX,GACAA,IAAc,OACd,GAAK,GACL,IAAMA,EAAY,GAClByB,EAAM,IAAMhC,EAAQ,SAElBf,EAASuC,EAAM,OACrBvC,EAAO,MAAM,OAAS,UAElBgD,EACAF,GAAO9C,EAAO,aAAe8C,EAChCE,EAAgBF,EACN9C,EAAO,aAAe+C,EAChCC,EAAgBD,EAEhBC,EAAgBhD,EAAO,aAGxBA,EAAO,MAAM,UAAYgD,eAGjBC,GACRC,EACAC,EAAc,CAEV,GAAApC,IAAUO,IACd4B,EAAI,MAAM,UAAY,SACtBA,EAAI,iBAAiB,QAASL,CAAM,EAE/B,EAAAM,EAAO,KAAI,GAChB,OAAAN,EAAM,CAAG,OAAQK,CAAG,CAAA,GAGnB,YAAeA,EAAI,oBAAoB,QAASL,CAAM,+UAiB1CjB,EAAEwB,wFAgBFxB,EAAEwB,wFAgBFxB,EAAEwB,wFAyBHxB,EAAEwB,2kBApLRxC,IAAU,MAAMuB,EAAA,EAAAvB,EAAQ,EAAE,uBAFvBgB,GAAMb,IAAUO,GAAauB,EAAS,CAAA,OAAQjB,CAAE,CAAA,mBAsBhDK,EAAa"}