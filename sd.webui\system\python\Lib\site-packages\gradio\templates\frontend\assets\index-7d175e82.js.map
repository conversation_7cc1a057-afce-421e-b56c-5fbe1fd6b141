{"version": 3, "file": "index-7d175e82.js", "sources": ["../../../../js/dataframe/interactive/InteractiveDataframe.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { Block } from \"@gradio/atoms\";\n\timport Table from \"../shared\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { afterUpdate } from \"svelte\";\n\n\ttype Headers = string[];\n\ttype Data = (string | number)[][];\n\ttype Datatype = \"str\" | \"markdown\" | \"html\" | \"number\" | \"bool\" | \"date\";\n\n\texport let headers: Headers = [];\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: { data: Data; headers: Headers } = {\n\t\tdata: [[\"\", \"\", \"\"]],\n\t\theaders: [\"1\", \"2\", \"3\"]\n\t};\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let height: number | undefined = undefined;\n\n\tlet old_value: string = JSON.stringify(value);\n\texport let value_is_output = false;\n\texport let col_count: [number, \"fixed\" | \"dynamic\"];\n\texport let row_count: [number, \"fixed\" | \"dynamic\"];\n\texport let label: string | null = null;\n\texport let wrap: boolean;\n\texport let datatype: Datatype | Datatype[];\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tselect: SelectData;\n\t\tinput: never;\n\t}>;\n\n\texport let loading_status: LoadingStatus;\n\n\tfunction handle_change(): void {\n\t\tgradio.dispatch(\"change\");\n\t\tif (!value_is_output) {\n\t\t\tgradio.dispatch(\"input\");\n\t\t}\n\t}\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\t$: {\n\t\tif (JSON.stringify(value) !== old_value) {\n\t\t\told_value = JSON.stringify(value);\n\t\t\thandle_change();\n\t\t}\n\t}\n</script>\n\n<Block\n\t{visible}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\tcontainer={false}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n>\n\t<StatusTracker {...loading_status} />\n\t<Table\n\t\t{label}\n\t\t{row_count}\n\t\t{col_count}\n\t\tvalues={value}\n\t\t{headers}\n\t\ton:change={({ detail }) => {\n\t\t\tvalue = detail;\n\t\t}}\n\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\teditable\n\t\t{wrap}\n\t\t{datatype}\n\t\t{latex_delimiters}\n\t\t{height}\n\t/>\n</Block>\n"], "names": ["ctx", "headers", "$$props", "elem_id", "elem_classes", "visible", "value", "latex_delimiters", "height", "old_value", "value_is_output", "col_count", "row_count", "label", "wrap", "datatype", "scale", "min_width", "gradio", "loading_status", "handle_change", "afterUpdate", "$$invalidate", "detail"], "mappings": "+XAuEoBA,EAAc,EAAA,CAAA,6IAKxBA,EAAK,CAAA,0PALKA,EAAc,EAAA,CAAA,CAAA,CAAA,mHAKxBA,EAAK,CAAA,kVAbL,4CAGE,8CAGK,gYAzDL,QAAAC,EAAO,EAAA,EAAAC,EACP,CAAA,QAAAC,EAAU,EAAE,EAAAD,GACZ,aAAAE,EAAY,EAAA,EAAAF,EACZ,CAAA,QAAAG,EAAU,EAAI,EAAAH,GACd,MAAAI,EAAK,CACf,KAAQ,CAAA,CAAA,GAAI,GAAI,EAAE,CAAA,EAClB,QAAU,CAAA,IAAK,IAAK,GAAG,OAEb,iBAAAC,CAIR,EAAAL,EACQ,CAAA,OAAAM,EAA6B,MAAS,EAAAN,EAE7CO,EAAoB,KAAK,UAAUH,CAAK,EACjC,CAAA,gBAAAI,EAAkB,EAAK,EAAAR,GACvB,UAAAS,CAAwC,EAAAT,GACxC,UAAAU,CAAwC,EAAAV,EACxC,CAAA,MAAAW,EAAuB,IAAI,EAAAX,GAC3B,KAAAY,CAAa,EAAAZ,GACb,SAAAa,CAA+B,EAAAb,EAC/B,CAAA,MAAAc,EAAuB,IAAI,EAAAd,EAC3B,CAAA,UAAAe,EAAgC,MAAS,EAAAf,GACzC,OAAAgB,CAIT,EAAAhB,GAES,eAAAiB,CAA6B,EAAAjB,WAE/BkB,GAAa,CACrBF,EAAO,SAAS,QAAQ,EACnBR,GACJQ,EAAO,SAAS,OAAO,EAGzBG,EAAW,IAAA,CACVC,EAAA,GAAAZ,EAAkB,EAAK,cA2BT,OAAAa,KAAM,CACnBD,EAAA,EAAAhB,EAAQiB,CAAM,KAEH,GAAML,EAAO,SAAS,SAAU,EAAE,MAAM,mpBA3BhD,KAAK,UAAUZ,CAAK,IAAMG,IAC7Ba,EAAA,GAAAb,EAAY,KAAK,UAAUH,CAAK,CAAA,EAChCc"}