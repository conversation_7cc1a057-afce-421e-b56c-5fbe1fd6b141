{"version": 3, "file": "UploadButton-92a68e12.js", "sources": ["../../../../js/uploadbutton/shared/UploadButton.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { BaseButton } from \"@gradio/button/static\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport type { FileData } from \"@gradio/upload\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let file_count: string;\n\texport let file_types: string[] = [];\n\texport let include_file_metadata = true;\n\texport let size: \"sm\" | \"lg\" = \"lg\";\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let variant: \"primary\" | \"secondary\" | \"stop\" = \"secondary\";\n\texport let label: string;\n\texport let disabled = false;\n\n\tlet hidden_upload: HTMLInputElement;\n\tconst dispatch = createEventDispatcher();\n\tlet accept_file_types: string | null;\n\tif (file_types == null) {\n\t\taccept_file_types = null;\n\t} else {\n\t\tfile_types = file_types.map((x) => {\n\t\t\tif (x.startsWith(\".\")) {\n\t\t\t\treturn x;\n\t\t\t}\n\t\t\treturn x + \"/*\";\n\t\t});\n\t\taccept_file_types = file_types.join(\", \");\n\t}\n\n\tfunction openFileUpload(): void {\n\t\thidden_upload.click();\n\t}\n\n\tfunction loadFiles(files: FileList): void {\n\t\tlet _files: File[] = Array.from(files);\n\t\tif (!files.length) {\n\t\t\treturn;\n\t\t}\n\t\tif (file_count === \"single\") {\n\t\t\t_files = [files[0]];\n\t\t}\n\t\tvar all_file_data: (FileData | File)[] = [];\n\t\t_files.forEach((f, i) => {\n\t\t\tall_file_data[i] = include_file_metadata\n\t\t\t\t? {\n\t\t\t\t\t\tname: f.name,\n\t\t\t\t\t\tsize: f.size,\n\t\t\t\t\t\tdata: \"\",\n\t\t\t\t\t\tblob: f\n\t\t\t\t  }\n\t\t\t\t: f;\n\t\t\tif (\n\t\t\t\tall_file_data.filter((x) => x !== undefined).length === files.length\n\t\t\t) {\n\t\t\t\tdispatch(\n\t\t\t\t\t\"load\",\n\t\t\t\t\tfile_count == \"single\" ? all_file_data[0] : all_file_data\n\t\t\t\t);\n\t\t\t}\n\t\t});\n\t}\n\n\tfunction loadFilesFromUpload(e: Event): void {\n\t\tconst target = e.target as HTMLInputElement;\n\t\tif (!target.files) {\n\t\t\treturn;\n\t\t}\n\t\tloadFiles(target.files);\n\t}\n\n\tfunction clearInputValue(e: Event): void {\n\t\tconst target = e.target as HTMLInputElement;\n\t\tif (target.value) target.value = \"\";\n\t}\n</script>\n\n<input\n\tclass=\"hide\"\n\taccept={accept_file_types}\n\ttype=\"file\"\n\tbind:this={hidden_upload}\n\ton:change={loadFilesFromUpload}\n\ton:click={clearInputValue}\n\tmultiple={file_count === \"multiple\" || undefined}\n\twebkitdirectory={file_count === \"directory\" || undefined}\n\tmozdirectory={file_count === \"directory\" || undefined}\n\tdata-testid=\"{label}-upload-button\"\n/>\n\n<BaseButton\n\t{size}\n\t{variant}\n\t{elem_id}\n\t{elem_classes}\n\t{visible}\n\ton:click={openFileUpload}\n\t{scale}\n\t{min_width}\n\t{disabled}\n>\n\t<slot />\n</BaseButton>\n\n<style>\n\t.hide {\n\t\tdisplay: none;\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "input", "anchor", "clearInputValue", "e", "elem_id", "$$props", "elem_classes", "visible", "file_count", "file_types", "include_file_metadata", "size", "scale", "min_width", "variant", "label", "disabled", "hidden_upload", "dispatch", "createEventDispatcher", "accept_file_types", "x", "openFileUpload", "loadFiles", "files", "_files", "all_file_data", "f", "i", "loadFilesFromUpload", "$$value"], "mappings": "urBAmGWA,EAAc,EAAA,CAAA,0FAjBhBA,EAAiB,EAAA,CAAA,kCAKfA,EAAU,CAAA,IAAK,YAAc,+BACtBA,EAAU,CAAA,IAAK,aAAe,MAAS,uBAC1CA,EAAU,CAAA,IAAK,aAAe,MAAS,sBACvCA,EAAK,CAAA,EAAA,gBAAA,UAVpBC,EAWCC,EAAAC,EAAAC,CAAA,sDANWJ,EAAmB,EAAA,CAAA,cACpBK,EAAe,8CAJjBL,EAAiB,EAAA,CAAA,mBAKfA,EAAU,CAAA,IAAK,YAAc,0CACtBA,EAAU,CAAA,IAAK,aAAe,oDACjCA,EAAU,CAAA,IAAK,aAAe,mDAC9BA,EAAK,CAAA,EAAA,mZAhBV,SAAAK,GAAgBC,EAAQ,OAC1BJ,EAASI,EAAE,OACbJ,EAAO,QAAOA,EAAO,MAAQ,qDAvEvB,CAAA,QAAAK,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,GACd,WAAAG,CAAkB,EAAAH,GAClB,WAAAI,EAAU,EAAA,EAAAJ,EACV,CAAA,sBAAAK,EAAwB,EAAI,EAAAL,EAC5B,CAAA,KAAAM,EAAoB,IAAI,EAAAN,EACxB,CAAA,MAAAO,EAAuB,IAAI,EAAAP,EAC3B,CAAA,UAAAQ,EAAgC,MAAS,EAAAR,EACzC,CAAA,QAAAS,EAA4C,WAAW,EAAAT,GACvD,MAAAU,CAAa,EAAAV,EACb,CAAA,SAAAW,EAAW,EAAK,EAAAX,EAEvBY,EACE,MAAAC,EAAWC,QACbC,EACAX,GAAc,KACjBW,EAAoB,MAEpBX,EAAaA,EAAW,IAAKY,GACxBA,EAAE,WAAW,GAAG,EACZA,EAEDA,EAAI,MAEZD,EAAoBX,EAAW,KAAK,IAAI,YAGhCa,GAAc,CACtBL,EAAc,MAAK,EAGX,SAAAM,EAAUC,EAAe,CAC7B,IAAAC,EAAiB,MAAM,KAAKD,CAAK,EAChC,GAAAA,EAAM,OAGP,CAAAhB,IAAe,WAClBiB,EAAM,CAAID,EAAM,CAAC,CAAA,OAEdE,EAAa,CAAA,EACjBD,EAAO,QAAS,CAAAE,EAAGC,IAAC,CACnBF,EAAcE,CAAC,EAAIlB,GAEhB,KAAMiB,EAAE,KACR,KAAMA,EAAE,KACR,KAAM,GACN,KAAMA,GAENA,EAEFD,EAAc,OAAQL,GAAMA,IAAM,MAAS,EAAE,SAAWG,EAAM,QAE9DN,EACC,OACAV,GAAc,SAAWkB,EAAc,CAAC,EAAIA,CAAa,KAMpD,SAAAG,EAAoB1B,EAAQ,OAC9BJ,EAASI,EAAE,OACZJ,EAAO,OAGZwB,EAAUxB,EAAO,KAAK,2CAaZkB,EAAaa"}