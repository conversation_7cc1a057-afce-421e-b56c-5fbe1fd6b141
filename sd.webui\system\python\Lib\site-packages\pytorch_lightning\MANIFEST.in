include src/version.info
# distribute the lite source code inside PL
include src/lightning_fabric/version.info
include src/lightning_fabric/CHANGELOG.md
recursive-include requirements/fabric *.txt
include src/pytorch_lightning/version.info
include src/pytorch_lightning/CHANGELOG.md
include src/pytorch_lightning/README.md
recursive-include requirements/pytorch *.txt
include .actions/assistant.py
include *.cff  # citation info
include src/pytorch_lightning/py.typed  # marker file for PEP 561
