import{S as T,e as V,s as W,F as v,o as C,Q as O,G as I,h as B,r as F,u as d,v as H,w as g,k as j,H as S,a0 as R,a1 as U,C as Y,E as z,m as N,g as w,M as G,N as L,j as y,p as $,af as b,Z as x,ad as p,V as ee,W as te}from"./index-afe51b5b.js";import{u as le}from"./utils-c3e3db58.js";import{B as se}from"./Button-b4eb936e.js";import{B as ne}from"./BlockLabel-4ae26392.js";import{I as ae}from"./IconButton-12dccad1.js";import{E as ie}from"./Empty-879644c1.js";import{S as oe}from"./ShareButton-34cde537.js";import{D as re}from"./Download-bad6f850.js";import{I as X}from"./Image-873e9e43.js";import{g as ue}from"./utils-90f3612b.js";function _e(n){let e,s,t,l,i,c,o,_,f=n[3]&&M(n),a=n[5]&&Q(n);return{c(){e=N("div"),f&&f.c(),s=C(),a&&a.c(),t=C(),l=N("img"),w(e,"class","icon-buttons svelte-1btp92j"),G(l.src,i=n[0])||w(l,"src",i),w(l,"alt",""),w(l,"class","svelte-1btp92j"),L(l,"selectable",n[4])},m(u,m){B(u,e,m),f&&f.m(e,null),y(e,s),a&&a.m(e,null),B(u,t,m),B(u,l,m),c=!0,o||(_=$(l,"click",n[7]),o=!0)},p(u,m){u[3]?f?(f.p(u,m),m&8&&g(f,1)):(f=M(u),f.c(),g(f,1),f.m(e,s)):f&&(F(),d(f,1,1,()=>{f=null}),H()),u[5]?a?(a.p(u,m),m&32&&g(a,1)):(a=Q(u),a.c(),g(a,1),a.m(e,null)):a&&(F(),d(a,1,1,()=>{a=null}),H()),(!c||m&1&&!G(l.src,i=u[0]))&&w(l,"src",i),(!c||m&16)&&L(l,"selectable",u[4])},i(u){c||(g(f),g(a),c=!0)},o(u){d(f),d(a),c=!1},d(u){u&&(j(e),j(t),j(l)),f&&f.d(),a&&a.d(),o=!1,_()}}}function fe(n){let e,s;return e=new ie({props:{unpadded_box:!0,size:"large",$$slots:{default:[ce]},$$scope:{ctx:n}}}),{c(){v(e.$$.fragment)},m(t,l){I(e,t,l),s=!0},p(t,l){const i={};l&4096&&(i.$$scope={dirty:l,ctx:t}),e.$set(i)},i(t){s||(g(e.$$.fragment,t),s=!0)},o(t){d(e.$$.fragment,t),s=!1},d(t){S(e,t)}}}function M(n){let e,s,t;return s=new ae({props:{Icon:re,label:n[6]("common.download")}}),{c(){e=N("a"),v(s.$$.fragment),w(e,"href",n[0]),w(e,"target",window.__is_colab__?"_blank":null),w(e,"download","image")},m(l,i){B(l,e,i),I(s,e,null),t=!0},p(l,i){const c={};i&64&&(c.label=l[6]("common.download")),s.$set(c),(!t||i&1)&&w(e,"href",l[0])},i(l){t||(g(s.$$.fragment,l),t=!0)},o(l){d(s.$$.fragment,l),t=!1},d(l){l&&j(e),S(s)}}}function Q(n){let e,s;return e=new oe({props:{formatter:n[8],value:n[0]}}),e.$on("share",n[9]),e.$on("error",n[10]),{c(){v(e.$$.fragment)},m(t,l){I(e,t,l),s=!0},p(t,l){const i={};l&1&&(i.value=t[0]),e.$set(i)},i(t){s||(g(e.$$.fragment,t),s=!0)},o(t){d(e.$$.fragment,t),s=!1},d(t){S(e,t)}}}function ce(n){let e,s;return e=new X({}),{c(){v(e.$$.fragment)},m(t,l){I(e,t,l),s=!0},i(t){s||(g(e.$$.fragment,t),s=!0)},o(t){d(e.$$.fragment,t),s=!1},d(t){S(e,t)}}}function he(n){let e,s,t,l,i,c;e=new ne({props:{show_label:n[2],Icon:X,label:n[1]||n[6]("image.image")}});const o=[fe,_e],_=[];function f(a,u){return a[0]===null?0:1}return t=f(n),l=_[t]=o[t](n),{c(){v(e.$$.fragment),s=C(),l.c(),i=O()},m(a,u){I(e,a,u),B(a,s,u),_[t].m(a,u),B(a,i,u),c=!0},p(a,[u]){const m={};u&4&&(m.show_label=a[2]),u&66&&(m.label=a[1]||a[6]("image.image")),e.$set(m);let k=t;t=f(a),t===k?_[t].p(a,u):(F(),d(_[k],1,1,()=>{_[k]=null}),H(),l=_[t],l?l.p(a,u):(l=_[t]=o[t](a),l.c()),g(l,1),l.m(i.parentNode,i))},i(a){c||(g(e.$$.fragment,a),g(l),c=!0)},o(a){d(e.$$.fragment,a),d(l),c=!1},d(a){a&&(j(s),j(i)),S(e,a),_[t].d(a)}}}function me(n,e,s){let t;R(n,U,h=>s(6,t=h));let{value:l}=e,{label:i=void 0}=e,{show_label:c}=e,{show_download_button:o=!0}=e,{selectable:_=!1}=e,{show_share_button:f=!1}=e;const a=Y(),u=h=>{let E=ue(h);E&&a("select",{index:E,value:null})},m=async h=>h?`<img src="${await le(h,"base64")}" />`:"";function k(h){z.call(this,n,h)}function q(h){z.call(this,n,h)}return n.$$set=h=>{"value"in h&&s(0,l=h.value),"label"in h&&s(1,i=h.label),"show_label"in h&&s(2,c=h.show_label),"show_download_button"in h&&s(3,o=h.show_download_button),"selectable"in h&&s(4,_=h.selectable),"show_share_button"in h&&s(5,f=h.show_share_button)},n.$$.update=()=>{n.$$.dirty&1&&l&&a("change",l)},[l,i,c,o,_,f,t,u,m,k,q]}class be extends T{constructor(e){super(),V(this,e,me,he,W,{value:0,label:1,show_label:2,show_download_button:3,selectable:4,show_share_button:5})}}function ge(n){let e,s,t,l;const i=[n[13]];let c={};for(let o=0;o<i.length;o+=1)c=x(c,i[o]);return e=new p({props:c}),t=new be({props:{value:n[0],label:n[4],show_label:n[5],show_download_button:n[6],selectable:n[9],show_share_button:n[14]}}),t.$on("select",n[17]),t.$on("share",n[18]),t.$on("error",n[19]),{c(){v(e.$$.fragment),s=C(),v(t.$$.fragment)},m(o,_){I(e,o,_),B(o,s,_),I(t,o,_),l=!0},p(o,_){const f=_&8192?ee(i,[te(o[13])]):{};e.$set(f);const a={};_&1&&(a.value=o[0]),_&16&&(a.label=o[4]),_&32&&(a.show_label=o[5]),_&64&&(a.show_download_button=o[6]),_&512&&(a.selectable=o[9]),_&16384&&(a.show_share_button=o[14]),t.$set(a)},i(o){l||(g(e.$$.fragment,o),g(t.$$.fragment,o),l=!0)},o(o){d(e.$$.fragment,o),d(t.$$.fragment,o),l=!1},d(o){o&&j(s),S(e,o),S(t,o)}}}function de(n){let e,s;return e=new se({props:{visible:n[3],variant:"solid",border_mode:n[16]?"focus":"base",padding:!1,elem_id:n[1],elem_classes:n[2],height:n[7]||void 0,width:n[8],allow_overflow:!1,container:n[10],scale:n[11],min_width:n[12],$$slots:{default:[ge]},$$scope:{ctx:n}}}),{c(){v(e.$$.fragment)},m(t,l){I(e,t,l),s=!0},p(t,[l]){const i={};l&8&&(i.visible=t[3]),l&2&&(i.elem_id=t[1]),l&4&&(i.elem_classes=t[2]),l&128&&(i.height=t[7]||void 0),l&256&&(i.width=t[8]),l&1024&&(i.container=t[10]),l&2048&&(i.scale=t[11]),l&4096&&(i.min_width=t[12]),l&1106545&&(i.$$scope={dirty:l,ctx:t}),e.$set(i)},i(t){s||(g(e.$$.fragment,t),s=!0)},o(t){d(e.$$.fragment,t),s=!1},d(t){S(e,t)}}}function we(n,e,s){let{elem_id:t=""}=e,{elem_classes:l=[]}=e,{visible:i=!0}=e,{value:c=null}=e,{label:o}=e,{show_label:_}=e,{show_download_button:f}=e,{height:a}=e,{width:u}=e,{selectable:m=!1}=e,{container:k=!0}=e,{scale:q=null}=e,{min_width:h=void 0}=e,{loading_status:E}=e,{show_share_button:P=!1}=e,{gradio:D}=e,Z;const A=({detail:r})=>D.dispatch("select",r),J=({detail:r})=>D.dispatch("share",r),K=({detail:r})=>D.dispatch("error",r);return n.$$set=r=>{"elem_id"in r&&s(1,t=r.elem_id),"elem_classes"in r&&s(2,l=r.elem_classes),"visible"in r&&s(3,i=r.visible),"value"in r&&s(0,c=r.value),"label"in r&&s(4,o=r.label),"show_label"in r&&s(5,_=r.show_label),"show_download_button"in r&&s(6,f=r.show_download_button),"height"in r&&s(7,a=r.height),"width"in r&&s(8,u=r.width),"selectable"in r&&s(9,m=r.selectable),"container"in r&&s(10,k=r.container),"scale"in r&&s(11,q=r.scale),"min_width"in r&&s(12,h=r.min_width),"loading_status"in r&&s(13,E=r.loading_status),"show_share_button"in r&&s(14,P=r.show_share_button),"gradio"in r&&s(15,D=r.gradio)},n.$$.update=()=>{n.$$.dirty&1&&s(0,c=c||null),n.$$.dirty&32769&&D.dispatch("change")},[c,t,l,i,o,_,f,a,u,m,k,q,h,E,P,D,Z,A,J,K]}class ke extends T{constructor(e){super(),V(this,e,we,de,W,{elem_id:1,elem_classes:2,visible:3,value:0,label:4,show_label:5,show_download_button:6,height:7,width:8,selectable:9,container:10,scale:11,min_width:12,loading_status:13,show_share_button:14,gradio:15})}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),b()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),b()}get visible(){return this.$$.ctx[3]}set visible(e){this.$$set({visible:e}),b()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),b()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),b()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),b()}get show_download_button(){return this.$$.ctx[6]}set show_download_button(e){this.$$set({show_download_button:e}),b()}get height(){return this.$$.ctx[7]}set height(e){this.$$set({height:e}),b()}get width(){return this.$$.ctx[8]}set width(e){this.$$set({width:e}),b()}get selectable(){return this.$$.ctx[9]}set selectable(e){this.$$set({selectable:e}),b()}get container(){return this.$$.ctx[10]}set container(e){this.$$set({container:e}),b()}get scale(){return this.$$.ctx[11]}set scale(e){this.$$set({scale:e}),b()}get min_width(){return this.$$.ctx[12]}set min_width(e){this.$$set({min_width:e}),b()}get loading_status(){return this.$$.ctx[13]}set loading_status(e){this.$$set({loading_status:e}),b()}get show_share_button(){return this.$$.ctx[14]}set show_share_button(e){this.$$set({show_share_button:e}),b()}get gradio(){return this.$$.ctx[15]}set gradio(e){this.$$set({gradio:e}),b()}}const He=ke;export{He as default};
//# sourceMappingURL=index-e6b54990.js.map
