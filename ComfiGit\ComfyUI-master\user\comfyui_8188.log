Failed to execute startup-script: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Manager\prestartup_script.py / Failed to initialize: Bad git executable.
The git executable must be specified in one of the following ways:
    - be included in your $PATH
    - be set via $GIT_PYTHON_GIT_EXECUTABLE
    - explicitly set via git.refresh(<full-path-to-git-executable>)

All git commands will error until this is rectified.

This initial message can be silenced or aggravated in the future by setting the
$GIT_PYTHON_REFRESH environment variable. Use one of the following values:
    - quiet|q|silence|s|silent|none|n|0: for no message or exception
    - warn|w|warning|log|l|1: for a warning message (logging level CRITICAL, displayed by default)
    - error|e|exception|raise|r|2: for a raised exception

Example:
    export GIT_PYTHON_REFRESH=quiet

[2025-06-28 17:03:02.277] 
Prestartup times for custom nodes:
[2025-06-28 17:03:02.277]    1.2 seconds (PRESTARTUP FAILED): C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Manager
[2025-06-28 17:03:02.277] 
[2025-06-28 17:03:03.550] Checkpoint files will always be loaded safely.
[2025-06-28 17:03:03.684] Total VRAM 24463 MB, total RAM 64957 MB
[2025-06-28 17:03:03.685] pytorch version: 2.7.1+cu128
[2025-06-28 17:03:03.685] Set vram state to: NORMAL_VRAM
[2025-06-28 17:03:03.686] Device: cuda:0 NVIDIA GeForce RTX 5090 Laptop GPU : cudaMallocAsync
