import{S as ze,e as Fe,s as Ie,o as K,m as S,g as m,N as T,h as B,p as J,r as P,u as H,v as Q,w as N,k as C,B as ge,E as Ae,R as V,t as te,x as le,n as re,F as j,G as x,H as $,Q as ut,ao as ot,U as se,K as De,j as q,ae as ct,X as ie,a0 as _t,a1 as dt,C as ht,f as W,a6 as X,I as Z,J as de,$ as he}from"./index-afe51b5b.js";import{c as gt}from"./utils-c3e3db58.js";import{U as mt}from"./Upload-eec15a82.js";import{a as Ke}from"./Button-b4eb936e.js";import{M as pt}from"./StaticMarkdown-15b756e1.js";import{d as bt}from"./dsv-576afacd.js";var Le=Object.prototype.hasOwnProperty;function ae(s,e){var t,l;if(s===e)return!0;if(s&&e&&(t=s.constructor)===e.constructor){if(t===Date)return s.getTime()===e.getTime();if(t===RegExp)return s.toString()===e.toString();if(t===Array){if((l=s.length)===e.length)for(;l--&&ae(s[l],e[l]););return l===-1}if(!t||typeof s=="object"){l=0;for(t in s)if(Le.call(s,t)&&++l&&!Le.call(e,t)||!(t in e)||!ae(s[t],e[t]))return!1;return Object.keys(e).length===l}}return s!==s&&e!==e}function Ee(s){let e,t,l;return{c(){e=S("input"),m(e,"tabindex","-1"),e.value=s[0],m(e,"class","svelte-q8uklq"),T(e,"header",s[3])},m(n,i){B(n,e,i),s[8](e),t||(l=[J(e,"keydown",s[7]),J(e,"blur",s[9])],t=!0)},p(n,i){i&1&&e.value!==n[0]&&(e.value=n[0]),i&8&&T(e,"header",n[3])},d(n){n&&C(e),s[8](null),t=!1,ge(l)}}}function kt(s){let e;return{c(){e=te(s[0])},m(t,l){B(t,e,l)},p(t,l){l&1&&le(e,t[0])},i:re,o:re,d(t){t&&C(e)}}}function wt(s){let e,t;return e=new pt({props:{message:s[0].toLocaleString(),latex_delimiters:s[5],chatbot:!1}}),{c(){j(e.$$.fragment)},m(l,n){x(e,l,n),t=!0},p(l,n){const i={};n&1&&(i.message=l[0].toLocaleString()),n&32&&(i.latex_delimiters=l[5]),e.$set(i)},i(l){t||(N(e.$$.fragment,l),t=!0)},o(l){H(e.$$.fragment,l),t=!1},d(l){$(e,l)}}}function yt(s){let e,t;return{c(){e=new ot(!1),t=ut(),e.a=t},m(l,n){e.m(s[0],l,n),B(l,t,n)},p(l,n){n&1&&e.p(l[0])},i:re,o:re,d(l){l&&(C(t),e.d())}}}function vt(s){let e,t,l,n,i,o,a,d=s[2]&&Ee(s);const g=[yt,wt,kt],h=[];function v(p,_){return p[4]==="html"?0:p[4]==="markdown"?1:2}return l=v(s),n=h[l]=g[l](s),{c(){d&&d.c(),e=K(),t=S("span"),n.c(),m(t,"tabindex","-1"),m(t,"role","button"),m(t,"class","svelte-q8uklq"),T(t,"edit",s[2])},m(p,_){d&&d.m(p,_),B(p,e,_),B(p,t,_),h[l].m(t,null),i=!0,o||(a=J(t,"dblclick",s[6]),o=!0)},p(p,[_]){p[2]?d?d.p(p,_):(d=Ee(p),d.c(),d.m(e.parentNode,e)):d&&(d.d(1),d=null);let u=l;l=v(p),l===u?h[l].p(p,_):(P(),H(h[u],1,1,()=>{h[u]=null}),Q(),n=h[l],n?n.p(p,_):(n=h[l]=g[l](p),n.c()),N(n,1),n.m(t,null)),(!i||_&4)&&T(t,"edit",p[2])},i(p){i||(N(n),i=!0)},o(p){H(n),i=!1},d(p){p&&(C(e),C(t)),d&&d.d(p),h[l].d(),o=!1,a()}}}function At(s,e,t){let{edit:l}=e,{value:n=""}=e,{header:i=!1}=e,{datatype:o="str"}=e,{latex_delimiters:a}=e,{el:d}=e;function g(_){Ae.call(this,s,_)}function h(_){Ae.call(this,s,_)}function v(_){V[_?"unshift":"push"](()=>{d=_,t(1,d)})}const p=({currentTarget:_})=>{t(0,n=_.value),_.setAttribute("tabindex","-1")};return s.$$set=_=>{"edit"in _&&t(2,l=_.edit),"value"in _&&t(0,n=_.value),"header"in _&&t(3,i=_.header),"datatype"in _&&t(4,o=_.datatype),"latex_delimiters"in _&&t(5,a=_.latex_delimiters),"el"in _&&t(1,d=_.el)},[n,d,l,i,o,a,g,h,v,p]}class Je extends ze{constructor(e){super(),Fe(this,e,At,vt,Ie,{edit:2,value:0,header:3,datatype:4,latex_delimiters:5,el:1})}}function Me(s,e,t){const l=s.slice();return l[56]=e[t],l[58]=t,l}function qe(s,e,t){const l=s.slice();return l[59]=e[t].value,l[60]=e[t].id,l[61]=e,l[62]=t,l}function Be(s,e,t){const l=s.slice();return l[59]=e[t].value,l[60]=e[t].id,l[63]=e,l[58]=t,l}function Ce(s){let e,t;return{c(){e=S("p"),t=te(s[1]),m(e,"class","svelte-1anhtf6")},m(l,n){B(l,e,n),q(e,t)},p(l,n){n[0]&2&&le(t,l[1])},d(l){l&&C(e)}}}function Te(s){let e,t;return{c(){e=S("caption"),t=te(s[1]),m(e,"class","sr-only")},m(l,n){B(l,e,n),q(e,t)},p(l,n){n[0]&2&&le(t,l[1])},d(l){l&&C(e)}}}function Ne(s,e){let t,l,n,i,o,a,d,g,h,v,p,_=e[60],u,D,y;function w(U){e[33](U,e[60])}function f(){return e[34](e[60])}let M={value:e[59],latex_delimiters:e[4],edit:e[15]===e[60],header:!0};e[12][e[60]].input!==void 0&&(M.el=e[12][e[60]].input),n=new Je({props:M}),V.push(()=>se(n,"el",w)),n.$on("keydown",e[24]),n.$on("dblclick",f);function R(){return e[35](e[58])}const E=()=>e[36](t,_),O=()=>e[36](null,_);return{key:s,first:null,c(){t=S("th"),l=S("div"),j(n.$$.fragment),o=K(),a=S("div"),d=W("svg"),g=W("path"),v=K(),m(g,"d","M4.49999 0L8.3971 6.75H0.602875L4.49999 0Z"),m(d,"width","1em"),m(d,"height","1em"),m(d,"viewBox","0 0 9 7"),m(d,"fill","none"),m(d,"xmlns","http://www.w3.org/2000/svg"),m(d,"class","svelte-1anhtf6"),m(a,"class",h="sort-button "+e[13]+" svelte-1anhtf6"),T(a,"sorted",e[14]===e[58]),T(a,"des",e[14]===e[58]&&e[13]==="des"),m(l,"class","cell-wrap svelte-1anhtf6"),m(t,"aria-sort",p=e[18](e[59],e[14],e[13])),m(t,"class","svelte-1anhtf6"),T(t,"editing",e[15]===e[60]),this.first=t},m(U,k){B(U,t,k),q(t,l),x(n,l,null),q(l,o),q(l,a),q(a,d),q(d,g),q(t,v),E(),u=!0,D||(y=J(a,"click",R),D=!0)},p(U,k){e=U;const z={};k[0]&1024&&(z.value=e[59]),k[0]&16&&(z.latex_delimiters=e[4]),k[0]&33792&&(z.edit=e[15]===e[60]),!i&&k[0]&5120&&(i=!0,z.el=e[12][e[60]].input,ie(()=>i=!1)),n.$set(z),(!u||k[0]&8192&&h!==(h="sort-button "+e[13]+" svelte-1anhtf6"))&&m(a,"class",h),(!u||k[0]&25600)&&T(a,"sorted",e[14]===e[58]),(!u||k[0]&25600)&&T(a,"des",e[14]===e[58]&&e[13]==="des"),(!u||k[0]&25600&&p!==(p=e[18](e[59],e[14],e[13])))&&m(t,"aria-sort",p),_!==e[60]&&(O(),_=e[60],E()),(!u||k[0]&33792)&&T(t,"editing",e[15]===e[60])},i(U){u||(N(n.$$.fragment,U),u=!0)},o(U){H(n.$$.fragment,U),u=!1},d(U){U&&C(t),$(n),O(),D=!1,y()}}}function Re(s,e){let t,l,n,i,o,a=e[60],d,g,h;function v(R){e[37](R,e[59],e[61],e[62])}function p(R){e[38](R,e[60])}let _={latex_delimiters:e[4],edit:e[9]===e[60],datatype:Array.isArray(e[0])?e[0][e[62]]:e[0]};e[59]!==void 0&&(_.value=e[59]),e[12][e[60]].input!==void 0&&(_.el=e[12][e[60]].input),n=new Je({props:_}),V.push(()=>se(n,"value",v)),V.push(()=>se(n,"el",p));const u=()=>e[39](t,a),D=()=>e[39](null,a);function y(){return e[40](e[60])}function w(){return e[41](e[60])}function f(){return e[42](e[60])}function M(...R){return e[43](e[58],e[62],e[60],...R)}return{key:s,first:null,c(){t=S("td"),l=S("div"),j(n.$$.fragment),m(l,"class","cell-wrap svelte-1anhtf6"),T(l,"border-transparent",e[8]!==e[60]),m(t,"tabindex","0"),m(t,"class","svelte-1anhtf6"),this.first=t},m(R,E){B(R,t,E),q(t,l),x(n,l,null),u(),d=!0,g||(h=[J(t,"touchstart",y,{passive:!0}),J(t,"click",w),J(t,"dblclick",f),J(t,"keydown",M)],g=!0)},p(R,E){e=R;const O={};E[0]&16&&(O.latex_delimiters=e[4]),E[0]&2560&&(O.edit=e[9]===e[60]),E[0]&2049&&(O.datatype=Array.isArray(e[0])?e[0][e[62]]:e[0]),!i&&E[0]&2048&&(i=!0,O.value=e[59],ie(()=>i=!1)),!o&&E[0]&6144&&(o=!0,O.el=e[12][e[60]].input,ie(()=>o=!1)),n.$set(O),(!d||E[0]&2304)&&T(l,"border-transparent",e[8]!==e[60]),a!==e[60]&&(D(),a=e[60],u())},i(R){d||(N(n.$$.fragment,R),d=!0)},o(R){H(n.$$.fragment,R),d=!1},d(R){R&&C(t),$(n),D(),g=!1,ge(h)}}}function Se(s,e){let t,l=[],n=new Map,i,o,a=Z(e[56]);const d=g=>g[60];for(let g=0;g<a.length;g+=1){let h=qe(e,a,g),v=d(h);n.set(v,l[g]=Re(v,h))}return{key:s,first:null,c(){t=S("tr");for(let g=0;g<l.length;g+=1)l[g].c();i=K(),m(t,"class","svelte-1anhtf6"),this.first=t},m(g,h){B(g,t,h);for(let v=0;v<l.length;v+=1)l[v]&&l[v].m(t,null);q(t,i),o=!0},p(g,h){e=g,h[0]&3676945&&(a=Z(e[56]),P(),l=de(l,h,d,1,e,a,n,t,he,Re,i,qe),Q())},i(g){if(!o){for(let h=0;h<a.length;h+=1)N(l[h]);o=!0}},o(g){for(let h=0;h<l.length;h+=1)H(l[h]);o=!1},d(g){g&&C(t);for(let h=0;h<l.length;h+=1)l[h].d()}}}function Dt(s){let e,t,l,n,i=[],o=new Map,a,d,g=[],h=new Map,v,p=s[1]&&s[1].length!==0&&Te(s),_=Z(s[10]);const u=w=>w[60];for(let w=0;w<_.length;w+=1){let f=Be(s,_,w),M=u(f);o.set(M,i[w]=Ne(M,f))}let D=Z(s[11]);const y=w=>w[56];for(let w=0;w<D.length;w+=1){let f=Me(s,D,w),M=y(f);h.set(M,g[w]=Se(M,f))}return{c(){e=S("table"),p&&p.c(),t=K(),l=S("thead"),n=S("tr");for(let w=0;w<i.length;w+=1)i[w].c();a=K(),d=S("tbody");for(let w=0;w<g.length;w+=1)g[w].c();m(n,"class","svelte-1anhtf6"),m(l,"class","svelte-1anhtf6"),m(d,"class","svelte-1anhtf6"),m(e,"class","svelte-1anhtf6"),T(e,"dragging",s[16])},m(w,f){B(w,e,f),p&&p.m(e,null),q(e,t),q(e,l),q(l,n);for(let M=0;M<i.length;M+=1)i[M]&&i[M].m(n,null);q(e,a),q(e,d);for(let M=0;M<g.length;M+=1)g[M]&&g[M].m(d,null);v=!0},p(w,f){w[1]&&w[1].length!==0?p?p.p(w,f):(p=Te(w),p.c(),p.m(e,t)):p&&(p.d(1),p=null),f[0]&29684752&&(_=Z(w[10]),P(),i=de(i,f,u,1,w,_,o,n,he,Ne,null,Be),Q()),f[0]&3676945&&(D=Z(w[11]),P(),g=de(g,f,y,1,w,D,h,d,he,Se,null,Me),Q()),(!v||f[0]&65536)&&T(e,"dragging",w[16])},i(w){if(!v){for(let f=0;f<_.length;f+=1)N(i[f]);for(let f=0;f<D.length;f+=1)N(g[f]);v=!0}},o(w){for(let f=0;f<i.length;f+=1)H(i[f]);for(let f=0;f<g.length;f+=1)H(g[f]);v=!1},d(w){w&&C(e),p&&p.d();for(let f=0;f<i.length;f+=1)i[f].d();for(let f=0;f<g.length;f+=1)g[f].d()}}}function He(s){let e,t,l,n=s[3][1]==="dynamic"&&Oe(s),i=s[2][1]==="dynamic"&&Ue(s);return{c(){e=S("div"),n&&n.c(),t=K(),i&&i.c(),m(e,"class","controls-wrap svelte-1anhtf6")},m(o,a){B(o,e,a),n&&n.m(e,null),q(e,t),i&&i.m(e,null),l=!0},p(o,a){o[3][1]==="dynamic"?n?(n.p(o,a),a[0]&8&&N(n,1)):(n=Oe(o),n.c(),N(n,1),n.m(e,t)):n&&(P(),H(n,1,1,()=>{n=null}),Q()),o[2][1]==="dynamic"?i?(i.p(o,a),a[0]&4&&N(i,1)):(i=Ue(o),i.c(),N(i,1),i.m(e,null)):i&&(P(),H(i,1,1,()=>{i=null}),Q())},i(o){l||(N(n),N(i),l=!0)},o(o){H(n),H(i),l=!1},d(o){o&&C(e),n&&n.d(),i&&i.d()}}}function Oe(s){let e,t,l;return t=new Ke({props:{variant:"secondary",size:"sm",$$slots:{default:[Lt]},$$scope:{ctx:s}}}),t.$on("click",s[46]),{c(){e=S("span"),j(t.$$.fragment),m(e,"class","button-wrap svelte-1anhtf6")},m(n,i){B(n,e,i),x(t,e,null),l=!0},p(n,i){const o={};i[0]&131072|i[2]&4&&(o.$$scope={dirty:i,ctx:n}),t.$set(o)},i(n){l||(N(t.$$.fragment,n),l=!0)},o(n){H(t.$$.fragment,n),l=!1},d(n){n&&C(e),$(t)}}}function Lt(s){let e,t,l,n=s[17]("dataframe.new_row")+"",i;return{c(){e=W("svg"),t=W("path"),l=K(),i=te(n),m(t,"fill","currentColor"),m(t,"d","M24.59 16.59L17 24.17V4h-2v20.17l-7.59-7.58L6 18l10 10l10-10l-1.41-1.41z"),m(e,"xmlns","http://www.w3.org/2000/svg"),m(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),m(e,"aria-hidden","true"),m(e,"role","img"),m(e,"width","1em"),m(e,"height","1em"),m(e,"preserveAspectRatio","xMidYMid meet"),m(e,"viewBox","0 0 32 32"),m(e,"class","svelte-1anhtf6")},m(o,a){B(o,e,a),q(e,t),B(o,l,a),B(o,i,a)},p(o,a){a[0]&131072&&n!==(n=o[17]("dataframe.new_row")+"")&&le(i,n)},d(o){o&&(C(e),C(l),C(i))}}}function Ue(s){let e,t,l;return t=new Ke({props:{variant:"secondary",size:"sm",$$slots:{default:[Et]},$$scope:{ctx:s}}}),t.$on("click",s[26]),{c(){e=S("span"),j(t.$$.fragment),m(e,"class","button-wrap svelte-1anhtf6")},m(n,i){B(n,e,i),x(t,e,null),l=!0},p(n,i){const o={};i[0]&131072|i[2]&4&&(o.$$scope={dirty:i,ctx:n}),t.$set(o)},i(n){l||(N(t.$$.fragment,n),l=!0)},o(n){H(t.$$.fragment,n),l=!1},d(n){n&&C(e),$(t)}}}function Et(s){let e,t,l,n=s[17]("dataframe.new_column")+"",i;return{c(){e=W("svg"),t=W("path"),l=K(),i=te(n),m(t,"fill","currentColor"),m(t,"d","m18 6l-1.43 1.393L24.15 15H4v2h20.15l-7.58 7.573L18 26l10-10L18 6z"),m(e,"xmlns","http://www.w3.org/2000/svg"),m(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),m(e,"aria-hidden","true"),m(e,"role","img"),m(e,"width","1em"),m(e,"height","1em"),m(e,"preserveAspectRatio","xMidYMid meet"),m(e,"viewBox","0 0 32 32"),m(e,"class","svelte-1anhtf6")},m(o,a){B(o,e,a),q(e,t),B(o,l,a),B(o,i,a)},p(o,a){a[0]&131072&&n!==(n=o[17]("dataframe.new_column")+"")&&le(i,n)},d(o){o&&(C(e),C(l),C(i))}}}function Mt(s){let e,t,l,n,i,o,a,d,g,h=s[1]&&s[1].length!==0&&Ce(s);function v(u){s[44](u)}let p={flex:!1,center:!1,boundedheight:!1,disable_click:!0,$$slots:{default:[Dt]},$$scope:{ctx:s}};s[16]!==void 0&&(p.dragging=s[16]),n=new mt({props:p}),V.push(()=>se(n,"dragging",v)),n.$on("load",s[45]);let _=s[5]&&He(s);return{c(){e=S("div"),h&&h.c(),t=K(),l=S("div"),j(n.$$.fragment),o=K(),_&&_.c(),m(l,"class","table-wrap svelte-1anhtf6"),De(l,"max-height",typeof s[7]===void 0?"auto":s[7]+"px"),T(l,"dragging",s[16]),T(l,"no-wrap",!s[6]),m(e,"class","svelte-1anhtf6"),T(e,"label",s[1]&&s[1].length!==0)},m(u,D){B(u,e,D),h&&h.m(e,null),q(e,t),q(e,l),x(n,l,null),q(e,o),_&&_.m(e,null),a=!0,d||(g=[J(window,"click",s[27]),J(window,"touchstart",s[27]),ct(gt.call(null,e))],d=!0)},p(u,D){u[1]&&u[1].length!==0?h?h.p(u,D):(h=Ce(u),h.c(),h.m(e,t)):h&&(h.d(1),h=null);const y={};D[0]&130835|D[2]&4&&(y.$$scope={dirty:D,ctx:u}),!i&&D[0]&65536&&(i=!0,y.dragging=u[16],ie(()=>i=!1)),n.$set(y),(!a||D[0]&128)&&De(l,"max-height",typeof u[7]===void 0?"auto":u[7]+"px"),(!a||D[0]&65536)&&T(l,"dragging",u[16]),(!a||D[0]&64)&&T(l,"no-wrap",!u[6]),u[5]?_?(_.p(u,D),D[0]&32&&N(_,1)):(_=He(u),_.c(),N(_,1),_.m(e,null)):_&&(P(),H(_,1,1,()=>{_=null}),Q()),(!a||D[0]&2)&&T(e,"label",u[1]&&u[1].length!==0)},i(u){a||(N(n.$$.fragment,u),N(_),a=!0)},o(u){H(n.$$.fragment,u),H(_),a=!1},d(u){u&&C(e),h&&h.d(),$(n),_&&_.d(),d=!1,ge(g)}}}function qt(s,e){return e.filter(t);function t(l){var n=-1;return s.split(`
`).every(i);function i(o){if(!o)return!0;var a=o.split(l).length;return n<0&&(n=a),n===a&&a>1}}}function Bt(s){const e=atob(s.split(",")[1]),t=s.split(",")[0].split(":")[1].split(";")[0],l=new ArrayBuffer(e.length),n=new Uint8Array(l);for(let i=0;i<e.length;i++)n[i]=e.charCodeAt(i);return new Blob([l],{type:t})}function Ct(s,e,t){let l;_t(s,dt,r=>t(17,l=r));let{datatype:n}=e,{label:i=null}=e,{headers:o=[]}=e,{values:a=[[]]}=e,{col_count:d}=e,{row_count:g}=e,{latex_delimiters:h}=e,{editable:v=!0}=e,{wrap:p=!1}=e,{height:_=void 0}=e,u=!1;const D=ht();let y=!1;const w=(r,c)=>k[r][c].value;let f={};function M(r){let c=r||[];if(d[1]==="fixed"&&c.length<d[0]){const b=Array(d[0]-c.length).fill("").map((A,L)=>`${L+c.length}`);c=c.concat(b)}return!c||c.length===0?Array(d[0]).fill(0).map((b,A)=>{const L=`h-${A}`;return t(12,f[L]={cell:null,input:null},f),{id:L,value:JSON.stringify(A+1)}}):c.map((b,A)=>{const L=`h-${A}`;return t(12,f[L]={cell:null,input:null},f),{id:L,value:b??""}})}function R(r){const c=r.length;return Array(g[1]==="fixed"||c<g[0]?g[0]:c).fill(0).map((b,A)=>Array(d[1]==="fixed"?d[0]:c>0?r[0].length:o.length).fill(0).map((L,I)=>{const Y=`${A}-${I}`;return t(12,f[Y]={input:null,cell:null},f),{value:r?.[A]?.[I]??"",id:Y}}))}let E=M(o),O;async function U(){typeof y=="string"?(await X(),f[y]?.input?.focus()):typeof u=="string"&&(await X(),f[u]?.input?.focus())}let k=[[]],z;function Xe(r,c,b){if(!c)return"none";if(o[c]===r){if(b==="asc")return"ascending";if(b==="des")return"descending"}return"none"}function me(r){return k.reduce((c,b,A)=>{const L=b.reduce((I,Y,ce)=>r===Y.id?ce:I,-1);return L===-1?c:[A,L]},[-1,-1])}async function ee(r,c){if(!v||y===r)return;if(c){const[A,L]=me(r);t(11,k[A][L].value="",k)}t(9,y=r),await X();const{input:b}=f[r];b?.focus()}async function pe(r,c,b,A){let L;switch(r.key){case"ArrowRight":if(y)break;r.preventDefault(),L=k[c][b+1],t(8,u=L?L.id:u);break;case"ArrowLeft":if(y)break;r.preventDefault(),L=k[c][b-1],t(8,u=L?L.id:u);break;case"ArrowDown":if(y)break;r.preventDefault(),L=k[c+1],t(8,u=L?L[b].id:u);break;case"ArrowUp":if(y)break;r.preventDefault(),L=k[c-1],t(8,u=L?L[b].id:u);break;case"Escape":if(!v)break;r.preventDefault(),t(8,u=y),t(9,y=!1);break;case"Enter":if(!v)break;if(r.preventDefault(),r.shiftKey){ue(c),await X();const[ft]=me(A);t(8,u=k[ft+1][b].id)}else y===A?t(9,y=!1):ee(A);break;case"Backspace":if(!v)break;y||(r.preventDefault(),t(11,k[c][b].value="",k));break;case"Delete":if(!v)break;y||(r.preventDefault(),t(11,k[c][b].value="",k));break;case"Tab":let I=r.shiftKey?-1:1,Y=k[c][b+I],ce=k?.[c+I]?.[I>0?0:E.length-1],_e=Y||ce;_e&&(r.preventDefault(),t(8,u=_e?_e.id:u)),t(9,y=!1);break;default:(!y||y&&y!==A)&&r.key.length===1&&ee(A,!0);break}}async function be(r){y!==r&&u!==r&&(t(9,y=!1),t(8,u=r))}async function ke(r,c){if(c==="edit"&&typeof r=="string"&&(await X(),f[r].input?.focus()),c==="edit"&&typeof r=="boolean"&&typeof u=="string"){let b=f[u]?.cell;await X(),b?.focus()}if(c==="select"&&typeof r=="string"){const{cell:b}=f[r];await X(),b?.focus()}}let G,ne;function Ye(r,c){c==="asc"?t(11,k=k.sort((b,A)=>b[r].value<A[r].value?-1:1)):c==="des"&&t(11,k=k.sort((b,A)=>b[r].value>A[r].value?-1:1))}function we(r){typeof ne!="number"||ne!==r?(t(13,G="asc"),t(14,ne=r)):G==="asc"?t(13,G="des"):G==="des"&&t(13,G="asc"),Ye(r,G)}let F;function ye(){if(typeof u=="string"){const r=f[u].input?.value;if(E.find(c=>c.id===u)){let c=E.find(b=>b.id===u);r&&(c.value=r)}else r&&E.push({id:u,value:r})}}async function fe(r,c){!v||d[1]!=="dynamic"||y===r||(t(15,F=r),await X(),f[r].input?.focus(),c&&f[r].input?.select())}function Ge(r){if(v)switch(r.key){case"Escape":case"Enter":case"Tab":r.preventDefault(),t(8,u=F),t(15,F=!1),ye();break}}function ue(r){if(g[1]==="dynamic"){if(k.length===0){t(30,a=[Array(o.length).fill("")]);return}k.splice(r?r+1:k.length,0,Array(k[0].length).fill(0).map((c,b)=>{const A=`${k.length}-${b}`;return t(12,f[A]={cell:null,input:null},f),{id:A,value:""}})),t(11,k),t(30,a),t(32,z)}}async function Pe(){if(d[1]!=="dynamic")return;for(let c=0;c<k.length;c++){const b=`${c}-${k[c].length}`;t(12,f[b]={cell:null,input:null},f),k[c].push({id:b,value:""})}const r=`h-${E.length}`;t(12,f[r]={cell:null,input:null},f),E.push({id:r,value:`Header ${E.length+1}`}),t(11,k),t(30,a),t(32,z),t(10,E),t(29,o),t(31,O),t(30,a),await X(),fe(r,!0)}function Qe(r){typeof y=="string"&&f[y]&&f[y].cell!==r.target&&!f[y].cell?.contains(r?.target)&&t(9,y=!1),typeof F=="string"&&f[F]&&f[F].cell!==r.target&&!f[F].cell?.contains(r.target)&&(t(8,u=F),t(15,F=!1),ye(),t(15,F=!1))}function ve(r){const c=new FileReader;function b(A){if(!A?.target?.result||typeof A.target.result!="string")return;const[L]=qt(A.target.result,[",","	"]),[I,...Y]=bt(L).parseRows(A.target.result);t(10,E=M(d[1]==="fixed"?I.slice(0,d[0]):I)),t(30,a=Y),c.removeEventListener("loadend",b)}c.addEventListener("loadend",b),c.readAsText(r)}let oe=!1;function Ve(r,c){s.$$.not_equal(f[c].input,r)&&(f[c].input=r,t(12,f))}const Ze=r=>fe(r),We=r=>we(r);function je(r,c){V[r?"unshift":"push"](()=>{f[c].cell=r,t(12,f)})}function xe(r,c,b,A){b[A].value=r,t(11,k),t(30,a),t(32,z)}function $e(r,c){s.$$.not_equal(f[c].input,r)&&(f[c].input=r,t(12,f))}function et(r,c){V[r?"unshift":"push"](()=>{f[c].cell=r,t(12,f)})}const tt=r=>ee(r),lt=r=>be(r),nt=r=>ee(r),rt=(r,c,b,A)=>pe(A,r,c,b);function st(r){oe=r,t(16,oe)}const it=r=>ve(Bt(r.detail.data)),at=()=>ue();return s.$$set=r=>{"datatype"in r&&t(0,n=r.datatype),"label"in r&&t(1,i=r.label),"headers"in r&&t(29,o=r.headers),"values"in r&&t(30,a=r.values),"col_count"in r&&t(2,d=r.col_count),"row_count"in r&&t(3,g=r.row_count),"latex_delimiters"in r&&t(4,h=r.latex_delimiters),"editable"in r&&t(5,v=r.editable),"wrap"in r&&t(6,p=r.wrap),"height"in r&&t(7,_=r.height)},s.$$.update=()=>{if(s.$$.dirty[0]&1073741824&&(a&&!Array.isArray(a)?(t(29,o=a.headers),t(30,a=a.data),t(8,u=!1)):a===null&&(t(30,a=[]),t(8,u=!1))),s.$$.dirty[0]&256&&u!==!1){const r=u.split("-"),c=parseInt(r[0]),b=parseInt(r[1]);!isNaN(c)&&!isNaN(b)&&D("select",{index:[c,b],value:w(c,b)})}s.$$.dirty[0]&536870912|s.$$.dirty[1]&1&&(ae(o,O)||(t(10,E=M(o)),t(31,O=o),U())),s.$$.dirty[0]&1073741824|s.$$.dirty[1]&2&&(ae(a,z)||(t(11,k=R(a)),t(32,z=a),U())),s.$$.dirty[0]&3072&&E&&D("change",{data:k.map(r=>r.map(({value:c})=>c)),headers:E.map(r=>r.value)}),s.$$.dirty[0]&512&&ke(y,"edit"),s.$$.dirty[0]&256&&ke(u,"select")},[n,i,d,g,h,v,p,_,u,y,E,k,f,G,ne,F,oe,l,Xe,ee,pe,be,we,fe,Ge,ue,Pe,Qe,ve,o,a,O,z,Ve,Ze,We,je,xe,$e,et,tt,lt,nt,rt,st,it,at]}class Ut extends ze{constructor(e){super(),Fe(this,e,Ct,Mt,Ie,{datatype:0,label:1,headers:29,values:30,col_count:2,row_count:3,latex_delimiters:4,editable:5,wrap:6,height:7},null,[-1,-1,-1])}}export{Ut as T};
//# sourceMappingURL=Table-2eea8e55.js.map
