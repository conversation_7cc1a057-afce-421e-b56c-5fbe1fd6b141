{"version": 3, "file": "index-952b6c50.js", "sources": ["../../../../js/model3D/interactive/Model3DUpload.svelte", "../../../../js/model3D/interactive/InteractiveModel3d.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher, tick, onMount } from \"svelte\";\n\timport { Upload, ModifyUpload } from \"@gradio/upload\";\n\timport type { FileData } from \"@gradio/upload\";\n\timport { BlockLabel } from \"@gradio/atoms\";\n\timport { File } from \"@gradio/icons\";\n\n\texport let value: null | FileData;\n\texport let clearColor: [number, number, number, number] = [0, 0, 0, 0];\n\texport let label = \"\";\n\texport let show_label: boolean;\n\n\tlet mounted = false;\n\n\tonMount(() => {\n\t\tif (value != null) {\n\t\t\taddNewModel();\n\t\t}\n\t\tmounted = true;\n\t});\n\n\t$: ({ data, is_file, name } = value || {\n\t\tdata: undefined,\n\t\tis_file: undefined,\n\t\tname: undefined\n\t});\n\n\t$: canvas && mounted && data != null && is_file && addNewModel();\n\n\tasync function handle_upload({\n\t\tdetail\n\t}: CustomEvent<FileData>): Promise<void> {\n\t\tvalue = detail;\n\t\tawait tick();\n\t\tdispatch(\"change\", value);\n\t\taddNewModel();\n\t}\n\n\tasync function handle_clear(): Promise<void> {\n\t\tif (scene && engine) {\n\t\t\tscene.dispose();\n\t\t\tengine.dispose();\n\t\t}\n\t\tvalue = null;\n\t\tawait tick();\n\t\tdispatch(\"clear\");\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: FileData | null;\n\t\tclear: undefined;\n\t\tdrag: boolean;\n\t}>();\n\n\tlet dragging = false;\n\n\timport * as BABYLON from \"babylonjs\";\n\timport * as BABYLON_LOADERS from \"babylonjs-loaders\";\n\n\tBABYLON_LOADERS.OBJFileLoader.IMPORT_VERTEX_COLORS = true;\n\n\tlet canvas: HTMLCanvasElement;\n\tlet scene: BABYLON.Scene;\n\tlet engine: BABYLON.Engine;\n\n\tfunction addNewModel(): void {\n\t\tif (scene && !scene.isDisposed && engine) {\n\t\t\tscene.dispose();\n\t\t\tengine.dispose();\n\t\t}\n\n\t\tengine = new BABYLON.Engine(canvas, true);\n\t\tscene = new BABYLON.Scene(engine);\n\t\tscene.createDefaultCameraOrLight();\n\t\tscene.clearColor = scene.clearColor = new BABYLON.Color4(...clearColor);\n\n\t\tengine.runRenderLoop(() => {\n\t\t\tscene.render();\n\t\t});\n\n\t\twindow.addEventListener(\"resize\", () => {\n\t\t\tengine.resize();\n\t\t});\n\n\t\tif (!value) return;\n\n\t\tlet url: string;\n\t\tif (value.is_file) {\n\t\t\turl = value.data;\n\t\t} else {\n\t\t\tlet base64_model_content = value.data;\n\t\t\tlet raw_content = BABYLON.Tools.DecodeBase64(base64_model_content);\n\t\t\tlet blob = new Blob([raw_content]);\n\t\t\turl = URL.createObjectURL(blob);\n\t\t}\n\n\t\tBABYLON.SceneLoader.ShowLoadingScreen = false;\n\t\tBABYLON.SceneLoader.Append(\n\t\t\turl,\n\t\t\t\"\",\n\t\t\tscene,\n\t\t\t() => {\n\t\t\t\tscene.createDefaultCamera(true, true, true);\n\t\t\t},\n\t\t\tundefined,\n\t\t\tundefined,\n\t\t\t\".\" + value.name.split(\".\")[1]\n\t\t);\n\t}\n\n\t$: dispatch(\"drag\", dragging);\n</script>\n\n<BlockLabel {show_label} Icon={File} label={label || \"3D Model\"} />\n\n{#if value === null}\n\t<Upload on:load={handle_upload} filetype=\".obj, .gltf, .glb\" bind:dragging>\n\t\t<slot />\n\t</Upload>\n{:else}\n\t<div class=\"input-model\">\n\t\t<ModifyUpload on:clear={handle_clear} absolute />\n\t\t<canvas bind:this={canvas} />\n\t</div>\n{/if}\n\n<style>\n\t.input-model {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-64);\n\t}\n\n\tcanvas {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: contain;\n\t\toverflow: hidden;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport type { FileData } from \"@gradio/upload\";\n\timport { normalise_file } from \"@gradio/upload\";\n\timport Model3DUpload from \"./Model3DUpload.svelte\";\n\timport { Block, UploadText } from \"@gradio/atoms\";\n\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: null | FileData = null;\n\texport let root: string;\n\texport let root_url: null | string;\n\texport let clearColor: [number, number, number, number];\n\texport let loading_status: LoadingStatus;\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let gradio: Gradio<{\n\t\tchange: typeof value;\n\t\tclear: never;\n\t}>;\n\n\tlet _value: null | FileData;\n\t$: _value = normalise_file(value, root, root_url);\n\n\tlet dragging = false;\n</script>\n\n<Block\n\t{visible}\n\tvariant={value === null ? \"dashed\" : \"solid\"}\n\tborder_mode={dragging ? \"focus\" : \"base\"}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n>\n\t<StatusTracker {...loading_status} />\n\n\t<Model3DUpload\n\t\t{label}\n\t\t{show_label}\n\t\t{clearColor}\n\t\tvalue={_value}\n\t\ton:change={({ detail }) => (value = detail)}\n\t\ton:drag={({ detail }) => (dragging = detail)}\n\t\ton:change={({ detail }) => gradio.dispatch(\"change\", detail)}\n\t\ton:clear={() => gradio.dispatch(\"clear\")}\n\t>\n\t\t<UploadText type=\"file\" />\n\t</Model3DUpload>\n</Block>\n"], "names": ["ctx", "insert", "target", "div", "anchor", "append", "canvas_1", "File", "dirty", "blocklabel_changes", "value", "$$props", "clearColor", "label", "show_label", "mounted", "onMount", "addNewModel", "$$invalidate", "handle_upload", "detail", "tick", "dispatch", "handle_clear", "scene", "engine", "createEventDispatcher", "dragging", "BABYLON_LOADERS.OBJFileLoader", "canvas", "BABYLON.Engine", "BABYLON.Scene", "BABYLON.Color4", "url", "base64_model_content", "raw_content", "BABYLON.Tools", "blob", "BABYLON.SceneLoader", "$$value", "data", "is_file", "name", "block_changes", "elem_id", "elem_classes", "visible", "root", "root_url", "loading_status", "container", "scale", "min_width", "gradio", "_value", "change_handler_1", "normalise_file"], "mappings": "gtBAyH0BA,EAAY,CAAA,CAAA,qIADrCC,EAGKC,EAAAC,EAAAC,CAAA,qBADJC,EAA4BF,EAAAG,CAAA,yVANZN,EAAa,CAAA,CAAA,0iBAHAO,GAAa,MAAAP,MAAS,mDAEhD,OAAAA,OAAU,KAAI,2KAFyBQ,EAAA,IAAAC,EAAA,MAAAT,MAAS,6UA1GzC,MAAAU,CAAsB,EAAAC,EACtB,CAAA,WAAAC,GAAgD,EAAG,EAAG,EAAG,CAAC,CAAA,EAAAD,EAC1D,CAAA,MAAAE,EAAQ,EAAE,EAAAF,GACV,WAAAG,CAAmB,EAAAH,EAE1BI,EAAU,GAEdC,EAAO,IAAA,CACFN,GAAS,MACZO,IAEDC,EAAA,EAAAH,EAAU,EAAI,IAWA,eAAAI,GACd,OAAAC,GAAM,CAENF,EAAA,EAAAR,EAAQU,CAAM,QACRC,EAAI,EACVC,EAAS,SAAUZ,CAAK,EACxBO,mBAGcM,GAAY,CACtBC,GAASC,IACZD,EAAM,QAAO,EACbC,EAAO,QAAO,GAEfP,EAAA,EAAAR,EAAQ,IAAI,QACNW,EAAI,EACVC,EAAS,OAAO,EAGX,MAAAA,EAAWI,IAMb,IAAAC,EAAW,GAKfC,GAA6B,cAAC,qBAAuB,OAEjDC,EACAL,EACAC,WAEKR,GAAW,IACfO,GAAU,CAAAA,EAAM,YAAcC,IACjCD,EAAM,QAAO,EACbC,EAAO,QAAO,GAGfA,MAAaK,EAAAA,OAAeD,EAAQ,EAAI,EACxCL,EAAY,IAAAO,QAAcN,CAAM,EAChCD,EAAM,2BAA0B,EAChCA,EAAM,WAAaA,EAAM,WAAU,IAAOQ,EAAc,OAAA,GAAIpB,CAAU,EAEtEa,EAAO,cAAa,IAAA,CACnBD,EAAM,OAAM,IAGb,OAAO,iBAAiB,SAAQ,IAAA,CAC/BC,EAAO,OAAM,KAGTf,EAAK,WAENuB,EACA,GAAAvB,EAAM,QACTuB,EAAMvB,EAAM,cAERwB,EAAuBxB,EAAM,KAC7ByB,EAAcC,EAAAA,MAAc,aAAaF,CAAoB,EAC7DG,EAAI,IAAO,KAAI,CAAEF,CAAW,CAAA,EAChCF,EAAM,IAAI,gBAAgBI,CAAI,EAG/BC,EAAmB,YAAC,kBAAoB,GACxCA,EAAAA,YAAoB,OACnBL,EACA,GACAT,OAECA,EAAM,oBAAoB,GAAM,GAAM,EAAI,GAE3C,OACA,OACA,IAAMd,EAAM,KAAK,MAAM,GAAG,EAAE,CAAC,qEAgBXmB,EAAMU,mOArGzBrB,KAAK,CAAA,KAAAsB,EAAM,QAAAC,EAAS,KAAAC,CAAI,EAAKhC,GAAK,CAClC,KAAM,OACN,QAAS,OACT,KAAM,4CAGJmB,GAAUd,GAAWyB,GAAQ,MAAQC,GAAWxB,EAAW,gBAmF3DK,EAAS,OAAQK,CAAQ,8WChET3B,EAAc,CAAA,CAAA,iJAMzBA,EAAM,EAAA,oPANKA,EAAc,CAAA,CAAA,CAAA,CAAA,qHAMzBA,EAAM,EAAA,qPAfL,QAAAA,EAAU,CAAA,IAAA,KAAO,SAAW,oBACxBA,EAAQ,EAAA,EAAG,QAAU,eACzB,6MAFAQ,EAAA,IAAAmC,EAAA,QAAA3C,EAAU,CAAA,IAAA,KAAO,SAAW,gCACxBA,EAAQ,EAAA,EAAG,QAAU,yRA3BvB,GAAA,CAAA,QAAA4C,EAAU,EAAE,EAAAjC,GACZ,aAAAkC,EAAY,EAAA,EAAAlC,EACZ,CAAA,QAAAmC,EAAU,EAAI,EAAAnC,EACd,CAAA,MAAAD,EAAyB,IAAI,EAAAC,GAC7B,KAAAoC,CAAY,EAAApC,GACZ,SAAAqC,CAAuB,EAAArC,GACvB,WAAAC,CAA4C,EAAAD,GAC5C,eAAAsC,CAA6B,EAAAtC,GAC7B,MAAAE,CAAa,EAAAF,GACb,WAAAG,CAAmB,EAAAH,EACnB,CAAA,UAAAuC,EAAY,EAAI,EAAAvC,EAChB,CAAA,MAAAwC,EAAuB,IAAI,EAAAxC,EAC3B,CAAA,UAAAyC,EAAgC,MAAS,EAAAzC,GACzC,OAAA0C,CAGT,EAAA1C,EAEE2C,EAGA3B,EAAW,aAqBA,OAAAP,CAAM,IAAAF,EAAA,EAAQR,EAAQU,CAAM,MAC9B,OAAAA,CAAM,IAAAF,EAAA,GAAQS,EAAWP,CAAM,EAC7BmC,EAAA,CAAA,CAAA,OAAAnC,KAAaiC,EAAO,SAAS,SAAUjC,CAAM,QAC3CiC,EAAO,SAAS,OAAO,uhBA1BvCnC,EAAA,GAAEoC,EAASE,GAAe9C,EAAOqC,EAAMC,CAAQ,CAAA"}