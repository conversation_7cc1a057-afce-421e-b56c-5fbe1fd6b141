{"version": 3, "file": "Empty-23f73391.js", "sources": ["../../../../js/atoms/src/Empty.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let size: \"small\" | \"large\" = \"small\";\n\texport let unpadded_box = false;\n\n\tlet el: HTMLDivElement;\n\t$: parent_height = compare_el_to_parent(el);\n\n\tfunction compare_el_to_parent(el: HTMLDivElement): boolean {\n\t\tif (!el) return false;\n\n\t\tconst { height: el_height } = el.getBoundingClientRect();\n\t\tconst { height: parent_height } =\n\t\t\tel.parentElement?.getBoundingClientRect() || { height: el_height };\n\n\t\treturn el_height > parent_height + 2;\n\t}\n</script>\n\n<div\n\tclass=\"empty\"\n\tclass:small={size === \"small\"}\n\tclass:large={size === \"large\"}\n\tclass:unpadded_box\n\tbind:this={el}\n\tclass:small_parent={parent_height}\n>\n\t<div class=\"icon\">\n\t\t<slot />\n\t</div>\n</div>\n\n<style>\n\t.empty {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmargin-top: calc(0px - var(--size-6));\n\t\theight: var(--size-full);\n\t}\n\n\t.icon {\n\t\topacity: 0.5;\n\t\theight: var(--size-5);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.small {\n\t\tmin-height: calc(var(--size-32) - 20px);\n\t}\n\n\t.large {\n\t\tmin-height: calc(var(--size-64) - 20px);\n\t}\n\n\t.unpadded_box {\n\t\tmargin-top: 0;\n\t}\n\n\t/* .unpadded_box.small {\n\t\tmin-height: var(--size-32);\n\t}\n\n\t.unpadded_box.large {\n\t\tmin-height: var(--size-64);\n\t} */\n\n\t.small_parent {\n\t\tmin-height: 100% !important;\n\t}\n</style>\n"], "names": ["toggle_class", "div1", "ctx", "insert", "target", "anchor", "append", "div0", "size", "$$props", "unpadded_box", "el", "compare_el_to_parent", "el_height", "parent_height", "$$value", "$$invalidate"], "mappings": "4VAoBcA,EAAAC,EAAA,QAAAC,OAAS,OAAO,EAChBF,EAAAC,EAAA,QAAAC,OAAS,OAAO,8CAGTA,EAAa,CAAA,CAAA,UANlCC,EAWKC,EAAAH,EAAAI,CAAA,EAHJC,EAEKL,EAAAM,CAAA,qHARQP,EAAAC,EAAA,QAAAC,OAAS,OAAO,aAChBF,EAAAC,EAAA,QAAAC,OAAS,OAAO,oEAGTA,EAAa,CAAA,CAAA,mIAvBtB,CAAA,KAAAM,EAA0B,OAAO,EAAAC,EACjC,CAAA,aAAAC,EAAe,EAAK,EAAAD,EAE3BE,EAGK,SAAAC,EAAqBD,EAAkB,CAC1C,GAAA,CAAAA,QAAW,GAER,KAAA,CAAA,OAAQE,CAAc,EAAAF,EAAG,sBAAqB,GAC9C,OAAQG,CAAa,EAC5BH,EAAG,eAAe,yBAAqB,CAAQ,OAAQE,UAEjDA,EAAYC,EAAgB,2CASzBH,EAAEI,kKAlBVC,EAAA,EAAAF,EAAgBF,EAAqBD,CAAE,CAAA"}