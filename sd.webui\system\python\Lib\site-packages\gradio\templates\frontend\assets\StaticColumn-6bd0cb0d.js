import{S as w,e as b,s as S,a8 as C,m as j,g as c,aq as d,N as f,K as o,h as q,aa as r,ab as k,ac as K,w as N,u as z,k as A}from"./index-2519a27e.js";function B(t){let e,_,m=`calc(min(${t[2]}px, 100%))`,i;const u=t[8].default,n=C(u,t,t[7],null);return{c(){e=j("div"),n&&n.c(),c(e,"id",t[3]),c(e,"class",_=d(t[4].join(" "))+" svelte-vt1mxs"),f(e,"gap",t[1]),f(e,"compact",t[6]==="compact"),f(e,"panel",t[6]==="panel"),f(e,"hide",!t[5]),o(e,"flex-grow",t[0]),o(e,"min-width",m)},m(l,s){q(l,e,s),n&&n.m(e,null),i=!0},p(l,[s]){n&&n.p&&(!i||s&128)&&r(n,u,l,l[7],i?K(u,l[7],s,null):k(l[7]),null),(!i||s&8)&&c(e,"id",l[3]),(!i||s&16&&_!==(_=d(l[4].join(" "))+" svelte-vt1mxs"))&&c(e,"class",_),(!i||s&18)&&f(e,"gap",l[1]),(!i||s&80)&&f(e,"compact",l[6]==="compact"),(!i||s&80)&&f(e,"panel",l[6]==="panel"),(!i||s&48)&&f(e,"hide",!l[5]),s&1&&o(e,"flex-grow",l[0]),s&4&&m!==(m=`calc(min(${l[2]}px, 100%))`)&&o(e,"min-width",m)},i(l){i||(N(n,l),i=!0)},o(l){z(n,l),i=!1},d(l){l&&A(e),n&&n.d(l)}}}function D(t,e,_){let{$$slots:m={},$$scope:i}=e,{scale:u=null}=e,{gap:n=!0}=e,{min_width:l=0}=e,{elem_id:s=""}=e,{elem_classes:g=[]}=e,{visible:h=!0}=e,{variant:v="default"}=e;return t.$$set=a=>{"scale"in a&&_(0,u=a.scale),"gap"in a&&_(1,n=a.gap),"min_width"in a&&_(2,l=a.min_width),"elem_id"in a&&_(3,s=a.elem_id),"elem_classes"in a&&_(4,g=a.elem_classes),"visible"in a&&_(5,h=a.visible),"variant"in a&&_(6,v=a.variant),"$$scope"in a&&_(7,i=a.$$scope)},[u,n,l,s,g,h,v,i,m]}class F extends w{constructor(e){super(),b(this,e,D,B,S,{scale:0,gap:1,min_width:2,elem_id:3,elem_classes:4,visible:5,variant:6})}}export{F as S};
//# sourceMappingURL=StaticColumn-6bd0cb0d.js.map
