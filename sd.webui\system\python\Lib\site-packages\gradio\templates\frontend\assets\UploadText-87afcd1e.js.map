{"version": 3, "file": "UploadText-87afcd1e.js", "sources": ["../../../../js/atoms/src/UploadText.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { _ } from \"svelte-i18n\";\n\n\texport let type: \"video\" | \"image\" | \"audio\" | \"file\" | \"csv\" = \"file\";\n\n\tconst defs = {\n\t\timage: \"upload_text.drop_image\",\n\t\tvideo: \"upload_text.drop_video\",\n\t\taudio: \"upload_text.drop_audio\",\n\t\tfile: \"upload_text.drop_file\",\n\t\tcsv: \"upload_text.drop_csv\"\n\t};\n</script>\n\n<div class=\"wrap\">\n\t{$_(defs[type])}\n\t<span class=\"or\">- {$_(\"common.or\")} -</span>\n\t{$_(\"upload_text.click_to_upload\")}\n</div>\n\n<style>\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\tmin-height: var(--size-60);\n\t\tcolor: var(--block-label-text-color);\n\t\tline-height: var(--line-md);\n\t}\n\n\t.or {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t@media (--screen-md) {\n\t\t.wrap {\n\t\t\tfont-size: var(--text-lg);\n\t\t}\n\t}\n</style>\n"], "names": ["ctx", "t3_value", "t6_value", "insert", "target", "div", "anchor", "append", "span", "set_data", "t0", "t0_value", "dirty", "t3", "t6", "type", "$$props", "defs"], "mappings": "wLAeEA,EAAE,CAAA,EAACA,EAAI,CAAA,EAACA,EAAI,CAAA,CAAA,CAAA,EAAA,WACOC,EAAAD,KAAG,WAAW,EAAA,SACjCE,EAAAF,KAAG,6BAA6B,EAAA,wDADhB,IAAE,aAAiB,IAAE,2FAFvCG,EAIKC,EAAAC,EAAAC,CAAA,gBAFJC,EAA4CF,EAAAG,CAAA,2DAD3CR,EAAE,CAAA,EAACA,EAAI,CAAA,EAACA,EAAI,CAAA,CAAA,CAAA,EAAA,KAAAS,EAAAC,EAAAC,CAAA,EACOC,EAAA,GAAAX,KAAAA,EAAAD,KAAG,WAAW,EAAA,KAAAS,EAAAI,EAAAZ,CAAA,EACjCW,EAAA,GAAAV,KAAAA,EAAAF,KAAG,6BAA6B,EAAA,KAAAS,EAAAK,EAAAZ,CAAA,qEAdtB,GAAA,CAAA,KAAAa,EAAqD,MAAM,EAAAC,QAEhEC,EAAI,CACT,MAAO,yBACP,MAAO,yBACP,MAAO,yBACP,KAAM,wBACN,IAAK"}