spandrel-0.3.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
spandrel-0.3.4.dist-info/METADATA,sha256=kM27A0FgKpi8gLQF2yzFeebPw8dOpfChZQTwo8B9N2s,14329
spandrel-0.3.4.dist-info/RECORD,,
spandrel-0.3.4.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel-0.3.4.dist-info/WHEEL,sha256=GJ7t_kWBFywbagK5eo9IoUwLW6oyOeTKmQ-9iHFVNxQ,92
spandrel-0.3.4.dist-info/top_level.txt,sha256=bNYky20zJCBLFqnR9qneKKk45heuhs9rCRGOrt3CFas,9
spandrel/__helpers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/__helpers/__pycache__/__init__.cpython-310.pyc,,
spandrel/__helpers/__pycache__/canonicalize.cpython-310.pyc,,
spandrel/__helpers/__pycache__/loader.cpython-310.pyc,,
spandrel/__helpers/__pycache__/main_registry.cpython-310.pyc,,
spandrel/__helpers/__pycache__/model_descriptor.cpython-310.pyc,,
spandrel/__helpers/__pycache__/registry.cpython-310.pyc,,
spandrel/__helpers/__pycache__/size_req.cpython-310.pyc,,
spandrel/__helpers/__pycache__/unpickler.cpython-310.pyc,,
spandrel/__helpers/canonicalize.py,sha256=CxYNBAK-9l0Xpapky652Ii7VwcYt8LoEVeWsb83b9ow,1421
spandrel/__helpers/loader.py,sha256=UPwpmrjtk2SsKxvLEddI-ZWSZwcIuQHgt2xgNfSu9cc,3801
spandrel/__helpers/main_registry.py,sha256=cbMEZPC4aDvAAdfJrrKU4RvcqBdlxJJKkyfefBxfuGU,2623
spandrel/__helpers/model_descriptor.py,sha256=GXobonsKX-Sv9YWqEaPsojmnc-GbBwbOJUiQHziy2Kg,19540
spandrel/__helpers/registry.py,sha256=mf-VFANwRYl0CYR5j_jCLA_NSjgfI5BfkPBPXo7liSA,5853
spandrel/__helpers/size_req.py,sha256=wd027UPsOuJ2pVVjXjh85mIzLTYC9bH8EmhRLq1JHVM,3026
spandrel/__helpers/unpickler.py,sha256=YA12ATt4jmjK_K3_v7g_cjU9X5Bi9SINEyvQYMgFcBs,976
spandrel/__init__.py,sha256=n7dY-W6wiD4D9UEDp3YJf7ux_EsDX1cZkThIlQvsaI8,1136
spandrel/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/ATD/__init__.py,sha256=QaAlIce1PY6yOrK4hPQ-q-wGCltInK5RErz5U7_QN9k,6537
spandrel/architectures/ATD/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/ATD/arch/__pycache__/atd_arch.cpython-310.pyc,,
spandrel/architectures/ATD/arch/atd_arch.py,sha256=TFlw8HrTqpeaKnsJud6gq98pXVB21gSG0O_hSfdcXnk,41329
spandrel/architectures/CRAFT/__init__.py,sha256=fgLgHrnNhRnbu7sM5Gu8Ou0h8HBSn6FHc_TBD-shuIs,4084
spandrel/architectures/CRAFT/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/CRAFT/arch/CRAFT.py,sha256=VQp57kOCkWyExHGFPOYjtkARfmRpfIqAEfRNLdxmrbQ,30815
spandrel/architectures/CRAFT/arch/__pycache__/CRAFT.cpython-310.pyc,,
spandrel/architectures/Compact/__init__.py,sha256=RzP7yCP9oHoXqcL5vQVnGbcXeV2Z2wRPHIQKGOE8fRw,1635
spandrel/architectures/Compact/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/Compact/arch/SRVGG.py,sha256=UrulTEGfzj4gdlGkXjCGyuEn7NrAb7fdnUNGyDjoK5s,2835
spandrel/architectures/Compact/arch/__pycache__/SRVGG.cpython-310.pyc,,
spandrel/architectures/DAT/__init__.py,sha256=2rHSoyA32-T0EEUtAAyK7l5HcYyAMB0ttppsCp2H-io,6629
spandrel/architectures/DAT/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/DAT/arch/DAT.py,sha256=2jW3OXQCQIea2erx6_SmUyBq6B5XgwqWcA9WiN2Wo9w,36845
spandrel/architectures/DAT/arch/__pycache__/DAT.cpython-310.pyc,,
spandrel/architectures/DCTLSA/__init__.py,sha256=6-fkmpMrJ3fErp4dyxk6hLsNhbxaoG5TNJOx9yfypuE,2773
spandrel/architectures/DCTLSA/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/DCTLSA/arch/__pycache__/dctlsa.cpython-310.pyc,,
spandrel/architectures/DCTLSA/arch/dctlsa.py,sha256=ysQDE4CW1ip97gOmxSMoiRC9Hx9mulab27A8Q0P_UQ8,15247
spandrel/architectures/DITN/__init__.py,sha256=1W1wCxdbrrfkC3y1FHaVyJz3SyfUUYxraF_p4qpq90g,2660
spandrel/architectures/DITN/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/DITN/arch/DITN_Real.py,sha256=-MfzAgI8eKfo6vGuZ_l5RT-yi85QWk4-e5cXB0YfFi4,9484
spandrel/architectures/DITN/arch/__pycache__/DITN_Real.cpython-310.pyc,,
spandrel/architectures/DRCT/__init__.py,sha256=anz2bU4bqdg7eYmK7CtydIj9TI3QHBKjQeZYBQMbjfM,5144
spandrel/architectures/DRCT/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/DRCT/arch/__pycache__/drct_arch.cpython-310.pyc,,
spandrel/architectures/DRCT/arch/drct_arch.py,sha256=yxEzUIfuCHfyQKQhEhqX3kLaSyO6jH9Tl0EoQm7b4lQ,28533
spandrel/architectures/DRUNet/__init__.py,sha256=8nm3A4ic3wnVBqoFzGHne2Wl0xjfV3MmT-xlDYYIhbE,3163
spandrel/architectures/DRUNet/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/DRUNet/arch/__pycache__/network_unet.cpython-310.pyc,,
spandrel/architectures/DRUNet/arch/network_unet.py,sha256=mD-0dFWB-KFn0DgZU_YuhDoMDZvBdS0ArLcrtlWvPu0,3630
spandrel/architectures/DnCNN/__init__.py,sha256=2SBr0NV1Tftf8A506tV5HjoeTBt_yRWCbCcRQlwJDYA,3849
spandrel/architectures/DnCNN/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/DnCNN/arch/__pycache__/network_dncnn.cpython-310.pyc,,
spandrel/architectures/DnCNN/arch/network_dncnn.py,sha256=8wUp_D0ZwK8NRHTDXl3cztd-fttNEbb4dBm2NIPSBhg,2889
spandrel/architectures/ESRGAN/__init__.py,sha256=7HL37LKKzo6JY1HTD3700rbfudLCNUlsDqK0QjjxuTM,7553
spandrel/architectures/ESRGAN/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/ESRGAN/arch/RRDB.py,sha256=MND164-1Q3MZZ3dE_jdI_xLX_BcxhkEpUMMjrl4qR-w,4456
spandrel/architectures/ESRGAN/arch/__pycache__/RRDB.cpython-310.pyc,,
spandrel/architectures/FBCNN/__init__.py,sha256=M-mnbRYkIi8Hld0XmF0n-94cuU_I7QQzslrvCK7t7rs,2578
spandrel/architectures/FBCNN/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/FBCNN/arch/FBCNN.py,sha256=ExcrHyCLQGYR74xY-9ZQ7qG78iRioCnL-EuEFy3G_lk,15971
spandrel/architectures/FBCNN/arch/__pycache__/FBCNN.cpython-310.pyc,,
spandrel/architectures/FFTformer/__init__.py,sha256=LM_BYPpQKArtN4tJna-bB3IZWaOpwrS_aD5etYBXIrw,3587
spandrel/architectures/FFTformer/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/FFTformer/arch/__pycache__/fftformer_arch.cpython-310.pyc,,
spandrel/architectures/FFTformer/arch/fftformer_arch.py,sha256=GyQZe-cBZz0K9UDnySbiyw9owd2Xm-v7E04oYK9Z1Sw,11840
spandrel/architectures/GFPGAN/__init__.py,sha256=xDVK1r_dbzqTeJNsrB7aobr8tkcBExfWZzueEqEF9hk,1773
spandrel/architectures/GFPGAN/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/GFPGAN/arch/__pycache__/arcface_arch.cpython-310.pyc,,
spandrel/architectures/GFPGAN/arch/__pycache__/fused_act.cpython-310.pyc,,
spandrel/architectures/GFPGAN/arch/__pycache__/gfpgan_bilinear_arch.cpython-310.pyc,,
spandrel/architectures/GFPGAN/arch/__pycache__/gfpganv1_arch.cpython-310.pyc,,
spandrel/architectures/GFPGAN/arch/__pycache__/gfpganv1_clean_arch.cpython-310.pyc,,
spandrel/architectures/GFPGAN/arch/__pycache__/stylegan2_arch.cpython-310.pyc,,
spandrel/architectures/GFPGAN/arch/__pycache__/stylegan2_bilinear_arch.cpython-310.pyc,,
spandrel/architectures/GFPGAN/arch/__pycache__/stylegan2_clean_arch.cpython-310.pyc,,
spandrel/architectures/GFPGAN/arch/__pycache__/upfirdn2d.cpython-310.pyc,,
spandrel/architectures/GFPGAN/arch/arcface_arch.py,sha256=1AwLakZlHBTcsk60RgZ42M5NY2P9_O5LShKyUp5Jj2c,8166
spandrel/architectures/GFPGAN/arch/fused_act.py,sha256=m1VAq1kWxAEXAjeW-debjB7NdI2dbRh73Dk3OYkl0Bk,2299
spandrel/architectures/GFPGAN/arch/gfpgan_bilinear_arch.py,sha256=btGvnRcmA4EPqsm3xhkkSI2o9IwWL17lrMUy1CQODJM,14296
spandrel/architectures/GFPGAN/arch/gfpganv1_arch.py,sha256=b_JRK2W7yMeB9IrBMEsVrdDaplrD-A_voC0YtQk_qCo,19614
spandrel/architectures/GFPGAN/arch/gfpganv1_clean_arch.py,sha256=78_oSp-tWNmItg8DQjXXbvDIqlcircah19kBW-xlPq4,13677
spandrel/architectures/GFPGAN/arch/stylegan2_arch.py,sha256=WLKPj3wMCsE-ZqrDCZQgiID9-mnZ9T-UTNovmIPbEXo,28263
spandrel/architectures/GFPGAN/arch/stylegan2_bilinear_arch.py,sha256=VVztMx2F7RQf_nLt4lnPwJjjmuqgOggB8kiqRh2cyVY,23081
spandrel/architectures/GFPGAN/arch/stylegan2_clean_arch.py,sha256=zgy_JYh_b85HDqkRJKdkIGZE6f_78B9O-exGA2wyGHk,16045
spandrel/architectures/GFPGAN/arch/upfirdn2d.py,sha256=OIs2L8svz8szh7wrjxN_KgRU-0VChvXGWoBNqJONzs0,5589
spandrel/architectures/GRL/__init__.py,sha256=h8Xubf2mhi96zCs-85p54lSFw83sXHDXeNeAquqMw18,13385
spandrel/architectures/GRL/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/GRL/arch/__pycache__/config.cpython-310.pyc,,
spandrel/architectures/GRL/arch/__pycache__/grl.cpython-310.pyc,,
spandrel/architectures/GRL/arch/__pycache__/mixed_attn_block.cpython-310.pyc,,
spandrel/architectures/GRL/arch/__pycache__/mixed_attn_block_efficient.cpython-310.pyc,,
spandrel/architectures/GRL/arch/__pycache__/ops.cpython-310.pyc,,
spandrel/architectures/GRL/arch/__pycache__/swin_v1_block.cpython-310.pyc,,
spandrel/architectures/GRL/arch/__pycache__/upsample.cpython-310.pyc,,
spandrel/architectures/GRL/arch/config.py,sha256=YiMgmGspwWj4_EY9rgU4m6GMWky9Ec12jP8j3i2kKOk,1225
spandrel/architectures/GRL/arch/grl.py,sha256=w55_XLI1p9EDHXmrY7j4eqd2LMvjachLVSomu-Usics,25424
spandrel/architectures/GRL/arch/mixed_attn_block.py,sha256=hyOQk5Lx_rwxQsjBypBng80Nrq2Kv5ECemWo2D9CiUA,7043
spandrel/architectures/GRL/arch/mixed_attn_block_efficient.py,sha256=j-NCLxWdaAaml4R58um8TQR4MlsUdiFldoyE_YygntM,20088
spandrel/architectures/GRL/arch/ops.py,sha256=pPIPQZASlQj8DaDOEc2C4hZ0nEW1MZQzgEHmKdxL77Y,8423
spandrel/architectures/GRL/arch/swin_v1_block.py,sha256=Yk4eWtddA9PHFiSe2A9jTNefte5SkPAObEObjP083ro,2021
spandrel/architectures/GRL/arch/upsample.py,sha256=2N11fI6-_x_btrB7vm9t4IYc0y8misZFxXB5js6wlF4,1575
spandrel/architectures/HAT/__init__.py,sha256=mLweW3F6g_OeJRkkv0mqAev8uv1rMOZLSUm8I1UNrp4,8179
spandrel/architectures/HAT/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/HAT/arch/HAT.py,sha256=CKl7bcsPyXUbhiOaJouXnZCcnLw8-3gXbyo7B5IumKs,39467
spandrel/architectures/HAT/arch/__pycache__/HAT.cpython-310.pyc,,
spandrel/architectures/IPT/__init__.py,sha256=sthm-JBb_XpKLenCQ_8ltet4JH8d7tSdLogEo5zpbAQ,5362
spandrel/architectures/IPT/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/IPT/arch/__pycache__/common.cpython-310.pyc,,
spandrel/architectures/IPT/arch/__pycache__/ipt.cpython-310.pyc,,
spandrel/architectures/IPT/arch/__pycache__/model.cpython-310.pyc,,
spandrel/architectures/IPT/arch/common.py,sha256=SoNAFE7LcUYG2aspWmyy9lUE3ETL1-Lhfm9HOcBXs_Q,2645
spandrel/architectures/IPT/arch/ipt.py,sha256=iUfcDaguz-CntKy-XjwoTzmmYc1jZNc32M749Wp_LH0,11879
spandrel/architectures/IPT/arch/model.py,sha256=AxwIG_hXTe7qqPvwGvsq8BOsL92MWWzepMihx68au0A,9255
spandrel/architectures/KBNet/__init__.py,sha256=jOikiXK84-r7zkYrrFrjFp5rSu2wAN7hGQTrl0eQDzM,5521
spandrel/architectures/KBNet/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/KBNet/arch/__pycache__/kb_utils.cpython-310.pyc,,
spandrel/architectures/KBNet/arch/__pycache__/kbnet_l.cpython-310.pyc,,
spandrel/architectures/KBNet/arch/__pycache__/kbnet_s.cpython-310.pyc,,
spandrel/architectures/KBNet/arch/kb_utils.py,sha256=cKU-a0Qn7dW5hHsGhCvQxUaEvfqipyTjmcohej1T9LY,4410
spandrel/architectures/KBNet/arch/kbnet_l.py,sha256=as1yCID7oFrHV8ti-uFdfZevHATIJMacrhEWNa450nE,11639
spandrel/architectures/KBNet/arch/kbnet_s.py,sha256=KTbMmuNAADilTAIDMu3slCTBrUUl89kkEla7tBBDJn0,8357
spandrel/architectures/LaMa/__init__.py,sha256=2WkYgZQtlZ4q1-QSsB1rDx3CNhEXVHyjsxcYuQdFJ3U,1484
spandrel/architectures/LaMa/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/LaMa/arch/LaMa.py,sha256=Krf94KRxANehSDizijPX8vXsq-dPwqnzElpyMx_Av3s,20902
spandrel/architectures/LaMa/arch/__pycache__/LaMa.cpython-310.pyc,,
spandrel/architectures/MMRealSR/__init__.py,sha256=xydrckxw2DnUq3ssKtebi5hUXYkid9zY0yxAzpgpq4c,6225
spandrel/architectures/MMRealSR/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/MMRealSR/arch/__pycache__/mmrealsr_arch.cpython-310.pyc,,
spandrel/architectures/MMRealSR/arch/mmrealsr_arch.py,sha256=fY3ungLFsNwGqJEG0FTKh6IB_5fIVugUujQf0i-ml5k,23377
spandrel/architectures/MixDehazeNet/__init__.py,sha256=Ywrfot9iNFYkS74b6mJ0zwg44URt6mQrJFw1Yxy_NUQ,4239
spandrel/architectures/MixDehazeNet/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/MixDehazeNet/arch/MixDehazeNet.py,sha256=0wXA0nKmGjRbxPjglYCDd0XwK-9eYLtAojIvP8IBNv4,8445
spandrel/architectures/MixDehazeNet/arch/__pycache__/MixDehazeNet.cpython-310.pyc,,
spandrel/architectures/NAFNet/__init__.py,sha256=u0z6YreuJx-v0BJ9SdeUAqPKnV8okgI87Xw33dfX31o,2489
spandrel/architectures/NAFNet/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/NAFNet/arch/NAFNet_arch.py,sha256=VzXBqWXIA7_xS534B96PfZf8tZZeyxfUM1F85CBy40c,5984
spandrel/architectures/NAFNet/arch/__pycache__/NAFNet_arch.cpython-310.pyc,,
spandrel/architectures/NAFNet/arch/__pycache__/arch_util.cpython-310.pyc,,
spandrel/architectures/NAFNet/arch/arch_util.py,sha256=uqck1vLPrEuuSVpA0v-wy54S_zFWMP3i1awCkgWZl2Q,1477
spandrel/architectures/OmniSR/__init__.py,sha256=a73WJEYyB7ApgAqShJC47g2MWN1pajvL5PvosdNgLJk,2916
spandrel/architectures/OmniSR/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/OmniSR/arch/ChannelAttention.py,sha256=DXvuY-ahJ58ty52CAXux8ctvk2DhcXbnbwdzevnGSso,3106
spandrel/architectures/OmniSR/arch/OSA.py,sha256=YzjtWUQFrs1rHsco_dKymhK24nvE-b8GZj-Wckw3vsc,15054
spandrel/architectures/OmniSR/arch/OSAG.py,sha256=s8SFztqA3dFyw7V-iXJuiNjvzgNZ-VVpLqdNIVhjD60,1714
spandrel/architectures/OmniSR/arch/OmniSR.py,sha256=WcICMPe1AtrEaw7fw0AcGnZFECO8o0xO4WYgEn6zJVo,2666
spandrel/architectures/OmniSR/arch/__pycache__/ChannelAttention.cpython-310.pyc,,
spandrel/architectures/OmniSR/arch/__pycache__/OSA.cpython-310.pyc,,
spandrel/architectures/OmniSR/arch/__pycache__/OSAG.cpython-310.pyc,,
spandrel/architectures/OmniSR/arch/__pycache__/OmniSR.cpython-310.pyc,,
spandrel/architectures/OmniSR/arch/__pycache__/esa.cpython-310.pyc,,
spandrel/architectures/OmniSR/arch/__pycache__/layernorm.cpython-310.pyc,,
spandrel/architectures/OmniSR/arch/__pycache__/pixelshuffle.cpython-310.pyc,,
spandrel/architectures/OmniSR/arch/esa.py,sha256=zzEIFOzQ9S7E_D1XiSmk0sLJSOYi0WHTUlOc0RdDqEc,8315
spandrel/architectures/OmniSR/arch/layernorm.py,sha256=bAllwJzSrqHlCXt9cftrXjj0hfuUEjp5izXUphbqWvo,2275
spandrel/architectures/OmniSR/arch/pixelshuffle.py,sha256=jIjiKer0lcfD9flZ65Zy98rLDJkdJiOW9mmpZ7SvA9U,850
spandrel/architectures/PLKSR/__init__.py,sha256=C0QDBqz8LVGz2Mm2Qg8saIJK4LDTM5EhKra0PUqYvwk,3716
spandrel/architectures/PLKSR/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/PLKSR/arch/PLKSR.py,sha256=WoPxTqVK9Mqna9BZ7_NQ-ir0whIMgpqfVn18KiNVTwE,10629
spandrel/architectures/PLKSR/arch/RealPLKSR.py,sha256=nMskqXBnlayKFDIGh7tuQK2y7N1v0OHK0FzLBNuihLk,3639
spandrel/architectures/PLKSR/arch/__pycache__/PLKSR.cpython-310.pyc,,
spandrel/architectures/PLKSR/arch/__pycache__/RealPLKSR.cpython-310.pyc,,
spandrel/architectures/RGT/__init__.py,sha256=rBCg54wNaE7cf7-jLbOKlwE57Gmd5G0gdDSHLrsAVc4,5925
spandrel/architectures/RGT/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/RGT/arch/__pycache__/rgt.cpython-310.pyc,,
spandrel/architectures/RGT/arch/rgt.py,sha256=Y7_BMs7itVCMKZrU-QcRAZ0PUgwNbNGfoxWCwWxEokU,29897
spandrel/architectures/RealCUGAN/__init__.py,sha256=uJVQm35UOFTl9StAAmwRXBP1ozRYPaIvxPhBG2g9OZc,3651
spandrel/architectures/RealCUGAN/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/RealCUGAN/arch/__pycache__/upcunet_v3.cpython-310.pyc,,
spandrel/architectures/RealCUGAN/arch/upcunet_v3.py,sha256=TS3lf2efMaQAtOqdXrHYzJirXX8Fd1xjv9_6kXBbuxw,14795
spandrel/architectures/RestoreFormer/__init__.py,sha256=6DEPQ2RA8fycLxIX6R4taNNG9mWVzJjSqeacB5e_vK8,3410
spandrel/architectures/RestoreFormer/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/RestoreFormer/arch/__pycache__/restoreformer_arch.cpython-310.pyc,,
spandrel/architectures/RestoreFormer/arch/restoreformer_arch.py,sha256=ZlvrLgGF_eBRrRiWBt8Z4SixuDpObQycRdlp9iuQJYE,23884
spandrel/architectures/SAFMN/__init__.py,sha256=-D2_tTavVgjt1Atk9a5qIs2cvkmAZKa-T8oSKS6hyQc,2100
spandrel/architectures/SAFMN/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/SAFMN/arch/__pycache__/safmn.cpython-310.pyc,,
spandrel/architectures/SAFMN/arch/safmn.py,sha256=FDb1DwzA5-zNXHjGhQ27jpHWt-xD0Hud1PTbhFjQgFI,5019
spandrel/architectures/SAFMNBCIE/__init__.py,sha256=mAikogocTj9Iu54Q5e5iOnApeWNCBsIgr52hp6clP-o,2596
spandrel/architectures/SAFMNBCIE/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/SAFMNBCIE/arch/__pycache__/safmn_bcie.cpython-310.pyc,,
spandrel/architectures/SAFMNBCIE/arch/safmn_bcie.py,sha256=x1f8BQ2VurANL3VNbxjAn6MOz_bDsOYRJhqfg7WubjQ,4168
spandrel/architectures/SCUNet/__init__.py,sha256=jdP_kK-NxieKK-Xlaiu1tES3NpLr_vbO-eZ8f2IhC5U,1904
spandrel/architectures/SCUNet/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/SCUNet/arch/SCUNet.py,sha256=WYZAsjw94KH922v7esG3ftSlEMOg6yBsshX8E7KgvqU,13786
spandrel/architectures/SCUNet/arch/__pycache__/SCUNet.cpython-310.pyc,,
spandrel/architectures/SPAN/__init__.py,sha256=3fNjQWKTdFzZvv6zA-hgehjQJJ3Yyj-5PTkxwkVzDFA,2289
spandrel/architectures/SPAN/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/SPAN/arch/__pycache__/span.cpython-310.pyc,,
spandrel/architectures/SPAN/arch/span.py,sha256=m5hXlUak8hoZsxyIz9BzjgQLkwTpMwKM6QOE28DIQuY,9234
spandrel/architectures/SwiftSRGAN/__init__.py,sha256=aJVGCf_eyU8I5ovrZt5dXDtCFmMF9LdUlro2f3KBOXI,1733
spandrel/architectures/SwiftSRGAN/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/SwiftSRGAN/arch/SwiftSRGAN.py,sha256=IOOndnoBIOuynDtyijIN1c_xNVMpDfFAueC8ioaMVbc,4104
spandrel/architectures/SwiftSRGAN/arch/__pycache__/SwiftSRGAN.cpython-310.pyc,,
spandrel/architectures/Swin2SR/__init__.py,sha256=wfWe8ZvjfgXK9ljvapuiiJmKOTM8ZwAc8Ojsres0SI8,6551
spandrel/architectures/Swin2SR/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/Swin2SR/arch/Swin2SR.py,sha256=cWH-jvjJjQQleJ1ziKhB0g2kqYFY2uImyPLGPjILyKw,46536
spandrel/architectures/Swin2SR/arch/__pycache__/Swin2SR.cpython-310.pyc,,
spandrel/architectures/SwinIR/__init__.py,sha256=_cIEt1jh1B0hy_fhahr31LYC49TskNcJBFx7byD4-bc,6067
spandrel/architectures/SwinIR/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/SwinIR/arch/SwinIR.py,sha256=mwfAWgNPkqvYJAxViP0UCGGT1RyvrEnyxy_DvjaN5Sc,38906
spandrel/architectures/SwinIR/arch/__pycache__/SwinIR.cpython-310.pyc,,
spandrel/architectures/Uformer/__init__.py,sha256=dIzS2WHhGkb6aMujkgodVdpLFh1APPH0mPd-ViBVrlw,5727
spandrel/architectures/Uformer/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/Uformer/arch/Uformer.py,sha256=qrM0naYnDnnus_CV0QmP8F8XulBET7n6gyTb3jf57dc,54675
spandrel/architectures/Uformer/arch/__pycache__/Uformer.cpython-310.pyc,,
spandrel/architectures/__arch_helpers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/__arch_helpers/__pycache__/__init__.cpython-310.pyc,,
spandrel/architectures/__arch_helpers/__pycache__/block.cpython-310.pyc,,
spandrel/architectures/__arch_helpers/__pycache__/dpir_basic_block.cpython-310.pyc,,
spandrel/architectures/__arch_helpers/__pycache__/padding.cpython-310.pyc,,
spandrel/architectures/__arch_helpers/block.py,sha256=4NwY4AjNWKlBQovbzKzlEBJMwKZfFoBvK3mu6CoAvo8,13495
spandrel/architectures/__arch_helpers/dpir_basic_block.py,sha256=LG-XW8KLKU1yWElyzn2KGWXc03FfTF68LUPenasznao,10601
spandrel/architectures/__arch_helpers/padding.py,sha256=azwwAAZ1fPaK37IG9TEQDbNcwOZXoa1xeGapV5oUI0k,770
spandrel/architectures/__init__.py,sha256=wfh0afLbLNyLCOeCYGPx6k092dQ-nkmtuckY0CVXIug,117
spandrel/architectures/__pycache__/__init__.cpython-310.pyc,,
spandrel/util/__init__.py,sha256=irRfy2qfHqgpVRWGpQOzVsJym4xOZ88WSvViPdqVu-A,6433
spandrel/util/__pycache__/__init__.cpython-310.pyc,,
spandrel/util/timm/__drop.py,sha256=qslYFOof8yBrjVBAEoSAocWOYDIZYJChEpFvxeqZr18,7368
spandrel/util/timm/__helpers.py,sha256=478WrzOnSDpaad97neragwGczLBIO3C6JfCEkcF1dik,788
spandrel/util/timm/__init__.py,sha256=JIG2mLkdHSaCFYU4ucmjyhy5zI2tuIhHSUheam9TnOc,314
spandrel/util/timm/__pycache__/__drop.cpython-310.pyc,,
spandrel/util/timm/__pycache__/__helpers.cpython-310.pyc,,
spandrel/util/timm/__pycache__/__init__.cpython-310.pyc,,
spandrel/util/timm/__pycache__/__weight_init.cpython-310.pyc,,
spandrel/util/timm/__weight_init.py,sha256=GrJiLolNRvps3py643cLdVB86keFoOT55bA5dkPtITI,5088
