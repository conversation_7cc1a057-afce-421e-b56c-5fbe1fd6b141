import{S as K,e as Q,s as W,f as G,g as _,h as b,j as z,n as T,k,m as p,C as be,at as Y,I as S,o as C,O as q,t as Z,x as A,p as R,B as ke,Q as X,N as M,K as D,F as O,G as V,w as j,u as H,H as E,a0 as ve,a1 as pe,Z as we,ad as je,V as ze,W as ye,r as J,v as P}from"./index-2519a27e.js";import{g as He}from"./color-ba58c3cc.js";import{B as Ne}from"./Button-748313a7.js";import{B as Be}from"./BlockLabel-ddfceeb6.js";import{E as Me}from"./Empty-23f73391.js";function Se(n){let e,t,l;return{c(){e=G("svg"),t=G("path"),l=G("path"),_(t,"fill","currentColor"),_(t,"d","M12 15H5a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5V5a1 1 0 0 0-1-1H3V2h6a3 3 0 0 1 3 3zM5 9a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h5V9zm15 14v2a1 1 0 0 0 1 1h5v-4h-5a1 1 0 0 0-1 1z"),_(l,"fill","currentColor"),_(l,"d","M2 30h28V2Zm26-2h-7a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5v-2a1 1 0 0 0-1-1h-6v-2h6a3 3 0 0 1 3 3Z"),_(e,"xmlns","http://www.w3.org/2000/svg"),_(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),_(e,"aria-hidden","true"),_(e,"role","img"),_(e,"class","iconify iconify--carbon"),_(e,"width","100%"),_(e,"height","100%"),_(e,"preserveAspectRatio","xMidYMid meet"),_(e,"viewBox","0 0 32 32")},m(i,o){b(i,e,o),z(e,t),z(e,l)},p:T,i:T,o:T,d(i){i&&k(e)}}}class ue extends K{constructor(e){super(),Q(this,e,null,Se,W,{})}}function U(n,e,t){const l=n.slice();l[19]=e[t][0],l[28]=e[t][1];const i=typeof l[28]=="string"?parseInt(l[28]):l[28];return l[29]=i,l}function x(n,e,t){const l=n.slice();return l[19]=e[t][0],l[20]=e[t][1],l[22]=t,l}function $(n,e,t){const l=n.slice();return l[23]=e[t],l[25]=t,l}function ee(n,e,t){const l=n.slice();return l[20]=e[t][0],l[26]=e[t][1],l[22]=t,l}function Ce(n){let e,t,l=n[1]&&le(),i=S(n[0]),o=[];for(let s=0;s<i.length;s+=1)o[s]=te(U(n,i,s));return{c(){l&&l.c(),e=C(),t=p("div");for(let s=0;s<o.length;s+=1)o[s].c();_(t,"class","textfield svelte-ju12zg"),_(t,"data-testid","highlighted-text:textfield")},m(s,f){l&&l.m(s,f),b(s,e,f),b(s,t,f);for(let a=0;a<o.length;a+=1)o[a]&&o[a].m(t,null)},p(s,f){if(s[1]?l||(l=le(),l.c(),l.m(e.parentNode,e)):l&&(l.d(1),l=null),f[0]&1){i=S(s[0]);let a;for(a=0;a<i.length;a+=1){const g=U(s,i,a);o[a]?o[a].p(g,f):(o[a]=te(g),o[a].c(),o[a].m(t,null))}for(;a<o.length;a+=1)o[a].d(1);o.length=i.length}},d(s){s&&(k(e),k(t)),l&&l.d(s),q(o,s)}}}function Oe(n){let e,t,l=n[1]&&ne(n),i=S(n[0]),o=[];for(let s=0;s<i.length;s+=1)o[s]=re(x(n,i,s));return{c(){l&&l.c(),e=C(),t=p("div");for(let s=0;s<o.length;s+=1)o[s].c();_(t,"class","textfield svelte-ju12zg")},m(s,f){l&&l.m(s,f),b(s,e,f),b(s,t,f);for(let a=0;a<o.length;a+=1)o[a]&&o[a].m(t,null)},p(s,f){if(s[1]?l?l.p(s,f):(l=ne(s),l.c(),l.m(e.parentNode,e)):l&&(l.d(1),l=null),f[0]&95){i=S(s[0]);let a;for(a=0;a<i.length;a+=1){const g=x(s,i,a);o[a]?o[a].p(g,f):(o[a]=re(g),o[a].c(),o[a].m(t,null))}for(;a<o.length;a+=1)o[a].d(1);o.length=i.length}},d(s){s&&(k(e),k(t)),l&&l.d(s),q(o,s)}}}function le(n){let e;return{c(){e=p("div"),e.innerHTML="<span>-1</span> <span>0</span> <span>+1</span>",_(e,"class","color-legend svelte-ju12zg"),_(e,"data-testid","highlighted-text:color-legend")},m(t,l){b(t,e,l)},d(t){t&&k(e)}}}function te(n){let e,t,l=n[19]+"",i,o,s;return{c(){e=p("span"),t=p("span"),i=Z(l),o=C(),_(t,"class","text svelte-ju12zg"),_(e,"class","textspan score-text svelte-ju12zg"),_(e,"style",s="background-color: rgba("+(n[29]<0?"128, 90, 213,"+-n[29]:"239, 68, 60,"+n[29])+")")},m(f,a){b(f,e,a),z(e,t),z(t,i),z(e,o)},p(f,a){a[0]&1&&l!==(l=f[19]+"")&&A(i,l),a[0]&1&&s!==(s="background-color: rgba("+(f[29]<0?"128, 90, 213,"+-f[29]:"239, 68, 60,"+f[29])+")")&&_(e,"style",s)},d(f){f&&k(e)}}}function ne(n){let e,t=S(Object.entries(n[3])),l=[];for(let i=0;i<t.length;i+=1)l[i]=se(ee(n,t,i));return{c(){e=p("div");for(let i=0;i<l.length;i+=1)l[i].c();_(e,"class","category-legend svelte-ju12zg"),_(e,"data-testid","highlighted-text:category-legend")},m(i,o){b(i,e,o);for(let s=0;s<l.length;s+=1)l[s]&&l[s].m(e,null)},p(i,o){if(o[0]&392){t=S(Object.entries(i[3]));let s;for(s=0;s<t.length;s+=1){const f=ee(i,t,s);l[s]?l[s].p(f,o):(l[s]=se(f),l[s].c(),l[s].m(e,null))}for(;s<l.length;s+=1)l[s].d(1);l.length=t.length}},d(i){i&&k(e),q(l,i)}}}function se(n){let e,t=n[20]+"",l,i,o,s,f;function a(){return n[10](n[20])}function g(){return n[11](n[20])}return{c(){e=p("div"),l=Z(t),i=C(),_(e,"class","category-label svelte-ju12zg"),_(e,"style",o="background-color:"+n[26].secondary)},m(r,h){b(r,e,h),z(e,l),z(e,i),s||(f=[R(e,"mouseover",a),R(e,"focus",g),R(e,"mouseout",n[12]),R(e,"blur",n[13])],s=!0)},p(r,h){n=r,h[0]&8&&t!==(t=n[20]+"")&&A(l,t),h[0]&8&&o!==(o="background-color:"+n[26].secondary)&&_(e,"style",o)},d(r){r&&k(e),s=!1,ke(f)}}}function ie(n){let e,t,l=n[23]+"",i,o,s,f,a=!n[1]&&n[20]!==null&&oe(n);function g(){return n[14](n[22],n[19],n[20])}return{c(){e=p("span"),t=p("span"),i=Z(l),o=C(),a&&a.c(),_(t,"class","text svelte-ju12zg"),M(t,"no-label",!n[3][n[20]]),_(e,"class","textspan svelte-ju12zg"),M(e,"no-cat",n[20]===null||n[4]&&n[4]!==n[20]),M(e,"hl",n[20]!==null),M(e,"selectable",n[2]),D(e,"background-color",n[20]===null||n[4]&&n[4]!==n[20]?"":n[3][n[20]].secondary)},m(r,h){b(r,e,h),z(e,t),z(t,i),z(e,o),a&&a.m(e,null),s||(f=R(e,"click",g),s=!0)},p(r,h){n=r,h[0]&1&&l!==(l=n[23]+"")&&A(i,l),h[0]&9&&M(t,"no-label",!n[3][n[20]]),!n[1]&&n[20]!==null?a?a.p(n,h):(a=oe(n),a.c(),a.m(e,null)):a&&(a.d(1),a=null),h[0]&17&&M(e,"no-cat",n[20]===null||n[4]&&n[4]!==n[20]),h[0]&1&&M(e,"hl",n[20]!==null),h[0]&4&&M(e,"selectable",n[2]),h[0]&25&&D(e,"background-color",n[20]===null||n[4]&&n[4]!==n[20]?"":n[3][n[20]].secondary)},d(r){r&&k(e),a&&a.d(),s=!1,f()}}}function oe(n){let e,t,l=n[20]+"",i;return{c(){e=Z(` 
								`),t=p("span"),i=Z(l),_(t,"class","label svelte-ju12zg"),D(t,"background-color",n[20]===null||n[4]&&n[4]!==n[20]?"":n[3][n[20]].primary)},m(o,s){b(o,e,s),b(o,t,s),z(t,i)},p(o,s){s[0]&1&&l!==(l=o[20]+"")&&A(i,l),s[0]&25&&D(t,"background-color",o[20]===null||o[4]&&o[4]!==o[20]?"":o[3][o[20]].primary)},d(o){o&&(k(e),k(t))}}}function ae(n){let e;return{c(){e=p("br")},m(t,l){b(t,e,l)},d(t){t&&k(e)}}}function fe(n){let e=n[23].trim()!=="",t,l=n[25]<L(n[19]).length-1,i,o=e&&ie(n),s=l&&ae();return{c(){o&&o.c(),t=C(),s&&s.c(),i=X()},m(f,a){o&&o.m(f,a),b(f,t,a),s&&s.m(f,a),b(f,i,a)},p(f,a){a[0]&1&&(e=f[23].trim()!==""),e?o?o.p(f,a):(o=ie(f),o.c(),o.m(t.parentNode,t)):o&&(o.d(1),o=null),a[0]&1&&(l=f[25]<L(f[19]).length-1),l?s||(s=ae(),s.c(),s.m(i.parentNode,i)):s&&(s.d(1),s=null)},d(f){f&&(k(t),k(i)),o&&o.d(f),s&&s.d(f)}}}function re(n){let e,t=S(L(n[19])),l=[];for(let i=0;i<t.length;i+=1)l[i]=fe($(n,t,i));return{c(){for(let i=0;i<l.length;i+=1)l[i].c();e=X()},m(i,o){for(let s=0;s<l.length;s+=1)l[s]&&l[s].m(i,o);b(i,e,o)},p(i,o){if(o[0]&95){t=S(L(i[19]));let s;for(s=0;s<t.length;s+=1){const f=$(i,t,s);l[s]?l[s].p(f,o):(l[s]=fe(f),l[s].c(),l[s].m(e.parentNode,e))}for(;s<l.length;s+=1)l[s].d(1);l.length=t.length}},d(i){i&&k(e),q(l,i)}}}function Ve(n){let e;function t(o,s){return o[5]==="categories"?Oe:Ce}let l=t(n),i=l(n);return{c(){e=p("div"),i.c(),_(e,"class","container svelte-ju12zg")},m(o,s){b(o,e,s),i.m(e,null)},p(o,s){l===(l=t(o))&&i?i.p(o,s):(i.d(1),i=l(o),i&&(i.c(),i.m(e,null)))},i:T,o:T,d(o){o&&k(e),i.d()}}}function L(n){return n.split(`
`)}function Ee(n,e,t){const l=typeof document<"u";let{value:i=[]}=e,{show_legend:o=!1}=e,{color_map:s={}}=e,{selectable:f=!1}=e,a,g={},r="";function h(){for(const m in s){const w=s[m].trim();w in Y?t(3,g[m]=Y[w],g):t(3,g[m]={primary:l?v(s[m],1):s[m],secondary:l?v(s[m],.5):s[m]},g)}}function v(m,w){if(!a){var I=document.createElement("canvas");a=I.getContext("2d")}a.fillStyle=m,a.fillRect(0,0,1,1);const[ge,he,de]=a.getImageData(0,0,1,1).data;return a.clearRect(0,0,1,1),`rgba(${ge}, ${he}, ${de}, ${255/w})`}const N=be();let c;function d(m){t(4,r=m)}function y(){t(4,r="")}const B=m=>d(m),F=m=>d(m),u=()=>y(),_e=()=>y(),me=(m,w,I)=>{N("select",{index:m,value:[w,I]})};return n.$$set=m=>{"value"in m&&t(0,i=m.value),"show_legend"in m&&t(1,o=m.show_legend),"color_map"in m&&t(9,s=m.color_map),"selectable"in m&&t(2,f=m.selectable)},n.$$.update=()=>{if(n.$$.dirty[0]&513){if(s||t(9,s={}),i.length>0){for(let[m,w]of i)if(w!==null)if(typeof w=="string"){if(t(5,c="categories"),!(w in s)){let I=He(Object.keys(s).length);t(9,s[w]=I,s)}}else t(5,c="scores")}h()}},[i,o,f,g,r,c,N,d,y,s,B,F,u,_e,me]}class Ie extends K{constructor(e){super(),Q(this,e,Ee,Ve,W,{value:0,show_legend:1,color_map:9,selectable:2},null,[-1,-1])}}function ce(n){let e,t;return e=new Be({props:{Icon:ue,label:n[6],float:!1,disable:n[7]===!1}}),{c(){O(e.$$.fragment)},m(l,i){V(e,l,i),t=!0},p(l,i){const o={};i&64&&(o.label=l[6]),i&128&&(o.disable=l[7]===!1),e.$set(o)},i(l){t||(j(e.$$.fragment,l),t=!0)},o(l){H(e.$$.fragment,l),t=!1},d(l){E(e,l)}}}function Re(n){let e,t;return e=new Me({props:{$$slots:{default:[Ze]},$$scope:{ctx:n}}}),{c(){O(e.$$.fragment)},m(l,i){V(e,l,i),t=!0},p(l,i){const o={};i&65536&&(o.$$scope={dirty:i,ctx:l}),e.$set(o)},i(l){t||(j(e.$$.fragment,l),t=!0)},o(l){H(e.$$.fragment,l),t=!1},d(l){E(e,l)}}}function Te(n){let e,t;return e=new Ie({props:{selectable:n[10],value:n[4],show_legend:n[5],color_map:n[0]}}),e.$on("select",n[14]),{c(){O(e.$$.fragment)},m(l,i){V(e,l,i),t=!0},p(l,i){const o={};i&1024&&(o.selectable=l[10]),i&16&&(o.value=l[4]),i&32&&(o.show_legend=l[5]),i&1&&(o.color_map=l[0]),e.$set(o)},i(l){t||(j(e.$$.fragment,l),t=!0)},o(l){H(e.$$.fragment,l),t=!1},d(l){E(e,l)}}}function Ze(n){let e,t;return e=new ue({}),{c(){O(e.$$.fragment)},m(l,i){V(e,l,i),t=!0},i(l){t||(j(e.$$.fragment,l),t=!0)},o(l){H(e.$$.fragment,l),t=!1},d(l){E(e,l)}}}function De(n){let e,t,l,i,o,s,f;const a=[n[12]];let g={};for(let c=0;c<a.length;c+=1)g=we(g,a[c]);e=new je({props:g});let r=n[6]&&ce(n);const h=[Te,Re],v=[];function N(c,d){return c[4]?0:1}return i=N(n),o=v[i]=h[i](n),{c(){O(e.$$.fragment),t=C(),r&&r.c(),l=C(),o.c(),s=X()},m(c,d){V(e,c,d),b(c,t,d),r&&r.m(c,d),b(c,l,d),v[i].m(c,d),b(c,s,d),f=!0},p(c,d){const y=d&4096?ze(a,[ye(c[12])]):{};e.$set(y),c[6]?r?(r.p(c,d),d&64&&j(r,1)):(r=ce(c),r.c(),j(r,1),r.m(l.parentNode,l)):r&&(J(),H(r,1,1,()=>{r=null}),P());let B=i;i=N(c),i===B?v[i].p(c,d):(J(),H(v[B],1,1,()=>{v[B]=null}),P(),o=v[i],o?o.p(c,d):(o=v[i]=h[i](c),o.c()),j(o,1),o.m(s.parentNode,s))},i(c){f||(j(e.$$.fragment,c),j(r),j(o),f=!0)},o(c){H(e.$$.fragment,c),H(r),H(o),f=!1},d(c){c&&(k(t),k(l),k(s)),E(e,c),r&&r.d(c),v[i].d(c)}}}function Le(n){let e,t;return e=new Ne({props:{test_id:"highlighted-text",visible:n[3],elem_id:n[1],elem_classes:n[2],padding:!1,container:n[7],scale:n[8],min_width:n[9],$$slots:{default:[De]},$$scope:{ctx:n}}}),{c(){O(e.$$.fragment)},m(l,i){V(e,l,i),t=!0},p(l,[i]){const o={};i&8&&(o.visible=l[3]),i&2&&(o.elem_id=l[1]),i&4&&(o.elem_classes=l[2]),i&128&&(o.container=l[7]),i&256&&(o.scale=l[8]),i&512&&(o.min_width=l[9]),i&72945&&(o.$$scope={dirty:i,ctx:l}),e.$set(o)},i(l){t||(j(e.$$.fragment,l),t=!0)},o(l){H(e.$$.fragment,l),t=!1},d(l){E(e,l)}}}function qe(n,e,t){let l;ve(n,pe,u=>t(15,l=u));let{elem_id:i=""}=e,{elem_classes:o=[]}=e,{visible:s=!0}=e,{value:f}=e,a,{show_legend:g}=e,{color_map:r={}}=e,{label:h=l("highlighted_text.highlighted_text")}=e,{container:v=!0}=e,{scale:N=null}=e,{min_width:c=void 0}=e,{selectable:d=!1}=e,{gradio:y}=e,{loading_status:B}=e;const F=({detail:u})=>y.dispatch("select",u);return n.$$set=u=>{"elem_id"in u&&t(1,i=u.elem_id),"elem_classes"in u&&t(2,o=u.elem_classes),"visible"in u&&t(3,s=u.visible),"value"in u&&t(4,f=u.value),"show_legend"in u&&t(5,g=u.show_legend),"color_map"in u&&t(0,r=u.color_map),"label"in u&&t(6,h=u.label),"container"in u&&t(7,v=u.container),"scale"in u&&t(8,N=u.scale),"min_width"in u&&t(9,c=u.min_width),"selectable"in u&&t(10,d=u.selectable),"gradio"in u&&t(11,y=u.gradio),"loading_status"in u&&t(12,B=u.loading_status)},n.$$.update=()=>{n.$$.dirty&1&&!r&&Object.keys(r).length&&t(0,r),n.$$.dirty&10256&&f!==a&&(t(13,a=f),y.dispatch("change"))},[r,i,o,s,f,g,h,v,N,c,d,y,B,a,F]}class Ae extends K{constructor(e){super(),Q(this,e,qe,Le,W,{elem_id:1,elem_classes:2,visible:3,value:4,show_legend:5,color_map:0,label:6,container:7,scale:8,min_width:9,selectable:10,gradio:11,loading_status:12})}}const Xe=Ae;export{Xe as default};
//# sourceMappingURL=index-f626b427.js.map
