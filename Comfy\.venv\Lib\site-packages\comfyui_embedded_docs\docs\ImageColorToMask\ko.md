`ImageColorToMask` 노드는 이미지에서 지정된 색상을 마스크로 변환하도록 설계되었습니다. 이미지를 처리하고 목표 색상을 지정하여, 해당 색상이 강조된 마스크를 생성함으로써 색상 기반 분할이나 객체 분리를 용이하게 합니다.

## 입력

| 매개변수 | 데이터 유형 | 설명                                                                                                                                            |
| -------- | ----------- | ----------------------------------------------------------------------------------------------------------------------------------------------- |
| `image`  | `IMAGE`     | 'image' 매개변수는 처리할 입력 이미지를 나타냅니다. 지정된 색상과 일치하는 이미지 영역을 마스크로 변환하는 데 필수적입니다.                     |
| `color`  | `INT`       | 'color' 매개변수는 이미지에서 마스크로 변환할 목표 색상을 지정합니다. 결과 마스크에서 강조할 특정 색상 영역을 식별하는 데 중요한 역할을 합니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                                                                                          |
| -------- | ----------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `mask`   | `MASK`      | 출력은 입력 이미지에서 지정된 색상과 일치하는 영역을 강조하는 마스크입니다. 이 마스크는 분할이나 객체 분리와 같은 추가 이미지 처리 작업에 사용할 수 있습니다. |
