{"version": 3, "file": "index-e6b54990.js", "sources": ["../../../../js/image/static/ImagePreview.svelte", "../../../../js/image/static/StaticImage.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { uploadToHuggingFace } from \"@gradio/utils\";\n\timport { BlockLabel, Empty, IconButton, ShareButton } from \"@gradio/atoms\";\n\timport { Download } from \"@gradio/icons\";\n\timport { get_coordinates_of_clicked_image } from \"../shared/utils\";\n\timport { _ } from \"svelte-i18n\";\n\n\timport { Image } from \"@gradio/icons\";\n\n\texport let value: null | string;\n\texport let label: string | undefined = undefined;\n\texport let show_label: boolean;\n\texport let show_download_button = true;\n\texport let selectable = false;\n\texport let show_share_button = false;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: string;\n\t\tselect: SelectData;\n\t}>();\n\n\t$: value && dispatch(\"change\", value);\n\n\tconst handle_click = (evt: MouseEvent): void => {\n\t\tlet coordinates = get_coordinates_of_clicked_image(evt);\n\t\tif (coordinates) {\n\t\t\tdispatch(\"select\", { index: coordinates, value: null });\n\t\t}\n\t};\n</script>\n\n<BlockLabel {show_label} Icon={Image} label={label || $_(\"image.image\")} />\n{#if value === null}\n\t<Empty unpadded_box={true} size=\"large\"><Image /></Empty>\n{:else}\n\t<div class=\"icon-buttons\">\n\t\t{#if show_download_button}\n\t\t\t<a\n\t\t\t\thref={value}\n\t\t\t\ttarget={window.__is_colab__ ? \"_blank\" : null}\n\t\t\t\tdownload={\"image\"}\n\t\t\t>\n\t\t\t\t<IconButton Icon={Download} label={$_(\"common.download\")} />\n\t\t\t</a>\n\t\t{/if}\n\t\t{#if show_share_button}\n\t\t\t<ShareButton\n\t\t\t\ton:share\n\t\t\t\ton:error\n\t\t\t\tformatter={async (value) => {\n\t\t\t\t\tif (!value) return \"\";\n\t\t\t\t\tlet url = await uploadToHuggingFace(value, \"base64\");\n\t\t\t\t\treturn `<img src=\"${url}\" />`;\n\t\t\t\t}}\n\t\t\t\t{value}\n\t\t\t/>\n\t\t{/if}\n\t</div>\n\t<!-- TODO: fix -->\n\t<!-- svelte-ignore a11y-click-events-have-key-events -->\n\t<!-- svelte-ignore a11y-no-noninteractive-element-interactions-->\n\t<img src={value} alt=\"\" class:selectable on:click={handle_click} />\n{/if}\n\n<style>\n\timg {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: contain;\n\t}\n\n\t.selectable {\n\t\tcursor: crosshair;\n\t}\n\n\t.icon-buttons {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: 6px;\n\t\tright: 6px;\n\t\tgap: var(--size-1);\n\t}\n</style>\n", "<svelte:options accessors={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport StaticImage from \"./ImagePreview.svelte\";\n\n\timport { Block } from \"@gradio/atoms\";\n\timport { _ } from \"svelte-i18n\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: null | string = null;\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let show_download_button: boolean;\n\n\texport let height: number | undefined;\n\texport let width: number | undefined;\n\n\texport let selectable = false;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let show_share_button = false;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\terror: string;\n\t\tselect: SelectData;\n\t\tshare: ShareData;\n\t}>;\n\n\t$: value, gradio.dispatch(\"change\");\n\tlet dragging: boolean;\n\n\t$: value = !value ? null : value;\n</script>\n\n<Block\n\t{visible}\n\tvariant={\"solid\"}\n\tborder_mode={dragging ? \"focus\" : \"base\"}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\theight={height || undefined}\n\t{width}\n\tallow_overflow={false}\n\t{container}\n\t{scale}\n\t{min_width}\n>\n\t<StatusTracker {...loading_status} />\n\t<StaticImage\n\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\ton:share={({ detail }) => gradio.dispatch(\"share\", detail)}\n\t\ton:error={({ detail }) => gradio.dispatch(\"error\", detail)}\n\t\t{value}\n\t\t{label}\n\t\t{show_label}\n\t\t{show_download_button}\n\t\t{selectable}\n\t\t{show_share_button}\n\t/>\n</Block>\n"], "names": ["ctx", "create_if_block_2", "create_if_block_1", "attr", "img", "img_src_value", "insert", "target", "div", "anchor", "Download", "a", "dirty", "iconbutton_changes", "Image", "value", "$$props", "label", "show_label", "show_download_button", "selectable", "show_share_button", "dispatch", "createEventDispatcher", "handle_click", "evt", "coordinates", "get_coordinates_of_clicked_image", "uploadToHuggingFace", "block_changes", "elem_id", "elem_classes", "visible", "height", "width", "container", "scale", "min_width", "loading_status", "gradio", "dragging", "select_handler", "detail", "share_handler", "error_handler"], "mappings": "2pBAsCOA,EAAoB,CAAA,GAAAC,EAAAD,CAAA,IASpBA,EAAiB,CAAA,GAAAE,EAAAF,CAAA,sHAgBbA,EAAK,CAAA,CAAA,GAAAG,EAAAC,EAAA,MAAAC,CAAA,6EA1BfC,EAsBKC,EAAAC,EAAAC,CAAA,gDAILH,EAAkEC,EAAAH,EAAAK,CAAA,yBAAfT,EAAY,CAAA,CAAA,gBAzBzDA,EAAoB,CAAA,6FASpBA,EAAiB,CAAA,sHAgBbA,EAAK,CAAA,CAAA,sNA5BM,4SASAU,GAAiB,MAAAV,KAAG,iBAAiB,+CAJjDA,EAAK,CAAA,CAAA,EACHG,EAAAQ,EAAA,SAAA,OAAO,aAAe,SAAW,IAAI,iBACnC,OAAO,UAHlBL,EAMGC,EAAAI,EAAAF,CAAA,sCADiCG,EAAA,KAAAC,EAAA,MAAAb,KAAG,iBAAiB,mCAJjDA,EAAK,CAAA,CAAA,omBAPgBc,QAAcd,EAAK,CAAA,GAAIA,EAAE,CAAA,EAAC,aAAa,0CACjE,OAAAA,OAAU,KAAI,0LAD0BA,EAAK,CAAA,GAAIA,EAAE,CAAA,EAAC,aAAa,2TAtB1D,MAAAe,CAAoB,EAAAC,EACpB,CAAA,MAAAC,EAA4B,MAAS,EAAAD,GACrC,WAAAE,CAAmB,EAAAF,EACnB,CAAA,qBAAAG,EAAuB,EAAI,EAAAH,EAC3B,CAAA,WAAAI,EAAa,EAAK,EAAAJ,EAClB,CAAA,kBAAAK,EAAoB,EAAK,EAAAL,EAE9B,MAAAM,EAAWC,IAOXC,EAAgBC,GAAe,KAChCC,EAAcC,GAAiCF,CAAG,EAClDC,GACHJ,EAAS,SAAY,CAAA,MAAOI,EAAa,MAAO,IAAI,CAAA,WAuBjCX,GACZA,eACW,MAAAa,GAAoBb,EAAO,QAAQ,QADhC,sWA7BpBA,GAASO,EAAS,SAAUP,CAAK,mNCgCjBf,EAAc,EAAA,CAAA,8XAAdA,EAAc,EAAA,CAAA,CAAA,CAAA,oZAZxB,oBACIA,EAAQ,EAAA,EAAG,QAAU,eACzB,kCAGD,OAAAA,MAAU,iCAEF,iOAFRY,EAAA,MAAAiB,EAAA,OAAA7B,MAAU,kQArCP,GAAA,CAAA,QAAA8B,EAAU,EAAE,EAAAd,GACZ,aAAAe,EAAY,EAAA,EAAAf,EACZ,CAAA,QAAAgB,EAAU,EAAI,EAAAhB,EACd,CAAA,MAAAD,EAAuB,IAAI,EAAAC,GAC3B,MAAAC,CAAa,EAAAD,GACb,WAAAE,CAAmB,EAAAF,GACnB,qBAAAG,CAA6B,EAAAH,GAE7B,OAAAiB,CAA0B,EAAAjB,GAC1B,MAAAkB,CAAyB,EAAAlB,EAEzB,CAAA,WAAAI,EAAa,EAAK,EAAAJ,EAClB,CAAA,UAAAmB,EAAY,EAAI,EAAAnB,EAChB,CAAA,MAAAoB,EAAuB,IAAI,EAAApB,EAC3B,CAAA,UAAAqB,EAAgC,MAAS,EAAArB,GACzC,eAAAsB,CAA6B,EAAAtB,EAC7B,CAAA,kBAAAK,EAAoB,EAAK,EAAAL,GACzB,OAAAuB,CAKT,EAAAvB,EAGEwB,EAqBW,MAAAC,EAAA,CAAA,CAAA,OAAAC,KAAaH,EAAO,SAAS,SAAUG,CAAM,EAC9CC,EAAA,CAAA,CAAA,OAAAD,KAAaH,EAAO,SAAS,QAASG,CAAM,EAC5CE,EAAA,CAAA,CAAA,OAAAF,KAAaH,EAAO,SAAS,QAASG,CAAM,qoBArBvD3B,EAASA,GAAQ,IAAY,oBAHtBwB,EAAO,SAAS,QAAQ"}