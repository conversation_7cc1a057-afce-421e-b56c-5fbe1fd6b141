{"version": 3, "file": "index-b91bc593.js", "sources": ["../../../../js/code/interactive/InteractiveCode.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport { afterUpdate } from \"svelte\";\n\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { _ } from \"svelte-i18n\";\n\timport Code from \"../shared\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport { Block, BlockLabel } from \"@gradio/atoms\";\n\timport { Code as CodeIcon } from \"@gradio/icons\";\n\n\texport let value = \"\";\n\texport let value_is_output = false;\n\texport let language = \"\";\n\texport let lines = 5;\n\texport let target: HTMLElement;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let label = $_(\"code.code\");\n\texport let show_label = true;\n\texport let loading_status: LoadingStatus;\n\texport let gradio: Gradio<{\n\t\tchange: typeof value;\n\t\tinput: never;\n\t}>;\n\n\tlet dark_mode = target.classList.contains(\"dark\");\n\n\tfunction handle_change(): void {\n\t\tgradio.dispatch(\"change\", value);\n\t\tif (!value_is_output) {\n\t\t\tgradio.dispatch(\"input\");\n\t\t}\n\t}\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\t$: value, handle_change();\n</script>\n\n<Block variant={\"solid\"} padding={false} {elem_id} {elem_classes} {visible}>\n\t<StatusTracker {...loading_status} />\n\n\t<BlockLabel Icon={CodeIcon} {show_label} {label} float={false} />\n\n\t<Code bind:value {language} {lines} {dark_mode} />\n</Block>\n"], "names": ["ctx", "CodeIcon", "value", "$$props", "value_is_output", "language", "lines", "target", "elem_id", "elem_classes", "visible", "label", "$_", "show_label", "loading_status", "gradio", "dark_mode", "handle_change", "afterUpdate", "$$invalidate"], "mappings": "iYA0CoBA,EAAc,CAAA,CAAA,2FAEfC,mCAAsC,2TAFrCD,EAAc,CAAA,CAAA,CAAA,CAAA,2aADlB,gBAAkB,6YA9BtB,GAAA,CAAA,MAAAE,EAAQ,EAAE,EAAAC,EACV,CAAA,gBAAAC,EAAkB,EAAK,EAAAD,EACvB,CAAA,SAAAE,EAAW,EAAE,EAAAF,EACb,CAAA,MAAAG,EAAQ,CAAC,EAAAH,GACT,OAAAI,CAAmB,EAAAJ,EACnB,CAAA,QAAAK,EAAU,EAAE,EAAAL,GACZ,aAAAM,EAAY,EAAA,EAAAN,EACZ,CAAA,QAAAO,EAAU,EAAI,EAAAP,GACd,MAAAQ,EAAQC,EAAG,WAAW,CAAA,EAAAT,EACtB,CAAA,WAAAU,EAAa,EAAI,EAAAV,GACjB,eAAAW,CAA6B,EAAAX,GAC7B,OAAAY,CAGT,EAAAZ,EAEEa,EAAYT,EAAO,UAAU,SAAS,MAAM,WAEvCU,GAAa,CACrBF,EAAO,SAAS,SAAUb,CAAK,EAC1BE,GACJW,EAAO,SAAS,OAAO,EAGzBG,EAAW,IAAA,CACVC,EAAA,GAAAf,EAAkB,EAAK,mfAEda,EAAa"}