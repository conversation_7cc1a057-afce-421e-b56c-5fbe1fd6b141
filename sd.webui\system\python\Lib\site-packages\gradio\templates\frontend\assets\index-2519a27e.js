(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))o(n);new MutationObserver(n=>{for(const i of n)if(i.type==="childList")for(const a of i.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&o(a)}).observe(document,{childList:!0,subtree:!0});function r(n){const i={};return n.integrity&&(i.integrity=n.integrity),n.referrerPolicy&&(i.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?i.credentials="include":n.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function o(n){if(n.ep)return;n.ep=!0;const i=r(n);fetch(n.href,i)}})();var tc=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function lo(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var St={},tt={},lr={exports:{}},U=String,co=function(){return{isColorSupported:!1,reset:U,bold:U,dim:U,italic:U,underline:U,inverse:U,hidden:U,strikethrough:U,black:U,red:U,green:U,yellow:U,blue:U,magenta:U,cyan:U,white:U,gray:U,bgBlack:U,bgRed:U,bgGreen:U,bgYellow:U,bgBlue:U,bgMagenta:U,bgCyan:U,bgWhite:U}};lr.exports=co();lr.exports.createColors=co;var ci=lr.exports;Object.defineProperty(tt,"__esModule",{value:!0});tt.dim=fi;tt.default=void 0;var ve=ui(ci);function ui(e){return e&&e.__esModule?e:{default:e}}let mr=new Set;function Nt(e,t,r){typeof process<"u"&&{}.JEST_WORKER_ID||r&&mr.has(r)||(r&&mr.add(r),console.warn(""),t.forEach(o=>console.warn(e,"-",o)))}function fi(e){return ve.default.dim(e)}var di={info(e,t){Nt(ve.default.bold(ve.default.cyan("info")),...Array.isArray(e)?[e]:[t,e])},warn(e,t){Nt(ve.default.bold(ve.default.yellow("warn")),...Array.isArray(e)?[e]:[t,e])},risk(e,t){Nt(ve.default.bold(ve.default.magenta("risk")),...Array.isArray(e)?[e]:[t,e])}};tt.default=di;Object.defineProperty(St,"__esModule",{value:!0});St.default=void 0;var hi=pi(tt);function pi(e){return e&&e.__esModule?e:{default:e}}function Ve({version:e,from:t,to:r}){hi.default.warn(`${t}-color-renamed`,[`As of Tailwind CSS ${e}, \`${t}\` has been renamed to \`${r}\`.`,"Update your configuration file to silence this warning."])}var gi={inherit:"inherit",current:"currentColor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"#f8fafc",100:"#f1f5f9",200:"#e2e8f0",300:"#cbd5e1",400:"#94a3b8",500:"#64748b",600:"#475569",700:"#334155",800:"#1e293b",900:"#0f172a"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827"},zinc:{50:"#fafafa",100:"#f4f4f5",200:"#e4e4e7",300:"#d4d4d8",400:"#a1a1aa",500:"#71717a",600:"#52525b",700:"#3f3f46",800:"#27272a",900:"#18181b"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717"},stone:{50:"#fafaf9",100:"#f5f5f4",200:"#e7e5e4",300:"#d6d3d1",400:"#a8a29e",500:"#78716c",600:"#57534e",700:"#44403c",800:"#292524",900:"#1c1917"},red:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d"},orange:{50:"#fff7ed",100:"#ffedd5",200:"#fed7aa",300:"#fdba74",400:"#fb923c",500:"#f97316",600:"#ea580c",700:"#c2410c",800:"#9a3412",900:"#7c2d12"},amber:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f"},yellow:{50:"#fefce8",100:"#fef9c3",200:"#fef08a",300:"#fde047",400:"#facc15",500:"#eab308",600:"#ca8a04",700:"#a16207",800:"#854d0e",900:"#713f12"},lime:{50:"#f7fee7",100:"#ecfccb",200:"#d9f99d",300:"#bef264",400:"#a3e635",500:"#84cc16",600:"#65a30d",700:"#4d7c0f",800:"#3f6212",900:"#365314"},green:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d"},emerald:{50:"#ecfdf5",100:"#d1fae5",200:"#a7f3d0",300:"#6ee7b7",400:"#34d399",500:"#10b981",600:"#059669",700:"#047857",800:"#065f46",900:"#064e3b"},teal:{50:"#f0fdfa",100:"#ccfbf1",200:"#99f6e4",300:"#5eead4",400:"#2dd4bf",500:"#14b8a6",600:"#0d9488",700:"#0f766e",800:"#115e59",900:"#134e4a"},cyan:{50:"#ecfeff",100:"#cffafe",200:"#a5f3fc",300:"#67e8f9",400:"#22d3ee",500:"#06b6d4",600:"#0891b2",700:"#0e7490",800:"#155e75",900:"#164e63"},sky:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e"},blue:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a"},indigo:{50:"#eef2ff",100:"#e0e7ff",200:"#c7d2fe",300:"#a5b4fc",400:"#818cf8",500:"#6366f1",600:"#4f46e5",700:"#4338ca",800:"#3730a3",900:"#312e81"},violet:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95"},purple:{50:"#faf5ff",100:"#f3e8ff",200:"#e9d5ff",300:"#d8b4fe",400:"#c084fc",500:"#a855f7",600:"#9333ea",700:"#7e22ce",800:"#6b21a8",900:"#581c87"},fuchsia:{50:"#fdf4ff",100:"#fae8ff",200:"#f5d0fe",300:"#f0abfc",400:"#e879f9",500:"#d946ef",600:"#c026d3",700:"#a21caf",800:"#86198f",900:"#701a75"},pink:{50:"#fdf2f8",100:"#fce7f3",200:"#fbcfe8",300:"#f9a8d4",400:"#f472b6",500:"#ec4899",600:"#db2777",700:"#be185d",800:"#9d174d",900:"#831843"},rose:{50:"#fff1f2",100:"#ffe4e6",200:"#fecdd3",300:"#fda4af",400:"#fb7185",500:"#f43f5e",600:"#e11d48",700:"#be123c",800:"#9f1239",900:"#881337"},get lightBlue(){return Ve({version:"v2.2",from:"lightBlue",to:"sky"}),this.sky},get warmGray(){return Ve({version:"v3.0",from:"warmGray",to:"stone"}),this.stone},get trueGray(){return Ve({version:"v3.0",from:"trueGray",to:"neutral"}),this.neutral},get coolGray(){return Ve({version:"v3.0",from:"coolGray",to:"gray"}),this.gray},get blueGray(){return Ve({version:"v3.0",from:"blueGray",to:"slate"}),this.slate}};St.default=gi;let Ct=St;var _i=(Ct.__esModule?Ct:{default:Ct}).default;const br=lo(_i),rc=["red","green","blue","yellow","purple","teal","orange","cyan","lime","pink"],mi=[{color:"red",primary:600,secondary:100},{color:"green",primary:600,secondary:100},{color:"blue",primary:600,secondary:100},{color:"yellow",primary:500,secondary:100},{color:"purple",primary:600,secondary:100},{color:"teal",primary:600,secondary:100},{color:"orange",primary:600,secondary:100},{color:"cyan",primary:600,secondary:100},{color:"lime",primary:500,secondary:100},{color:"pink",primary:600,secondary:100}],oc=mi.reduce((e,{color:t,primary:r,secondary:o})=>({...e,[t]:{primary:br[t][r],secondary:br[t][o]}}),{}),bi="modulepreload",yi=function(e,t){return new URL(e,t).href},yr={},gt=function(t,r,o){if(!r||r.length===0)return t();const n=document.getElementsByTagName("link");return Promise.all(r.map(i=>{if(i=yi(i,o),i in yr)return;yr[i]=!0;const a=i.endsWith(".css"),l=a?'[rel="stylesheet"]':"";if(!!o)for(let c=n.length-1;c>=0;c--){const f=n[c];if(f.href===i&&(!a||f.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${i}"]${l}`))return;const s=document.createElement("link");if(s.rel=a?"stylesheet":bi,a||(s.as="script",s.crossOrigin=""),s.href=i,document.head.appendChild(s),a)return new Promise((c,f)=>{s.addEventListener("load",c),s.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${i}`)))})})).then(()=>t())};var Ot=new Intl.Collator(0,{numeric:1}).compare;function vr(e,t,r){return e=e.split("."),t=t.split("."),Ot(e[0],t[0])||Ot(e[1],t[1])||(t[2]=t.slice(2).join("."),r=/[.-]/.test(e[2]=e.slice(2).join(".")),r==/[.-]/.test(t[2])?Ot(e[2],t[2]):r?-1:1)}function Mt(e){if(e.startsWith("http")){const{protocol:t,host:r}=new URL(e);return r.endsWith("hf.space")?{ws_protocol:"wss",host:r,http_protocol:t}:{ws_protocol:t==="https:"?"wss":"ws",http_protocol:t,host:r}}return{ws_protocol:"wss",http_protocol:"https:",host:e}}const uo=/^[^\/]*\/[^\/]*$/,vi=/.*hf\.space\/{0,1}$/;async function wi(e,t){const r={};t&&(r.Authorization=`Bearer ${t}`);const o=e.trim();if(uo.test(o))try{const n=await fetch(`https://huggingface.co/api/spaces/${o}/host`,{headers:r});if(n.status!==200)throw new Error("Space metadata could not be loaded.");const i=(await n.json()).host;return{space_id:e,...Mt(i)}}catch(n){throw new Error("Space metadata could not be loaded."+n.message)}if(vi.test(o)){const{ws_protocol:n,http_protocol:i,host:a}=Mt(o);return{space_id:a.replace(".hf.space",""),ws_protocol:n,http_protocol:i,host:a}}return{space_id:!1,...Mt(o)}}function xi(e){let t={};return e.forEach(({api_name:r},o)=>{r&&(t[r]=o)}),t}const Ei=/^(?=[^]*\b[dD]iscussions{0,1}\b)(?=[^]*\b[dD]isabled\b)[^]*$/;async function wr(e){try{const r=(await fetch(`https://huggingface.co/api/spaces/${e}/discussions`,{method:"HEAD"})).headers.get("x-error-message");return!(r&&Ei.test(r))}catch{return!1}}const Si="This application is too busy. Keep trying!",at="Connection errored out.";let fo;function ki(e){return{post_data:t,upload_files:r,client:o,handle_blob:n};async function t(i,a,l){const u={"Content-Type":"application/json"};l&&(u.Authorization=`Bearer ${l}`);try{var s=await e(i,{method:"POST",body:JSON.stringify(a),headers:u})}catch{return[{error:at},500]}return[await s.json(),s.status]}async function r(i,a,l){const u={};l&&(u.Authorization=`Bearer ${l}`);const s=1e3,c=[];for(let d=0;d<a.length;d+=s){const g=a.slice(d,d+s),h=new FormData;g.forEach(v=>{h.append("files",v)});try{var f=await e(`${i}/upload`,{method:"POST",body:h,headers:u})}catch{return{error:at}}const _=await f.json();c.push(..._)}return{files:c}}async function o(i,a={normalise_files:!0}){return new Promise(async l=>{const{status_callback:u,hf_token:s,normalise_files:c}=a,f={predict:z,submit:J,view_api:q},d=c??!0;if(typeof window>"u"||!("WebSocket"in window)){const P=await gt(()=>import("./wrapper-6f348d45-38be7a64.js"),["./wrapper-6f348d45-38be7a64.js","./__vite-browser-external-b25bb000.js"],import.meta.url);fo=(await gt(()=>import("./__vite-browser-external-b25bb000.js"),[],import.meta.url)).Blob,global.WebSocket=P.WebSocket}const{ws_protocol:g,http_protocol:h,host:_,space_id:v}=await wi(i,s),x=Math.random().toString(36).substring(2),E={};let p,w={},T=!1;s&&v&&(T=await Ti(v,s));async function C(P){p=P,w=xi(P?.dependencies||[]);try{S=await q(p)}catch(D){console.error(`Could not get api details: ${D.message}`)}return{config:p,...f}}let S;async function N(P){if(u&&u(P),P.status==="running")try{p=await Tr(e,`${h}//${_}`,s);const D=await C(p);l(D)}catch(D){console.error(D),u&&u({status:"error",message:"Could not load this space.",load_status:"error",detail:"NOT_FOUND"})}}try{p=await Tr(e,`${h}//${_}`,s);const P=await C(p);l(P)}catch(P){console.error(P),v?Vt(v,uo.test(v)?"space_name":"subdomain",N):u&&u({status:"error",message:"Could not load this space.",load_status:"error",detail:"NOT_FOUND"})}function z(P,D,X){let H=!1,ce=!1,F;if(typeof P=="number")F=p.dependencies[P];else{const j=P.replace(/^\//,"");F=p.dependencies[w[j]]}if(F.types.continuous)throw new Error("Cannot call predict on this function as it may run forever. Use submit instead");return new Promise((j,ie)=>{const ae=J(P,D,X);let ue;ae.on("data",y=>{ce&&(ae.destroy(),j(y)),H=!0,ue=y}).on("status",y=>{y.stage==="error"&&ie(y),y.stage==="complete"&&(ce=!0,ae.destroy(),H&&j(ue))})})}function J(P,D,X){let H,ce;if(typeof P=="number")H=P,ce=S.unnamed_endpoints[H];else{const V=P.replace(/^\//,"");H=w[V],ce=S.named_endpoints[P.trim()]}if(typeof H!="number")throw new Error("There is no endpoint matching that name of fn_index matching that number.");let F;const j=typeof P=="number"?"/predict":P;let ie,ae=!1;const ue={};n(`${h}//${_+p.path}`,D,ce,s).then(V=>{if(ie={data:V||[],event_data:X,fn_index:H},Pi(H,p))y({type:"status",endpoint:j,stage:"pending",queue:!1,fn_index:H,time:new Date}),t(`${h}//${_+p.path}/run${j.startsWith("/")?j:`/${j}`}`,{...ie,session_hash:x},s).then(([W,te])=>{const re=d?Sr(W.data,ce,p.root,p.root_url):W.data;te==200?(y({type:"data",endpoint:j,fn_index:H,data:re,time:new Date}),y({type:"status",endpoint:j,fn_index:H,stage:"complete",eta:W.average_duration,queue:!1,time:new Date})):y({type:"status",stage:"error",endpoint:j,fn_index:H,message:W.error,queue:!1,time:new Date})}).catch(W=>{y({type:"status",stage:"error",message:W.message,endpoint:j,fn_index:H,queue:!1,time:new Date})});else{y({type:"status",stage:"pending",queue:!0,endpoint:j,fn_index:H,time:new Date});let W=new URL(`${g}://${_}${p.path}
							/queue/join`);T&&W.searchParams.set("__sign",T),F=new WebSocket(W),F.onclose=te=>{te.wasClean||y({type:"status",stage:"error",broken:!0,message:at,queue:!0,endpoint:j,fn_index:H,time:new Date})},F.onmessage=function(te){const re=JSON.parse(te.data),{type:de,status:m,data:Y}=Hi(re,E[H]);if(de==="update"&&m&&!ae)y({type:"status",endpoint:j,fn_index:H,time:new Date,...m}),m.stage==="error"&&F.close();else if(de==="hash"){F.send(JSON.stringify({fn_index:H,session_hash:x}));return}else de==="data"?F.send(JSON.stringify({...ie,session_hash:x})):de==="complete"?ae=m:de==="log"?y({type:"log",log:Y.log,level:Y.level,endpoint:j,fn_index:H}):de==="generating"&&y({type:"status",time:new Date,...m,stage:m?.stage,queue:!0,endpoint:j,fn_index:H});Y&&(y({type:"data",time:new Date,data:d?Sr(Y.data,ce,p.root,p.root_url):Y.data,endpoint:j,fn_index:H}),ae&&(y({type:"status",time:new Date,...ae,stage:m?.stage,queue:!0,endpoint:j,fn_index:H}),F.close()))},vr(p.version||"2.0.0","3.6")<0&&addEventListener("open",()=>F.send(JSON.stringify({hash:x})))}});function y(V){const te=ue[V.type]||[];te?.forEach(re=>re(V))}function Ae(V,W){const te=ue,re=te[V]||[];return te[V]=re,re?.push(W),{on:Ae,off:ye,cancel:Fe,destroy:qe}}function ye(V,W){const te=ue;let re=te[V]||[];return re=re?.filter(de=>de!==W),te[V]=re,{on:Ae,off:ye,cancel:Fe,destroy:qe}}async function Fe(){const V={stage:"complete",queue:!1,time:new Date};ae=V,y({...V,type:"status",endpoint:j,fn_index:H}),F&&F.readyState===0?F.addEventListener("open",()=>{F.close()}):F.close();try{await e(`${h}//${_+p.path}/reset`,{headers:{"Content-Type":"application/json"},method:"POST",body:JSON.stringify({fn_index:H,session_hash:x})})}catch{console.warn("The `/reset` endpoint could not be called. Subsequent endpoint results may be unreliable.")}}function qe(){for(const V in ue)ue[V].forEach(W=>{ye(V,W)})}return{on:Ae,off:ye,cancel:Fe,destroy:qe}}async function q(P){if(S)return S;const D={"Content-Type":"application/json"};s&&(D.Authorization=`Bearer ${s}`);let X;if(vr(P.version||"2.0.0","3.30")<0?X=await e("https://gradio-space-api-fetcher-v2.hf.space/api",{method:"POST",body:JSON.stringify({serialize:!1,config:JSON.stringify(P)}),headers:D}):X=await e(`${P.root}/info`,{headers:D}),!X.ok)throw new Error(at);let H=await X.json();return"api"in H&&(H=H.api),H.named_endpoints["/predict"]&&!H.unnamed_endpoints[0]&&(H.unnamed_endpoints[0]=H.named_endpoints["/predict"]),Ai(H,P,w)}})}async function n(i,a,l,u){const s=await qt(a,void 0,[],!0,l);return Promise.all(s.map(async({path:c,blob:f,data:d,type:g})=>{if(f){const h=(await r(i,[f],u)).files[0];return{path:c,file_url:h,type:g}}return{path:c,base64:d,type:g}})).then(c=>(c.forEach(({path:f,file_url:d,base64:g,type:h})=>{if(g)It(a,g,f);else if(h==="Gallery")It(a,d,f);else if(d){const _={is_file:!0,name:`${d}`,data:null};It(a,_,f)}}),a))}}const{post_data:nc,upload_files:xr,client:Er,handle_blob:ic}=ki(fetch);function Sr(e,t,r,o){return e.map((n,i)=>{var a,l,u,s;return((l=(a=t?.returns)==null?void 0:a[i])==null?void 0:l.component)==="File"?Ze(n,r,o):((s=(u=t?.returns)==null?void 0:u[i])==null?void 0:s.component)==="Gallery"?n.map(c=>Array.isArray(c)?[Ze(c[0],r,o),c[1]]:[Ze(c,r,o),null]):typeof n=="object"&&n?.is_file?Ze(n,r,o):n})}function Ze(e,t,r){if(e==null)return null;if(typeof e=="string")return{name:"file_data",data:e};if(Array.isArray(e)){const o=[];for(const n of e)n===null?o.push(null):o.push(Ze(n,t,r));return o}else e.is_file&&(r?e.data="/proxy="+r+"file="+e.name:e.data=t+"/file="+e.name);return e}function kr(e,t,r,o){switch(e.type){case"string":return"string";case"boolean":return"boolean";case"number":return"number"}if(r==="JSONSerializable"||r==="StringSerializable")return"any";if(r==="ListStringSerializable")return"string[]";if(t==="Image")return o==="parameter"?"Blob | File | Buffer":"string";if(r==="FileSerializable")return e?.type==="array"?o==="parameter"?"(Blob | File | Buffer)[]":"{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}[]":o==="parameter"?"Blob | File | Buffer":"{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}";if(r==="GallerySerializable")return o==="parameter"?"[(Blob | File | Buffer), (string | null)][]":"[{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}, (string | null))][]"}function Ar(e,t){return t==="GallerySerializable"?"array of [file, label] tuples":t==="ListStringSerializable"?"array of strings":t==="FileSerializable"?"array of files or single file":e.description}function Ai(e,t,r){const o={named_endpoints:{},unnamed_endpoints:{}};for(const n in e){const i=e[n];for(const a in i){const l=t.dependencies[a]?a:r[a.replace("/","")],u=i[a];o[n][a]={},o[n][a].parameters={},o[n][a].returns={},o[n][a].type=t.dependencies[l].types,o[n][a].parameters=u.parameters.map(({label:s,component:c,type:f,serializer:d})=>({label:s,component:c,type:kr(f,c,d,"parameter"),description:Ar(f,d)})),o[n][a].returns=u.returns.map(({label:s,component:c,type:f,serializer:d})=>({label:s,component:c,type:kr(f,c,d,"return"),description:Ar(f,d)}))}}return o}async function Ti(e,t){try{return(await(await fetch(`https://huggingface.co/api/spaces/${e}/jwt`,{headers:{Authorization:`Bearer ${t}`}})).json()).token||!1}catch(r){return console.error(r),!1}}function It(e,t,r){for(;r.length>1;)e=e[r.shift()];e[r.shift()]=t}async function qt(e,t=void 0,r=[],o=!1,n=void 0){if(Array.isArray(e)){let i=[];return await Promise.all(e.map(async(a,l)=>{var u;let s=r.slice();s.push(l);const c=await qt(e[l],o?((u=n?.parameters[l])==null?void 0:u.component)||void 0:t,s,!1,n);i=i.concat(c)})),i}else if(globalThis.Buffer&&e instanceof globalThis.Buffer){const i=t==="Image";return[{path:r,blob:i?!1:new fo([e]),data:i?`${e.toString("base64")}`:!1,type:t}]}else if(e instanceof Blob||typeof window<"u"&&e instanceof File){if(t==="Image"){let i;if(typeof window<"u")i=await Bi(e);else{const a=await e.arrayBuffer();i=Buffer.from(a).toString("base64")}return[{path:r,data:i,type:t,blob:!1}]}return[{path:r,blob:e,type:t,data:!1}]}else if(typeof e=="object"){let i=[];for(let a in e)if(e.hasOwnProperty(a)){let l=r.slice();l.push(a),i=i.concat(await qt(e[a],void 0,l,!1,n))}return i}return[]}function Bi(e){return new Promise((t,r)=>{const o=new FileReader;o.onloadend=()=>t(o.result),o.readAsDataURL(e)})}function Pi(e,t){var r,o,n,i;return!(((o=(r=t?.dependencies)==null?void 0:r[e])==null?void 0:o.queue)===null?t.enable_queue:(i=(n=t?.dependencies)==null?void 0:n[e])!=null&&i.queue)||!1}async function Tr(e,t,r){const o={};if(r&&(o.Authorization=`Bearer ${r}`),typeof window<"u"&&window.gradio_config&&location.origin!=="http://localhost:9876"){const n=window.gradio_config.root,i=window.gradio_config;return i.root=t+i.root,{...i,path:n}}else if(t){let n=await e(`${t}/config`,{headers:o});if(n.status===200){const i=await n.json();return i.path=i.path??"",i.root=t,i}throw new Error("Could not get config.")}throw new Error("No config or app endpoint found")}async function Vt(e,t,r){let o=t==="subdomain"?`https://huggingface.co/api/spaces/by-subdomain/${e}`:`https://huggingface.co/api/spaces/${e}`,n,i;try{if(n=await fetch(o),i=n.status,i!==200)throw new Error;n=await n.json()}catch{r({status:"error",load_status:"error",message:"Could not get space status",detail:"NOT_FOUND"});return}if(!n||i!==200)return;const{runtime:{stage:a},id:l}=n;switch(a){case"STOPPED":case"SLEEPING":r({status:"sleeping",load_status:"pending",message:"Space is asleep. Waking it up...",detail:a}),setTimeout(()=>{Vt(e,t,r)},1e3);break;case"PAUSED":r({status:"paused",load_status:"error",message:"This space has been paused by the author. If you would like to try this demo, consider duplicating the space.",detail:a,discussions_enabled:await wr(l)});break;case"RUNNING":case"RUNNING_BUILDING":r({status:"running",load_status:"complete",message:"",detail:a});break;case"BUILDING":r({status:"building",load_status:"pending",message:"Space is building...",detail:a}),setTimeout(()=>{Vt(e,t,r)},1e3);break;default:r({status:"space_error",load_status:"error",message:"This space is experiencing an issue.",detail:a,discussions_enabled:await wr(l)});break}}function Hi(e,t){switch(e.msg){case"send_data":return{type:"data"};case"send_hash":return{type:"hash"};case"queue_full":return{type:"update",status:{queue:!0,message:Si,stage:"error",code:e.code,success:e.success}};case"estimation":return{type:"update",status:{queue:!0,stage:t||"pending",code:e.code,size:e.queue_size,position:e.rank,eta:e.rank_eta,success:e.success}};case"progress":return{type:"update",status:{queue:!0,stage:"pending",code:e.code,progress_data:e.progress_data,success:e.success}};case"log":return{type:"log",data:e};case"process_generating":return{type:"generating",status:{queue:!0,message:e.success?null:e.output.error,stage:e.success?"generating":"error",code:e.code,progress_data:e.progress_data,eta:e.average_duration},data:e.success?e.output:null};case"process_completed":return"error"in e.output?{type:"update",status:{queue:!0,message:e.output.error,stage:"error",code:e.code,success:e.success}}:{type:"complete",status:{queue:!0,message:e.success?void 0:e.output.error,stage:e.success?"complete":"error",code:e.code,progress_data:e.progress_data,eta:e.output.average_duration},data:e.success?e.output:null};case"process_starts":return{type:"update",status:{queue:!0,stage:"pending",code:e.code,size:e.rank,position:0,success:e.success}}}return{type:"none",status:{stage:"error",queue:!0}}}function Xt(e,t){if(document.querySelector(`link[href='${e}']`))return Promise.resolve();const o=document.createElement("link");return o.rel="stylesheet",o.href=e,new Promise((n,i)=>{o.addEventListener("load",()=>n()),o.addEventListener("error",()=>{console.error(`Unable to preload CSS for ${e}`),n()}),t.appendChild(o)})}function Z(){}const cr=e=>e;function ho(e,t){for(const r in t)e[r]=t[r];return e}function po(e){return e()}function Br(){return Object.create(null)}function pe(e){e.forEach(po)}function Ee(e){return typeof e=="function"}function rt(e,t){return e!=e?t==t:e!==t||e&&typeof e=="object"||typeof e=="function"}let st;function Ni(e,t){return st||(st=document.createElement("a")),st.href=t,e===st.href}function Ci(e){return Object.keys(e).length===0}function go(e,...t){if(e==null){for(const o of t)o(void 0);return Z}const r=e.subscribe(...t);return r.unsubscribe?()=>r.unsubscribe():r}function xe(e,t,r){e.$$.on_destroy.push(go(t,r))}function _o(e,t,r,o){if(e){const n=mo(e,t,r,o);return e[0](n)}}function mo(e,t,r,o){return e[1]&&o?ho(r.ctx.slice(),e[1](o(t))):r.ctx}function bo(e,t,r,o){if(e[2]&&o){const n=e[2](o(r));if(t.dirty===void 0)return n;if(typeof n=="object"){const i=[],a=Math.max(t.dirty.length,n.length);for(let l=0;l<a;l+=1)i[l]=t.dirty[l]|n[l];return i}return t.dirty|n}return t.dirty}function yo(e,t,r,o,n,i){if(n){const a=mo(t,r,o,i);e.p(a,n)}}function vo(e){if(e.ctx.length>32){const t=[],r=e.ctx.length/32;for(let o=0;o<r;o++)t[o]=-1;return t}return-1}function ac(e){return e??""}function sc(e,t,r){return e.set(r),t}function lc(e){return e&&Ee(e.destroy)?e.destroy:Z}function cc(e){const t=typeof e=="string"&&e.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return t?[parseFloat(t[1]),t[2]||"px"]:[e,"px"]}const wo=typeof window<"u";let Qe=wo?()=>window.performance.now():()=>Date.now(),ur=wo?e=>requestAnimationFrame(e):Z;const Ce=new Set;function xo(e){Ce.forEach(t=>{t.c(e)||(Ce.delete(t),t.f())}),Ce.size!==0&&ur(xo)}function kt(e){let t;return Ce.size===0&&ur(xo),{promise:new Promise(r=>{Ce.add(t={c:e,f:r})}),abort(){Ce.delete(t)}}}const Oi=typeof window<"u"?window:typeof globalThis<"u"?globalThis:global;"WeakMap"in Oi;function B(e,t){e.appendChild(t)}function Eo(e){if(!e)return document;const t=e.getRootNode?e.getRootNode():e.ownerDocument;return t&&t.host?t:e.ownerDocument}function Mi(e){const t=R("style");return t.textContent="/* empty */",Ii(Eo(e),t),t.sheet}function Ii(e,t){return B(e.head||e,t),t.sheet}function A(e,t,r){e.insertBefore(t,r||null)}function k(e){e.parentNode&&e.parentNode.removeChild(e)}function So(e,t){for(let r=0;r<e.length;r+=1)e[r]&&e[r].d(t)}function R(e){return document.createElement(e)}function fe(e){return document.createElementNS("http://www.w3.org/2000/svg",e)}function M(e){return document.createTextNode(e)}function $(){return M(" ")}function Se(){return M("")}function Pr(e,t,r,o){return e.addEventListener(t,r,o),()=>e.removeEventListener(t,r,o)}function uc(e){return function(t){return t.preventDefault(),e.call(this,t)}}function fc(e){return function(t){return t.stopPropagation(),e.call(this,t)}}function b(e,t,r){r==null?e.removeAttribute(t):e.getAttribute(t)!==r&&e.setAttribute(t,r)}const Li=["width","height"];function Ri(e,t){const r=Object.getOwnPropertyDescriptors(e.__proto__);for(const o in t)t[o]==null?e.removeAttribute(o):o==="style"?e.style.cssText=t[o]:o==="__value"?e.value=e[o]=t[o]:r[o]&&r[o].set&&Li.indexOf(o)===-1?e[o]=t[o]:b(e,o,t[o])}function zi(e,t){Object.keys(t).forEach(r=>{Di(e,r,t[r])})}function Di(e,t,r){t in e?e[t]=typeof e[t]=="boolean"&&r===""?!0:r:b(e,t,r)}function dc(e){return/-/.test(e)?zi:Ri}function hc(e){let t;return{p(...r){t=r,t.forEach(o=>e.push(o))},r(){t.forEach(r=>e.splice(e.indexOf(r),1))}}}function pc(e){return e===""?null:+e}function ji(e){return Array.from(e.childNodes)}function ee(e,t){t=""+t,e.data!==t&&(e.data=t)}function gc(e,t){e.value=t??""}function oe(e,t,r,o){r==null?e.style.removeProperty(t):e.style.setProperty(t,r,o?"important":"")}let lt;function Ui(){if(lt===void 0){lt=!1;try{typeof window<"u"&&window.parent&&window.parent.document}catch{lt=!0}}return lt}function _c(e,t){getComputedStyle(e).position==="static"&&(e.style.position="relative");const o=R("iframe");o.setAttribute("style","display: block; position: absolute; top: 0; left: 0; width: 100%; height: 100%; overflow: hidden; border: 0; opacity: 0; pointer-events: none; z-index: -1;"),o.setAttribute("aria-hidden","true"),o.tabIndex=-1;const n=Ui();let i;return n?(o.src="data:text/html,<script>onresize=function(){parent.postMessage(0,'*')}<\/script>",i=Pr(window,"message",a=>{a.source===o.contentWindow&&t()})):(o.src="about:blank",o.onload=()=>{i=Pr(o.contentWindow,"resize",t),t()}),B(e,o),()=>{(n||i&&o.contentWindow)&&i(),k(o)}}function Q(e,t,r){e.classList.toggle(t,!!r)}function ko(e,t,{bubbles:r=!1,cancelable:o=!1}={}){return new CustomEvent(e,{detail:t,bubbles:r,cancelable:o})}class mc{is_svg=!1;e=void 0;n=void 0;t=void 0;a=void 0;constructor(t=!1){this.is_svg=t,this.e=this.n=null}c(t){this.h(t)}m(t,r,o=null){this.e||(this.is_svg?this.e=fe(r.nodeName):this.e=R(r.nodeType===11?"TEMPLATE":r.nodeName),this.t=r.tagName!=="TEMPLATE"?r:r.content,this.c(t)),this.i(o)}h(t){this.e.innerHTML=t,this.n=Array.from(this.e.nodeName==="TEMPLATE"?this.e.content.childNodes:this.e.childNodes)}i(t){for(let r=0;r<this.n.length;r+=1)A(this.t,this.n[r],t)}p(t){this.d(),this.h(t),this.i(this.a)}d(){this.n.forEach(k)}}function bc(e,t){return new e(t)}const _t=new Map;let mt=0;function Gi(e){let t=5381,r=e.length;for(;r--;)t=(t<<5)-t^e.charCodeAt(r);return t>>>0}function Fi(e,t){const r={stylesheet:Mi(t),rules:{}};return _t.set(e,r),r}function bt(e,t,r,o,n,i,a,l=0){const u=16.666/o;let s=`{
`;for(let v=0;v<=1;v+=u){const x=t+(r-t)*i(v);s+=v*100+`%{${a(x,1-x)}}
`}const c=s+`100% {${a(r,1-r)}}
}`,f=`__svelte_${Gi(c)}_${l}`,d=Eo(e),{stylesheet:g,rules:h}=_t.get(d)||Fi(d,e);h[f]||(h[f]=!0,g.insertRule(`@keyframes ${f} ${c}`,g.cssRules.length));const _=e.style.animation||"";return e.style.animation=`${_?`${_}, `:""}${f} ${o}ms linear ${n}ms 1 both`,mt+=1,f}function yt(e,t){const r=(e.style.animation||"").split(", "),o=r.filter(t?i=>i.indexOf(t)<0:i=>i.indexOf("__svelte")===-1),n=r.length-o.length;n&&(e.style.animation=o.join(", "),mt-=n,mt||qi())}function qi(){ur(()=>{mt||(_t.forEach(e=>{const{ownerNode:t}=e.stylesheet;t&&k(t)}),_t.clear())})}let Ke;function Ye(e){Ke=e}function ke(){if(!Ke)throw new Error("Function called outside component initialization");return Ke}function yc(e){ke().$$.before_update.push(e)}function Wt(e){ke().$$.on_mount.push(e)}function vc(e){ke().$$.after_update.push(e)}function Vi(e){ke().$$.on_destroy.push(e)}function wc(){const e=ke();return(t,r,{cancelable:o=!1}={})=>{const n=e.$$.callbacks[t];if(n){const i=ko(t,r,{cancelable:o});return n.slice().forEach(a=>{a.call(e,i)}),!i.defaultPrevented}return!0}}function Xi(e,t){return ke().$$.context.set(e,t),t}function xc(e){return ke().$$.context.get(e)}function Ec(e,t){const r=e.$$.callbacks[t.type];r&&r.slice().forEach(o=>o.call(this,t))}const He=[],_e=[];let Oe=[];const Zt=[],Ao=Promise.resolve();let Jt=!1;function To(){Jt||(Jt=!0,Ao.then(Bo))}function Wi(){return To(),Ao}function Me(e){Oe.push(e)}function Yt(e){Zt.push(e)}const Lt=new Set;let Be=0;function Bo(){if(Be!==0)return;const e=Ke;do{try{for(;Be<He.length;){const t=He[Be];Be++,Ye(t),Zi(t.$$)}}catch(t){throw He.length=0,Be=0,t}for(Ye(null),He.length=0,Be=0;_e.length;)_e.pop()();for(let t=0;t<Oe.length;t+=1){const r=Oe[t];Lt.has(r)||(Lt.add(r),r())}Oe.length=0}while(He.length);for(;Zt.length;)Zt.pop()();Jt=!1,Lt.clear(),Ye(e)}function Zi(e){if(e.fragment!==null){e.update(),pe(e.before_update);const t=e.dirty;e.dirty=[-1],e.fragment&&e.fragment.p(e.ctx,t),e.after_update.forEach(Me)}}function Ji(e){const t=[],r=[];Oe.forEach(o=>e.indexOf(o)===-1?t.push(o):r.push(o)),r.forEach(o=>o()),Oe=t}let Xe;function fr(){return Xe||(Xe=Promise.resolve(),Xe.then(()=>{Xe=null})),Xe}function we(e,t,r){e.dispatchEvent(ko(`${t?"intro":"outro"}${r}`))}const ft=new Set;let he;function vt(){he={r:0,c:[],p:he}}function wt(){he.r||pe(he.c),he=he.p}function K(e,t){e&&e.i&&(ft.delete(e),e.i(t))}function ne(e,t,r,o){if(e&&e.o){if(ft.has(e))return;ft.add(e),he.c.push(()=>{ft.delete(e),o&&(r&&e.d(1),o())}),e.o(t)}else o&&o()}const dr={duration:0};function Sc(e,t,r){const o={direction:"in"};let n=t(e,r,o),i=!1,a,l,u=0;function s(){a&&yt(e,a)}function c(){const{delay:d=0,duration:g=300,easing:h=cr,tick:_=Z,css:v}=n||dr;v&&(a=bt(e,0,1,g,d,h,v,u++)),_(0,1);const x=Qe()+d,E=x+g;l&&l.abort(),i=!0,Me(()=>we(e,!0,"start")),l=kt(p=>{if(i){if(p>=E)return _(1,0),we(e,!0,"end"),s(),i=!1;if(p>=x){const w=h((p-x)/g);_(w,1-w)}}return i})}let f=!1;return{start(){f||(f=!0,yt(e),Ee(n)?(n=n(o),fr().then(c)):c())},invalidate(){f=!1},end(){i&&(s(),i=!1)}}}function kc(e,t,r){const o={direction:"out"};let n=t(e,r,o),i=!0,a;const l=he;l.r+=1;let u;function s(){const{delay:c=0,duration:f=300,easing:d=cr,tick:g=Z,css:h}=n||dr;h&&(a=bt(e,1,0,f,c,d,h));const _=Qe()+c,v=_+f;Me(()=>we(e,!1,"start")),"inert"in e&&(u=e.inert,e.inert=!0),kt(x=>{if(i){if(x>=v)return g(0,1),we(e,!1,"end"),--l.r||pe(l.c),!1;if(x>=_){const E=d((x-_)/f);g(1-E,E)}}return i})}return Ee(n)?fr().then(()=>{n=n(o),s()}):s(),{end(c){c&&"inert"in e&&(e.inert=u),c&&n.tick&&n.tick(1,0),i&&(a&&yt(e,a),i=!1)}}}function Ac(e,t,r,o){let i=t(e,r,{direction:"both"}),a=o?0:1,l=null,u=null,s=null,c;function f(){s&&yt(e,s)}function d(h,_){const v=h.b-a;return _*=Math.abs(v),{a,b:h.b,d:v,duration:_,start:h.start,end:h.start+_,group:h.group}}function g(h){const{delay:_=0,duration:v=300,easing:x=cr,tick:E=Z,css:p}=i||dr,w={start:Qe()+_,b:h};h||(w.group=he,he.r+=1),"inert"in e&&(h?c!==void 0&&(e.inert=c):(c=e.inert,e.inert=!0)),l||u?u=w:(p&&(f(),s=bt(e,a,h,v,_,x,p)),h&&E(0,1),l=d(w,v),Me(()=>we(e,h,"start")),kt(T=>{if(u&&T>u.start&&(l=d(u,v),u=null,we(e,l.b,"start"),p&&(f(),s=bt(e,a,l.b,l.duration,0,x,i.css))),l){if(T>=l.end)E(a=l.b,1-a),we(e,l.b,"end"),u||(l.b?f():--l.group.r||pe(l.group.c)),l=null;else if(T>=l.start){const C=T-l.start;a=l.a+l.d*x(C/l.duration),E(a,1-a)}}return!!(l||u)}))}return{run(h){Ee(i)?fr().then(()=>{i=i({direction:h?"in":"out"}),g(h)}):g(h)},end(){f(),l=u=null}}}function xt(e){return e?.length!==void 0?e:Array.from(e)}function Tc(e,t){e.d(1),t.delete(e.key)}function Yi(e,t){ne(e,1,1,()=>{t.delete(e.key)})}function Bc(e,t){e.f(),Yi(e,t)}function Pc(e,t,r,o,n,i,a,l,u,s,c,f){let d=e.length,g=i.length,h=d;const _={};for(;h--;)_[e[h].key]=h;const v=[],x=new Map,E=new Map,p=[];for(h=g;h--;){const S=f(n,i,h),N=r(S);let z=a.get(N);z?o&&p.push(()=>z.p(S,t)):(z=s(N,S),z.c()),x.set(N,v[h]=z),N in _&&E.set(N,Math.abs(h-_[N]))}const w=new Set,T=new Set;function C(S){K(S,1),S.m(l,c),a.set(S.key,S),c=S.first,g--}for(;d&&g;){const S=v[g-1],N=e[d-1],z=S.key,J=N.key;S===N?(c=S.first,d--,g--):x.has(J)?!a.has(z)||w.has(z)?C(S):T.has(J)?d--:E.get(z)>E.get(J)?(T.add(z),C(S)):(w.add(J),d--):(u(N,a),d--)}for(;d--;){const S=e[d];x.has(S.key)||u(S,a)}for(;g;)C(v[g-1]);return pe(p),v}function Qi(e,t){const r={},o={},n={$$scope:1};let i=e.length;for(;i--;){const a=e[i],l=t[i];if(l){for(const u in a)u in l||(o[u]=1);for(const u in l)n[u]||(r[u]=l[u],n[u]=1);e[i]=l}else for(const u in a)n[u]=1}for(const a in o)a in r||(r[a]=void 0);return r}function Ki(e){return typeof e=="object"&&e!==null?e:{}}const $i=["allowfullscreen","allowpaymentrequest","async","autofocus","autoplay","checked","controls","default","defer","disabled","formnovalidate","hidden","inert","ismap","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected"];[...$i];function Qt(e,t,r){const o=e.$$.props[t];o!==void 0&&(e.$$.bound[o]=r,r(e.$$.ctx[o]))}function ot(e){e&&e.c()}function De(e,t,r){const{fragment:o,after_update:n}=e.$$;o&&o.m(t,r),Me(()=>{const i=e.$$.on_mount.map(po).filter(Ee);e.$$.on_destroy?e.$$.on_destroy.push(...i):pe(i),e.$$.on_mount=[]}),n.forEach(Me)}function je(e,t){const r=e.$$;r.fragment!==null&&(Ji(r.after_update),pe(r.on_destroy),r.fragment&&r.fragment.d(t),r.on_destroy=r.fragment=null,r.ctx=[])}function ea(e,t){e.$$.dirty[0]===-1&&(He.push(e),To(),e.$$.dirty.fill(0)),e.$$.dirty[t/31|0]|=1<<t%31}function At(e,t,r,o,n,i,a,l=[-1]){const u=Ke;Ye(e);const s=e.$$={fragment:null,ctx:[],props:i,update:Z,not_equal:n,bound:Br(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(t.context||(u?u.$$.context:[])),callbacks:Br(),dirty:l,skip_bound:!1,root:t.target||u.$$.root};a&&a(s.root);let c=!1;if(s.ctx=r?r(e,t.props||{},(f,d,...g)=>{const h=g.length?g[0]:d;return s.ctx&&n(s.ctx[f],s.ctx[f]=h)&&(!s.skip_bound&&s.bound[f]&&s.bound[f](h),c&&ea(e,f)),d}):[],s.update(),c=!0,pe(s.before_update),s.fragment=o?o(s.ctx):!1,t.target){if(t.hydrate){const f=ji(t.target);s.fragment&&s.fragment.l(f),f.forEach(k)}else s.fragment&&s.fragment.c();t.intro&&K(e.$$.fragment),De(e,t.target,t.anchor),Bo()}Ye(u)}class Tt{$$=void 0;$$set=void 0;$destroy(){je(this,1),this.$destroy=Z}$on(t,r){if(!Ee(r))return Z;const o=this.$$.callbacks[t]||(this.$$.callbacks[t]=[]);return o.push(r),()=>{const n=o.indexOf(r);n!==-1&&o.splice(n,1)}}$set(t){this.$$set&&!Ci(t)&&(this.$$.skip_bound=!0,this.$$set(t),this.$$.skip_bound=!1)}}const ta="4";typeof window<"u"&&(window.__svelte||(window.__svelte={v:new Set})).v.add(ta);const Pe=[];function ra(e,t){return{subscribe:me(e,t).subscribe}}function me(e,t=Z){let r;const o=new Set;function n(l){if(rt(e,l)&&(e=l,r)){const u=!Pe.length;for(const s of o)s[1](),Pe.push(s,e);if(u){for(let s=0;s<Pe.length;s+=2)Pe[s][0](Pe[s+1]);Pe.length=0}}}function i(l){n(l(e))}function a(l,u=Z){const s=[l,u];return o.add(s),o.size===1&&(r=t(n,i)||Z),l(e),()=>{o.delete(s),o.size===0&&r&&(r(),r=null)}}return{set:n,update:i,subscribe:a}}function Ue(e,t,r){const o=!Array.isArray(e),n=o?[e]:e;if(!n.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const i=t.length<2;return ra(r,(a,l)=>{let u=!1;const s=[];let c=0,f=Z;const d=()=>{if(c)return;f();const h=t(o?s[0]:s,a,l);i?a(h):f=Ee(h)?h:Z},g=n.map((h,_)=>go(h,v=>{s[_]=v,c&=~(1<<_),u&&d()},()=>{c|=1<<_}));return u=!0,d(),function(){pe(g),f(),u=!1}})}const oa=""+new URL("spaces-a79177ad.svg",import.meta.url).href;var na=function(t){return ia(t)&&!aa(t)};function ia(e){return!!e&&typeof e=="object"}function aa(e){var t=Object.prototype.toString.call(e);return t==="[object RegExp]"||t==="[object Date]"||ca(e)}var sa=typeof Symbol=="function"&&Symbol.for,la=sa?Symbol.for("react.element"):60103;function ca(e){return e.$$typeof===la}function ua(e){return Array.isArray(e)?[]:{}}function $e(e,t){return t.clone!==!1&&t.isMergeableObject(e)?Ie(ua(e),e,t):e}function fa(e,t,r){return e.concat(t).map(function(o){return $e(o,r)})}function da(e,t){if(!t.customMerge)return Ie;var r=t.customMerge(e);return typeof r=="function"?r:Ie}function ha(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter(function(t){return Object.propertyIsEnumerable.call(e,t)}):[]}function Hr(e){return Object.keys(e).concat(ha(e))}function Po(e,t){try{return t in e}catch{return!1}}function pa(e,t){return Po(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))}function ga(e,t,r){var o={};return r.isMergeableObject(e)&&Hr(e).forEach(function(n){o[n]=$e(e[n],r)}),Hr(t).forEach(function(n){pa(e,n)||(Po(e,n)&&r.isMergeableObject(t[n])?o[n]=da(n,r)(e[n],t[n],r):o[n]=$e(t[n],r))}),o}function Ie(e,t,r){r=r||{},r.arrayMerge=r.arrayMerge||fa,r.isMergeableObject=r.isMergeableObject||na,r.cloneUnlessOtherwiseSpecified=$e;var o=Array.isArray(t),n=Array.isArray(e),i=o===n;return i?o?r.arrayMerge(e,t,r):ga(e,t,r):$e(t,r)}Ie.all=function(t,r){if(!Array.isArray(t))throw new Error("first argument should be an array");return t.reduce(function(o,n){return Ie(o,n,r)},{})};var _a=Ie,ma=_a;const ba=lo(ma);var Kt=function(e,t){return Kt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,o){r.__proto__=o}||function(r,o){for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(r[n]=o[n])},Kt(e,t)};function Bt(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");Kt(e,t);function r(){this.constructor=e}e.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}var L=function(){return L=Object.assign||function(t){for(var r,o=1,n=arguments.length;o<n;o++){r=arguments[o];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t},L.apply(this,arguments)};function Rt(e,t,r){if(r||arguments.length===2)for(var o=0,n=t.length,i;o<n;o++)(i||!(o in t))&&(i||(i=Array.prototype.slice.call(t,0,o)),i[o]=t[o]);return e.concat(i||Array.prototype.slice.call(t))}var O;(function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"})(O||(O={}));var G;(function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"})(G||(G={}));var Le;(function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"})(Le||(Le={}));function Nr(e){return e.type===G.literal}function ya(e){return e.type===G.argument}function Ho(e){return e.type===G.number}function No(e){return e.type===G.date}function Co(e){return e.type===G.time}function Oo(e){return e.type===G.select}function Mo(e){return e.type===G.plural}function va(e){return e.type===G.pound}function Io(e){return e.type===G.tag}function Lo(e){return!!(e&&typeof e=="object"&&e.type===Le.number)}function $t(e){return!!(e&&typeof e=="object"&&e.type===Le.dateTime)}var Ro=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,wa=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;function xa(e){var t={};return e.replace(wa,function(r){var o=r.length;switch(r[0]){case"G":t.era=o===4?"long":o===5?"narrow":"short";break;case"y":t.year=o===2?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw new RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw new RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":t.month=["numeric","2-digit","short","long","narrow"][o-1];break;case"w":case"W":throw new RangeError("`w/W` (week) patterns are not supported");case"d":t.day=["numeric","2-digit"][o-1];break;case"D":case"F":case"g":throw new RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":t.weekday=o===4?"short":o===5?"narrow":"short";break;case"e":if(o<4)throw new RangeError("`e..eee` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][o-4];break;case"c":if(o<4)throw new RangeError("`c..ccc` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][o-4];break;case"a":t.hour12=!0;break;case"b":case"B":throw new RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":t.hourCycle="h12",t.hour=["numeric","2-digit"][o-1];break;case"H":t.hourCycle="h23",t.hour=["numeric","2-digit"][o-1];break;case"K":t.hourCycle="h11",t.hour=["numeric","2-digit"][o-1];break;case"k":t.hourCycle="h24",t.hour=["numeric","2-digit"][o-1];break;case"j":case"J":case"C":throw new RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":t.minute=["numeric","2-digit"][o-1];break;case"s":t.second=["numeric","2-digit"][o-1];break;case"S":case"A":throw new RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":t.timeZoneName=o<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw new RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),t}var Ea=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i;function Sa(e){if(e.length===0)throw new Error("Number skeleton cannot be empty");for(var t=e.split(Ea).filter(function(d){return d.length>0}),r=[],o=0,n=t;o<n.length;o++){var i=n[o],a=i.split("/");if(a.length===0)throw new Error("Invalid number skeleton");for(var l=a[0],u=a.slice(1),s=0,c=u;s<c.length;s++){var f=c[s];if(f.length===0)throw new Error("Invalid number skeleton")}r.push({stem:l,options:u})}return r}function ka(e){return e.replace(/^(.*?)-/,"")}var Cr=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,zo=/^(@+)?(\+|#+)?[rs]?$/g,Aa=/(\*)(0+)|(#+)(0+)|(0+)/g,Do=/^(0+)$/;function Or(e){var t={};return e[e.length-1]==="r"?t.roundingPriority="morePrecision":e[e.length-1]==="s"&&(t.roundingPriority="lessPrecision"),e.replace(zo,function(r,o,n){return typeof n!="string"?(t.minimumSignificantDigits=o.length,t.maximumSignificantDigits=o.length):n==="+"?t.minimumSignificantDigits=o.length:o[0]==="#"?t.maximumSignificantDigits=o.length:(t.minimumSignificantDigits=o.length,t.maximumSignificantDigits=o.length+(typeof n=="string"?n.length:0)),""}),t}function jo(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function Ta(e){var t;if(e[0]==="E"&&e[1]==="E"?(t={notation:"engineering"},e=e.slice(2)):e[0]==="E"&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if(r==="+!"?(t.signDisplay="always",e=e.slice(2)):r==="+?"&&(t.signDisplay="exceptZero",e=e.slice(2)),!Do.test(e))throw new Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}function Mr(e){var t={},r=jo(e);return r||t}function Ba(e){for(var t={},r=0,o=e;r<o.length;r++){var n=o[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=ka(n.options[0]);continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=L(L(L({},t),{notation:"scientific"}),n.options.reduce(function(u,s){return L(L({},u),Mr(s))},{}));continue;case"engineering":t=L(L(L({},t),{notation:"engineering"}),n.options.reduce(function(u,s){return L(L({},u),Mr(s))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"integer-width":if(n.options.length>1)throw new RangeError("integer-width stems only accept a single optional option");n.options[0].replace(Aa,function(u,s,c,f,d,g){if(s)t.minimumIntegerDigits=c.length;else{if(f&&d)throw new Error("We currently do not support maximum integer digits");if(g)throw new Error("We currently do not support exact integer digits")}return""});continue}if(Do.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(Cr.test(n.stem)){if(n.options.length>1)throw new RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(Cr,function(u,s,c,f,d,g){return c==="*"?t.minimumFractionDigits=s.length:f&&f[0]==="#"?t.maximumFractionDigits=f.length:d&&g?(t.minimumFractionDigits=d.length,t.maximumFractionDigits=d.length+g.length):(t.minimumFractionDigits=s.length,t.maximumFractionDigits=s.length),""});var i=n.options[0];i==="w"?t=L(L({},t),{trailingZeroDisplay:"stripIfInteger"}):i&&(t=L(L({},t),Or(i)));continue}if(zo.test(n.stem)){t=L(L({},t),Or(n.stem));continue}var a=jo(n.stem);a&&(t=L(L({},t),a));var l=Ta(n.stem);l&&(t=L(L({},t),l))}return t}var ct={AX:["H"],BQ:["H"],CP:["H"],CZ:["H"],DK:["H"],FI:["H"],ID:["H"],IS:["H"],ML:["H"],NE:["H"],RU:["H"],SE:["H"],SJ:["H"],SK:["H"],AS:["h","H"],BT:["h","H"],DJ:["h","H"],ER:["h","H"],GH:["h","H"],IN:["h","H"],LS:["h","H"],PG:["h","H"],PW:["h","H"],SO:["h","H"],TO:["h","H"],VU:["h","H"],WS:["h","H"],"001":["H","h"],AL:["h","H","hB"],TD:["h","H","hB"],"ca-ES":["H","h","hB"],CF:["H","h","hB"],CM:["H","h","hB"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],LU:["H","h","hB"],NP:["H","h","hB"],PF:["H","h","hB"],SC:["H","h","hB"],SM:["H","h","hB"],SN:["H","h","hB"],TF:["H","h","hB"],VA:["H","h","hB"],CY:["h","H","hb","hB"],GR:["h","H","hb","hB"],CO:["h","H","hB","hb"],DO:["h","H","hB","hb"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],NA:["h","H","hB","hb"],PA:["h","H","hB","hb"],PR:["h","H","hB","hb"],VE:["h","H","hB","hb"],AC:["H","h","hb","hB"],AI:["H","h","hb","hB"],BW:["H","h","hb","hB"],BZ:["H","h","hb","hB"],CC:["H","h","hb","hB"],CK:["H","h","hb","hB"],CX:["H","h","hb","hB"],DG:["H","h","hb","hB"],FK:["H","h","hb","hB"],GB:["H","h","hb","hB"],GG:["H","h","hb","hB"],GI:["H","h","hb","hB"],IE:["H","h","hb","hB"],IM:["H","h","hb","hB"],IO:["H","h","hb","hB"],JE:["H","h","hb","hB"],LT:["H","h","hb","hB"],MK:["H","h","hb","hB"],MN:["H","h","hb","hB"],MS:["H","h","hb","hB"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],PN:["H","h","hb","hB"],SH:["H","h","hb","hB"],SX:["H","h","hb","hB"],TA:["H","h","hb","hB"],ZA:["H","h","hb","hB"],"af-ZA":["H","h","hB","hb"],AR:["H","h","hB","hb"],CL:["H","h","hB","hb"],CR:["H","h","hB","hb"],CU:["H","h","hB","hb"],EA:["H","h","hB","hb"],"es-BO":["H","h","hB","hb"],"es-BR":["H","h","hB","hb"],"es-EC":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"es-PE":["H","h","hB","hb"],GT:["H","h","hB","hb"],HN:["H","h","hB","hb"],IC:["H","h","hB","hb"],KG:["H","h","hB","hb"],KM:["H","h","hB","hb"],LK:["H","h","hB","hb"],MA:["H","h","hB","hb"],MX:["H","h","hB","hb"],NI:["H","h","hB","hb"],PY:["H","h","hB","hb"],SV:["H","h","hB","hb"],UY:["H","h","hB","hb"],JP:["H","h","K"],AD:["H","hB"],AM:["H","hB"],AO:["H","hB"],AT:["H","hB"],AW:["H","hB"],BE:["H","hB"],BF:["H","hB"],BJ:["H","hB"],BL:["H","hB"],BR:["H","hB"],CG:["H","hB"],CI:["H","hB"],CV:["H","hB"],DE:["H","hB"],EE:["H","hB"],FR:["H","hB"],GA:["H","hB"],GF:["H","hB"],GN:["H","hB"],GP:["H","hB"],GW:["H","hB"],HR:["H","hB"],IL:["H","hB"],IT:["H","hB"],KZ:["H","hB"],MC:["H","hB"],MD:["H","hB"],MF:["H","hB"],MQ:["H","hB"],MZ:["H","hB"],NC:["H","hB"],NL:["H","hB"],PM:["H","hB"],PT:["H","hB"],RE:["H","hB"],RO:["H","hB"],SI:["H","hB"],SR:["H","hB"],ST:["H","hB"],TG:["H","hB"],TR:["H","hB"],WF:["H","hB"],YT:["H","hB"],BD:["h","hB","H"],PK:["h","hB","H"],AZ:["H","hB","h"],BA:["H","hB","h"],BG:["H","hB","h"],CH:["H","hB","h"],GE:["H","hB","h"],LI:["H","hB","h"],ME:["H","hB","h"],RS:["H","hB","h"],UA:["H","hB","h"],UZ:["H","hB","h"],XK:["H","hB","h"],AG:["h","hb","H","hB"],AU:["h","hb","H","hB"],BB:["h","hb","H","hB"],BM:["h","hb","H","hB"],BS:["h","hb","H","hB"],CA:["h","hb","H","hB"],DM:["h","hb","H","hB"],"en-001":["h","hb","H","hB"],FJ:["h","hb","H","hB"],FM:["h","hb","H","hB"],GD:["h","hb","H","hB"],GM:["h","hb","H","hB"],GU:["h","hb","H","hB"],GY:["h","hb","H","hB"],JM:["h","hb","H","hB"],KI:["h","hb","H","hB"],KN:["h","hb","H","hB"],KY:["h","hb","H","hB"],LC:["h","hb","H","hB"],LR:["h","hb","H","hB"],MH:["h","hb","H","hB"],MP:["h","hb","H","hB"],MW:["h","hb","H","hB"],NZ:["h","hb","H","hB"],SB:["h","hb","H","hB"],SG:["h","hb","H","hB"],SL:["h","hb","H","hB"],SS:["h","hb","H","hB"],SZ:["h","hb","H","hB"],TC:["h","hb","H","hB"],TT:["h","hb","H","hB"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],VC:["h","hb","H","hB"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],ZM:["h","hb","H","hB"],BO:["H","hB","h","hb"],EC:["H","hB","h","hb"],ES:["H","hB","h","hb"],GQ:["H","hB","h","hb"],PE:["H","hB","h","hb"],AE:["h","hB","hb","H"],"ar-001":["h","hB","hb","H"],BH:["h","hB","hb","H"],DZ:["h","hB","hb","H"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],HK:["h","hB","hb","H"],IQ:["h","hB","hb","H"],JO:["h","hB","hb","H"],KW:["h","hB","hb","H"],LB:["h","hB","hb","H"],LY:["h","hB","hb","H"],MO:["h","hB","hb","H"],MR:["h","hB","hb","H"],OM:["h","hB","hb","H"],PH:["h","hB","hb","H"],PS:["h","hB","hb","H"],QA:["h","hB","hb","H"],SA:["h","hB","hb","H"],SD:["h","hB","hb","H"],SY:["h","hB","hb","H"],TN:["h","hB","hb","H"],YE:["h","hB","hb","H"],AF:["H","hb","hB","h"],LA:["H","hb","hB","h"],CN:["H","hB","hb","h"],LV:["H","hB","hb","h"],TL:["H","hB","hb","h"],"zu-ZA":["H","hB","hb","h"],CD:["hB","H"],IR:["hB","H"],"hi-IN":["hB","h","H"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"te-IN":["hB","h","H"],KH:["hB","h","H","hb"],"ta-IN":["hB","h","hb","H"],BN:["hb","hB","h","H"],MY:["hb","hB","h","H"],ET:["hB","hb","h","H"],"gu-IN":["hB","hb","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],TW:["hB","hb","h","H"],KE:["hB","hb","H","h"],MM:["hB","hb","H","h"],TZ:["hB","hb","H","h"],UG:["hB","hb","H","h"]};function Pa(e,t){for(var r="",o=0;o<e.length;o++){var n=e.charAt(o);if(n==="j"){for(var i=0;o+1<e.length&&e.charAt(o+1)===n;)i++,o++;var a=1+(i&1),l=i<2?1:3+(i>>1),u="a",s=Ha(t);for((s=="H"||s=="k")&&(l=0);l-- >0;)r+=u;for(;a-- >0;)r=s+r}else n==="J"?r+="H":r+=n}return r}function Ha(e){var t=e.hourCycle;if(t===void 0&&e.hourCycles&&e.hourCycles.length&&(t=e.hourCycles[0]),t)switch(t){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw new Error("Invalid hourCycle")}var r=e.language,o;r!=="root"&&(o=e.maximize().region);var n=ct[o||""]||ct[r||""]||ct["".concat(r,"-001")]||ct["001"];return n[0]}var zt,Na=new RegExp("^".concat(Ro.source,"*")),Ca=new RegExp("".concat(Ro.source,"*$"));function I(e,t){return{start:e,end:t}}var Oa=!!String.prototype.startsWith,Ma=!!String.fromCodePoint,Ia=!!Object.fromEntries,La=!!String.prototype.codePointAt,Ra=!!String.prototype.trimStart,za=!!String.prototype.trimEnd,Da=!!Number.isSafeInteger,ja=Da?Number.isSafeInteger:function(e){return typeof e=="number"&&isFinite(e)&&Math.floor(e)===e&&Math.abs(e)<=9007199254740991},er=!0;try{var Ua=Go("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");er=((zt=Ua.exec("a"))===null||zt===void 0?void 0:zt[0])==="a"}catch{er=!1}var Ir=Oa?function(t,r,o){return t.startsWith(r,o)}:function(t,r,o){return t.slice(o,o+r.length)===r},tr=Ma?String.fromCodePoint:function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var o="",n=t.length,i=0,a;n>i;){if(a=t[i++],a>1114111)throw RangeError(a+" is not a valid code point");o+=a<65536?String.fromCharCode(a):String.fromCharCode(((a-=65536)>>10)+55296,a%1024+56320)}return o},Lr=Ia?Object.fromEntries:function(t){for(var r={},o=0,n=t;o<n.length;o++){var i=n[o],a=i[0],l=i[1];r[a]=l}return r},Uo=La?function(t,r){return t.codePointAt(r)}:function(t,r){var o=t.length;if(!(r<0||r>=o)){var n=t.charCodeAt(r),i;return n<55296||n>56319||r+1===o||(i=t.charCodeAt(r+1))<56320||i>57343?n:(n-55296<<10)+(i-56320)+65536}},Ga=Ra?function(t){return t.trimStart()}:function(t){return t.replace(Na,"")},Fa=za?function(t){return t.trimEnd()}:function(t){return t.replace(Ca,"")};function Go(e,t){return new RegExp(e,t)}var rr;if(er){var Rr=Go("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");rr=function(t,r){var o;Rr.lastIndex=r;var n=Rr.exec(t);return(o=n[1])!==null&&o!==void 0?o:""}}else rr=function(t,r){for(var o=[];;){var n=Uo(t,r);if(n===void 0||Fo(n)||Wa(n))break;o.push(n),r+=n>=65536?2:1}return tr.apply(void 0,o)};var qa=function(){function e(t,r){r===void 0&&(r={}),this.message=t,this.position={offset:0,line:1,column:1},this.ignoreTag=!!r.ignoreTag,this.locale=r.locale,this.requiresOtherClause=!!r.requiresOtherClause,this.shouldParseSkeletons=!!r.shouldParseSkeletons}return e.prototype.parse=function(){if(this.offset()!==0)throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(t,r,o){for(var n=[];!this.isEOF();){var i=this.char();if(i===123){var a=this.parseArgument(t,o);if(a.err)return a;n.push(a.val)}else{if(i===125&&t>0)break;if(i===35&&(r==="plural"||r==="selectordinal")){var l=this.clonePosition();this.bump(),n.push({type:G.pound,location:I(l,this.clonePosition())})}else if(i===60&&!this.ignoreTag&&this.peek()===47){if(o)break;return this.error(O.UNMATCHED_CLOSING_TAG,I(this.clonePosition(),this.clonePosition()))}else if(i===60&&!this.ignoreTag&&or(this.peek()||0)){var a=this.parseTag(t,r);if(a.err)return a;n.push(a.val)}else{var a=this.parseLiteral(t,r);if(a.err)return a;n.push(a.val)}}}return{val:n,err:null}},e.prototype.parseTag=function(t,r){var o=this.clonePosition();this.bump();var n=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:G.literal,value:"<".concat(n,"/>"),location:I(o,this.clonePosition())},err:null};if(this.bumpIf(">")){var i=this.parseMessage(t+1,r,!0);if(i.err)return i;var a=i.val,l=this.clonePosition();if(this.bumpIf("</")){if(this.isEOF()||!or(this.char()))return this.error(O.INVALID_TAG,I(l,this.clonePosition()));var u=this.clonePosition(),s=this.parseTagName();return n!==s?this.error(O.UNMATCHED_CLOSING_TAG,I(u,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">")?{val:{type:G.tag,value:n,children:a,location:I(o,this.clonePosition())},err:null}:this.error(O.INVALID_TAG,I(l,this.clonePosition())))}else return this.error(O.UNCLOSED_TAG,I(o,this.clonePosition()))}else return this.error(O.INVALID_TAG,I(o,this.clonePosition()))},e.prototype.parseTagName=function(){var t=this.offset();for(this.bump();!this.isEOF()&&Xa(this.char());)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(t,r){for(var o=this.clonePosition(),n="";;){var i=this.tryParseQuote(r);if(i){n+=i;continue}var a=this.tryParseUnquoted(t,r);if(a){n+=a;continue}var l=this.tryParseLeftAngleBracket();if(l){n+=l;continue}break}var u=I(o,this.clonePosition());return{val:{type:G.literal,value:n,location:u},err:null}},e.prototype.tryParseLeftAngleBracket=function(){return!this.isEOF()&&this.char()===60&&(this.ignoreTag||!Va(this.peek()||0))?(this.bump(),"<"):null},e.prototype.tryParseQuote=function(t){if(this.isEOF()||this.char()!==39)return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if(t==="plural"||t==="selectordinal")break;return null;default:return null}this.bump();var r=[this.char()];for(this.bump();!this.isEOF();){var o=this.char();if(o===39)if(this.peek()===39)r.push(39),this.bump();else{this.bump();break}else r.push(o);this.bump()}return tr.apply(void 0,r)},e.prototype.tryParseUnquoted=function(t,r){if(this.isEOF())return null;var o=this.char();return o===60||o===123||o===35&&(r==="plural"||r==="selectordinal")||o===125&&t>0?null:(this.bump(),tr(o))},e.prototype.parseArgument=function(t,r){var o=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(O.EXPECT_ARGUMENT_CLOSING_BRACE,I(o,this.clonePosition()));if(this.char()===125)return this.bump(),this.error(O.EMPTY_ARGUMENT,I(o,this.clonePosition()));var n=this.parseIdentifierIfPossible().value;if(!n)return this.error(O.MALFORMED_ARGUMENT,I(o,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(O.EXPECT_ARGUMENT_CLOSING_BRACE,I(o,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:G.argument,value:n,location:I(o,this.clonePosition())},err:null};case 44:return this.bump(),this.bumpSpace(),this.isEOF()?this.error(O.EXPECT_ARGUMENT_CLOSING_BRACE,I(o,this.clonePosition())):this.parseArgumentOptions(t,r,n,o);default:return this.error(O.MALFORMED_ARGUMENT,I(o,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var t=this.clonePosition(),r=this.offset(),o=rr(this.message,r),n=r+o.length;this.bumpTo(n);var i=this.clonePosition(),a=I(t,i);return{value:o,location:a}},e.prototype.parseArgumentOptions=function(t,r,o,n){var i,a=this.clonePosition(),l=this.parseIdentifierIfPossible().value,u=this.clonePosition();switch(l){case"":return this.error(O.EXPECT_ARGUMENT_TYPE,I(a,u));case"number":case"date":case"time":{this.bumpSpace();var s=null;if(this.bumpIf(",")){this.bumpSpace();var c=this.clonePosition(),f=this.parseSimpleArgStyleIfPossible();if(f.err)return f;var d=Fa(f.val);if(d.length===0)return this.error(O.EXPECT_ARGUMENT_STYLE,I(this.clonePosition(),this.clonePosition()));var g=I(c,this.clonePosition());s={style:d,styleLocation:g}}var h=this.tryParseArgumentClose(n);if(h.err)return h;var _=I(n,this.clonePosition());if(s&&Ir(s?.style,"::",0)){var v=Ga(s.style.slice(2));if(l==="number"){var f=this.parseNumberSkeletonFromString(v,s.styleLocation);return f.err?f:{val:{type:G.number,value:o,location:_,style:f.val},err:null}}else{if(v.length===0)return this.error(O.EXPECT_DATE_TIME_SKELETON,_);var x=v;this.locale&&(x=Pa(v,this.locale));var d={type:Le.dateTime,pattern:x,location:s.styleLocation,parsedOptions:this.shouldParseSkeletons?xa(x):{}},E=l==="date"?G.date:G.time;return{val:{type:E,value:o,location:_,style:d},err:null}}}return{val:{type:l==="number"?G.number:l==="date"?G.date:G.time,value:o,location:_,style:(i=s?.style)!==null&&i!==void 0?i:null},err:null}}case"plural":case"selectordinal":case"select":{var p=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(O.EXPECT_SELECT_ARGUMENT_OPTIONS,I(p,L({},p)));this.bumpSpace();var w=this.parseIdentifierIfPossible(),T=0;if(l!=="select"&&w.value==="offset"){if(!this.bumpIf(":"))return this.error(O.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,I(this.clonePosition(),this.clonePosition()));this.bumpSpace();var f=this.tryParseDecimalInteger(O.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,O.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(f.err)return f;this.bumpSpace(),w=this.parseIdentifierIfPossible(),T=f.val}var C=this.tryParsePluralOrSelectOptions(t,l,r,w);if(C.err)return C;var h=this.tryParseArgumentClose(n);if(h.err)return h;var S=I(n,this.clonePosition());return l==="select"?{val:{type:G.select,value:o,options:Lr(C.val),location:S},err:null}:{val:{type:G.plural,value:o,options:Lr(C.val),offset:T,pluralType:l==="plural"?"cardinal":"ordinal",location:S},err:null}}default:return this.error(O.INVALID_ARGUMENT_TYPE,I(a,u))}},e.prototype.tryParseArgumentClose=function(t){return this.isEOF()||this.char()!==125?this.error(O.EXPECT_ARGUMENT_CLOSING_BRACE,I(t,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var t=0,r=this.clonePosition();!this.isEOF();){var o=this.char();switch(o){case 39:{this.bump();var n=this.clonePosition();if(!this.bumpUntil("'"))return this.error(O.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,I(n,this.clonePosition()));this.bump();break}case 123:{t+=1,this.bump();break}case 125:{if(t>0)t-=1;else return{val:this.message.slice(r.offset,this.offset()),err:null};break}default:this.bump();break}}return{val:this.message.slice(r.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(t,r){var o=[];try{o=Sa(t)}catch{return this.error(O.INVALID_NUMBER_SKELETON,r)}return{val:{type:Le.number,tokens:o,location:r,parsedOptions:this.shouldParseSkeletons?Ba(o):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(t,r,o,n){for(var i,a=!1,l=[],u=new Set,s=n.value,c=n.location;;){if(s.length===0){var f=this.clonePosition();if(r!=="select"&&this.bumpIf("=")){var d=this.tryParseDecimalInteger(O.EXPECT_PLURAL_ARGUMENT_SELECTOR,O.INVALID_PLURAL_ARGUMENT_SELECTOR);if(d.err)return d;c=I(f,this.clonePosition()),s=this.message.slice(f.offset,this.offset())}else break}if(u.has(s))return this.error(r==="select"?O.DUPLICATE_SELECT_ARGUMENT_SELECTOR:O.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,c);s==="other"&&(a=!0),this.bumpSpace();var g=this.clonePosition();if(!this.bumpIf("{"))return this.error(r==="select"?O.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:O.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,I(this.clonePosition(),this.clonePosition()));var h=this.parseMessage(t+1,r,o);if(h.err)return h;var _=this.tryParseArgumentClose(g);if(_.err)return _;l.push([s,{value:h.val,location:I(g,this.clonePosition())}]),u.add(s),this.bumpSpace(),i=this.parseIdentifierIfPossible(),s=i.value,c=i.location}return l.length===0?this.error(r==="select"?O.EXPECT_SELECT_ARGUMENT_SELECTOR:O.EXPECT_PLURAL_ARGUMENT_SELECTOR,I(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!a?this.error(O.MISSING_OTHER_CLAUSE,I(this.clonePosition(),this.clonePosition())):{val:l,err:null}},e.prototype.tryParseDecimalInteger=function(t,r){var o=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(o=-1);for(var i=!1,a=0;!this.isEOF();){var l=this.char();if(l>=48&&l<=57)i=!0,a=a*10+(l-48),this.bump();else break}var u=I(n,this.clonePosition());return i?(a*=o,ja(a)?{val:a,err:null}:this.error(r,u)):this.error(t,u)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var t=this.position.offset;if(t>=this.message.length)throw Error("out of bound");var r=Uo(this.message,t);if(r===void 0)throw Error("Offset ".concat(t," is at invalid UTF-16 code unit boundary"));return r},e.prototype.error=function(t,r){return{val:null,err:{kind:t,message:this.message,location:r}}},e.prototype.bump=function(){if(!this.isEOF()){var t=this.char();t===10?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=t<65536?1:2)}},e.prototype.bumpIf=function(t){if(Ir(this.message,t,this.offset())){for(var r=0;r<t.length;r++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(t){var r=this.offset(),o=this.message.indexOf(t,r);return o>=0?(this.bumpTo(o),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(t){if(this.offset()>t)throw Error("targetOffset ".concat(t," must be greater than or equal to the current offset ").concat(this.offset()));for(t=Math.min(t,this.message.length);;){var r=this.offset();if(r===t)break;if(r>t)throw Error("targetOffset ".concat(t," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&Fo(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var t=this.char(),r=this.offset(),o=this.message.charCodeAt(r+(t>=65536?2:1));return o??null},e}();function or(e){return e>=97&&e<=122||e>=65&&e<=90}function Va(e){return or(e)||e===47}function Xa(e){return e===45||e===46||e>=48&&e<=57||e===95||e>=97&&e<=122||e>=65&&e<=90||e==183||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039}function Fo(e){return e>=9&&e<=13||e===32||e===133||e>=8206&&e<=8207||e===8232||e===8233}function Wa(e){return e>=33&&e<=35||e===36||e>=37&&e<=39||e===40||e===41||e===42||e===43||e===44||e===45||e>=46&&e<=47||e>=58&&e<=59||e>=60&&e<=62||e>=63&&e<=64||e===91||e===92||e===93||e===94||e===96||e===123||e===124||e===125||e===126||e===161||e>=162&&e<=165||e===166||e===167||e===169||e===171||e===172||e===174||e===176||e===177||e===182||e===187||e===191||e===215||e===247||e>=8208&&e<=8213||e>=8214&&e<=8215||e===8216||e===8217||e===8218||e>=8219&&e<=8220||e===8221||e===8222||e===8223||e>=8224&&e<=8231||e>=8240&&e<=8248||e===8249||e===8250||e>=8251&&e<=8254||e>=8257&&e<=8259||e===8260||e===8261||e===8262||e>=8263&&e<=8273||e===8274||e===8275||e>=8277&&e<=8286||e>=8592&&e<=8596||e>=8597&&e<=8601||e>=8602&&e<=8603||e>=8604&&e<=8607||e===8608||e>=8609&&e<=8610||e===8611||e>=8612&&e<=8613||e===8614||e>=8615&&e<=8621||e===8622||e>=8623&&e<=8653||e>=8654&&e<=8655||e>=8656&&e<=8657||e===8658||e===8659||e===8660||e>=8661&&e<=8691||e>=8692&&e<=8959||e>=8960&&e<=8967||e===8968||e===8969||e===8970||e===8971||e>=8972&&e<=8991||e>=8992&&e<=8993||e>=8994&&e<=9e3||e===9001||e===9002||e>=9003&&e<=9083||e===9084||e>=9085&&e<=9114||e>=9115&&e<=9139||e>=9140&&e<=9179||e>=9180&&e<=9185||e>=9186&&e<=9254||e>=9255&&e<=9279||e>=9280&&e<=9290||e>=9291&&e<=9311||e>=9472&&e<=9654||e===9655||e>=9656&&e<=9664||e===9665||e>=9666&&e<=9719||e>=9720&&e<=9727||e>=9728&&e<=9838||e===9839||e>=9840&&e<=10087||e===10088||e===10089||e===10090||e===10091||e===10092||e===10093||e===10094||e===10095||e===10096||e===10097||e===10098||e===10099||e===10100||e===10101||e>=10132&&e<=10175||e>=10176&&e<=10180||e===10181||e===10182||e>=10183&&e<=10213||e===10214||e===10215||e===10216||e===10217||e===10218||e===10219||e===10220||e===10221||e===10222||e===10223||e>=10224&&e<=10239||e>=10240&&e<=10495||e>=10496&&e<=10626||e===10627||e===10628||e===10629||e===10630||e===10631||e===10632||e===10633||e===10634||e===10635||e===10636||e===10637||e===10638||e===10639||e===10640||e===10641||e===10642||e===10643||e===10644||e===10645||e===10646||e===10647||e===10648||e>=10649&&e<=10711||e===10712||e===10713||e===10714||e===10715||e>=10716&&e<=10747||e===10748||e===10749||e>=10750&&e<=11007||e>=11008&&e<=11055||e>=11056&&e<=11076||e>=11077&&e<=11078||e>=11079&&e<=11084||e>=11085&&e<=11123||e>=11124&&e<=11125||e>=11126&&e<=11157||e===11158||e>=11159&&e<=11263||e>=11776&&e<=11777||e===11778||e===11779||e===11780||e===11781||e>=11782&&e<=11784||e===11785||e===11786||e===11787||e===11788||e===11789||e>=11790&&e<=11798||e===11799||e>=11800&&e<=11801||e===11802||e===11803||e===11804||e===11805||e>=11806&&e<=11807||e===11808||e===11809||e===11810||e===11811||e===11812||e===11813||e===11814||e===11815||e===11816||e===11817||e>=11818&&e<=11822||e===11823||e>=11824&&e<=11833||e>=11834&&e<=11835||e>=11836&&e<=11839||e===11840||e===11841||e===11842||e>=11843&&e<=11855||e>=11856&&e<=11857||e===11858||e>=11859&&e<=11903||e>=12289&&e<=12291||e===12296||e===12297||e===12298||e===12299||e===12300||e===12301||e===12302||e===12303||e===12304||e===12305||e>=12306&&e<=12307||e===12308||e===12309||e===12310||e===12311||e===12312||e===12313||e===12314||e===12315||e===12316||e===12317||e>=12318&&e<=12319||e===12320||e===12336||e===64830||e===64831||e>=65093&&e<=65094}function nr(e){e.forEach(function(t){if(delete t.location,Oo(t)||Mo(t))for(var r in t.options)delete t.options[r].location,nr(t.options[r].value);else Ho(t)&&Lo(t.style)||(No(t)||Co(t))&&$t(t.style)?delete t.style.location:Io(t)&&nr(t.children)})}function Za(e,t){t===void 0&&(t={}),t=L({shouldParseSkeletons:!0,requiresOtherClause:!0},t);var r=new qa(e,t).parse();if(r.err){var o=SyntaxError(O[r.err.kind]);throw o.location=r.err.location,o.originalMessage=r.err.message,o}return t?.captureLocation||nr(r.val),r.val}function Dt(e,t){var r=t&&t.cache?t.cache:es,o=t&&t.serializer?t.serializer:$a,n=t&&t.strategy?t.strategy:Ya;return n(e,{cache:r,serializer:o})}function Ja(e){return e==null||typeof e=="number"||typeof e=="boolean"}function qo(e,t,r,o){var n=Ja(o)?o:r(o),i=t.get(n);return typeof i>"u"&&(i=e.call(this,o),t.set(n,i)),i}function Vo(e,t,r){var o=Array.prototype.slice.call(arguments,3),n=r(o),i=t.get(n);return typeof i>"u"&&(i=e.apply(this,o),t.set(n,i)),i}function hr(e,t,r,o,n){return r.bind(t,e,o,n)}function Ya(e,t){var r=e.length===1?qo:Vo;return hr(e,this,r,t.cache.create(),t.serializer)}function Qa(e,t){return hr(e,this,Vo,t.cache.create(),t.serializer)}function Ka(e,t){return hr(e,this,qo,t.cache.create(),t.serializer)}var $a=function(){return JSON.stringify(arguments)};function pr(){this.cache=Object.create(null)}pr.prototype.get=function(e){return this.cache[e]};pr.prototype.set=function(e,t){this.cache[e]=t};var es={create:function(){return new pr}},jt={variadic:Qa,monadic:Ka},Re;(function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"})(Re||(Re={}));var Pt=function(e){Bt(t,e);function t(r,o,n){var i=e.call(this,r)||this;return i.code=o,i.originalMessage=n,i}return t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),zr=function(e){Bt(t,e);function t(r,o,n,i){return e.call(this,'Invalid values for "'.concat(r,'": "').concat(o,'". Options are "').concat(Object.keys(n).join('", "'),'"'),Re.INVALID_VALUE,i)||this}return t}(Pt),ts=function(e){Bt(t,e);function t(r,o,n){return e.call(this,'Value for "'.concat(r,'" must be of type ').concat(o),Re.INVALID_VALUE,n)||this}return t}(Pt),rs=function(e){Bt(t,e);function t(r,o){return e.call(this,'The intl string context variable "'.concat(r,'" was not provided to the string "').concat(o,'"'),Re.MISSING_VALUE,o)||this}return t}(Pt),le;(function(e){e[e.literal=0]="literal",e[e.object=1]="object"})(le||(le={}));function os(e){return e.length<2?e:e.reduce(function(t,r){var o=t[t.length-1];return!o||o.type!==le.literal||r.type!==le.literal?t.push(r):o.value+=r.value,t},[])}function ns(e){return typeof e=="function"}function dt(e,t,r,o,n,i,a){if(e.length===1&&Nr(e[0]))return[{type:le.literal,value:e[0].value}];for(var l=[],u=0,s=e;u<s.length;u++){var c=s[u];if(Nr(c)){l.push({type:le.literal,value:c.value});continue}if(va(c)){typeof i=="number"&&l.push({type:le.literal,value:r.getNumberFormat(t).format(i)});continue}var f=c.value;if(!(n&&f in n))throw new rs(f,a);var d=n[f];if(ya(c)){(!d||typeof d=="string"||typeof d=="number")&&(d=typeof d=="string"||typeof d=="number"?String(d):""),l.push({type:typeof d=="string"?le.literal:le.object,value:d});continue}if(No(c)){var g=typeof c.style=="string"?o.date[c.style]:$t(c.style)?c.style.parsedOptions:void 0;l.push({type:le.literal,value:r.getDateTimeFormat(t,g).format(d)});continue}if(Co(c)){var g=typeof c.style=="string"?o.time[c.style]:$t(c.style)?c.style.parsedOptions:o.time.medium;l.push({type:le.literal,value:r.getDateTimeFormat(t,g).format(d)});continue}if(Ho(c)){var g=typeof c.style=="string"?o.number[c.style]:Lo(c.style)?c.style.parsedOptions:void 0;g&&g.scale&&(d=d*(g.scale||1)),l.push({type:le.literal,value:r.getNumberFormat(t,g).format(d)});continue}if(Io(c)){var h=c.children,_=c.value,v=n[_];if(!ns(v))throw new ts(_,"function",a);var x=dt(h,t,r,o,n,i),E=v(x.map(function(T){return T.value}));Array.isArray(E)||(E=[E]),l.push.apply(l,E.map(function(T){return{type:typeof T=="string"?le.literal:le.object,value:T}}))}if(Oo(c)){var p=c.options[d]||c.options.other;if(!p)throw new zr(c.value,d,Object.keys(c.options),a);l.push.apply(l,dt(p.value,t,r,o,n));continue}if(Mo(c)){var p=c.options["=".concat(d)];if(!p){if(!Intl.PluralRules)throw new Pt(`Intl.PluralRules is not available in this environment.
Try polyfilling it using "@formatjs/intl-pluralrules"
`,Re.MISSING_INTL_API,a);var w=r.getPluralRules(t,{type:c.pluralType}).select(d-(c.offset||0));p=c.options[w]||c.options.other}if(!p)throw new zr(c.value,d,Object.keys(c.options),a);l.push.apply(l,dt(p.value,t,r,o,n,d-(c.offset||0)));continue}}return os(l)}function is(e,t){return t?L(L(L({},e||{}),t||{}),Object.keys(e).reduce(function(r,o){return r[o]=L(L({},e[o]),t[o]||{}),r},{})):e}function as(e,t){return t?Object.keys(e).reduce(function(r,o){return r[o]=is(e[o],t[o]),r},L({},e)):e}function Ut(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}function ss(e){return e===void 0&&(e={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:Dt(function(){for(var t,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];return new((t=Intl.NumberFormat).bind.apply(t,Rt([void 0],r,!1)))},{cache:Ut(e.number),strategy:jt.variadic}),getDateTimeFormat:Dt(function(){for(var t,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];return new((t=Intl.DateTimeFormat).bind.apply(t,Rt([void 0],r,!1)))},{cache:Ut(e.dateTime),strategy:jt.variadic}),getPluralRules:Dt(function(){for(var t,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];return new((t=Intl.PluralRules).bind.apply(t,Rt([void 0],r,!1)))},{cache:Ut(e.pluralRules),strategy:jt.variadic})}}var ls=function(){function e(t,r,o,n){var i=this;if(r===void 0&&(r=e.defaultLocale),this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(a){var l=i.formatToParts(a);if(l.length===1)return l[0].value;var u=l.reduce(function(s,c){return!s.length||c.type!==le.literal||typeof s[s.length-1]!="string"?s.push(c.value):s[s.length-1]+=c.value,s},[]);return u.length<=1?u[0]||"":u},this.formatToParts=function(a){return dt(i.ast,i.locales,i.formatters,i.formats,a,void 0,i.message)},this.resolvedOptions=function(){return{locale:i.resolvedLocale.toString()}},this.getAst=function(){return i.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),typeof t=="string"){if(this.message=t,!e.__parse)throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");this.ast=e.__parse(t,{ignoreTag:n?.ignoreTag,locale:this.resolvedLocale})}else this.ast=t;if(!Array.isArray(this.ast))throw new TypeError("A message must be provided as a String or AST.");this.formats=as(e.formats,o),this.formatters=n&&n.formatters||ss(this.formatterCache)}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(t){var r=Intl.NumberFormat.supportedLocalesOf(t);return r.length>0?new Intl.Locale(r[0]):new Intl.Locale(typeof t=="string"?t:t[0])},e.__parse=Za,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();const ge={},cs=(e,t,r)=>r&&(t in ge||(ge[t]={}),e in ge[t]||(ge[t][e]=r),r),Xo=(e,t)=>{if(t==null)return;if(t in ge&&e in ge[t])return ge[t][e];const r=it(t);for(let o=0;o<r.length;o++){const n=us(r[o],e);if(n)return cs(e,t,n)}};let gr;const nt=me({});function Wo(e){return e in gr}function us(e,t){if(!Wo(e))return null;const r=function(o){return gr[o]||null}(e);return function(o,n){if(n==null)return;if(n in o)return o[n];const i=n.split(".");let a=o;for(let l=0;l<i.length;l++)if(typeof a=="object"){if(l>0){const u=i.slice(l,i.length).join(".");if(u in a){a=a[u];break}}a=a[i[l]]}else a=void 0;return a}(r,t)}function Zo(e,...t){delete ge[e],nt.update(r=>(r[e]=ba.all([r[e]||{},...t]),r))}Ue([nt],([e])=>Object.keys(e));nt.subscribe(e=>gr=e);const ht={};function Jo(e){return ht[e]}function Et(e){return e!=null&&it(e).some(t=>{var r;return(r=Jo(t))===null||r===void 0?void 0:r.size})}function fs(e,t){return Promise.all(t.map(o=>(function(n,i){ht[n].delete(i),ht[n].size===0&&delete ht[n]}(e,o),o().then(n=>n.default||n)))).then(o=>Zo(e,...o))}const We={};function Yo(e){if(!Et(e))return e in We?We[e]:Promise.resolve();const t=function(r){return it(r).map(o=>{const n=Jo(o);return[o,n?[...n]:[]]}).filter(([,o])=>o.length>0)}(e);return We[e]=Promise.all(t.map(([r,o])=>fs(r,o))).then(()=>{if(Et(e))return Yo(e);delete We[e]}),We[e]}function ds({locale:e,id:t}){console.warn(`[svelte-i18n] The message "${t}" was not found in "${it(e).join('", "')}".${Et(be())?`

Note: there are at least one loader still registered to this locale that wasn't executed.`:""}`)}const Je={fallbackLocale:null,loadingDelay:200,formats:{number:{scientific:{notation:"scientific"},engineering:{notation:"engineering"},compactLong:{notation:"compact",compactDisplay:"long"},compactShort:{notation:"compact",compactDisplay:"short"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},warnOnMissingMessages:!0,handleMissingMessage:void 0,ignoreTag:!0};function ze(){return Je}function hs(e){const{formats:t,...r}=e,o=e.initialLocale||e.fallbackLocale;return r.warnOnMissingMessages&&(delete r.warnOnMissingMessages,r.handleMissingMessage==null?r.handleMissingMessage=ds:console.warn('[svelte-i18n] The "warnOnMissingMessages" option is deprecated. Please use the "handleMissingMessage" option instead.')),Object.assign(Je,r,{initialLocale:o}),t&&("number"in t&&Object.assign(Je.formats.number,t.number),"date"in t&&Object.assign(Je.formats.date,t.date),"time"in t&&Object.assign(Je.formats.time,t.time)),Ge.set(o)}const Gt=me(!1);let ir;const pt=me(null);function Dr(e){return e.split("-").map((t,r,o)=>o.slice(0,r+1).join("-")).reverse()}function it(e,t=ze().fallbackLocale){const r=Dr(e);return t?[...new Set([...r,...Dr(t)])]:r}function be(){return ir??void 0}pt.subscribe(e=>{ir=e??void 0,typeof window<"u"&&e!=null&&document.documentElement.setAttribute("lang",e)});const Ge={...pt,set:e=>{if(e&&function(t){if(t==null)return;const r=it(t);for(let o=0;o<r.length;o++){const n=r[o];if(Wo(n))return n}}(e)&&Et(e)){const{loadingDelay:t}=ze();let r;return typeof window<"u"&&be()!=null&&t?r=window.setTimeout(()=>Gt.set(!0),t):Gt.set(!0),Yo(e).then(()=>{pt.set(e)}).finally(()=>{clearTimeout(r),Gt.set(!1)})}return pt.set(e)}},ps=()=>typeof window>"u"?null:window.navigator.language||window.navigator.languages[0],Ht=e=>{const t=Object.create(null);return r=>{const o=JSON.stringify(r);return o in t?t[o]:t[o]=e(r)}},et=(e,t)=>{const{formats:r}=ze();if(e in r&&t in r[e])return r[e][t];throw new Error(`[svelte-i18n] Unknown "${t}" ${e} format.`)},gs=Ht(({locale:e,format:t,...r})=>{if(e==null)throw new Error('[svelte-i18n] A "locale" must be set to format numbers');return t&&(r=et("number",t)),new Intl.NumberFormat(e,r)}),_s=Ht(({locale:e,format:t,...r})=>{if(e==null)throw new Error('[svelte-i18n] A "locale" must be set to format dates');return t?r=et("date",t):Object.keys(r).length===0&&(r=et("date","short")),new Intl.DateTimeFormat(e,r)}),ms=Ht(({locale:e,format:t,...r})=>{if(e==null)throw new Error('[svelte-i18n] A "locale" must be set to format time values');return t?r=et("time",t):Object.keys(r).length===0&&(r=et("time","short")),new Intl.DateTimeFormat(e,r)}),bs=({locale:e=be(),...t}={})=>gs({locale:e,...t}),ys=({locale:e=be(),...t}={})=>_s({locale:e,...t}),vs=({locale:e=be(),...t}={})=>ms({locale:e,...t}),ws=Ht((e,t=be())=>new ls(e,t,ze().formats,{ignoreTag:ze().ignoreTag})),xs=(e,t={})=>{var r,o,n,i;let a=t;typeof e=="object"&&(a=e,e=a.id);const{values:l,locale:u=be(),default:s}=a;if(u==null)throw new Error("[svelte-i18n] Cannot format a message without first setting the initial locale.");let c=Xo(e,u);if(c){if(typeof c!="string")return console.warn(`[svelte-i18n] Message with id "${e}" must be of type "string", found: "${typeof c}". Gettin its value through the "$format" method is deprecated; use the "json" method instead.`),c}else c=(i=(n=(o=(r=ze()).handleMissingMessage)===null||o===void 0?void 0:o.call(r,{locale:u,id:e,defaultValue:s}))!==null&&n!==void 0?n:s)!==null&&i!==void 0?i:e;if(!l)return c;let f=c;try{f=ws(c,u).format(l)}catch(d){d instanceof Error&&console.warn(`[svelte-i18n] Message "${e}" has syntax error:`,d.message)}return f},Es=(e,t)=>vs(t).format(e),Ss=(e,t)=>ys(t).format(e),ks=(e,t)=>bs(t).format(e),As=(e,t=be())=>Xo(e,t),_r=Ue([Ge,nt],()=>xs);Ue([Ge],()=>Es);Ue([Ge],()=>Ss);Ue([Ge],()=>ks);Ue([Ge,nt],()=>As);function jr(e){let t,r,o,n,i,a,l,u=e[8]("common.built_with")+"",s,c,f,d,g,h,_=e[8]("common.hosted_on")+"",v,x,E,p,w,T,C;return{c(){t=R("div"),r=R("span"),o=R("a"),n=M(e[4]),a=$(),l=R("span"),s=M(u),c=$(),f=R("a"),f.textContent="Gradio",d=M("."),g=$(),h=R("span"),v=M(_),x=$(),E=R("a"),p=R("span"),w=R("img"),C=M(" Spaces"),b(o,"href",i="https://huggingface.co/spaces/"+e[4]),b(o,"class","title svelte-1kyws56"),b(r,"class","svelte-1kyws56"),b(f,"class","gradio svelte-1kyws56"),b(f,"href","https://gradio.app"),b(l,"class","svelte-1kyws56"),Ni(w.src,T=oa)||b(w,"src",T),b(w,"alt","Hugging Face Space }"),b(w,"class","svelte-1kyws56"),b(p,"class","space-logo svelte-1kyws56"),b(E,"class","hf svelte-1kyws56"),b(E,"href","https://huggingface.co/spaces"),b(h,"class","svelte-1kyws56"),b(t,"class","info svelte-1kyws56")},m(S,N){A(S,t,N),B(t,r),B(r,o),B(o,n),B(t,a),B(t,l),B(l,s),B(l,c),B(l,f),B(l,d),B(t,g),B(t,h),B(h,v),B(h,x),B(h,E),B(E,p),B(p,w),B(E,C)},p(S,N){N&16&&ee(n,S[4]),N&16&&i!==(i="https://huggingface.co/spaces/"+S[4])&&b(o,"href",i),N&256&&u!==(u=S[8]("common.built_with")+"")&&ee(s,u),N&256&&_!==(_=S[8]("common.hosted_on")+"")&&ee(v,_)},d(S){S&&k(t)}}}function Ts(e){let t,r,o,n,i;const a=e[10].default,l=_o(a,e,e[9],null);let u=e[5]&&e[4]&&e[6]&&jr(e);return{c(){t=R("div"),r=R("div"),l&&l.c(),o=$(),u&&u.c(),b(r,"class","main svelte-1kyws56"),b(t,"class",n="gradio-container gradio-container-"+e[1]+" svelte-1kyws56"),Q(t,"app",!e[5]&&!e[3]),Q(t,"embed-container",e[5]),Q(t,"with-info",e[6]),oe(t,"min-height",e[7]?"initial":e[2]),oe(t,"flex-grow",e[5]?"auto":"1")},m(s,c){A(s,t,c),B(t,r),l&&l.m(r,null),B(t,o),u&&u.m(t,null),e[11](t),i=!0},p(s,[c]){l&&l.p&&(!i||c&512)&&yo(l,a,s,s[9],i?bo(a,s[9],c,null):vo(s[9]),null),s[5]&&s[4]&&s[6]?u?u.p(s,c):(u=jr(s),u.c(),u.m(t,null)):u&&(u.d(1),u=null),(!i||c&2&&n!==(n="gradio-container gradio-container-"+s[1]+" svelte-1kyws56"))&&b(t,"class",n),(!i||c&42)&&Q(t,"app",!s[5]&&!s[3]),(!i||c&34)&&Q(t,"embed-container",s[5]),(!i||c&66)&&Q(t,"with-info",s[6]),c&132&&oe(t,"min-height",s[7]?"initial":s[2]),c&32&&oe(t,"flex-grow",s[5]?"auto":"1")},i(s){i||(K(l,s),i=!0)},o(s){ne(l,s),i=!1},d(s){s&&k(t),l&&l.d(s),u&&u.d(),e[11](null)}}}function Bs(e,t,r){let o;xe(e,_r,_=>r(8,o=_));let{$$slots:n={},$$scope:i}=t,{wrapper:a}=t,{version:l}=t,{initial_height:u}=t,{is_embed:s}=t,{space:c}=t,{display:f}=t,{info:d}=t,{loaded:g}=t;function h(_){_e[_?"unshift":"push"](()=>{a=_,r(0,a)})}return e.$$set=_=>{"wrapper"in _&&r(0,a=_.wrapper),"version"in _&&r(1,l=_.version),"initial_height"in _&&r(2,u=_.initial_height),"is_embed"in _&&r(3,s=_.is_embed),"space"in _&&r(4,c=_.space),"display"in _&&r(5,f=_.display),"info"in _&&r(6,d=_.info),"loaded"in _&&r(7,g=_.loaded),"$$scope"in _&&r(9,i=_.$$scope)},[a,l,u,s,c,f,d,g,o,i,n,h]}class Ps extends Tt{constructor(t){super(),At(this,t,Bs,Ts,rt,{wrapper:0,version:1,initial_height:2,is_embed:3,space:4,display:5,info:6,loaded:7})}}function Ne(e){let t=["","k","M","G","T","P","E","Z"],r=0;for(;e>1e3&&r<t.length-1;)e/=1e3,r++;let o=t[r];return(Number.isInteger(e)?e:e.toFixed(1))+o}function Hc(){const e=me({}),t=[],r=[],o=new Map,n=new Map,i=new Map,a=[];function l({fn_index:s,status:c,queue:f=!0,size:d,position:g=null,eta:h=null,message:_=null,progress:v}){const x=r[s],E=t[s],p=a[s],w=x.map(T=>{let C;const S=o.get(T)||0;if(p==="pending"&&c!=="pending"){let N=S-1;o.set(T,N<0?0:N),C=N>0?"pending":c}else p==="pending"&&c==="pending"?C="pending":p!=="pending"&&c==="pending"?(C="pending",o.set(T,S+1)):C=c;return{id:T,queue_position:g,queue_size:d,eta:h,status:C,message:_,progress:v}});E.forEach(T=>{const C=n.get(T)||0;if(p==="pending"&&c!=="pending"){let S=C-1;n.set(T,S<0?0:S),i.set(T,c)}else p!=="pending"&&c==="pending"?(n.set(T,C+1),i.set(T,c)):i.delete(T)}),e.update(T=>(w.forEach(({id:C,queue_position:S,queue_size:N,eta:z,status:J,message:q,progress:P})=>{T[C]={queue:f,queue_size:N,queue_position:S,eta:z,message:q,progress:P,status:J,fn_index:s}}),T)),a[s]=c}function u(s,c,f){t[s]=c,r[s]=f}return{update:l,register:u,subscribe:e.subscribe,get_status_for_fn(s){return a[s]},get_inputs_to_update(){return i}}}const Hs=me({autoscroll:!1});function Ur(e){return Object.prototype.toString.call(e)==="[object Date]"}function ar(e,t,r,o){if(typeof r=="number"||Ur(r)){const n=o-r,i=(r-t)/(e.dt||1/60),a=e.opts.stiffness*n,l=e.opts.damping*i,u=(a-l)*e.inv_mass,s=(i+u)*e.dt;return Math.abs(s)<e.opts.precision&&Math.abs(n)<e.opts.precision?o:(e.settled=!1,Ur(r)?new Date(r.getTime()+s):r+s)}else{if(Array.isArray(r))return r.map((n,i)=>ar(e,t[i],r[i],o[i]));if(typeof r=="object"){const n={};for(const i in r)n[i]=ar(e,t[i],r[i],o[i]);return n}else throw new Error(`Cannot spring ${typeof r} values`)}}function Gr(e,t={}){const r=me(e),{stiffness:o=.15,damping:n=.8,precision:i=.01}=t;let a,l,u,s=e,c=e,f=1,d=0,g=!1;function h(v,x={}){c=v;const E=u={};return e==null||x.hard||_.stiffness>=1&&_.damping>=1?(g=!0,a=Qe(),s=v,r.set(e=c),Promise.resolve()):(x.soft&&(d=1/((x.soft===!0?.5:+x.soft)*60),f=0),l||(a=Qe(),g=!1,l=kt(p=>{if(g)return g=!1,l=null,!1;f=Math.min(f+d,1);const w={inv_mass:f,opts:_,settled:!0,dt:(p-a)*60/1e3},T=ar(w,s,e,c);return a=p,s=e,r.set(e=T),w.settled&&(l=null),!w.settled})),new Promise(p=>{l.promise.then(()=>{E===u&&p()})}))}const _={set:h,update:(v,x)=>h(v(c,e),x),subscribe:r.subscribe,stiffness:o,damping:n,precision:i};return _}function Ns(e){let t,r,o,n,i,a,l,u,s,c,f,d;return{c(){t=R("div"),r=fe("svg"),o=fe("g"),n=fe("path"),i=fe("path"),a=fe("path"),l=fe("path"),u=fe("g"),s=fe("path"),c=fe("path"),f=fe("path"),d=fe("path"),b(n,"d","M255.926 0.754768L509.702 139.936V221.027L255.926 81.8465V0.754768Z"),b(n,"fill","#FF7C00"),b(n,"fill-opacity","0.4"),b(n,"class","svelte-zyxd38"),b(i,"d","M509.69 139.936L254.981 279.641V361.255L509.69 221.55V139.936Z"),b(i,"fill","#FF7C00"),b(i,"class","svelte-zyxd38"),b(a,"d","M0.250138 139.937L254.981 279.641V361.255L0.250138 221.55V139.937Z"),b(a,"fill","#FF7C00"),b(a,"fill-opacity","0.4"),b(a,"class","svelte-zyxd38"),b(l,"d","M255.923 0.232622L0.236328 139.936V221.55L255.923 81.8469V0.232622Z"),b(l,"fill","#FF7C00"),b(l,"class","svelte-zyxd38"),oe(o,"transform","translate("+e[1][0]+"px, "+e[1][1]+"px)"),b(s,"d","M255.926 141.5L509.702 280.681V361.773L255.926 222.592V141.5Z"),b(s,"fill","#FF7C00"),b(s,"fill-opacity","0.4"),b(s,"class","svelte-zyxd38"),b(c,"d","M509.69 280.679L254.981 420.384V501.998L509.69 362.293V280.679Z"),b(c,"fill","#FF7C00"),b(c,"class","svelte-zyxd38"),b(f,"d","M0.250138 280.681L254.981 420.386V502L0.250138 362.295V280.681Z"),b(f,"fill","#FF7C00"),b(f,"fill-opacity","0.4"),b(f,"class","svelte-zyxd38"),b(d,"d","M255.923 140.977L0.236328 280.68V362.294L255.923 222.591V140.977Z"),b(d,"fill","#FF7C00"),b(d,"class","svelte-zyxd38"),oe(u,"transform","translate("+e[2][0]+"px, "+e[2][1]+"px)"),b(r,"viewBox","-1200 -1200 3000 3000"),b(r,"fill","none"),b(r,"xmlns","http://www.w3.org/2000/svg"),b(r,"class","svelte-zyxd38"),b(t,"class","svelte-zyxd38"),Q(t,"margin",e[0])},m(g,h){A(g,t,h),B(t,r),B(r,o),B(o,n),B(o,i),B(o,a),B(o,l),B(r,u),B(u,s),B(u,c),B(u,f),B(u,d)},p(g,[h]){h&2&&oe(o,"transform","translate("+g[1][0]+"px, "+g[1][1]+"px)"),h&4&&oe(u,"transform","translate("+g[2][0]+"px, "+g[2][1]+"px)"),h&1&&Q(t,"margin",g[0])},i:Z,o:Z,d(g){g&&k(t)}}}function Cs(e,t,r){let o,n,{margin:i=!0}=t;const a=Gr([0,0]);xe(e,a,d=>r(1,o=d));const l=Gr([0,0]);xe(e,l,d=>r(2,n=d));let u;async function s(){await Promise.all([a.set([125,140]),l.set([-125,-140])]),await Promise.all([a.set([-125,140]),l.set([125,-140])]),await Promise.all([a.set([-125,0]),l.set([125,-0])]),await Promise.all([a.set([125,0]),l.set([-125,0])])}async function c(){await s(),u||c()}async function f(){await Promise.all([a.set([125,0]),l.set([-125,0])]),c()}return Wt(()=>(f(),()=>u=!0)),e.$$set=d=>{"margin"in d&&r(0,i=d.margin)},[i,o,n,a,l]}class Os extends Tt{constructor(t){super(),At(this,t,Cs,Ns,rt,{margin:0})}}const Ms=e=>({}),Fr=e=>({});function qr(e,t,r){const o=e.slice();return o[37]=t[r],o[39]=r,o}function Vr(e,t,r){const o=e.slice();return o[37]=t[r],o}function Is(e){let t,r=e[19]("common.error")+"",o,n,i;const a=e[28].error,l=_o(a,e,e[27],Fr);return{c(){t=R("span"),o=M(r),n=$(),l&&l.c(),b(t,"class","error svelte-zlszon")},m(u,s){A(u,t,s),B(t,o),A(u,n,s),l&&l.m(u,s),i=!0},p(u,s){(!i||s[0]&524288)&&r!==(r=u[19]("common.error")+"")&&ee(o,r),l&&l.p&&(!i||s[0]&134217728)&&yo(l,a,u,u[27],i?bo(a,u[27],s,Ms):vo(u[27]),Fr)},i(u){i||(K(l,u),i=!0)},o(u){ne(l,u),i=!1},d(u){u&&(k(t),k(n)),l&&l.d(u)}}}function Ls(e){let t,r,o,n,i,a,l,u,s,c=e[7]==="default"&&e[16]&&e[5]==="full"&&Xr(e);function f(p,w){if(p[6])return Ds;if(p[1]!==null&&p[2]!==void 0&&p[1]>=0)return zs;if(p[1]===0)return Rs}let d=f(e),g=d&&d(e),h=e[4]&&Jr(e);const _=[Fs,Gs],v=[];function x(p,w){return p[13]!=null?0:p[5]==="full"?1:-1}~(i=x(e))&&(a=v[i]=_[i](e));let E=!e[4]&&ro(e);return{c(){c&&c.c(),t=$(),r=R("div"),g&&g.c(),o=$(),h&&h.c(),n=$(),a&&a.c(),l=$(),E&&E.c(),u=Se(),b(r,"class","progress-text svelte-zlszon"),Q(r,"meta-text-center",e[7]==="center"),Q(r,"meta-text",e[7]==="default")},m(p,w){c&&c.m(p,w),A(p,t,w),A(p,r,w),g&&g.m(r,null),B(r,o),h&&h.m(r,null),A(p,n,w),~i&&v[i].m(p,w),A(p,l,w),E&&E.m(p,w),A(p,u,w),s=!0},p(p,w){p[7]==="default"&&p[16]&&p[5]==="full"?c?c.p(p,w):(c=Xr(p),c.c(),c.m(t.parentNode,t)):c&&(c.d(1),c=null),d===(d=f(p))&&g?g.p(p,w):(g&&g.d(1),g=d&&d(p),g&&(g.c(),g.m(r,o))),p[4]?h?h.p(p,w):(h=Jr(p),h.c(),h.m(r,null)):h&&(h.d(1),h=null),(!s||w[0]&128)&&Q(r,"meta-text-center",p[7]==="center"),(!s||w[0]&128)&&Q(r,"meta-text",p[7]==="default");let T=i;i=x(p),i===T?~i&&v[i].p(p,w):(a&&(vt(),ne(v[T],1,1,()=>{v[T]=null}),wt()),~i?(a=v[i],a?a.p(p,w):(a=v[i]=_[i](p),a.c()),K(a,1),a.m(l.parentNode,l)):a=null),p[4]?E&&(E.d(1),E=null):E?E.p(p,w):(E=ro(p),E.c(),E.m(u.parentNode,u))},i(p){s||(K(a),s=!0)},o(p){ne(a),s=!1},d(p){p&&(k(t),k(r),k(n),k(l),k(u)),c&&c.d(p),g&&g.d(),h&&h.d(),~i&&v[i].d(p),E&&E.d(p)}}}function Xr(e){let t,r=`translateX(${(e[15]||0)*100-100}%)`;return{c(){t=R("div"),b(t,"class","eta-bar svelte-zlszon"),oe(t,"transform",r)},m(o,n){A(o,t,n)},p(o,n){n[0]&32768&&r!==(r=`translateX(${(o[15]||0)*100-100}%)`)&&oe(t,"transform",r)},d(o){o&&k(t)}}}function Rs(e){let t;return{c(){t=M("processing |")},m(r,o){A(r,t,o)},p:Z,d(r){r&&k(t)}}}function zs(e){let t,r=e[1]+1+"",o,n,i,a;return{c(){t=M("queue: "),o=M(r),n=M("/"),i=M(e[2]),a=M(" |")},m(l,u){A(l,t,u),A(l,o,u),A(l,n,u),A(l,i,u),A(l,a,u)},p(l,u){u[0]&2&&r!==(r=l[1]+1+"")&&ee(o,r),u[0]&4&&ee(i,l[2])},d(l){l&&(k(t),k(o),k(n),k(i),k(a))}}}function Ds(e){let t,r=xt(e[6]),o=[];for(let n=0;n<r.length;n+=1)o[n]=Zr(Vr(e,r,n));return{c(){for(let n=0;n<o.length;n+=1)o[n].c();t=Se()},m(n,i){for(let a=0;a<o.length;a+=1)o[a]&&o[a].m(n,i);A(n,t,i)},p(n,i){if(i[0]&64){r=xt(n[6]);let a;for(a=0;a<r.length;a+=1){const l=Vr(n,r,a);o[a]?o[a].p(l,i):(o[a]=Zr(l),o[a].c(),o[a].m(t.parentNode,t))}for(;a<o.length;a+=1)o[a].d(1);o.length=r.length}},d(n){n&&k(t),So(o,n)}}}function Wr(e){let t,r=e[37].unit+"",o,n,i=" ",a;function l(c,f){return c[37].length!=null?Us:js}let u=l(e),s=u(e);return{c(){s.c(),t=$(),o=M(r),n=M(" | "),a=M(i)},m(c,f){s.m(c,f),A(c,t,f),A(c,o,f),A(c,n,f),A(c,a,f)},p(c,f){u===(u=l(c))&&s?s.p(c,f):(s.d(1),s=u(c),s&&(s.c(),s.m(t.parentNode,t))),f[0]&64&&r!==(r=c[37].unit+"")&&ee(o,r)},d(c){c&&(k(t),k(o),k(n),k(a)),s.d(c)}}}function js(e){let t=Ne(e[37].index||0)+"",r;return{c(){r=M(t)},m(o,n){A(o,r,n)},p(o,n){n[0]&64&&t!==(t=Ne(o[37].index||0)+"")&&ee(r,t)},d(o){o&&k(r)}}}function Us(e){let t=Ne(e[37].index||0)+"",r,o,n=Ne(e[37].length)+"",i;return{c(){r=M(t),o=M("/"),i=M(n)},m(a,l){A(a,r,l),A(a,o,l),A(a,i,l)},p(a,l){l[0]&64&&t!==(t=Ne(a[37].index||0)+"")&&ee(r,t),l[0]&64&&n!==(n=Ne(a[37].length)+"")&&ee(i,n)},d(a){a&&(k(r),k(o),k(i))}}}function Zr(e){let t,r=e[37].index!=null&&Wr(e);return{c(){r&&r.c(),t=Se()},m(o,n){r&&r.m(o,n),A(o,t,n)},p(o,n){o[37].index!=null?r?r.p(o,n):(r=Wr(o),r.c(),r.m(t.parentNode,t)):r&&(r.d(1),r=null)},d(o){o&&k(t),r&&r.d(o)}}}function Jr(e){let t,r=e[0]?`/${e[17]}`:"",o,n;return{c(){t=M(e[18]),o=M(r),n=M("s")},m(i,a){A(i,t,a),A(i,o,a),A(i,n,a)},p(i,a){a[0]&262144&&ee(t,i[18]),a[0]&131073&&r!==(r=i[0]?`/${i[17]}`:"")&&ee(o,r)},d(i){i&&(k(t),k(o),k(n))}}}function Gs(e){let t,r;return t=new Os({props:{margin:e[7]==="default"}}),{c(){ot(t.$$.fragment)},m(o,n){De(t,o,n),r=!0},p(o,n){const i={};n[0]&128&&(i.margin=o[7]==="default"),t.$set(i)},i(o){r||(K(t.$$.fragment,o),r=!0)},o(o){ne(t.$$.fragment,o),r=!1},d(o){je(t,o)}}}function Fs(e){let t,r,o,n,i,a=`${e[13]*100}%`,l=e[6]!=null&&Yr(e);return{c(){t=R("div"),r=R("div"),l&&l.c(),o=$(),n=R("div"),i=R("div"),b(r,"class","progress-level-inner svelte-zlszon"),b(i,"class","progress-bar svelte-zlszon"),oe(i,"width",a),b(n,"class","progress-bar-wrap svelte-zlszon"),b(t,"class","progress-level svelte-zlszon")},m(u,s){A(u,t,s),B(t,r),l&&l.m(r,null),B(t,o),B(t,n),B(n,i),e[29](i)},p(u,s){u[6]!=null?l?l.p(u,s):(l=Yr(u),l.c(),l.m(r,null)):l&&(l.d(1),l=null),s[0]&8192&&a!==(a=`${u[13]*100}%`)&&oe(i,"width",a)},i:Z,o:Z,d(u){u&&k(t),l&&l.d(),e[29](null)}}}function Yr(e){let t,r=xt(e[6]),o=[];for(let n=0;n<r.length;n+=1)o[n]=to(qr(e,r,n));return{c(){for(let n=0;n<o.length;n+=1)o[n].c();t=Se()},m(n,i){for(let a=0;a<o.length;a+=1)o[a]&&o[a].m(n,i);A(n,t,i)},p(n,i){if(i[0]&4160){r=xt(n[6]);let a;for(a=0;a<r.length;a+=1){const l=qr(n,r,a);o[a]?o[a].p(l,i):(o[a]=to(l),o[a].c(),o[a].m(t.parentNode,t))}for(;a<o.length;a+=1)o[a].d(1);o.length=r.length}},d(n){n&&k(t),So(o,n)}}}function Qr(e){let t,r,o,n,i=e[39]!==0&&qs(),a=e[37].desc!=null&&Kr(e),l=e[37].desc!=null&&e[12]&&e[12][e[39]]!=null&&$r(),u=e[12]!=null&&eo(e);return{c(){i&&i.c(),t=$(),a&&a.c(),r=$(),l&&l.c(),o=$(),u&&u.c(),n=Se()},m(s,c){i&&i.m(s,c),A(s,t,c),a&&a.m(s,c),A(s,r,c),l&&l.m(s,c),A(s,o,c),u&&u.m(s,c),A(s,n,c)},p(s,c){s[37].desc!=null?a?a.p(s,c):(a=Kr(s),a.c(),a.m(r.parentNode,r)):a&&(a.d(1),a=null),s[37].desc!=null&&s[12]&&s[12][s[39]]!=null?l||(l=$r(),l.c(),l.m(o.parentNode,o)):l&&(l.d(1),l=null),s[12]!=null?u?u.p(s,c):(u=eo(s),u.c(),u.m(n.parentNode,n)):u&&(u.d(1),u=null)},d(s){s&&(k(t),k(r),k(o),k(n)),i&&i.d(s),a&&a.d(s),l&&l.d(s),u&&u.d(s)}}}function qs(e){let t;return{c(){t=M(" /")},m(r,o){A(r,t,o)},d(r){r&&k(t)}}}function Kr(e){let t=e[37].desc+"",r;return{c(){r=M(t)},m(o,n){A(o,r,n)},p(o,n){n[0]&64&&t!==(t=o[37].desc+"")&&ee(r,t)},d(o){o&&k(r)}}}function $r(e){let t;return{c(){t=M("-")},m(r,o){A(r,t,o)},d(r){r&&k(t)}}}function eo(e){let t=(100*(e[12][e[39]]||0)).toFixed(1)+"",r,o;return{c(){r=M(t),o=M("%")},m(n,i){A(n,r,i),A(n,o,i)},p(n,i){i[0]&4096&&t!==(t=(100*(n[12][n[39]]||0)).toFixed(1)+"")&&ee(r,t)},d(n){n&&(k(r),k(o))}}}function to(e){let t,r=(e[37].desc!=null||e[12]&&e[12][e[39]]!=null)&&Qr(e);return{c(){r&&r.c(),t=Se()},m(o,n){r&&r.m(o,n),A(o,t,n)},p(o,n){o[37].desc!=null||o[12]&&o[12][o[39]]!=null?r?r.p(o,n):(r=Qr(o),r.c(),r.m(t.parentNode,t)):r&&(r.d(1),r=null)},d(o){o&&k(t),r&&r.d(o)}}}function ro(e){let t,r;return{c(){t=R("p"),r=M(e[8]),b(t,"class","loading svelte-zlszon")},m(o,n){A(o,t,n),B(t,r)},p(o,n){n[0]&256&&ee(r,o[8])},d(o){o&&k(t)}}}function Vs(e){let t,r,o,n,i;const a=[Ls,Is],l=[];function u(s,c){return s[3]==="pending"?0:s[3]==="error"?1:-1}return~(r=u(e))&&(o=l[r]=a[r](e)),{c(){t=R("div"),o&&o.c(),b(t,"class",n="wrap "+e[7]+" "+e[5]+" svelte-zlszon"),Q(t,"hide",!e[3]||e[3]==="complete"||e[5]==="hidden"),Q(t,"translucent",e[7]==="center"&&(e[3]==="pending"||e[3]==="error")||e[10]||e[5]==="minimal"),Q(t,"generating",e[3]==="generating"),oe(t,"position",e[9]?"absolute":"static"),oe(t,"padding",e[9]?"0":"var(--size-8) 0")},m(s,c){A(s,t,c),~r&&l[r].m(t,null),e[30](t),i=!0},p(s,c){let f=r;r=u(s),r===f?~r&&l[r].p(s,c):(o&&(vt(),ne(l[f],1,1,()=>{l[f]=null}),wt()),~r?(o=l[r],o?o.p(s,c):(o=l[r]=a[r](s),o.c()),K(o,1),o.m(t,null)):o=null),(!i||c[0]&160&&n!==(n="wrap "+s[7]+" "+s[5]+" svelte-zlszon"))&&b(t,"class",n),(!i||c[0]&168)&&Q(t,"hide",!s[3]||s[3]==="complete"||s[5]==="hidden"),(!i||c[0]&1192)&&Q(t,"translucent",s[7]==="center"&&(s[3]==="pending"||s[3]==="error")||s[10]||s[5]==="minimal"),(!i||c[0]&168)&&Q(t,"generating",s[3]==="generating"),c[0]&512&&oe(t,"position",s[9]?"absolute":"static"),c[0]&512&&oe(t,"padding",s[9]?"0":"var(--size-8) 0")},i(s){i||(K(o),i=!0)},o(s){ne(o),i=!1},d(s){s&&k(t),~r&&l[r].d(),e[30](null)}}}let ut=[],Ft=!1;async function Xs(e,t=!0){if(!(window.__gradio_mode__==="website"||window.__gradio_mode__!=="app"&&t!==!0)){if(ut.push(e),!Ft)Ft=!0;else return;await Wi(),requestAnimationFrame(()=>{let r=[0,0];for(let o=0;o<ut.length;o++){const i=ut[o].getBoundingClientRect();(o===0||i.top+window.scrollY<=r[0])&&(r[0]=i.top+window.scrollY,r[1]=o)}window.scrollTo({top:r[0]-20,behavior:"smooth"}),Ft=!1,ut=[]})}}function Ws(e,t,r){let o,n,i;xe(e,Hs,y=>r(26,n=y)),xe(e,_r,y=>r(19,i=y));let{$$slots:a={},$$scope:l}=t,{eta:u=null}=t,{queue:s=!1}=t,{queue_position:c}=t,{queue_size:f}=t,{status:d}=t,{scroll_to_output:g=!1}=t,{timer:h=!0}=t,{show_progress:_="full"}=t,{message:v=null}=t,{progress:x=null}=t,{variant:E="default"}=t,{loading_text:p="Loading..."}=t,{absolute:w=!0}=t,{translucent:T=!1}=t,C,S=!1,N=0,z=0,J=null,q=0,P=null,D,X=null,H=!0;const ce=()=>{r(23,N=performance.now()),r(24,z=0),S=!0,F()};function F(){requestAnimationFrame(()=>{r(24,z=(performance.now()-N)/1e3),S&&F()})}function j(){r(24,z=0),S&&(S=!1)}Vi(()=>{S&&j()});let ie=null;function ae(y){_e[y?"unshift":"push"](()=>{X=y,r(14,X),r(6,x),r(12,P),r(13,D)})}function ue(y){_e[y?"unshift":"push"](()=>{C=y,r(11,C)})}return e.$$set=y=>{"eta"in y&&r(0,u=y.eta),"queue"in y&&r(20,s=y.queue),"queue_position"in y&&r(1,c=y.queue_position),"queue_size"in y&&r(2,f=y.queue_size),"status"in y&&r(3,d=y.status),"scroll_to_output"in y&&r(21,g=y.scroll_to_output),"timer"in y&&r(4,h=y.timer),"show_progress"in y&&r(5,_=y.show_progress),"message"in y&&r(22,v=y.message),"progress"in y&&r(6,x=y.progress),"variant"in y&&r(7,E=y.variant),"loading_text"in y&&r(8,p=y.loading_text),"absolute"in y&&r(9,w=y.absolute),"translucent"in y&&r(10,T=y.translucent),"$$scope"in y&&r(27,l=y.$$scope)},e.$$.update=()=>{e.$$.dirty[0]&42991617&&(u===null?r(0,u=J):s&&r(0,u=(performance.now()-N)/1e3+u),u!=null&&(r(17,ie=u.toFixed(1)),r(25,J=u))),e.$$.dirty[0]&16777217&&r(15,q=u===null||u<=0||!z?null:Math.min(z/u,1)),e.$$.dirty[0]&64&&x!=null&&r(16,H=!1),e.$$.dirty[0]&28736&&(x!=null?r(12,P=x.map(y=>{if(y.index!=null&&y.length!=null)return y.index/y.length;if(y.progress!=null)return y.progress})):r(12,P=null),P?(r(13,D=P[P.length-1]),X&&(D===0?r(14,X.style.transition="0",X):r(14,X.style.transition="150ms",X))):r(13,D=void 0)),e.$$.dirty[0]&8&&(d==="pending"?ce():j()),e.$$.dirty[0]&69208072&&C&&g&&(d==="pending"||d==="complete")&&Xs(C,n.autoscroll),e.$$.dirty[0]&4194312,e.$$.dirty[0]&16777216&&r(18,o=z.toFixed(1))},[u,c,f,d,h,_,x,E,p,w,T,C,P,D,X,q,H,ie,o,i,s,g,v,N,z,J,n,l,a,ae,ue]}class Zs extends Tt{constructor(t){super(),At(this,t,Ws,Vs,rt,{eta:0,queue:20,queue_position:1,queue_size:2,status:3,scroll_to_output:21,timer:4,show_progress:5,message:22,progress:6,variant:7,loading_text:8,absolute:9,translucent:10},null,[-1,-1])}}const Qo={built_with_gradio:"تم الإنشاء بإستخدام Gradio",clear:"أمسح",or:"أو",submit:"أرسل"},Ko={click_to_upload:"إضغط للتحميل",drop_audio:"أسقط الملف الصوتي هنا",drop_csv:"أسقط ملف البيانات هنا",drop_file:"أسقط الملف هنا",drop_image:"أسقط الصورة هنا",drop_video:"أسقط الفيديو هنا"},Js={common:Qo,upload_text:Ko},Ys=Object.freeze(Object.defineProperty({__proto__:null,common:Qo,default:Js,upload_text:Ko},Symbol.toStringTag,{value:"Module"})),$o={built_with_gradio:"Construït amb gradio",clear:"Neteja",empty:"Buit",error:"Error",loading:"S'està carregant",or:"o",submit:"Envia"},en={click_to_upload:"Feu clic per pujar",drop_audio:"Deixeu anar l'àudio aquí",drop_csv:"Deixeu anar el CSV aquí",drop_file:"Deixeu anar el fitxer aquí",drop_image:"Deixeu anar la imatge aquí",drop_video:"Deixeu anar el vídeo aquí"},Qs={common:$o,upload_text:en},Ks=Object.freeze(Object.defineProperty({__proto__:null,common:$o,default:Qs,upload_text:en},Symbol.toStringTag,{value:"Module"})),tn={built_with_gradio:"Mit Gradio erstellt",clear:"Löschen",or:"oder",submit:"Absenden"},rn={click_to_upload:"Hochladen",drop_audio:"Audio hier ablegen",drop_csv:"CSV Datei hier ablegen",drop_file:"Datei hier ablegen",drop_image:"Bild hier ablegen",drop_video:"Video hier ablegen"},$s={common:tn,upload_text:rn},el=Object.freeze(Object.defineProperty({__proto__:null,common:tn,default:$s,upload_text:rn},Symbol.toStringTag,{value:"Module"})),on={annotated_image:"Annotated Image"},nn={allow_recording_access:"Please allow access to the microphone for recording.",audio:"Audio",record_from_microphone:"Record from microphone",stop_recording:"Stop recording"},an={connection_can_break:"On mobile, the connection can break if this tab is unfocused or the device sleeps, losing your position in queue.",long_requests_queue:"There is a long queue of requests pending. Duplicate this Space to skip.",lost_connection:"Lost connection due to leaving page. Rejoining queue..."},sn={checkbox:"Checkbox",checkbox_group:"Checkbox Group"},ln={code:"Code"},cn={color_picker:"Color Picker"},un={built_with:"built with",built_with_gradio:"Built with Gradio",clear:"Clear",download:"Download",edit:"Edit",empty:"Empty",error:"Error",hosted_on:"Hosted on",loading:"Loading",logo:"logo",or:"or",remove:"Remove",share:"Share",submit:"Submit"},fn={incorrect_format:"Incorrect format, only CSV and TSV files are supported",new_column:"New column",new_row:"New row"},dn={dropdown:"Dropdown"},hn={build_error:"there is a build error",config_error:"there is a config error",contact_page_author:"Please contact the author of the page to let them know.",no_app_file:"there is no app file",runtime_error:"there is a runtime error",space_not_working:`"Space isn't working because" {0}`,space_paused:"the space is paused",use_via_api:"Use via API"},pn={uploading:"Uploading..."},gn={highlighted_text:"Highlighted Text"},_n={allow_webcam_access:"Please allow access to the webcam for recording.",brush_color:"Brush color",brush_radius:"Brush radius",image:"Image",remove_image:"Remove Image",select_brush_color:"Select brush color",start_drawing:"Start drawing",undo:"Undo",use_brush:"Use brush"},mn={label:"Label"},bn={enable_cookies:"If you are visiting a HuggingFace Space in Incognito mode, you must enable third party cookies.",incorrect_credentials:"Incorrect Credentials",login:"Login"},yn={number:"Number"},vn={plot:"Plot"},wn={radio:"Radio"},xn={slider:"Slider"},En={click_to_upload:"Click to Upload",drop_audio:"Drop Audio Here",drop_csv:"Drop CSV Here",drop_file:"Drop File Here",drop_image:"Drop Image Here",drop_video:"Drop Video Here"},tl={"3D_model":{"3d_model":"3D Model"},annotated_image:on,audio:nn,blocks:an,checkbox:sn,code:ln,color_picker:cn,common:un,dataframe:fn,dropdown:dn,errors:hn,file:pn,highlighted_text:gn,image:_n,label:mn,login:bn,number:yn,plot:vn,radio:wn,slider:xn,upload_text:En},rl=Object.freeze(Object.defineProperty({__proto__:null,annotated_image:on,audio:nn,blocks:an,checkbox:sn,code:ln,color_picker:cn,common:un,dataframe:fn,default:tl,dropdown:dn,errors:hn,file:pn,highlighted_text:gn,image:_n,label:mn,login:bn,number:yn,plot:vn,radio:wn,slider:xn,upload_text:En},Symbol.toStringTag,{value:"Module"})),Sn={built_with_gradio:"Construido con Gradio",clear:"Limpiar",or:"o",submit:"Enviar"},kn={click_to_upload:"Haga click para cargar",drop_audio:"Coloque el audio aquí",drop_csv:"Coloque el CSV aquí",drop_file:"Coloque el archivo aquí",drop_image:"Coloque la imagen aquí",drop_video:"Coloque el video aquí"},ol={common:Sn,upload_text:kn},nl=Object.freeze(Object.defineProperty({__proto__:null,common:Sn,default:ol,upload_text:kn},Symbol.toStringTag,{value:"Module"})),An={built_with_gradio:"ساخته شده با gradio",clear:"حذف",or:"یا",submit:"ارسال"},Tn={click_to_upload:"برای آپلود کلیک کنید",drop_audio:"صوت را اینجا رها کنید",drop_csv:"فایل csv را  اینجا رها کنید",drop_file:"فایل را اینجا رها کنید",drop_image:"تصویر را اینجا رها کنید",drop_video:"ویدیو را اینجا رها کنید"},il={common:An,upload_text:Tn},al=Object.freeze(Object.defineProperty({__proto__:null,common:An,default:il,upload_text:Tn},Symbol.toStringTag,{value:"Module"})),Bn={allow_recording_access:"Veuillez autoriser l'accès à l'enregistrement",audio:"Audio",record_from_microphone:"Enregistrer avec le microphone",stop_recording:"Arrêter l'enregistrement"},Pn={built_with:"Construit avec",built_with_gradio:"Construit avec Gradio",clear:"Effacer",download:"Télécharger",edit:"Éditer",error:"Erreur",loading:"Chargement",logo:"logo",or:"ou",remove:"Supprimer",share:"Partager",submit:"Soumettre"},Hn={click_to_upload:"Cliquer pour Télécharger",drop_audio:"Déposer l'Audio Ici",drop_csv:"Déposer le CSV Ici",drop_file:"Déposer le Fichier Ici",drop_image:"Déposer l'Image Ici",drop_video:"Déposer la Vidéo Ici"},sl={audio:Bn,common:Pn,upload_text:Hn},ll=Object.freeze(Object.defineProperty({__proto__:null,audio:Bn,common:Pn,default:sl,upload_text:Hn},Symbol.toStringTag,{value:"Module"})),Nn={built_with_gradio:"בנוי עם גרדיו",clear:"נקה",or:"או",submit:"שלח"},Cn={click_to_upload:"לחץ כדי להעלות",drop_audio:"גרור לכאן קובץ שמע",drop_csv:"גרור csv קובץ לכאן",drop_file:"גרור קובץ לכאן",drop_image:"גרור קובץ תמונה לכאן",drop_video:"גרור קובץ סרטון לכאן"},cl={common:Nn,upload_text:Cn},ul=Object.freeze(Object.defineProperty({__proto__:null,common:Nn,default:cl,upload_text:Cn},Symbol.toStringTag,{value:"Module"})),On={built_with_gradio:"Gradio से बना",clear:"हटाये",or:"या",submit:"सबमिट करे"},Mn={click_to_upload:"अपलोड के लिए बटन दबायें",drop_audio:"यहाँ ऑडियो ड्रॉप करें",drop_csv:"यहाँ CSV ड्रॉप करें",drop_file:"यहाँ File ड्रॉप करें",drop_image:"यहाँ इमेज ड्रॉप करें",drop_video:"यहाँ वीडियो ड्रॉप करें"},fl={common:On,upload_text:Mn},dl=Object.freeze(Object.defineProperty({__proto__:null,common:On,default:fl,upload_text:Mn},Symbol.toStringTag,{value:"Module"})),In={built_with_gradio:"gradioで作ろう",clear:"クリア",or:"または",submit:"送信"},Ln={click_to_upload:"クリックしてアップロード",drop_audio:"ここに音声をドロップ",drop_csv:"ここにCSVをドロップ",drop_file:"ここにファイルをドロップ",drop_image:"ここに画像をドロップ",drop_video:"ここに動画をドロップ"},hl={common:In,upload_text:Ln},pl=Object.freeze(Object.defineProperty({__proto__:null,common:In,default:hl,upload_text:Ln},Symbol.toStringTag,{value:"Module"})),Rn={built_with_gradio:"gradio로 제작되었습니다",clear:"클리어",or:"또는",submit:"제출하기"},zn={click_to_upload:"클릭해서 업로드하기",drop_audio:"오디오를 끌어 놓으세요",drop_csv:"CSV파일을 끌어 놓으세요",drop_file:"파일을 끌어 놓으세요",drop_image:"이미지를 끌어 놓으세요",drop_video:"비디오를 끌어 놓으세요"},gl={common:Rn,upload_text:zn},_l=Object.freeze(Object.defineProperty({__proto__:null,common:Rn,default:gl,upload_text:zn},Symbol.toStringTag,{value:"Module"})),Dn={built_with_gradio:"sukurta su gradio",clear:"Trinti",or:"arba",submit:"Pateikti"},jn={click_to_upload:"Spustelėkite norėdami įkelti",drop_audio:"Įkelkite garso įrašą čia",drop_csv:"Įkelkite CSV čia",drop_file:"Įkelkite bylą čia",drop_image:"Įkelkite paveikslėlį čia",drop_video:"Įkelkite vaizdo įrašą čia"},ml={common:Dn,upload_text:jn},bl=Object.freeze(Object.defineProperty({__proto__:null,common:Dn,default:ml,upload_text:jn},Symbol.toStringTag,{value:"Module"})),Un={built_with_gradio:"gemaakt met gradio",clear:"Wis",or:"of",submit:"Zend in"},Gn={click_to_upload:"Klik om the Uploaden",drop_audio:"Sleep een Geluidsbestand hier",drop_csv:"Sleep een CSV hier",drop_file:"Sleep een Document hier",drop_image:"Sleep een Afbeelding hier",drop_video:"Sleep een Video hier"},yl={common:Un,upload_text:Gn},vl=Object.freeze(Object.defineProperty({__proto__:null,common:Un,default:yl,upload_text:Gn},Symbol.toStringTag,{value:"Module"})),Fn={built_with_gradio:"utworzone z gradio",clear:"Wyczyść",or:"lub",submit:"Zatwierdź"},qn={click_to_upload:"Kliknij, aby przesłać",drop_audio:"Przeciągnij tutaj audio",drop_csv:"Przeciągnij tutaj CSV",drop_file:"Przeciągnij tutaj plik",drop_image:"Przeciągnij tutaj zdjęcie",drop_video:"Przeciągnij tutaj video"},wl={common:Fn,upload_text:qn},xl=Object.freeze(Object.defineProperty({__proto__:null,common:Fn,default:wl,upload_text:qn},Symbol.toStringTag,{value:"Module"})),Vn={built_with_gradio:"Construído com gradio",clear:"Limpar",error:"Erro",flag:"Marcar",loading:"Carregando",or:"ou",submit:"Enviar"},Xn={click_to_upload:"Clique para o Upload",drop_audio:"Solte o Áudio Aqui",drop_csv:"Solte o CSV Aqui",drop_file:"Solte o Arquivo Aqui",drop_image:"Solte a Imagem Aqui",drop_video:"Solte o Vídeo Aqui"},El={common:Vn,upload_text:Xn},Sl=Object.freeze(Object.defineProperty({__proto__:null,common:Vn,default:El,upload_text:Xn},Symbol.toStringTag,{value:"Module"})),Wn={built_with_gradio:"сделано с помощью gradio",clear:"Очистить",or:"или",submit:"Исполнить"},Zn={click_to_upload:"Нажмите, чтобы загрузить",drop_audio:"Поместите Аудио Здесь",drop_csv:"Поместите CSV Здесь",drop_file:"Поместите Документ Здесь",drop_image:"Поместите Изображение Здесь",drop_video:"Поместите Видео Здесь"},kl={common:Wn,upload_text:Zn},Al=Object.freeze(Object.defineProperty({__proto__:null,common:Wn,default:kl,upload_text:Zn},Symbol.toStringTag,{value:"Module"})),Jn={built_with_gradio:"கிரேடியோ வுடன் உருவாக்கப்பட்டது",clear:"அழிக்கவும்",or:"அல்லது",submit:"சமர்ப்பிக்கவும்"},Yn={click_to_upload:"பதிவேற்ற அழுத்தவும்",drop_audio:"ஆடியோவை பதிவேற்றவும்",drop_csv:"csv ஐ பதிவேற்றவும்",drop_file:"கோப்பை பதிவேற்றவும்",drop_image:"படத்தை பதிவேற்றவும்",drop_video:"காணொளியை பதிவேற்றவும்"},Tl={common:Jn,upload_text:Yn},Bl=Object.freeze(Object.defineProperty({__proto__:null,common:Jn,default:Tl,upload_text:Yn},Symbol.toStringTag,{value:"Module"})),Qn={built_with_gradio:"Gradio ile oluşturulmuştur",clear:"Temizle",or:"veya",submit:"Yükle"},Kn={click_to_upload:"Yüklemek için Tıkla",drop_audio:"Kaydı Buraya Sürükle",drop_csv:"CSV'yi Buraya Sürükle",drop_file:"Dosyayı Buraya Sürükle",drop_image:"Resmi Buraya Sürükle",drop_video:"Videoyu Buraya Sürükle"},Pl={common:Qn,upload_text:Kn},Hl=Object.freeze(Object.defineProperty({__proto__:null,common:Qn,default:Pl,upload_text:Kn},Symbol.toStringTag,{value:"Module"})),$n={built_with_gradio:"Зроблено на основі gradio",clear:"Очистити",or:"або",submit:"Надіслати"},ei={click_to_upload:"Натисніть щоб завантажити",drop_audio:"Перетягніть аудіо сюди",drop_csv:"Перетягніть CSV-файл сюди",drop_file:"Перетягніть файл сюди",drop_image:"Перетягніть зображення сюди",drop_video:"Перетягніть відео сюди"},Nl={common:$n,upload_text:ei},Cl=Object.freeze(Object.defineProperty({__proto__:null,common:$n,default:Nl,upload_text:ei},Symbol.toStringTag,{value:"Module"})),ti={built_with_gradio:"کے ساتھ بنایا گیا Gradio",clear:"ہٹا دیں",or:"یا",submit:"جمع کریں"},ri={click_to_upload:"اپ لوڈ کے لیے کلک کریں",drop_audio:"یہاں آڈیو ڈراپ کریں",drop_csv:"یہاں فائل ڈراپ کریں",drop_file:"یہاں فائل ڈراپ کریں",drop_image:"یہاں تصویر ڈراپ کریں",drop_video:"یہاں ویڈیو ڈراپ کریں"},Ol={common:ti,upload_text:ri},Ml=Object.freeze(Object.defineProperty({__proto__:null,common:ti,default:Ol,upload_text:ri},Symbol.toStringTag,{value:"Module"})),oi={built_with_gradio:"gradio bilan qilingan",clear:"Tozalash",submit:"Yubor"},ni={click_to_upload:"Yuklash uchun Bosing",drop_audio:"Audioni Shu Yerga Tashlang",drop_csv:"CSVni Shu Yerga Tashlang",drop_file:"Faylni Shu Yerga Tashlang",drop_image:"Rasmni Shu Yerga Tashlang",drop_video:"Videoni Shu Yerga Tashlang"},Il={common:oi,upload_text:ni},Ll=Object.freeze(Object.defineProperty({__proto__:null,common:oi,default:Il,upload_text:ni},Symbol.toStringTag,{value:"Module"})),ii={built_with_gradio:"使用Gradio构建",clear:"清除",or:"或",submit:"提交"},ai={click_to_upload:"点击上传",drop_audio:"拖放音频至此处",drop_csv:"拖放CSV至此处",drop_file:"拖放文件至此处",drop_image:"拖放图片至此处",drop_video:"拖放视频至此处"},Rl={common:ii,upload_text:ai},zl=Object.freeze(Object.defineProperty({__proto__:null,common:ii,default:Rl,upload_text:ai},Symbol.toStringTag,{value:"Module"})),si={built_with_gradio:"使用Gradio構建",clear:"清除",or:"或",submit:"提交"},li={click_to_upload:"點擊上傳",drop_audio:"刪除音頻",drop_csv:"刪除CSV",drop_file:"刪除檔案",drop_image:"刪除圖片",drop_video:"刪除影片"},Dl={common:si,upload_text:li},jl=Object.freeze(Object.defineProperty({__proto__:null,common:si,default:Dl,upload_text:li},Symbol.toStringTag,{value:"Module"})),oo=Object.assign({"./lang/ar.json":Ys,"./lang/ca.json":Ks,"./lang/de.json":el,"./lang/en.json":rl,"./lang/es.json":nl,"./lang/fa.json":al,"./lang/fr.json":ll,"./lang/he.json":ul,"./lang/hi.json":dl,"./lang/ja.json":pl,"./lang/ko.json":_l,"./lang/lt.json":bl,"./lang/nl.json":vl,"./lang/pl.json":xl,"./lang/pt-BR.json":Sl,"./lang/ru.json":Al,"./lang/ta.json":Bl,"./lang/tr.json":Hl,"./lang/uk.json":Cl,"./lang/ur.json":Ml,"./lang/uz.json":Ll,"./lang/zh-CN.json":zl,"./lang/zh-tw.json":jl});function Ul(){let e={};for(const t in oo){const r=t.split("/").pop().split(".").shift();e[r]=oo[t].default}return e}const no=Ul();for(const e in no)Zo(e,no[e]);function Gl(){hs({fallbackLocale:"en",initialLocale:ps()})}function io(e){let t,r;return t=new Zs({props:{absolute:!e[4],status:e[14],timer:!1,queue_position:null,queue_size:null,translucent:!0,loading_text:e[21],$$slots:{error:[Vl]},$$scope:{ctx:e}}}),{c(){ot(t.$$.fragment)},m(o,n){De(t,o,n),r=!0},p(o,n){const i={};n[0]&16&&(i.absolute=!o[4]),n[0]&16384&&(i.status=o[14]),n[0]&1057024|n[1]&16384&&(i.$$scope={dirty:n,ctx:o}),t.$set(i)},i(o){r||(K(t.$$.fragment,o),r=!0)},o(o){ne(t.$$.fragment,o),r=!1},d(o){je(t,o)}}}function Fl(e){let t,r=e[20]("errors.contact_page_author")+"",o;return{c(){t=R("p"),o=M(r),b(t,"class","svelte-y6l4b")},m(n,i){A(n,t,i),B(t,o)},p(n,i){i[0]&1048576&&r!==(r=n[20]("errors.contact_page_author")+"")&&ee(o,r)},d(n){n&&k(t)}}}function ql(e){let t,r,o,n,i,a;return{c(){t=R("p"),r=M("Please "),o=R("a"),n=M("contact the author of the space"),a=M(" to let them know."),b(o,"href",i="https://huggingface.co/spaces/"+e[8]+"/discussions/new?title="+e[22].title(e[13]?.detail)+"&description="+e[22].description(e[13]?.detail,location.origin)),b(o,"class","svelte-y6l4b"),b(t,"class","svelte-y6l4b")},m(l,u){A(l,t,u),B(t,r),B(t,o),B(o,n),B(t,a)},p(l,u){u[0]&8448&&i!==(i="https://huggingface.co/spaces/"+l[8]+"/discussions/new?title="+l[22].title(l[13]?.detail)+"&description="+l[22].description(l[13]?.detail,location.origin))&&b(o,"href",i)},d(l){l&&k(t)}}}function Vl(e){let t,r,o,n=(e[13]?.message||"")+"",i,a;function l(c,f){return(c[13].status==="space_error"||c[13].status==="paused")&&c[13].discussions_enabled?ql:Fl}let u=l(e),s=u(e);return{c(){t=R("div"),r=R("p"),o=R("strong"),i=M(n),a=$(),s.c(),b(r,"class","svelte-y6l4b"),b(t,"class","error svelte-y6l4b"),b(t,"slot","error")},m(c,f){A(c,t,f),B(t,r),B(r,o),B(o,i),B(t,a),s.m(t,null)},p(c,f){f[0]&8192&&n!==(n=(c[13]?.message||"")+"")&&ee(i,n),u===(u=l(c))&&s?s.p(c,f):(s.d(1),s=u(c),s&&(s.c(),s.m(t,null)))},d(c){c&&k(t),s.d()}}}function Xl(e){let t,r,o,n;const i=[{app:e[16]},e[12],{theme_mode:e[15]},{control_page_title:e[5]},{target:e[9]},{autoscroll:e[0]},{show_footer:!e[4]},{app_mode:e[3]},{version:e[1]}];function a(s){e[31](s)}function l(s){e[32](s)}let u={};for(let s=0;s<i.length;s+=1)u=ho(u,i[s]);return e[10]!==void 0&&(u.ready=e[10]),e[11]!==void 0&&(u.render_complete=e[11]),t=new e[18]({props:u}),_e.push(()=>Qt(t,"ready",a)),_e.push(()=>Qt(t,"render_complete",l)),{c(){ot(t.$$.fragment)},m(s,c){De(t,s,c),n=!0},p(s,c){const f=c[0]&102971?Qi(i,[c[0]&65536&&{app:s[16]},c[0]&4096&&Ki(s[12]),c[0]&32768&&{theme_mode:s[15]},c[0]&32&&{control_page_title:s[5]},c[0]&512&&{target:s[9]},c[0]&1&&{autoscroll:s[0]},c[0]&16&&{show_footer:!s[4]},c[0]&8&&{app_mode:s[3]},c[0]&2&&{version:s[1]}]):{};!r&&c[0]&1024&&(r=!0,f.ready=s[10],Yt(()=>r=!1)),!o&&c[0]&2048&&(o=!0,f.render_complete=s[11],Yt(()=>o=!1)),t.$set(f)},i(s){n||(K(t.$$.fragment,s),n=!0)},o(s){ne(t.$$.fragment,s),n=!1},d(s){je(t,s)}}}function Wl(e){let t,r;return t=new e[19]({props:{auth_message:e[12].auth_message,root:e[12].root,space_id:e[8],app_mode:e[3]}}),{c(){ot(t.$$.fragment)},m(o,n){De(t,o,n),r=!0},p(o,n){const i={};n[0]&4096&&(i.auth_message=o[12].auth_message),n[0]&4096&&(i.root=o[12].root),n[0]&256&&(i.space_id=o[8]),n[0]&8&&(i.app_mode=o[3]),t.$set(i)},i(o){r||(K(t.$$.fragment,o),r=!0)},o(o){ne(t.$$.fragment,o),r=!1},d(o){je(t,o)}}}function Zl(e){let t,r,o,n,i,a=(e[14]==="pending"||e[14]==="error")&&!(e[12]&&e[12]?.auth_required)&&io(e);const l=[Wl,Xl],u=[];function s(c,f){return c[12]?.auth_required&&c[19]?0:c[12]&&c[18]&&c[17]?1:-1}return~(r=s(e))&&(o=u[r]=l[r](e)),{c(){a&&a.c(),t=$(),o&&o.c(),n=Se()},m(c,f){a&&a.m(c,f),A(c,t,f),~r&&u[r].m(c,f),A(c,n,f),i=!0},p(c,f){(c[14]==="pending"||c[14]==="error")&&!(c[12]&&c[12]?.auth_required)?a?(a.p(c,f),f[0]&20480&&K(a,1)):(a=io(c),a.c(),K(a,1),a.m(t.parentNode,t)):a&&(vt(),ne(a,1,1,()=>{a=null}),wt());let d=r;r=s(c),r===d?~r&&u[r].p(c,f):(o&&(vt(),ne(u[d],1,1,()=>{u[d]=null}),wt()),~r?(o=u[r],o?o.p(c,f):(o=u[r]=l[r](c),o.c()),K(o,1),o.m(n.parentNode,n)):o=null)},i(c){i||(K(a),K(o),i=!0)},o(c){ne(a),ne(o),i=!1},d(c){c&&(k(t),k(n)),a&&a.d(c),~r&&u[r].d(c)}}}function Jl(e){let t,r,o;function n(a){e[33](a)}let i={display:e[6]&&e[4],is_embed:e[4],info:!!e[8]&&e[7],version:e[1],initial_height:e[2],space:e[8],loaded:e[14]==="complete",$$slots:{default:[Zl]},$$scope:{ctx:e}};return e[9]!==void 0&&(i.wrapper=e[9]),t=new Ps({props:i}),_e.push(()=>Qt(t,"wrapper",n)),{c(){ot(t.$$.fragment)},m(a,l){De(t,a,l),o=!0},p(a,l){const u={};l[0]&80&&(u.display=a[6]&&a[4]),l[0]&16&&(u.is_embed=a[4]),l[0]&384&&(u.info=!!a[8]&&a[7]),l[0]&2&&(u.version=a[1]),l[0]&4&&(u.initial_height=a[2]),l[0]&256&&(u.space=a[8]),l[0]&16384&&(u.loaded=a[14]==="complete"),l[0]&2096955|l[1]&16384&&(u.$$scope={dirty:l,ctx:a}),!r&&l[0]&512&&(r=!0,u.wrapper=a[9],Yt(()=>r=!1)),t.$set(u)},i(a){o||(K(t.$$.fragment,a),o=!0)},o(a){ne(t.$$.fragment,a),o=!1},d(a){je(t,a)}}}let Yl=-1;function Ql(){const e=me({}),t=new Map,r=new IntersectionObserver(n=>{n.forEach(i=>{if(i.isIntersecting){let a=t.get(i.target);a!==void 0&&e.update(l=>({...l,[a]:!0}))}})});function o(n,i){t.set(i,n),r.observe(i)}return{register:o,subscribe:e.subscribe}}const ao=Ql();function Kl(e,t,r){let o,n;xe(e,_r,m=>r(20,o=m)),xe(e,ao,m=>r(30,n=m)),Gl();let{autoscroll:i}=t,{version:a}=t,{initial_height:l}=t,{app_mode:u}=t,{is_embed:s}=t,{theme_mode:c="system"}=t,{control_page_title:f}=t,{container:d}=t,{info:g}=t,{eager:h}=t,{mount_css:_=Xt}=t,{client:v}=t,{upload_files:x}=t,{space:E}=t,{host:p}=t,{src:w}=t,T=Yl++,C="pending",S=null,N,z=!1,J=!1,q,P=o("common.loading")+"...",D;async function X(m,Y){if(Y){let se=document.createElement("style");se.innerHTML=Y,m.appendChild(se)}await _(q.root+"/theme.css",document.head),q.stylesheets&&await Promise.all(q.stylesheets.map(se=>{let Te=se.startsWith("http:")||se.startsWith("https:");return _(Te?se:q.root+"/"+se,document.head)}))}async function H(m){const Y=await(await fetch(m+"/app_id")).text();S===null?S=Y:S!=Y&&location.reload(),setTimeout(()=>H(m),250)}function ce(m){let se=new URL(window.location.toString()).searchParams.get("__theme");return r(15,D=c||se||"system"),D==="dark"||D==="light"?j(m,D):r(15,D=F(m)),D}function F(m){const Y=se();window?.matchMedia("(prefers-color-scheme: dark)")?.addEventListener("change",se);function se(){let Te=window?.matchMedia?.("(prefers-color-scheme: dark)").matches?"dark":"light";return j(m,Te),Te}return Y}function j(m,Y){const se=s?m.parentElement:document.body,Te=s?m:m.parentElement;Te.style.background="var(--body-background-fill)",Y==="dark"?se.classList.add("dark"):se.classList.remove("dark")}let ie={message:"",load_status:"pending",status:"sleeping",detail:"SLEEPING"},ae,ue=!1;function y(m){r(13,ie=m)}Wt(async()=>{window.__gradio_mode__!=="website"&&r(15,D=ce(N));const m=p||E||w||location.origin;r(16,ae=await v(m,{status_callback:y,normalise_files:!1})),r(12,q=ae.config),window.__gradio_space__=q.space_id,r(13,ie={message:"",load_status:"complete",status:"running",detail:"RUNNING"}),await X(N,q.css),r(17,ue=!0),window.__is_colab__=q.is_colab,q.dev_mode&&H(q.root)}),Xi("upload_files",x);let Ae,ye;async function Fe(){r(18,Ae=(await gt(()=>import("./Blocks-b3881047.js").then(m=>m.B),["./Blocks-b3881047.js","./Button-748313a7.js","./Button-620848cb.css","./Blocks-0733f3b3.css"],import.meta.url)).default)}async function qe(){r(19,ye=(await gt(()=>import("./Login-ff3377b2.js"),["./Login-ff3377b2.js","./StaticForm-23a48556.js","./StaticForm-3812b7f1.css","./InteractiveTextbox-6c073f5e.js","./Textbox-c3e160c8.js","./Button-748313a7.js","./Button-620848cb.css","./BlockTitle-de7b2d6e.js","./Info-02b862eb.js","./Copy-754acc5f.js","./Textbox-dde6f8cc.css","./StaticColumn-6bd0cb0d.js","./StaticColumn-2853eb31.css","./Login-9c3cc0eb.css"],import.meta.url)).default)}function V(){q.auth_required?qe():Fe()}const W={readable_error:{NO_APP_FILE:o("errors.no_app_file"),CONFIG_ERROR:o("errors.config_error"),BUILD_ERROR:o("errors.build_error"),RUNTIME_ERROR:o("errors.runtime_error"),PAUSED:o("errors.space_paused")},title(m){return encodeURIComponent(o("errors.space_not_working"))},description(m,Y){return encodeURIComponent(`Hello,

Firstly, thanks for creating this space!

I noticed that the space isn't working correctly because there is ${this.readable_error[m]||"an error"}.

It would be great if you could take a look at this because this space is being embedded on ${Y}.

Thanks!`)}};Wt(async()=>{ao.register(T,N)});function te(m){z=m,r(10,z)}function re(m){J=m,r(11,J)}function de(m){N=m,r(9,N)}return e.$$set=m=>{"autoscroll"in m&&r(0,i=m.autoscroll),"version"in m&&r(1,a=m.version),"initial_height"in m&&r(2,l=m.initial_height),"app_mode"in m&&r(3,u=m.app_mode),"is_embed"in m&&r(4,s=m.is_embed),"theme_mode"in m&&r(23,c=m.theme_mode),"control_page_title"in m&&r(5,f=m.control_page_title),"container"in m&&r(6,d=m.container),"info"in m&&r(7,g=m.info),"eager"in m&&r(24,h=m.eager),"mount_css"in m&&r(25,_=m.mount_css),"client"in m&&r(26,v=m.client),"upload_files"in m&&r(27,x=m.upload_files),"space"in m&&r(8,E=m.space),"host"in m&&r(28,p=m.host),"src"in m&&r(29,w=m.src)},e.$$.update=()=>{e.$$.dirty[0]&9216&&r(14,C=!z&&ie.load_status!=="error"?"pending":!z&&ie.load_status==="error"?"error":ie.load_status),e.$$.dirty[0]&1090523136&&q&&(h||n[T])&&V(),e.$$.dirty[0]&2560&&J&&N.dispatchEvent(new CustomEvent("render",{bubbles:!0,cancelable:!1,composed:!0}))},[i,a,l,u,s,f,d,g,E,N,z,J,q,ie,C,D,ae,ue,Ae,ye,o,P,W,c,h,_,v,x,p,w,n,te,re,de]}class so extends Tt{constructor(t){super(),At(this,t,Kl,Jl,rt,{autoscroll:0,version:1,initial_height:2,app_mode:3,is_embed:4,theme_mode:23,control_page_title:5,container:6,info:7,eager:24,mount_css:25,client:26,upload_files:27,space:8,host:28,src:29},null,[-1,-1])}}const $l="./assets/index-29fa5a20.css";let sr;sr=[];function ec(){class e extends HTMLElement{constructor(){super(),this.host=this.getAttribute("host"),this.space=this.getAttribute("space"),this.src=this.getAttribute("src"),this.control_page_title=this.getAttribute("control_page_title"),this.initial_height=this.getAttribute("initial_height")??"300px",this.is_embed=this.getAttribute("embed")??"true",this.container=this.getAttribute("container")??"true",this.info=this.getAttribute("info")??!0,this.autoscroll=this.getAttribute("autoscroll"),this.eager=this.getAttribute("eager"),this.theme_mode=this.getAttribute("theme_mode"),this.updating=!1,this.loading=!1}async connectedCallback(){this.loading=!0,this.app&&this.app.$destroy(),typeof sr!="string"&&sr.forEach(n=>Xt(n,document.head)),await Xt($l,document.head);const r=new CustomEvent("domchange",{bubbles:!0,cancelable:!1,composed:!0});new MutationObserver(n=>{this.dispatchEvent(r)}).observe(this,{childList:!0}),this.app=new so({target:this,props:{space:this.space?this.space.trim():this.space,src:this.src?this.src.trim():this.src,host:this.host?this.host.trim():this.host,info:this.info!=="false",container:this.container!=="false",is_embed:this.is_embed!=="false",initial_height:this.initial_height,eager:this.eager==="true",version:"3-41-2",theme_mode:this.theme_mode,autoscroll:this.autoscroll==="true",control_page_title:this.control_page_title==="true",client:Er,upload_files:xr,app_mode:window.__gradio_mode__==="app"}}),this.updating&&this.setAttribute(this.updating.name,this.updating.value),this.loading=!1}static get observedAttributes(){return["src","space","host"]}attributeChangedCallback(r,o,n){if((r==="host"||r==="space"||r==="src")&&n!==o){if(this.updating={name:r,value:n},this.loading)return;this.app&&this.app.$destroy(),this.space=null,this.host=null,this.src=null,r==="host"?this.host=n:r==="space"?this.space=n:r==="src"&&(this.src=n),this.app=new so({target:this,props:{space:this.space?this.space.trim():this.space,src:this.src?this.src.trim():this.src,host:this.host?this.host.trim():this.host,info:this.info!=="false",container:this.container!=="false",is_embed:this.is_embed!=="false",initial_height:this.initial_height,eager:this.eager==="true",version:"3-41-2",theme_mode:this.theme_mode,autoscroll:this.autoscroll==="true",control_page_title:this.control_page_title==="true",client:Er,upload_files:xr,app_mode:window.__gradio_mode__==="app"}}),this.updating=!1}}}customElements.get("gradio-app")||customElements.define("gradio-app",e)}ec();export{Yi as $,kc as A,pe as B,wc as C,Wt as D,Ec as E,ot as F,De as G,je as H,xt as I,Pc as J,oe as K,Bc as L,Ni as M,Q as N,So as O,Os as P,Se as Q,_e as R,Tt as S,bc as T,Qt as U,Qi as V,Ki as W,Yt as X,Xi as Y,ho as Z,gt as _,Qe as a,xe as a0,_r as a1,Gl as a2,Hc as a3,Hs as a4,Oi as a5,Wi as a6,cc as a7,_o as a8,dc as a9,me as aA,sc as aB,ur as aC,yo as aa,vo as ab,bo as ac,Zs as ad,lc as ae,Bo as af,uc as ag,Gr as ah,go as ai,Vi as aj,yc as ak,vc as al,Ac as am,gc as an,mc as ao,_c as ap,ac as aq,xc as ar,xr as as,oc as at,tc as au,lo as av,rc as aw,pc as ax,Tc as ay,hc as az,Ee as b,bt as c,yt as d,At as e,fe as f,b as g,A as h,cr as i,B as j,k,kt as l,R as m,Z as n,$ as o,Pr as p,fc as q,vt as r,rt as s,M as t,ne as u,wt as v,K as w,ee as x,Me as y,Sc as z};
//# sourceMappingURL=index-2519a27e.js.map
