{"version": 3, "file": "index-8a93944d.js", "sources": ["../../../../js/app/src/components/Interpretation/utils.ts", "../../../../js/app/src/components/Interpretation/InterpretationComponents/Number.svelte", "../../../../js/app/src/components/Interpretation/InterpretationComponents/Dropdown.svelte", "../../../../js/app/src/components/Interpretation/InterpretationComponents/Checkbox.svelte", "../../../../js/app/src/components/Interpretation/InterpretationComponents/CheckboxGroup.svelte", "../../../../js/app/src/components/Interpretation/InterpretationComponents/Slider.svelte", "../../../../js/app/src/components/Interpretation/InterpretationComponents/Radio.svelte", "../../../../js/app/src/components/Interpretation/InterpretationComponents/Image.svelte", "../../../../js/app/src/components/Interpretation/InterpretationComponents/Audio.svelte", "../../../../js/app/src/components/Interpretation/InterpretationComponents/Textbox.svelte", "../../../../js/app/src/components/Interpretation/directory.ts", "../../../../js/app/src/components/Interpretation/Interpretation.svelte"], "sourcesContent": ["export const getSaliencyColor = (value: number): string => {\n\tvar color: [number, number, number] | null = null;\n\tif (value < 0) {\n\t\tcolor = [52, 152, 219];\n\t} else {\n\t\tcolor = [231, 76, 60];\n\t}\n\treturn colorToString(interpolate(Math.abs(value), [255, 255, 255], color));\n};\n\nconst interpolate = (\n\tval: number,\n\trgb1: [number, number, number],\n\trgb2: [number, number, number]\n): [number, number, number] => {\n\tif (val > 1) {\n\t\tval = 1;\n\t}\n\tval = Math.sqrt(val);\n\tvar rgb: [number, number, number] = [0, 0, 0];\n\tvar i;\n\tfor (i = 0; i < 3; i++) {\n\t\trgb[i] = Math.round(rgb1[i] * (1.0 - val) + rgb2[i] * val);\n\t}\n\treturn rgb;\n};\n\nconst colorToString = (rgb: [number, number, number]): string => {\n\treturn \"rgb(\" + rgb[0] + \", \" + rgb[1] + \", \" + rgb[2] + \")\";\n};\n\nexport function getObjectFitSize(\n\tcontains: boolean /* true = contain, false = cover */,\n\tcontainerWidth: number,\n\tcontainerHeight: number,\n\twidth: number,\n\theight: number\n): { width: number; height: number; x: number; y: number } {\n\tvar doRatio = width / height;\n\tvar cRatio = containerWidth / containerHeight;\n\tvar targetWidth = 0;\n\tvar targetHeight = 0;\n\tvar test = contains ? doRatio > cRatio : doRatio < cRatio;\n\n\tif (test) {\n\t\ttargetWidth = containerWidth;\n\t\ttargetHeight = targetWidth / doRatio;\n\t} else {\n\t\ttargetHeight = containerHeight;\n\t\ttargetWidth = targetHeight * doRatio;\n\t}\n\n\treturn {\n\t\twidth: targetWidth,\n\t\theight: targetHeight,\n\t\tx: (containerWidth - targetWidth) / 2,\n\t\ty: (containerHeight - targetHeight) / 2\n\t};\n}\n", "<script lang=\"ts\">\n\timport { getSaliencyColor } from \"../utils\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\n\texport let interpretation: [number, number][];\n\texport let label = \"\";\n</script>\n\n<div class=\"input-number\">\n\t<BlockTitle>{label}</BlockTitle>\n\t<div class=\"range\">\n\t\t{#each interpretation as interpret_value}\n\t\t\t<div\n\t\t\t\tclass=\"item\"\n\t\t\t\tstyle={\"background-color: \" + getSaliencyColor(interpret_value[1])}\n\t\t\t>\n\t\t\t\t{interpret_value[0]}\n\t\t\t</div>\n\t\t{/each}\n\t</div>\n</div>\n\n<style>\n\t.input-number {\n\t\ttransition: 150ms;\n\t\tbox-shadow: var(--shadow-drop);\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.input-number:hover {\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t}\n\t.range {\n\t\tdisplay: flex;\n\t}\n\n\t.item {\n\t\tflex: 1 1 0%;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { getSaliencyColor } from \"../utils\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\n\texport let interpretation: number[];\n\texport let choices: string[];\n\texport let label = \"\";\n</script>\n\n<div>\n\t<BlockTitle>{label}</BlockTitle>\n\t<ul class=\"dropdown-menu\">\n\t\t{#each choices as choice, i}\n\t\t\t<li\n\t\t\t\tclass=\"dropdown-item\"\n\t\t\t\tstyle={\"background-color: \" + getSaliencyColor(interpretation[i])}\n\t\t\t>\n\t\t\t\t{choice}\n\t\t\t</li>\n\t\t{/each}\n\t</ul>\n</div>\n\n<style>\n\t.dropdown-menu {\n\t\tbox-shadow: var(--shadow-drop);\n\t}\n\t.dropdown-item {\n\t\tdisplay: block;\n\t\ttransition: 150ms;\n\t\tcursor: pointer;\n\t\tbackground: var(--background-fill-primary);\n\t\tpadding: var(--size-2) var(--size-3);\n\t\twhite-space: nowrap;\n\t}\n\n\t.dropdown-item:first-child {\n\t\tborder-top-right-radius: var(--radius-md);\n\t\tborder-top-left-radius: var(--radius-md);\n\t}\n\t.dropdown-item:last-child {\n\t\tborder-bottom-right-radius: var(--radius-md);\n\t\tborder-bottom-left-radius: var(--radius-md);\n\t}\n\n\t.dropdown-item:hover {\n\t\tfont-weight: var(--weight-semibold);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { getSaliencyColor } from \"../utils\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\n\texport let label = \"\";\n\texport let original: boolean;\n\texport let interpretation: [number, number];\n</script>\n\n<div class=\"input-checkbox\">\n\t<BlockTitle>{label}</BlockTitle>\n\t<button class=\"checkbox-item\" class:selected={original}>\n\t\t<div\n\t\t\tclass=\"checkbox\"\n\t\t\tstyle={\"background-color: \" + getSaliencyColor(interpretation[0])}\n\t\t/>\n\t\t<div\n\t\t\tclass=\"checkbox\"\n\t\t\tstyle={\"background-color: \" + getSaliencyColor(interpretation[1])}\n\t\t>\n\t\t\t<svg viewBox=\"-10 -10 20 20\">\n\t\t\t\t<line\n\t\t\t\t\tx1=\"-7.5\"\n\t\t\t\t\ty1=\"0\"\n\t\t\t\t\tx2=\"-2.5\"\n\t\t\t\t\ty2=\"5\"\n\t\t\t\t\tstroke=\"black\"\n\t\t\t\t\tstroke-width=\"4\"\n\t\t\t\t\tstroke-linecap=\"round\"\n\t\t\t\t/>\n\t\t\t\t<line\n\t\t\t\t\tx1=\"-2.5\"\n\t\t\t\t\ty1=\"5\"\n\t\t\t\t\tx2=\"7.5\"\n\t\t\t\t\ty2=\"-7.5\"\n\t\t\t\t\tstroke=\"black\"\n\t\t\t\t\tstroke-width=\"4\"\n\t\t\t\t\tstroke-linecap=\"round\"\n\t\t\t\t/>\n\t\t\t</svg>\n\t\t</div>\n\t</button>\n</div>\n\n<style lang=\"postcss\">\n\t.input-checkbox {\n\t\tdisplay: inline-block;\n\t}\n\n\tsvg {\n\t\twidth: var(--size-4);\n\t\theight: var(--size-3);\n\t}\n\t.selected svg {\n\t\topacity: 1;\n\t}\n\t.input-checkbox {\n\t\tdisplay: flex;\n\t\tgap: var(--size-1);\n\t\tcursor: pointer;\n\t\tborder-radius: var(--radius-md);\n\t\tpadding: var(--size-2) var(--size-3);\n\t}\n\n\t.checkbox {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tbackground: var(--background-fill-primary);\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t}\n\n\t.checkbox-item {\n\t\ttransition: 150ms;\n\t\tbox-shadow: var(--shadow-drop);\n\t\tbackground: var(--background-fill-primary);\n\t}\n\n\t.checkbox-item:hover {\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t}\n\t.checkbox-item.selected {\n\t\tbackground: var(--color-accent-base);\n\t\tcolor: white;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { getSaliencyColor } from \"../utils\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\n\texport let original: string[];\n\texport let interpretation: [number, number][];\n\texport let choices: string[];\n\texport let label = \"\";\n</script>\n\n<div class=\"input-checkbox-group\">\n\t<BlockTitle>{label}</BlockTitle>\n\t{#each choices as choice, i}\n\t\t<button class=\"checkbox-item\" class:selected={original.includes(choice)}>\n\t\t\t<div\n\t\t\t\tclass=\"checkbox\"\n\t\t\t\tstyle={\"background-color: \" + getSaliencyColor(interpretation[i][0])}\n\t\t\t/>\n\t\t\t<div\n\t\t\t\tclass=\"checkbox\"\n\t\t\t\tstyle={\"background-color: \" + getSaliencyColor(interpretation[i][1])}\n\t\t\t>\n\t\t\t\t<svg viewBox=\"-10 -10 20 20\">\n\t\t\t\t\t<line\n\t\t\t\t\t\tx1=\"-7.5\"\n\t\t\t\t\t\ty1=\"0\"\n\t\t\t\t\t\tx2=\"-2.5\"\n\t\t\t\t\t\ty2=\"5\"\n\t\t\t\t\t\tstroke=\"black\"\n\t\t\t\t\t\tstroke-width=\"4\"\n\t\t\t\t\t\tstroke-linecap=\"round\"\n\t\t\t\t\t/>\n\t\t\t\t\t<line\n\t\t\t\t\t\tx1=\"-2.5\"\n\t\t\t\t\t\ty1=\"5\"\n\t\t\t\t\t\tx2=\"7.5\"\n\t\t\t\t\t\ty2=\"-7.5\"\n\t\t\t\t\t\tstroke=\"black\"\n\t\t\t\t\t\tstroke-width=\"4\"\n\t\t\t\t\t\tstroke-linecap=\"round\"\n\t\t\t\t\t/>\n\t\t\t\t</svg>\n\t\t\t</div>\n\t\t\t{choice}\n\t\t</button>\n\t{/each}\n</div>\n\n<style lang=\"postcss\">\n\tsvg {\n\t\twidth: var(--size-4);\n\t\theight: var(--size-3);\n\t}\n\t.selected svg {\n\t\topacity: 1;\n\t}\n\t.input-checkbox-group {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: var(--size-2);\n\t}\n\n\t.checkbox-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: var(--size-1);\n\t\ttransition: 150ms;\n\t\tcursor: pointer;\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--background-fill-primary);\n\t\tpadding: var(--size-2) var(--size-3);\n\t\tfont-weight: var(--weight-semibold);\n\t}\n\n\t.checkbox-item:hover {\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t}\n\n\t.checkbox {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tbackground: var(--background-fill-primary);\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t}\n\t.checkbox-item.selected {\n\t\tbackground: var(--color-accent-base);\n\t\tcolor: white;\n\t}\n\t.selected .checkbox {\n\t\tbackground: var(--color-accent-base);\n\t}\n\n\t.checkbox-item {\n\t\ttransition: 150ms;\n\t\tbox-shadow: var(--shadow-drop);\n\t\tbackground: var(--background-fill-primary);\n\t}\n\n\t.checkbox-item.selected {\n\t\tbackground: var(--color-accent-base);\n\t\tcolor: white;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { getSaliencyColor } from \"../utils\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\n\texport let original: number;\n\texport let interpretation: number[];\n\texport let minimum: number;\n\texport let maximum: number;\n\texport let step: number;\n\texport let label = \"\";\n</script>\n\n<div class=\"input-slider\">\n\t<BlockTitle>{label}</BlockTitle>\n\t<input type=\"range\" disabled min={minimum} max={maximum} {step} />\n\t<div class=\"range\">\n\t\t{#each interpretation as interpret_value}\n\t\t\t<div style={\"background-color: \" + getSaliencyColor(interpret_value)} />\n\t\t{/each}\n\t</div>\n\t<div class=\"original\">\n\t\t{original}\n\t</div>\n</div>\n\n<style>\n\tinput::-webkit-slider-thumb,\n\t.range::-moz-range-thumb {\n\t\t-webkit-appearance: none;\n\t\tappearance: none;\n\t\tcursor: pointer;\n\t\tborder-radius: var(--radius-md);\n\t\twidth: var(--size-5);\n\t\theight: var(--size-5);\n\t}\n\n\t.input-slider {\n\t\ttext-align: center;\n\t}\n\n\t.range {\n\t\tdisplay: flex;\n\t}\n\n\tinput {\n\t\ttransition: 150ms;\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--background-fill-primary);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-3);\n\t}\n\n\tinput:hover {\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t}\n\n\tinput::-webkit-slider-thumb,\n\tinput::-moz-range-thumb {\n\t\tbox-shadow: var(--shadow-drop);\n\t\tbackground: linear-gradient(\n\t\t\tto bottom,\n\t\t\tvar(--color-orange-300),\n\t\t\tvar(--color-orange-500)\n\t\t);\n\t}\n\n\t.original {\n\t\tdisplay: inline-block;\n\t\tmargin: var(--size-1) auto;\n\t\tborder-radius: var(--radius-md);\n\t\tpadding: var(--size-0-5) var(--size-2);\n\t}\n\n\t.range > div {\n\t\tflex: 1 1 0%;\n\t\theight: var(--size-4);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { getSaliencyColor } from \"../utils\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\n\texport let original: string;\n\texport let interpretation: number[];\n\texport let choices: string[];\n\texport let label = \"\";\n</script>\n\n<div class=\"input-radio\">\n\t<BlockTitle>{label}</BlockTitle>\n\t{#each choices as choice, i}\n\t\t<button class=\"radio-item\" class:selected={original === choice}>\n\t\t\t<div\n\t\t\t\tclass=\"radio-circle\"\n\t\t\t\tstyle={\"background-color: \" + getSaliencyColor(interpretation[i])}\n\t\t\t/>\n\t\t\t{choice}\n\t\t</button>\n\t{/each}\n</div>\n\n<style>\n\t.input-radio {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: var(--size-2);\n\t}\n\n\t.radio-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: var(--size-2);\n\t\ttransition: 150ms;\n\t\tcursor: pointer;\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--background-fill-primary);\n\t\tpadding: var(--size-2) var(--size-3);\n\t\tfont-weight: var(--weight-semibold);\n\t}\n\n\t.radio-item:hover {\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t}\n\t.radio-circle {\n\t\tbox-sizing: border-box;\n\t\tborder-radius: var(--radius-full);\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t}\n\t.radio-item.selected {\n\t\tbox-shadow: var(--shadow-drop);\n\t\tbackground: var(--color-accent-base);\n\t\tcolor: white;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { getSaliencyColor, getObjectFitSize } from \"../utils\";\n\timport { afterUpdate } from \"svelte\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\n\texport let original: string;\n\texport let interpretation: number[][];\n\texport let shape: undefined | [number, number];\n\texport let label = \"\";\n\n\tlet saliency_layer: HTMLCanvasElement;\n\tlet image: HTMLImageElement;\n\n\tfunction paintSaliency(\n\t\tdata: number[][],\n\t\tctx: CanvasRenderingContext2D,\n\t\twidth: number,\n\t\theight: number\n\t): void {\n\t\tvar cell_width = width / data[0].length;\n\t\tvar cell_height = height / data.length;\n\t\tvar r = 0;\n\t\tdata.forEach(function (row) {\n\t\t\tvar c = 0;\n\t\t\trow.forEach(function (cell) {\n\t\t\t\tctx.fillStyle = getSaliencyColor(cell);\n\t\t\t\tctx.fillRect(c * cell_width, r * cell_height, cell_width, cell_height);\n\t\t\t\tc++;\n\t\t\t});\n\t\t\tr++;\n\t\t});\n\t}\n\n\tafterUpdate(() => {\n\t\tlet size = getObjectFitSize(\n\t\t\ttrue,\n\t\t\timage.width,\n\t\t\timage.height,\n\t\t\timage.naturalWidth,\n\t\t\timage.naturalHeight\n\t\t);\n\t\tif (shape) {\n\t\t\tsize = getObjectFitSize(\n\t\t\t\ttrue,\n\t\t\t\tsize.width,\n\t\t\t\tsize.height,\n\t\t\t\tshape[0],\n\t\t\t\tshape[1]\n\t\t\t);\n\t\t}\n\t\tlet width = size.width;\n\t\tlet height = size.height;\n\t\tsaliency_layer.setAttribute(\"height\", `${height}`);\n\t\tsaliency_layer.setAttribute(\"width\", `${width}`);\n\t\tpaintSaliency(\n\t\t\tinterpretation,\n\t\t\tsaliency_layer.getContext(\"2d\")!,\n\t\t\twidth,\n\t\t\theight\n\t\t);\n\t});\n</script>\n\n<div class=\"input-image\">\n\t<BlockTitle>{label}</BlockTitle>\n\t<div class=\"image-preview\">\n\t\t<div class=\"interpretation\">\n\t\t\t<canvas bind:this={saliency_layer} />\n\t\t</div>\n\t\t<!-- svelte-ignore a11y-missing-attribute -->\n\t\t<img bind:this={image} src={original} />\n\t</div>\n</div>\n\n<style>\n\t.image-preview {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tbackground: var(--background-fill-primary);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-60);\n\t}\n\n\t.interpretation {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\topacity: 0.9;\n\t\ttransition: 150ms;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t.interpretation:hover {\n\t\topacity: 0.2;\n\t}\n\timg {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: contain;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { getSaliencyColor } from \"../utils\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\texport let interpretation: number[];\n\texport let label = \"\";\n</script>\n\n<div>\n\t<BlockTitle>{label}</BlockTitle>\n\t<div class=\"range\">\n\t\t{#each interpretation as interpret_value}\n\t\t\t<div\n\t\t\t\tclass=\"item\"\n\t\t\t\tstyle={\"background-color: \" + getSaliencyColor(interpret_value)}\n\t\t\t/>\n\t\t{/each}\n\t</div>\n</div>\n\n<style>\n\t.range {\n\t\tdisplay: flex;\n\t}\n\n\t.item {\n\t\tdisplay: flex;\n\t\theight: var(--size-4);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { getSaliencyColor } from \"../utils\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\texport let label = \"\";\n\texport let interpretation: [string, number][];\n</script>\n\n<div class=\"input-text\">\n\t<BlockTitle>{label}</BlockTitle>\n\t{#each interpretation as [text, saliency]}\n\t\t<span\n\t\t\tclass=\"text-span\"\n\t\t\tstyle={\"background-color: \" + getSaliencyColor(saliency)}\n\t\t>\n\t\t\t{text}\n\t\t</span>\n\t{/each}\n</div>\n\n<style>\n\t.input-text {\n\t\tborder-radius: var(--radius-md);\n\t\tpadding: var(--size-2);\n\t\twidth: var(--size-full);\n\t\toverflow-wrap: break-word;\n\t}\n\n\t.text-span {\n\t\tpadding: var(--size-1);\n\t}\n</style>\n", "import InterpretationNumber from \"./InterpretationComponents/Number.svelte\";\nimport InterpretationDropdown from \"./InterpretationComponents/Dropdown.svelte\";\nimport InterpretationCheckbox from \"./InterpretationComponents/Checkbox.svelte\";\nimport InterpretationCheckboxGroup from \"./InterpretationComponents/CheckboxGroup.svelte\";\nimport InterpretationSlider from \"./InterpretationComponents/Slider.svelte\";\nimport InterpretationRadio from \"./InterpretationComponents/Radio.svelte\";\nimport InterpretationImage from \"./InterpretationComponents/Image.svelte\";\nimport InterpretationAudio from \"./InterpretationComponents/Audio.svelte\";\nimport InterpretationTextbox from \"./InterpretationComponents/Textbox.svelte\";\n\nexport const component_map = {\n\taudio: InterpretationAudio,\n\tdropdown: InterpretationDropdown,\n\tcheckbox: InterpretationCheckbox,\n\tcheckboxgroup: InterpretationCheckboxGroup,\n\tnumber: InterpretationNumber,\n\tslider: InterpretationSlider,\n\tradio: InterpretationRadio,\n\timage: InterpretationImage,\n\ttextbox: InterpretationTextbox\n};\n", "<script lang=\"ts\">\n\timport type { ComponentType } from \"svelte\";\n\timport type { SvelteComponent } from \"svelte\";\n\timport { component_map } from \"./directory\";\n\n\texport let component: keyof typeof component_map;\n\texport let component_props: Record<string, any>;\n\texport let value: any;\n\n\t$: _component = component_map[component] as ComponentType<SvelteComponent>;\n</script>\n\n{#if value}\n\t<svelte:component\n\t\tthis={_component}\n\t\t{...component_props}\n\t\toriginal={value.original}\n\t\tinterpretation={value.interpretation}\n\t/>\n{/if}\n"], "names": ["getSaliencyColor", "value", "color", "colorToString", "interpolate", "val", "rgb1", "rgb2", "rgb", "i", "getObjectFitSize", "contains", "containerWidth", "containerHeight", "width", "height", "doRatio", "cRatio", "targetWidth", "targetHeight", "test", "ctx", "t0_value", "attr", "div", "div_style_value", "insert", "target", "anchor", "dirty", "set_data", "t0", "div1", "append", "div0", "interpretation", "$$props", "label", "li", "li_style_value", "ul", "choices", "div0_style_value", "div1_style_value", "div2", "button", "svg", "line0", "line1", "current", "original", "t2", "t2_value", "input", "minimum", "maximum", "step", "toggle_class", "t1", "t1_value", "img", "img_src_value", "canvas", "shape", "saliency_layer", "image", "paintSaliency", "data", "cell_width", "cell_height", "r", "row", "c", "cell", "afterUpdate", "size", "$$value", "span", "component_map", "InterpretationAudio", "InterpretationDropdown", "InterpretationCheckbox", "InterpretationCheckboxGroup", "InterpretationNumber", "InterpretationSlider", "InterpretationRadio", "InterpretationImage", "InterpretationTextbox", "create_if_block", "component", "component_props", "$$invalidate", "_component"], "mappings": "sVAAa,MAAAA,EAAoBC,GAA0B,CAC1D,IAAIC,EAAyC,KAC7C,OAAID,EAAQ,EACHC,EAAA,CAAC,GAAI,IAAK,GAAG,EAEbA,EAAA,CAAC,IAAK,GAAI,EAAE,EAEdC,GAAcC,GAAY,KAAK,IAAIH,CAAK,EAAG,CAAC,IAAK,IAAK,GAAG,EAAGC,CAAK,CAAC,CAC1E,EAEME,GAAc,CACnBC,EACAC,EACAC,IAC8B,CAC1BF,EAAM,IACHA,EAAA,GAEDA,EAAA,KAAK,KAAKA,CAAG,EACnB,IAAIG,EAAgC,CAAC,EAAG,EAAG,CAAC,EACxCC,EACJ,IAAKA,EAAI,EAAGA,EAAI,EAAGA,IAClBD,EAAIC,CAAC,EAAI,KAAK,MAAMH,EAAKG,CAAC,GAAK,EAAMJ,GAAOE,EAAKE,CAAC,EAAIJ,CAAG,EAEnD,OAAAG,CACR,EAEML,GAAiBK,GACf,OAASA,EAAI,CAAC,EAAI,KAAOA,EAAI,CAAC,EAAI,KAAOA,EAAI,CAAC,EAAI,IAGnD,SAASE,EACfC,EACAC,EACAC,EACAC,EACAC,EAC0D,CAC1D,IAAIC,EAAUF,EAAQC,EAClBE,EAASL,EAAiBC,EAC1BK,EAAc,EACdC,EAAe,EACfC,EAAOT,EAAWK,EAAUC,EAASD,EAAUC,EAEnD,OAAIG,GACWF,EAAAN,EACdO,EAAeD,EAAcF,IAEdG,EAAAN,EACfK,EAAcC,EAAeH,GAGvB,CACN,MAAOE,EACP,OAAQC,EACR,GAAIP,EAAiBM,GAAe,EACpC,GAAIL,EAAkBM,GAAgB,CAAA,CAExC,4FCjDcE,EAAK,CAAA,CAAA,oCAALA,EAAK,CAAA,CAAA,sCAOdC,EAAAD,KAAgB,CAAC,EAAA,8EAFXE,EAAAC,EAAA,QAAAC,EAAA,qBAAuBzB,EAAiBqB,KAAgB,CAAC,CAAA,CAAA,UAFjEK,EAKKC,EAAAH,EAAAI,CAAA,wBADHC,EAAA,GAAAP,KAAAA,EAAAD,KAAgB,CAAC,EAAA,KAAAS,EAAAC,EAAAT,CAAA,EAFXO,EAAA,GAAAJ,KAAAA,EAAA,qBAAuBzB,EAAiBqB,KAAgB,CAAC,CAAA,kIAH3DA,EAAc,CAAA,CAAA,uBAAnB,OAAIZ,GAAA,qMAHRiB,EAYKC,EAAAK,EAAAJ,CAAA,qBAVJK,EASKD,EAAAE,CAAA,qIARGb,EAAc,CAAA,CAAA,oBAAnB,OAAIZ,GAAA,EAAA,iHAAJ,4HAPQ,eAAA0B,CAAkC,EAAAC,EAClC,CAAA,MAAAC,EAAQ,EAAE,EAAAD,gSCKRf,EAAK,CAAA,CAAA,oCAALA,EAAK,CAAA,CAAA,wCAOdA,EAAM,CAAA,EAAA,uFAFAE,EAAAe,EAAA,QAAAC,EAAA,qBAAuBvC,EAAiBqB,KAAeA,EAAC,CAAA,CAAA,CAAA,CAAA,UAFhEK,EAKIC,EAAAW,EAAAV,CAAA,oCADFP,EAAM,CAAA,EAAA,KAAAS,EAAAC,EAAAT,CAAA,EAFAO,EAAA,GAAAU,KAAAA,EAAA,qBAAuBvC,EAAiBqB,KAAeA,EAAC,CAAA,CAAA,CAAA,kIAH1DA,EAAO,CAAA,CAAA,uBAAZ,OAAIZ,GAAA,mKAHRiB,EAYKC,EAAAH,EAAAI,CAAA,qBAVJK,EASIT,EAAAgB,CAAA,qIARInB,EAAO,CAAA,CAAA,oBAAZ,OAAIZ,GAAA,EAAA,iHAAJ,4HARQ,eAAA0B,CAAwB,EAAAC,GACxB,QAAAK,CAAiB,EAAAL,EACjB,CAAA,MAAAC,EAAQ,EAAE,EAAAD,8QCIRf,EAAK,CAAA,CAAA,oCAALA,EAAK,CAAA,CAAA,2RAITE,EAAAW,EAAA,QAAAQ,EAAA,qBAAuB1C,EAAiBqB,KAAe,CAAC,CAAA,CAAA,4XAIxDE,EAAAS,EAAA,QAAAW,EAAA,qBAAuB3C,EAAiBqB,KAAe,CAAC,CAAA,CAAA,6DAPnBA,EAAQ,CAAA,CAAA,uDAFvDK,EAiCKC,EAAAiB,EAAAhB,CAAA,qBA/BJK,EA8BQW,EAAAC,CAAA,EA7BPZ,EAGCY,EAAAX,CAAA,SACDD,EAwBKY,EAAAb,CAAA,EApBJC,EAmBKD,EAAAc,CAAA,EAlBJb,EAQCa,EAAAC,CAAA,EACDd,EAQCa,EAAAE,CAAA,wEAxBK,CAAAC,GAAApB,EAAA,GAAAa,KAAAA,EAAA,qBAAuB1C,EAAiBqB,KAAe,CAAC,CAAA,qBAIxD,CAAA4B,GAAApB,EAAA,GAAAc,KAAAA,EAAA,qBAAuB3C,EAAiBqB,KAAe,CAAC,CAAA,8CAPnBA,EAAQ,CAAA,CAAA,2GAP3C,GAAA,CAAA,MAAAgB,EAAQ,EAAE,EAAAD,GACV,SAAAc,CAAiB,EAAAd,GACjB,eAAAD,CAAgC,EAAAC,+UCK9Bf,EAAK,CAAA,CAAA,oCAALA,EAAK,CAAA,CAAA,0DAgCfA,EAAM,CAAA,EAAA,yJA3BCE,EAAAW,EAAA,QAAAQ,EAAA,qBAAuB1C,EAAiBqB,EAAe,CAAA,EAAAA,MAAG,CAAC,CAAA,CAAA,4XAI3DE,EAAAS,EAAA,QAAAW,EAAA,qBAAuB3C,EAAiBqB,EAAe,CAAA,EAAAA,MAAG,CAAC,CAAA,CAAA,6DAPtBA,EAAQ,CAAA,EAAC,SAASA,EAAM,CAAA,CAAA,CAAA,UAAtEK,EA+BQC,EAAAkB,EAAAjB,CAAA,EA9BPK,EAGCY,EAAAX,CAAA,SACDD,EAwBKY,EAAAb,CAAA,EApBJC,EAmBKD,EAAAc,CAAA,EAlBJb,EAQCa,EAAAC,CAAA,EACDd,EAQCa,EAAAE,CAAA,+BAxBKnB,EAAA,GAAAa,KAAAA,EAAA,qBAAuB1C,EAAiBqB,EAAe,CAAA,EAAAA,MAAG,CAAC,CAAA,mBAI3DQ,EAAA,GAAAc,KAAAA,EAAA,qBAAuB3C,EAAiBqB,EAAe,CAAA,EAAAA,MAAG,CAAC,CAAA,+BAuBlEA,EAAM,CAAA,EAAA,KAAAS,EAAAqB,EAAAC,CAAA,sBA9BsC/B,EAAQ,CAAA,EAAC,SAASA,EAAM,CAAA,CAAA,CAAA,+GADhEA,EAAO,CAAA,CAAA,uBAAZ,OAAIZ,GAAA,gKAFPiB,EAoCKC,EAAAH,EAAAI,CAAA,yJAlCGP,EAAO,CAAA,CAAA,oBAAZ,OAAIZ,GAAA,EAAA,iHAAJ,4HARS,SAAAyC,CAAkB,EAAAd,GAClB,eAAAD,CAAkC,EAAAC,GAClC,QAAAK,CAAiB,EAAAL,EACjB,CAAA,MAAAC,EAAQ,EAAE,EAAAD,oXCMRf,EAAK,CAAA,CAAA,qCAALA,EAAK,CAAA,CAAA,4EAIJ,qBAAuBrB,EAAiBqB,EAAe,CAAA,CAAA,CAAA,wCAAnEK,EAAuEC,EAAAH,EAAAI,CAAA,sBAA3D,qBAAuB5B,EAAiBqB,EAAe,CAAA,CAAA,4IAD7DA,EAAc,CAAA,CAAA,uBAAnB,OAAIZ,GAAA,wJAKLY,EAAQ,CAAA,CAAA,8CAPwBA,EAAO,CAAA,CAAA,YAAOA,EAAO,CAAA,CAAA,+KAFxDK,EAWKC,EAAAiB,EAAAhB,CAAA,qBATJK,EAAiEW,EAAAS,CAAA,SACjEpB,EAIKW,EAAAV,CAAA,0DACLD,EAEKW,EAAAZ,CAAA,wGAR6BX,EAAO,CAAA,CAAA,uBAAOA,EAAO,CAAA,CAAA,wCAE/CA,EAAc,CAAA,CAAA,oBAAnB,OAAIZ,GAAA,EAAA,iHAAJ,sBAKDY,EAAQ,CAAA,CAAA,sHAjBC,SAAA6B,CAAgB,EAAAd,GAChB,eAAAD,CAAwB,EAAAC,GACxB,QAAAkB,CAAe,EAAAlB,GACf,QAAAmB,CAAe,EAAAnB,GACf,KAAAoB,CAAY,EAAApB,EACZ,CAAA,MAAAC,EAAQ,EAAE,EAAAD,2cCERf,EAAK,CAAA,CAAA,oCAALA,EAAK,CAAA,CAAA,+CAOfA,EAAM,CAAA,EAAA,yGAFCE,EAAAC,EAAA,QAAAC,EAAA,qBAAuBzB,EAAiBqB,KAAeA,EAAC,CAAA,CAAA,CAAA,CAAA,2CAHtBoC,EAAAZ,EAAA,WAAAxB,OAAaA,EAAM,CAAA,CAAA,UAA9DK,EAMQC,EAAAkB,EAAAjB,CAAA,EALPK,EAGCY,EAAArB,CAAA,+BADOK,EAAA,GAAAJ,KAAAA,EAAA,qBAAuBzB,EAAiBqB,KAAeA,EAAC,CAAA,CAAA,CAAA,+BAE/DA,EAAM,CAAA,EAAA,KAAAS,EAAA4B,EAAAC,CAAA,OALmCF,EAAAZ,EAAA,WAAAxB,OAAaA,EAAM,CAAA,CAAA,+GADxDA,EAAO,CAAA,CAAA,uBAAZ,OAAIZ,GAAA,yJAFPiB,EAWKC,EAAAH,EAAAI,CAAA,yJATGP,EAAO,CAAA,CAAA,oBAAZ,OAAIZ,GAAA,EAAA,mHAAJ,4HARS,SAAAyC,CAAgB,EAAAd,GAChB,eAAAD,CAAwB,EAAAC,GACxB,QAAAK,CAAiB,EAAAL,EACjB,CAAA,MAAAC,EAAQ,EAAE,EAAAD,6TCyDRf,EAAK,CAAA,CAAA,oCAALA,EAAK,CAAA,CAAA,4QAMWA,EAAQ,CAAA,CAAA,GAAAE,EAAAqC,EAAA,MAAAC,CAAA,6GAPtCnC,EASKC,EAAAiB,EAAAhB,CAAA,qBAPJK,EAMKW,EAAAZ,CAAA,EALJC,EAEKD,EAAAE,CAAA,EADJD,EAAoCC,EAAA4B,CAAA,iBAGrC7B,EAAuCD,EAAA4B,CAAA,sGAAXvC,EAAQ,CAAA,CAAA,oJAjE1B,SAAA6B,CAAgB,EAAAd,GAChB,eAAAD,CAA0B,EAAAC,GAC1B,MAAA2B,CAAmC,EAAA3B,EACnC,CAAA,MAAAC,EAAQ,EAAE,EAAAD,EAEjB4B,EACAC,EAEK,SAAAC,EACRC,EACA9C,EACAP,EACAC,EAAc,CAEV,IAAAqD,EAAatD,EAAQqD,EAAK,CAAC,EAAE,OAC7BE,EAActD,EAASoD,EAAK,OAC5BG,EAAI,EACRH,EAAK,QAAO,SAAWI,GAAG,CACrB,IAAAC,EAAI,EACRD,GAAI,QAAO,SAAWE,GAAI,CACzBpD,EAAI,UAAYrB,EAAiByE,EAAI,EACrCpD,EAAI,SAASmD,EAAIJ,EAAYE,EAAID,EAAaD,EAAYC,CAAW,EACrEG,MAEDF,MAIFI,GAAW,IAAA,CACN,IAAAC,EAAOjE,EACV,GACAuD,EAAM,MACNA,EAAM,OACNA,EAAM,aACNA,EAAM,aAAa,EAEhBF,IACHY,EAAOjE,EACN,GACAiE,EAAK,MACLA,EAAK,OACLZ,EAAM,CAAC,EACPA,EAAM,CAAC,CAAA,OAGLjD,EAAQ6D,EAAK,MACb5D,EAAS4D,EAAK,OAClBX,EAAe,aAAa,YAAajD,GAAM,EAC/CiD,EAAe,aAAa,WAAYlD,GAAK,EAC7CoD,EACC/B,EACA6B,EAAe,WAAW,IAAI,EAC9BlD,EACAC,CAAM,6CASaiD,EAAcY,oDAGlBX,EAAKW,gYC9DTvD,EAAK,CAAA,CAAA,oCAALA,EAAK,CAAA,CAAA,gHAKR,qBAAuBrB,EAAiBqB,EAAe,CAAA,CAAA,CAAA,UAF/DK,EAGCC,EAAAH,EAAAI,CAAA,sBADO,qBAAuB5B,EAAiBqB,EAAe,CAAA,CAAA,kIAHzDA,EAAc,CAAA,CAAA,uBAAnB,OAAIZ,GAAA,8JAHRiB,EAUKC,EAAAK,EAAAJ,CAAA,qBARJK,EAOKD,EAAAE,CAAA,qIANGb,EAAc,CAAA,CAAA,oBAAnB,OAAIZ,GAAA,EAAA,mHAAJ,4HAPQ,eAAA0B,CAAwB,EAAAC,EACxB,CAAA,MAAAC,EAAQ,EAAE,EAAAD,0SCIRf,EAAK,CAAA,CAAA,oCAALA,EAAK,CAAA,CAAA,yCAMfA,EAAI,CAAA,EAAA,mGAFE,qBAAuBrB,EAAiBqB,EAAQ,CAAA,CAAA,CAAA,UAFxDK,EAKMC,EAAAkD,EAAAjD,CAAA,oCADJP,EAAI,CAAA,EAAA,KAAAS,EAAAC,EAAAT,CAAA,cAFE,qBAAuBtB,EAAiBqB,EAAQ,CAAA,CAAA,gIAHlDA,EAAc,CAAA,CAAA,uBAAnB,OAAIZ,GAAA,wJAFPiB,EAUKC,EAAAH,EAAAI,CAAA,wJARGP,EAAc,CAAA,CAAA,oBAAnB,OAAIZ,GAAA,EAAA,mHAAJ,wHANS,GAAA,CAAA,MAAA4B,EAAQ,EAAE,EAAAD,GACV,eAAAD,CAAkC,EAAAC,8LCMvC,MAAM0C,GAAgB,CAC5B,MAAOC,GACP,SAAUC,GACV,SAAUC,GACV,cAAeC,GACf,OAAQC,GACR,OAAQC,GACR,MAAOC,GACP,MAAOC,GACP,QAASC,EACV,oCCLMlE,EAAe,CAAA,EACT,CAAA,SAAAA,KAAM,QAAQ,GACR,eAAAA,KAAM,uBAHhBA,EAAU,CAAA,6MACZA,EAAe,CAAA,CAAA,EACTQ,EAAA,GAAA,CAAA,SAAAR,KAAM,QAAQ,QACR,eAAAA,KAAM,oCAHhBA,EAAU,CAAA,GAAA,uRAFbA,EAAK,CAAA,GAAAmE,GAAAnE,CAAA,yEAALA,EAAK,CAAA,oMAPE,UAAAoE,CAAqC,EAAArD,GACrC,gBAAAsD,CAAoC,EAAAtD,GACpC,MAAAnC,CAAU,EAAAmC,qKAElBuD,EAAA,EAAAC,EAAad,GAAcW,CAAS,CAAA"}