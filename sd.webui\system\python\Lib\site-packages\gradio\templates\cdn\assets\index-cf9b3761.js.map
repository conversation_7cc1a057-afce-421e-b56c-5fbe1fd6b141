{"version": 3, "file": "index-cf9b3761.js", "sources": ["../../../../js/icons/src/TextHighlight.svelte", "../../../../js/highlightedtext/static/Highlightedtext.svelte", "../../../../js/highlightedtext/static/StaticHighlightedtext.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\taria-hidden=\"true\"\n\trole=\"img\"\n\tclass=\"iconify iconify--carbon\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tpreserveAspectRatio=\"xMidYMid meet\"\n\tviewBox=\"0 0 32 32\"\n>\n\t<path\n\t\tfill=\"currentColor\"\n\t\td=\"M12 15H5a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5V5a1 1 0 0 0-1-1H3V2h6a3 3 0 0 1 3 3zM5 9a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h5V9zm15 14v2a1 1 0 0 0 1 1h5v-4h-5a1 1 0 0 0-1 1z\"\n\t/>\n\t<path\n\t\tfill=\"currentColor\"\n\t\td=\"M2 30h28V2Zm26-2h-7a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5v-2a1 1 0 0 0-1-1h-6v-2h6a3 3 0 0 1 3 3Z\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\tconst browser = typeof document !== \"undefined\";\n\timport { colors } from \"@gradio/theme\";\n\timport { get_next_color } from \"@gradio/utils\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\texport let value: [string, string | number][] = [];\n\texport let show_legend = false;\n\texport let color_map: Record<string, string> = {};\n\texport let selectable = false;\n\n\tlet ctx: CanvasRenderingContext2D;\n\n\tlet _color_map: Record<string, { primary: string; secondary: string }> = {};\n\tlet active = \"\";\n\n\tfunction splitTextByNewline(text: string): string[] {\n\t\treturn text.split(\"\\n\");\n\t}\n\n\tfunction correct_color_map(): void {\n\t\tfor (const col in color_map) {\n\t\t\tconst _c = color_map[col].trim();\n\t\t\tif (_c in colors) {\n\t\t\t\t_color_map[col] = colors[_c as keyof typeof colors];\n\t\t\t} else {\n\t\t\t\t_color_map[col] = {\n\t\t\t\t\tprimary: browser ? name_to_rgba(color_map[col], 1) : color_map[col],\n\t\t\t\t\tsecondary: browser\n\t\t\t\t\t\t? name_to_rgba(color_map[col], 0.5)\n\t\t\t\t\t\t: color_map[col]\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction name_to_rgba(name: string, a: number): string {\n\t\tif (!ctx) {\n\t\t\tvar canvas = document.createElement(\"canvas\");\n\t\t\tctx = canvas.getContext(\"2d\")!;\n\t\t}\n\t\tctx.fillStyle = name;\n\t\tctx.fillRect(0, 0, 1, 1);\n\t\tconst [r, g, b] = ctx.getImageData(0, 0, 1, 1).data;\n\t\tctx.clearRect(0, 0, 1, 1);\n\t\treturn `rgba(${r}, ${g}, ${b}, ${255 / a})`;\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tselect: SelectData;\n\t}>();\n\n\tlet mode: \"categories\" | \"scores\";\n\n\t$: {\n\t\tif (!color_map) {\n\t\t\tcolor_map = {};\n\t\t}\n\t\tif (value.length > 0) {\n\t\t\tfor (let [_, label] of value) {\n\t\t\t\tif (label !== null) {\n\t\t\t\t\tif (typeof label === \"string\") {\n\t\t\t\t\t\tmode = \"categories\";\n\t\t\t\t\t\tif (!(label in color_map)) {\n\t\t\t\t\t\t\tlet color = get_next_color(Object.keys(color_map).length);\n\t\t\t\t\t\t\tcolor_map[label] = color;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmode = \"scores\";\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tcorrect_color_map();\n\t}\n\n\tfunction handle_mouseover(label: string): void {\n\t\tactive = label;\n\t}\n\tfunction handle_mouseout(): void {\n\t\tactive = \"\";\n\t}\n</script>\n\n<!-- \n\t@todo victor: try reimplementing without flex (negative margins on container to avoid left margin on linebreak). \n\tIf not possible hijack the copy execution like this:\n\n<svelte:window\n\ton:copy|preventDefault={() => {\n\t\tconst selection =.getSelection()?.toString();\n\t\tconsole.log(selection?.replaceAll(\"\\n\", \" \"));\n\t}}\n/>\n-->\n\n<div class=\"container\">\n\t{#if mode === \"categories\"}\n\t\t{#if show_legend}\n\t\t\t<div\n\t\t\t\tclass=\"category-legend\"\n\t\t\t\tdata-testid=\"highlighted-text:category-legend\"\n\t\t\t>\n\t\t\t\t{#each Object.entries(_color_map) as [category, color], i}\n\t\t\t\t\t<!-- TODO: fix -->\n\t\t\t\t\t<!-- svelte-ignore a11y-no-static-element-interactions -->\n\t\t\t\t\t<div\n\t\t\t\t\t\ton:mouseover={() => handle_mouseover(category)}\n\t\t\t\t\t\ton:focus={() => handle_mouseover(category)}\n\t\t\t\t\t\ton:mouseout={() => handle_mouseout()}\n\t\t\t\t\t\ton:blur={() => handle_mouseout()}\n\t\t\t\t\t\tclass=\"category-label\"\n\t\t\t\t\t\tstyle={\"background-color:\" + color.secondary}\n\t\t\t\t\t>\n\t\t\t\t\t\t{category}\n\t\t\t\t\t</div>\n\t\t\t\t{/each}\n\t\t\t</div>\n\t\t{/if}\n\t\t<div class=\"textfield\">\n\t\t\t{#each value as [text, category], i}\n\t\t\t\t{#each splitTextByNewline(text) as line, j}\n\t\t\t\t\t{#if line.trim() !== \"\"}\n\t\t\t\t\t\t<!-- TODO: fix -->\n\t\t\t\t\t\t<!-- svelte-ignore a11y-no-static-element-interactions -->\n\t\t\t\t\t\t<!-- svelte-ignore a11y-click-events-have-key-events-->\n\t\t\t\t\t\t<span\n\t\t\t\t\t\t\tclass=\"textspan\"\n\t\t\t\t\t\t\tstyle:background-color={category === null ||\n\t\t\t\t\t\t\t(active && active !== category)\n\t\t\t\t\t\t\t\t? \"\"\n\t\t\t\t\t\t\t\t: _color_map[category].secondary}\n\t\t\t\t\t\t\tclass:no-cat={category === null ||\n\t\t\t\t\t\t\t\t(active && active !== category)}\n\t\t\t\t\t\t\tclass:hl={category !== null}\n\t\t\t\t\t\t\tclass:selectable\n\t\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\t\tdispatch(\"select\", {\n\t\t\t\t\t\t\t\t\tindex: i,\n\t\t\t\t\t\t\t\t\tvalue: [text, category]\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<span class:no-label={!_color_map[category]} class=\"text\"\n\t\t\t\t\t\t\t\t>{line}</span\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{#if !show_legend && category !== null}\n\t\t\t\t\t\t\t\t&nbsp;\n\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass=\"label\"\n\t\t\t\t\t\t\t\t\tstyle:background-color={category === null ||\n\t\t\t\t\t\t\t\t\t(active && active !== category)\n\t\t\t\t\t\t\t\t\t\t? \"\"\n\t\t\t\t\t\t\t\t\t\t: _color_map[category].primary}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{category}\n\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t</span>\n\t\t\t\t\t{/if}\n\t\t\t\t\t{#if j < splitTextByNewline(text).length - 1}\n\t\t\t\t\t\t<br />\n\t\t\t\t\t{/if}\n\t\t\t\t{/each}\n\t\t\t{/each}\n\t\t</div>\n\t{:else}\n\t\t{#if show_legend}\n\t\t\t<div class=\"color-legend\" data-testid=\"highlighted-text:color-legend\">\n\t\t\t\t<span>-1</span>\n\t\t\t\t<span>0</span>\n\t\t\t\t<span>+1</span>\n\t\t\t</div>\n\t\t{/if}\n\t\t<div class=\"textfield\" data-testid=\"highlighted-text:textfield\">\n\t\t\t{#each value as [text, _score]}\n\t\t\t\t{@const score = typeof _score === \"string\" ? parseInt(_score) : _score}\n\t\t\t\t<span\n\t\t\t\t\tclass=\"textspan score-text\"\n\t\t\t\t\tstyle={\"background-color: rgba(\" +\n\t\t\t\t\t\t(score < 0 ? \"128, 90, 213,\" + -score : \"239, 68, 60,\" + score) +\n\t\t\t\t\t\t\")\"}\n\t\t\t\t>\n\t\t\t\t\t<span class=\"text\">{text}</span>\n\t\t\t\t</span>\n\t\t\t{/each}\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-sm);\n\t\tpadding: var(--block-padding);\n\t}\n\t.hl + .hl {\n\t\tmargin-left: var(--size-1);\n\t}\n\n\t.textspan:last-child > .label {\n\t\tmargin-right: 0;\n\t}\n\n\t.category-legend {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: var(--spacing-sm);\n\t\tcolor: black;\n\t}\n\n\t.category-label {\n\t\tcursor: pointer;\n\t\tborder-radius: var(--radius-xs);\n\t\tpadding-right: var(--size-2);\n\t\tpadding-left: var(--size-2);\n\t\tfont-weight: var(--weight-semibold);\n\t}\n\n\t.color-legend {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tborder-radius: var(--radius-xs);\n\t\tbackground: linear-gradient(\n\t\t\tto right,\n\t\t\tvar(--color-purple),\n\t\t\trgba(255, 255, 255, 0),\n\t\t\tvar(--color-red)\n\t\t);\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tfont-weight: var(--weight-semibold);\n\t}\n\n\t.textfield {\n\t\tbox-sizing: border-box;\n\t\tborder-radius: var(--radius-xs);\n\t\tbackground: var(--background-fill-primary);\n\t\tbackground-color: transparent;\n\t\tmax-width: var(--size-full);\n\t\tline-height: var(--scale-4);\n\t\tword-break: break-all;\n\t}\n\n\t.textspan {\n\t\ttransition: 150ms;\n\t\tborder-radius: var(--radius-xs);\n\t\tpadding-top: 2.5px;\n\t\tpadding-right: var(--size-1);\n\t\tpadding-bottom: 3.5px;\n\t\tpadding-left: var(--size-1);\n\t\tcolor: black;\n\t}\n\n\t.label {\n\t\ttransition: 150ms;\n\t\tmargin-top: 1px;\n\t\tborder-radius: var(--radius-xs);\n\t\tpadding: 1px 5px;\n\t\tcolor: var(--body-text-color);\n\t\tcolor: white;\n\t\tfont-weight: var(--weight-bold);\n\t\tfont-size: var(--text-sm);\n\t\ttext-transform: uppercase;\n\t}\n\n\t.text {\n\t\tcolor: black;\n\t\twhite-space: pre-wrap;\n\t}\n\n\t.score-text .text {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.score-text {\n\t\tmargin-right: var(--size-1);\n\t\tpadding: var(--size-1);\n\t}\n\n\t.no-cat {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.no-label {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.selectable {\n\t\tcursor: pointer;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport HighlightedText from \"./Highlightedtext.svelte\";\n\timport { Block, BlockLabel, Empty } from \"@gradio/atoms\";\n\timport { TextHighlight } from \"@gradio/icons\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: [string, string | number][];\n\tlet old_value: [string, string | number][];\n\texport let show_legend: boolean;\n\texport let color_map: Record<string, string> = {};\n\texport let label = $_(\"highlighted_text.highlighted_text\");\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let selectable = false;\n\texport let gradio: Gradio<{\n\t\tselect: SelectData;\n\t\tchange: never;\n\t}>;\n\n\t$: if (!color_map && Object.keys(color_map).length) {\n\t\tcolor_map = color_map;\n\t}\n\n\texport let loading_status: LoadingStatus;\n\n\t$: {\n\t\tif (value !== old_value) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}\n\t}\n</script>\n\n<Block\n\ttest_id=\"highlighted-text\"\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\tpadding={false}\n\t{container}\n\t{scale}\n\t{min_width}\n>\n\t<StatusTracker {...loading_status} />\n\t{#if label}\n\t\t<BlockLabel\n\t\t\tIcon={TextHighlight}\n\t\t\t{label}\n\t\t\tfloat={false}\n\t\t\tdisable={container === false}\n\t\t/>\n\t{/if}\n\n\t{#if value}\n\t\t<HighlightedText\n\t\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\t\t{selectable}\n\t\t\t{value}\n\t\t\t{show_legend}\n\t\t\t{color_map}\n\t\t/>\n\t{:else}\n\t\t<Empty>\n\t\t\t<TextHighlight />\n\t\t</Empty>\n\t{/if}\n</Block>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path0", "path1", "constants_0", "child_ctx", "ctx", "create_if_block_5", "i", "div", "create_if_block_4", "span1", "span0", "set_data", "t0", "t0_value", "create_if_block_3", "toggle_class", "set_style", "span", "t1", "t1_value", "br", "show_if", "splitTextByNewline", "dirty", "each_value_1", "ensure_array_like", "create_if_block", "text", "browser", "value", "$$props", "show_legend", "color_map", "selectable", "_color_map", "active", "correct_color_map", "col", "_c", "colors", "$$invalidate", "name_to_rgba", "name", "a", "canvas", "r", "g", "b", "dispatch", "createEventDispatcher", "mode", "handle_mouseover", "label", "handle_mouseout", "mouseover_handler", "category", "focus_handler", "_", "color", "get_next_color", "TextHighlight", "blocklabel_changes", "create_if_block_1", "elem_id", "elem_classes", "visible", "old_value", "$_", "container", "scale", "min_width", "gradio", "loading_status", "select_handler", "detail"], "mappings": "goCAAAA,EAmBKC,EAAAC,EAAAC,CAAA,EARJC,EAGCF,EAAAG,CAAA,EACDD,EAGCF,EAAAI,CAAA,gKCgKyB,MAAAC,EAAA,OAAAC,OAAW,SAAW,SAASA,EAAM,EAAA,CAAA,EAAIA,EAAM,EAAA,+QATnEC,EAAW,CAAA,GAAAC,GAAA,MAQRD,EAAK,CAAA,CAAA,uBAAV,OAAIE,GAAA,kNADPX,EAYKC,EAAAW,EAAAT,CAAA,8DAnBAM,EAAW,CAAA,uEAQRA,EAAK,CAAA,CAAA,oBAAV,OAAIE,GAAA,EAAA,kHAAJ,yEA7EEF,EAAW,CAAA,GAAAI,GAAAJ,CAAA,MAsBRA,EAAK,CAAA,CAAA,uBAAV,OAAIE,GAAA,kKADPX,EA8CKC,EAAAW,EAAAT,CAAA,8DAnEAM,EAAW,CAAA,iFAsBRA,EAAK,CAAA,CAAA,oBAAV,OAAIE,GAAA,EAAA,kHAAJ,6PAgDFX,EAIKC,EAAAW,EAAAT,CAAA,2CAWiBM,EAAI,EAAA,EAAA,0JAJjB,2BACLA,EAAK,EAAA,EAAG,EAAI,iBAAmBA,EAAK,EAAA,EAAG,eAAiBA,EAAK,EAAA,GAC9D,GAAG,UAJLT,EAOMC,EAAAa,EAAAX,CAAA,EADLC,EAA+BU,EAAAC,CAAA,uCAAXN,EAAI,EAAA,EAAA,KAAAO,EAAAC,EAAAC,CAAA,iBAJjB,2BACLT,EAAK,EAAA,EAAG,EAAI,iBAAmBA,EAAK,EAAA,EAAG,eAAiBA,EAAK,EAAA,GAC9D,8DA9EK,OAAO,QAAQA,EAAU,CAAA,CAAA,CAAA,uBAA9B,OAAI,GAAA,2LAJPT,EAkBKC,EAAAW,EAAAT,CAAA,4EAdG,OAAO,QAAQM,EAAU,CAAA,CAAA,CAAA,oBAA9B,OAAIE,GAAA,EAAA,mHAAJ,sDAWCF,EAAQ,EAAA,EAAA,4KAFF,oBAAsBA,EAAK,EAAA,EAAC,SAAS,UAN7CT,EASKC,EAAAW,EAAAT,CAAA,mIADHM,EAAQ,EAAA,EAAA,KAAAO,EAAAC,EAAAC,CAAA,iBAFF,oBAAsBT,EAAK,EAAA,EAAC,+EAgC/BA,EAAI,EAAA,EAAA,cAEDA,EAAW,CAAA,GAAIA,EAAQ,EAAA,IAAK,MAAIU,GAAAV,CAAA,0IAHfW,EAAAL,EAAA,WAAA,CAAAN,KAAWA,EAAQ,EAAA,CAAA,CAAA,wCAX5BW,EAAAN,EAAA,SAAAL,QAAa,MACzBA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,EAAA,CAAA,EACrBW,EAAAN,EAAA,KAAAL,QAAa,IAAI,yBANHY,EAAAP,EAAA,mBAAAL,QAAa,MACpCA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,EAAA,EAC3B,GACAA,EAAU,CAAA,EAACA,EAAQ,EAAA,CAAA,EAAE,SAAS,UALlCT,EAgCMC,EAAAa,EAAAX,CAAA,EAfLC,EAEAU,EAAAC,CAAA,qFADGN,EAAI,EAAA,EAAA,KAAAO,EAAAC,EAAAC,CAAA,UADgBE,EAAAL,EAAA,WAAA,CAAAN,KAAWA,EAAQ,EAAA,CAAA,CAAA,GAGpCA,EAAW,CAAA,GAAIA,EAAQ,EAAA,IAAK,wEAdpBW,EAAAN,EAAA,SAAAL,QAAa,MACzBA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,EAAA,CAAA,UACrBW,EAAAN,EAAA,KAAAL,QAAa,IAAI,0CANHY,EAAAP,EAAA,mBAAAL,QAAa,MACpCA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,EAAA,EAC3B,GACAA,EAAU,CAAA,EAACA,EAAQ,EAAA,CAAA,EAAE,SAAS,6DAwB9BA,EAAQ,EAAA,EAAA,oBAT2B;AAAA,SAErC,wDAEyBY,EAAAC,EAAA,mBAAAb,QAAa,MACpCA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,EAAA,EAC3B,GACAA,EAAU,CAAA,EAACA,EAAQ,EAAA,CAAA,EAAE,OAAO,mBALhCT,EAQMC,EAAAqB,EAAAnB,CAAA,gCADJM,EAAQ,EAAA,EAAA,KAAAO,EAAAO,EAAAC,CAAA,WALeH,EAAAC,EAAA,mBAAAb,QAAa,MACpCA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,EAAA,EAC3B,GACAA,EAAU,CAAA,EAACA,EAAQ,EAAA,CAAA,EAAE,OAAO,2EAQlCT,EAAKC,EAAAwB,EAAAtB,CAAA,uCAvCDM,EAAI,EAAA,EAAC,KAAI,IAAO,KAsChBiB,EAAAjB,MAAIkB,EAAmBlB,EAAM,EAAA,CAAA,EAAA,OAAS,uIAtCtCA,EAAI,EAAA,EAAC,KAAI,IAAO,wEAsChBmB,EAAA,CAAA,EAAA,IAAAF,EAAAjB,MAAIkB,EAAmBlB,EAAM,EAAA,CAAA,EAAA,OAAS,8HAvCrCoB,EAAAC,EAAAH,EAAmBlB,EAAI,EAAA,CAAA,CAAA,uBAA5B,OAAI,GAAA,6JAACoB,EAAAC,EAAAH,EAAmBlB,EAAI,EAAA,CAAA,CAAA,oBAA5B,OAAIE,GAAA,EAAA,0HAAJ,oEAxBA,OAAAF,OAAS,aAAYsB,mGAD3B/B,EA4FKC,EAAAW,EAAAT,CAAA,oHA7KK,SAAAwB,EAAmBK,EAAY,QAChCA,EAAK,MAAM;AAAA,CAAI,2BAjBjBC,EAAO,OAAU,SAAa,QAMzB,MAAAC,EAAK,EAAA,EAAAC,EACL,CAAA,YAAAC,EAAc,EAAK,EAAAD,GACnB,UAAAE,EAAS,EAAA,EAAAF,EACT,CAAA,WAAAG,EAAa,EAAK,EAAAH,EAEzB1B,EAEA8B,EAAU,CAAA,EACVC,EAAS,YAMJC,GAAiB,CACd,UAAAC,KAAOL,EAAS,CACpB,MAAAM,EAAKN,EAAUK,CAAG,EAAE,KAAI,EAC1BC,KAAMC,EACTC,EAAA,EAAAN,EAAWG,CAAG,EAAIE,EAAOD,CAAyB,EAAAJ,CAAA,MAElDA,EAAWG,CAAG,EAAA,CACb,QAAST,EAAUa,EAAaT,EAAUK,CAAG,EAAG,CAAC,EAAIL,EAAUK,CAAG,EAClE,UAAWT,EACRa,EAAaT,EAAUK,CAAG,EAAG,EAAG,EAChCL,EAAUK,CAAG,gBAMXI,EAAaC,EAAcC,EAAS,KACvCvC,EAAG,CACH,IAAAwC,EAAS,SAAS,cAAc,QAAQ,EAC5CxC,EAAMwC,EAAO,WAAW,IAAI,EAE7BxC,EAAI,UAAYsC,EAChBtC,EAAI,SAAS,EAAG,EAAG,EAAG,CAAC,EAChB,KAAA,CAAAyC,GAAGC,GAAGC,EAAC,EAAI3C,EAAI,aAAa,EAAG,EAAG,EAAG,CAAC,EAAE,KAC/C,OAAAA,EAAI,UAAU,EAAG,EAAG,EAAG,CAAC,EACT,QAAAyC,OAAMC,OAAMC,OAAM,IAAMJ,KAGlC,MAAAK,EAAWC,SAIbC,EAyBK,SAAAC,EAAiBC,EAAa,CACtCZ,EAAA,EAAAL,EAASiB,CAAK,WAENC,GAAe,CACvBb,EAAA,EAAAL,EAAS,EAAE,EA2Ba,MAAAmB,EAAAC,GAAAJ,EAAiBI,CAAQ,EAC7BC,EAAAD,GAAAJ,EAAiBI,CAAQ,QACtBF,WACJA,iBA2BbL,EAAS,SAAQ,CAChB,MAAO1C,EACP,MAAK,CAAGqB,EAAM4B,CAAQ,CAAA,CAAA,2MAtF7B,IACKvB,OACJA,EAAS,CAAA,CAAA,EAENH,EAAM,OAAS,WACR4B,EAAGL,CAAK,IAAKvB,EAClB,GAAAuB,IAAU,KACF,GAAA,OAAAA,GAAU,UAEd,GADNZ,EAAA,EAAAU,EAAO,YAAY,EACb,EAAAE,KAASpB,GAAS,KACnB0B,EAAQC,GAAe,OAAO,KAAK3B,CAAS,EAAE,MAAM,MACxDA,EAAUoB,CAAK,EAAIM,EAAK1B,CAAA,QAGzBQ,EAAA,EAAAU,EAAO,QAAQ,EAMnBd,uNCtBOwB,oBAEC,GACE,QAAAxD,OAAc,0FAAdmB,EAAA,MAAAsC,EAAA,QAAAzD,OAAc,w8BANNA,EAAc,EAAA,CAAA,8EAC5BA,EAAK,CAAA,GAAA0D,GAAA1D,CAAA,8CASLA,EAAK,CAAA,EAAA,iMAVSA,EAAc,EAAA,CAAA,CAAA,CAAA,eAC5BA,EAAK,CAAA,+fAND,sdApCE,GAAA,CAAA,QAAA2D,EAAU,EAAE,EAAAjC,GACZ,aAAAkC,EAAY,EAAA,EAAAlC,EACZ,CAAA,QAAAmC,EAAU,EAAI,EAAAnC,GACd,MAAAD,CAAkC,EAAAC,EACzCoC,GACO,YAAAnC,CAAoB,EAAAD,GACpB,UAAAE,EAAS,EAAA,EAAAF,GACT,MAAAsB,EAAQe,EAAG,mCAAmC,CAAA,EAAArC,EAC9C,CAAA,UAAAsC,EAAY,EAAI,EAAAtC,EAChB,CAAA,MAAAuC,EAAuB,IAAI,EAAAvC,EAC3B,CAAA,UAAAwC,EAAgC,MAAS,EAAAxC,EACzC,CAAA,WAAAG,EAAa,EAAK,EAAAH,GAClB,OAAAyC,CAGT,EAAAzC,GAMS,eAAA0C,CAA6B,EAAA1C,EAgCxB,MAAA2C,EAAA,CAAA,CAAA,OAAAC,KAAaH,EAAO,SAAS,SAAUG,CAAM,4fApC5D,CAAO1C,GAAa,OAAO,KAAKA,CAAS,EAAE,iCAOvCH,IAAUqC,IACb1B,EAAA,GAAA0B,EAAYrC,CAAK,EACjB0C,EAAO,SAAS,QAAQ"}