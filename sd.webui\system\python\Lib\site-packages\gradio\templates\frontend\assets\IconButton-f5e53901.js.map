{"version": 3, "file": "IconButton-f5e53901.js", "sources": ["../../../../js/atoms/src/IconButton.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let Icon: any;\n\texport let label = \"\";\n\texport let show_label = false;\n\texport let pending = false;\n</script>\n\n<button on:click aria-label={label} title={label} class:pending>\n\t{#if show_label}<span>{label}</span>{/if}\n\t<div><Icon /></div>\n</button>\n\n<style>\n\tbutton {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tgap: 1px;\n\t\tz-index: var(--layer-1);\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--button-secondary-border-color);\n\t\tborder-radius: var(--radius-sm);\n\t\tbackground: var(--background-fill-primary);\n\t\tpadding: 2px;\n\t\tcolor: var(--block-label-text-color);\n\t}\n\n\tbutton:hover {\n\t\tcursor: pointer;\n\t\tborder: 2px solid var(--button-secondary-border-color-hover);\n\t\tpadding: 1px;\n\t\tcolor: var(--block-label-text-color);\n\t}\n\n\tspan {\n\t\tpadding: 0px 1px;\n\t\tfont-size: 10px;\n\t}\n\n\tdiv {\n\t\tpadding: 2px;\n\t\twidth: 14px;\n\t\theight: 14px;\n\t}\n\n\t.pending {\n\t\tanimation: flash 0.5s infinite;\n\t}\n\n\t@keyframes flash {\n\t\t0% {\n\t\t\topacity: 0.5;\n\t\t}\n\t\t50% {\n\t\t\topacity: 1;\n\t\t}\n\t\t100% {\n\t\t\topacity: 0.5;\n\t\t}\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "span", "anchor", "create_if_block", "button", "append", "div", "Icon", "$$props", "label", "show_label", "pending"], "mappings": "+OAQwBA,EAAK,CAAA,CAAA,wCAAZC,EAAoBC,EAAAC,EAAAC,CAAA,0BAAbJ,EAAK,CAAA,CAAA,oDAAvBA,EAAU,CAAA,GAAAK,EAAAL,CAAA,qIADaA,EAAK,CAAA,CAAA,cAASA,EAAK,CAAA,CAAA,4DAAhDC,EAGQC,EAAAI,EAAAF,CAAA,wBADPG,EAAkBD,EAAAE,CAAA,2DADbR,EAAU,CAAA,oFADaA,EAAK,CAAA,CAAA,yBAASA,EAAK,CAAA,CAAA,+JANpC,KAAAS,CAAS,EAAAC,EACT,CAAA,MAAAC,EAAQ,EAAE,EAAAD,EACV,CAAA,WAAAE,EAAa,EAAK,EAAAF,EAClB,CAAA,QAAAG,EAAU,EAAK,EAAAH"}