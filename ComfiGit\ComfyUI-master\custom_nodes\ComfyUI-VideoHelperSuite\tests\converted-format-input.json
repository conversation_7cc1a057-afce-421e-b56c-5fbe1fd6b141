{"last_node_id": 19, "last_link_id": 20, "nodes": [{"id": 18, "type": "PrimitiveNode", "pos": [318, 618], "size": [210, 82], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [18], "widget": {"name": "crf"}}], "properties": {"Run widget replace on values": false}, "widgets_values": [60, "fixed"]}, {"id": 11, "type": "LoadImage", "pos": [260.4530029296875, 233.2003173828125], "size": [315, 314], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [19], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["example.png", "image"]}, {"id": 17, "type": "VHS_VideoCombine", "pos": [733.3749389648438, 338.28924560546875], "size": [222.91415405273438, 522.9141845703125], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 19}, {"name": "audio", "type": "AUDIO", "link": null, "shape": 7}, {"name": "meta_batch", "type": "VHS_BatchManager", "link": null, "shape": 7}, {"name": "vae", "type": "VAE", "link": null, "shape": 7}, {"name": "crf", "type": "INT", "link": 18, "widget": {"name": "crf"}}, {"name": "pix_fmt", "type": ["yuv420p", "yuv420p10le"], "link": 20, "widget": {"name": "pix_fmt"}}], "outputs": [{"name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 8, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p10le", "crf": 60, "save_metadata": true, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00001.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 8}, "muted": false}}}, {"id": 19, "type": "PrimitiveNode", "pos": [300, 760], "size": [290, 110], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "COMBO", "type": "COMBO", "links": [20], "widget": {"name": "pix_fmt"}}], "properties": {"Run widget replace on values": false}, "widgets_values": ["yuv420p10le", "fixed", ""]}], "links": [[18, 18, 0, 17, 4, "INT"], [19, 11, 0, 17, 0, "IMAGE"], [20, 19, 0, 17, 5, ["yuv420p", "yuv420p10le"]]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.8264462809917354, "offset": [45.8650452880864, -157.46987175292935]}}, "version": 0.4, "tests": {"17": [{"type": "video", "key": "pix_fmt", "value": "yuv420p10le"}], "length": 1}}