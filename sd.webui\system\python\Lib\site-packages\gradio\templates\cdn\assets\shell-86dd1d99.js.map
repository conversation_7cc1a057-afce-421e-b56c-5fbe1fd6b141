{"version": 3, "file": "shell-86dd1d99.js", "sources": ["../../../../node_modules/.pnpm/@codemirror+legacy-modes@6.3.1/node_modules/@codemirror/legacy-modes/mode/shell.js"], "sourcesContent": ["var words = {};\nfunction define(style, dict) {\n  for(var i = 0; i < dict.length; i++) {\n    words[dict[i]] = style;\n  }\n};\n\nvar commonAtoms = [\"true\", \"false\"];\nvar commonKeywords = [\"if\", \"then\", \"do\", \"else\", \"elif\", \"while\", \"until\", \"for\", \"in\", \"esac\", \"fi\",\n                      \"fin\", \"fil\", \"done\", \"exit\", \"set\", \"unset\", \"export\", \"function\"];\nvar commonCommands = [\"ab\", \"awk\", \"bash\", \"beep\", \"cat\", \"cc\", \"cd\", \"chown\", \"chmod\", \"chroot\", \"clear\",\n                      \"cp\", \"curl\", \"cut\", \"diff\", \"echo\", \"find\", \"gawk\", \"gcc\", \"get\", \"git\", \"grep\", \"hg\", \"kill\", \"killall\",\n                      \"ln\", \"ls\", \"make\", \"mkdir\", \"openssl\", \"mv\", \"nc\", \"nl\", \"node\", \"npm\", \"ping\", \"ps\", \"restart\", \"rm\",\n                      \"rmdir\", \"sed\", \"service\", \"sh\", \"shopt\", \"shred\", \"source\", \"sort\", \"sleep\", \"ssh\", \"start\", \"stop\",\n                      \"su\", \"sudo\", \"svn\", \"tee\", \"telnet\", \"top\", \"touch\", \"vi\", \"vim\", \"wall\", \"wc\", \"wget\", \"who\", \"write\",\n                      \"yes\", \"zsh\"];\n\ndefine('atom', commonAtoms);\ndefine('keyword', commonKeywords);\ndefine('builtin', commonCommands);\n\nfunction tokenBase(stream, state) {\n  if (stream.eatSpace()) return null;\n\n  var sol = stream.sol();\n  var ch = stream.next();\n\n  if (ch === '\\\\') {\n    stream.next();\n    return null;\n  }\n  if (ch === '\\'' || ch === '\"' || ch === '`') {\n    state.tokens.unshift(tokenString(ch, ch === \"`\" ? \"quote\" : \"string\"));\n    return tokenize(stream, state);\n  }\n  if (ch === '#') {\n    if (sol && stream.eat('!')) {\n      stream.skipToEnd();\n      return 'meta'; // 'comment'?\n    }\n    stream.skipToEnd();\n    return 'comment';\n  }\n  if (ch === '$') {\n    state.tokens.unshift(tokenDollar);\n    return tokenize(stream, state);\n  }\n  if (ch === '+' || ch === '=') {\n    return 'operator';\n  }\n  if (ch === '-') {\n    stream.eat('-');\n    stream.eatWhile(/\\w/);\n    return 'attribute';\n  }\n  if (ch == \"<\") {\n    if (stream.match(\"<<\")) return \"operator\"\n    var heredoc = stream.match(/^<-?\\s*['\"]?([^'\"]*)['\"]?/)\n    if (heredoc) {\n      state.tokens.unshift(tokenHeredoc(heredoc[1]))\n      return 'string.special'\n    }\n  }\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/\\d/);\n    if(stream.eol() || !/\\w/.test(stream.peek())) {\n      return 'number';\n    }\n  }\n  stream.eatWhile(/[\\w-]/);\n  var cur = stream.current();\n  if (stream.peek() === '=' && /\\w+/.test(cur)) return 'def';\n  return words.hasOwnProperty(cur) ? words[cur] : null;\n}\n\nfunction tokenString(quote, style) {\n  var close = quote == \"(\" ? \")\" : quote == \"{\" ? \"}\" : quote\n  return function(stream, state) {\n    var next, escaped = false;\n    while ((next = stream.next()) != null) {\n      if (next === close && !escaped) {\n        state.tokens.shift();\n        break;\n      } else if (next === '$' && !escaped && quote !== \"'\" && stream.peek() != close) {\n        escaped = true;\n        stream.backUp(1);\n        state.tokens.unshift(tokenDollar);\n        break;\n      } else if (!escaped && quote !== close && next === quote) {\n        state.tokens.unshift(tokenString(quote, style))\n        return tokenize(stream, state)\n      } else if (!escaped && /['\"]/.test(next) && !/['\"]/.test(quote)) {\n        state.tokens.unshift(tokenStringStart(next, \"string\"));\n        stream.backUp(1);\n        break;\n      }\n      escaped = !escaped && next === '\\\\';\n    }\n    return style;\n  };\n};\n\nfunction tokenStringStart(quote, style) {\n  return function(stream, state) {\n    state.tokens[0] = tokenString(quote, style)\n    stream.next()\n    return tokenize(stream, state)\n  }\n}\n\nvar tokenDollar = function(stream, state) {\n  if (state.tokens.length > 1) stream.eat('$');\n  var ch = stream.next()\n  if (/['\"({]/.test(ch)) {\n    state.tokens[0] = tokenString(ch, ch == \"(\" ? \"quote\" : ch == \"{\" ? \"def\" : \"string\");\n    return tokenize(stream, state);\n  }\n  if (!/\\d/.test(ch)) stream.eatWhile(/\\w/);\n  state.tokens.shift();\n  return 'def';\n};\n\nfunction tokenHeredoc(delim) {\n  return function(stream, state) {\n    if (stream.sol() && stream.string == delim) state.tokens.shift()\n    stream.skipToEnd()\n    return \"string.special\"\n  }\n}\n\nfunction tokenize(stream, state) {\n  return (state.tokens[0] || tokenBase) (stream, state);\n};\n\nexport const shell = {\n  name: \"shell\",\n  startState: function() {return {tokens:[]};},\n  token: function(stream, state) {\n    return tokenize(stream, state);\n  },\n  languageData: {\n    autocomplete: commonAtoms.concat(commonKeywords, commonCommands),\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"`\"]},\n    commentTokens: {line: \"#\"}\n  }\n};\n"], "names": ["words", "define", "style", "dict", "i", "commonAtoms", "commonKeywords", "commonCommands", "tokenBase", "stream", "state", "sol", "ch", "tokenString", "tokenize", "<PERSON><PERSON><PERSON><PERSON>", "heredoc", "tokenHeredoc", "cur", "quote", "close", "next", "escaped", "tokenStringStart", "delim", "shell"], "mappings": "AAAA,IAAIA,EAAQ,CAAA,EACZ,SAASC,EAAOC,EAAOC,EAAM,CAC3B,QAAQC,EAAI,EAAGA,EAAID,EAAK,OAAQC,IAC9BJ,EAAMG,EAAKC,CAAC,CAAC,EAAIF,CAErB,CAEA,IAAIG,EAAc,CAAC,OAAQ,OAAO,EAC9BC,EAAiB,CAAC,KAAM,OAAQ,KAAM,OAAQ,OAAQ,QAAS,QAAS,MAAO,KAAM,OAAQ,KAC3E,MAAO,MAAO,OAAQ,OAAQ,MAAO,QAAS,SAAU,UAAU,EACpFC,EAAiB,CAAC,KAAM,MAAO,OAAQ,OAAQ,MAAO,KAAM,KAAM,QAAS,QAAS,SAAU,QAC5E,KAAM,OAAQ,MAAO,OAAQ,OAAQ,OAAQ,OAAQ,MAAO,MAAO,MAAO,OAAQ,KAAM,OAAQ,UAChG,KAAM,KAAM,OAAQ,QAAS,UAAW,KAAM,KAAM,KAAM,OAAQ,MAAO,OAAQ,KAAM,UAAW,KAClG,QAAS,MAAO,UAAW,KAAM,QAAS,QAAS,SAAU,OAAQ,QAAS,MAAO,QAAS,OAC9F,KAAM,OAAQ,MAAO,MAAO,SAAU,MAAO,QAAS,KAAM,MAAO,OAAQ,KAAM,OAAQ,MAAO,QAChG,MAAO,KAAK,EAElCN,EAAO,OAAQI,CAAW,EAC1BJ,EAAO,UAAWK,CAAc,EAChCL,EAAO,UAAWM,CAAc,EAEhC,SAASC,EAAUC,EAAQC,EAAO,CAChC,GAAID,EAAO,WAAY,OAAO,KAE9B,IAAIE,EAAMF,EAAO,MACbG,EAAKH,EAAO,OAEhB,GAAIG,IAAO,KACT,OAAAH,EAAO,KAAI,EACJ,KAET,GAAIG,IAAO,KAAQA,IAAO,KAAOA,IAAO,IACtC,OAAAF,EAAM,OAAO,QAAQG,EAAYD,EAAIA,IAAO,IAAM,QAAU,QAAQ,CAAC,EAC9DE,EAASL,EAAQC,CAAK,EAE/B,GAAIE,IAAO,IACT,OAAID,GAAOF,EAAO,IAAI,GAAG,GACvBA,EAAO,UAAS,EACT,SAETA,EAAO,UAAS,EACT,WAET,GAAIG,IAAO,IACT,OAAAF,EAAM,OAAO,QAAQK,CAAW,EACzBD,EAASL,EAAQC,CAAK,EAE/B,GAAIE,IAAO,KAAOA,IAAO,IACvB,MAAO,WAET,GAAIA,IAAO,IACT,OAAAH,EAAO,IAAI,GAAG,EACdA,EAAO,SAAS,IAAI,EACb,YAET,GAAIG,GAAM,IAAK,CACb,GAAIH,EAAO,MAAM,IAAI,EAAG,MAAO,WAC/B,IAAIO,EAAUP,EAAO,MAAM,2BAA2B,EACtD,GAAIO,EACF,OAAAN,EAAM,OAAO,QAAQO,EAAaD,EAAQ,CAAC,CAAC,CAAC,EACtC,iBAGX,GAAI,KAAK,KAAKJ,CAAE,IACdH,EAAO,SAAS,IAAI,EACjBA,EAAO,IAAG,GAAM,CAAC,KAAK,KAAKA,EAAO,KAAI,CAAE,GACzC,MAAO,SAGXA,EAAO,SAAS,OAAO,EACvB,IAAIS,EAAMT,EAAO,UACjB,OAAIA,EAAO,KAAI,IAAO,KAAO,MAAM,KAAKS,CAAG,EAAU,MAC9ClB,EAAM,eAAekB,CAAG,EAAIlB,EAAMkB,CAAG,EAAI,IAClD,CAEA,SAASL,EAAYM,EAAOjB,EAAO,CACjC,IAAIkB,EAAQD,GAAS,IAAM,IAAMA,GAAS,IAAM,IAAMA,EACtD,OAAO,SAASV,EAAQC,EAAO,CAE7B,QADIW,EAAMC,EAAU,IACZD,EAAOZ,EAAO,KAAI,IAAO,MAAM,CACrC,GAAIY,IAASD,GAAS,CAACE,EAAS,CAC9BZ,EAAM,OAAO,QACb,cACSW,IAAS,KAAO,CAACC,GAAWH,IAAU,KAAOV,EAAO,KAAM,GAAIW,EAAO,CAC9EE,EAAU,GACVb,EAAO,OAAO,CAAC,EACfC,EAAM,OAAO,QAAQK,CAAW,EAChC,UACK,IAAI,CAACO,GAAWH,IAAUC,GAASC,IAASF,EACjD,OAAAT,EAAM,OAAO,QAAQG,EAAYM,EAAOjB,CAAK,CAAC,EACvCY,EAASL,EAAQC,CAAK,EACxB,GAAI,CAACY,GAAW,OAAO,KAAKD,CAAI,GAAK,CAAC,OAAO,KAAKF,CAAK,EAAG,CAC/DT,EAAM,OAAO,QAAQa,EAAiBF,EAAM,QAAQ,CAAC,EACrDZ,EAAO,OAAO,CAAC,EACf,OAEFa,EAAU,CAACA,GAAWD,IAAS,KAEjC,OAAOnB,CACX,CACA,CAEA,SAASqB,EAAiBJ,EAAOjB,EAAO,CACtC,OAAO,SAASO,EAAQC,EAAO,CAC7B,OAAAA,EAAM,OAAO,CAAC,EAAIG,EAAYM,EAAOjB,CAAK,EAC1CO,EAAO,KAAM,EACNK,EAASL,EAAQC,CAAK,CAC9B,CACH,CAEA,IAAIK,EAAc,SAASN,EAAQC,EAAO,CACpCA,EAAM,OAAO,OAAS,GAAGD,EAAO,IAAI,GAAG,EAC3C,IAAIG,EAAKH,EAAO,KAAM,EACtB,MAAI,SAAS,KAAKG,CAAE,GAClBF,EAAM,OAAO,CAAC,EAAIG,EAAYD,EAAIA,GAAM,IAAM,QAAUA,GAAM,IAAM,MAAQ,QAAQ,EAC7EE,EAASL,EAAQC,CAAK,IAE1B,KAAK,KAAKE,CAAE,GAAGH,EAAO,SAAS,IAAI,EACxCC,EAAM,OAAO,QACN,MACT,EAEA,SAASO,EAAaO,EAAO,CAC3B,OAAO,SAASf,EAAQC,EAAO,CAC7B,OAAID,EAAO,IAAG,GAAMA,EAAO,QAAUe,GAAOd,EAAM,OAAO,MAAO,EAChED,EAAO,UAAW,EACX,gBACR,CACH,CAEA,SAASK,EAASL,EAAQC,EAAO,CAC/B,OAAQA,EAAM,OAAO,CAAC,GAAKF,GAAYC,EAAQC,CAAK,CACtD,CAEY,MAACe,EAAQ,CACnB,KAAM,QACN,WAAY,UAAW,CAAC,MAAO,CAAC,OAAO,CAAA,CAAE,CAAE,EAC3C,MAAO,SAAShB,EAAQC,EAAO,CAC7B,OAAOI,EAASL,EAAQC,CAAK,CAC9B,EACD,aAAc,CACZ,aAAcL,EAAY,OAAOC,EAAgBC,CAAc,EAC/D,cAAe,CAAC,SAAU,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,GAAG,CAAC,EACxD,cAAe,CAAC,KAAM,GAAG,CAC1B,CACH", "x_google_ignoreList": [0]}