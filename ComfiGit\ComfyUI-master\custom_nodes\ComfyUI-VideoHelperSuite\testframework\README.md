Code to automate execution of the tests and evaluate the results.  
Distributed as a `custom node`,  and can be installed by copying or simlinking to the `custom_nodes` directory.  
Requires that ffprobe be available and added to the path. Note that imageio-ffmpeg does not bundle ffprobe.

When installed, it adds a new sidebar tab to automate running one, or a folder of tests. This requires that the `Use new menu and workflow management` setting not be disabled
