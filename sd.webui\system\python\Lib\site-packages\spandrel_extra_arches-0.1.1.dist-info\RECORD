spandrel_extra_arches-0.1.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
spandrel_extra_arches-0.1.1.dist-info/METADATA,sha256=Hp-hQYHIoVWJFU67CitttWfSseVhAzrSiQrt0e0p3cY,3013
spandrel_extra_arches-0.1.1.dist-info/RECORD,,
spandrel_extra_arches-0.1.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel_extra_arches-0.1.1.dist-info/WHEEL,sha256=GJ7t_kWBFywbagK5eo9IoUwLW6oyOeTKmQ-9iHFVNxQ,92
spandrel_extra_arches-0.1.1.dist-info/top_level.txt,sha256=ZbfPA4seF3Dbf1kgB0NEZLe459ne33_1aY8Ln4X1Jkc,22
spandrel_extra_arches/__helper.py,sha256=oyVKIBywAYYrn5NaZanunYVK13SvK7FLBG0uICaMh4g,842
spandrel_extra_arches/__init__.py,sha256=Ax31xEYUlD9BUItdPmTo5_zmtyitKqJmMv3aCuaHAkE,90
spandrel_extra_arches/__pycache__/__helper.cpython-310.pyc,,
spandrel_extra_arches/__pycache__/__init__.cpython-310.pyc,,
spandrel_extra_arches/architectures/AdaCode/__init__.py,sha256=KO4H00XqVuJpntAKVIjReOrgeIKoBA-L2Xoq05DjyL8,5428
spandrel_extra_arches/architectures/AdaCode/__pycache__/__init__.cpython-310.pyc,,
spandrel_extra_arches/architectures/AdaCode/arch/__pycache__/adacode_contrast_arch.cpython-310.pyc,,
spandrel_extra_arches/architectures/AdaCode/arch/adacode_contrast_arch.py,sha256=bCDmvgsSrudbc0vyZsbdIIeidRg8s6zn2DybpaZPy0w,7794
spandrel_extra_arches/architectures/CodeFormer/__init__.py,sha256=4IJUCmJEKE_Q0TMxd1hZgRIcgSqDRY-fsqNZ1ovgnT8,2293
spandrel_extra_arches/architectures/CodeFormer/__pycache__/__init__.cpython-310.pyc,,
spandrel_extra_arches/architectures/CodeFormer/arch/__pycache__/codeformer.cpython-310.pyc,,
spandrel_extra_arches/architectures/CodeFormer/arch/codeformer.py,sha256=n3UBOD-OTOrZgx9NFf5bJ4ldU8eSv2MIoLtN3EYNAdg,25339
spandrel_extra_arches/architectures/DDColor/__init__.py,sha256=b_F0wyh9-IunR5rqd4DJqKnChJRUzlZuBis4kD2Rv9Q,7218
spandrel_extra_arches/architectures/DDColor/__pycache__/__init__.cpython-310.pyc,,
spandrel_extra_arches/architectures/DDColor/__pycache__/color.cpython-310.pyc,,
spandrel_extra_arches/architectures/DDColor/arch/__pycache__/convnext.cpython-310.pyc,,
spandrel_extra_arches/architectures/DDColor/arch/__pycache__/ddcolor.cpython-310.pyc,,
spandrel_extra_arches/architectures/DDColor/arch/__pycache__/position_encoding.cpython-310.pyc,,
spandrel_extra_arches/architectures/DDColor/arch/__pycache__/transformer.cpython-310.pyc,,
spandrel_extra_arches/architectures/DDColor/arch/__pycache__/transformer_utils.cpython-310.pyc,,
spandrel_extra_arches/architectures/DDColor/arch/__pycache__/unet.cpython-310.pyc,,
spandrel_extra_arches/architectures/DDColor/arch/convnext.py,sha256=W16ttdRvFWPvuaIq0jXtHaAnChDOHp2gaJxOJejCsc4,6996
spandrel_extra_arches/architectures/DDColor/arch/ddcolor.py,sha256=zK1df2VX-Aw-R_uRn1awrppnNBlhhZPCzTcuhRVsXI0,12406
spandrel_extra_arches/architectures/DDColor/arch/position_encoding.py,sha256=Bnn6KirRs_0Ya7EWa1q_hytgTs9Ks7kITGd0ZXNnteo,2069
spandrel_extra_arches/architectures/DDColor/arch/transformer.py,sha256=-GbgjEd1Y0yr5lgHnYzb4RNBVGQJJe2QSRkgR1a74kM,11952
spandrel_extra_arches/architectures/DDColor/arch/transformer_utils.py,sha256=6kTwgi4o0gUC3pWznBjIY4dI0Y4Ihl13I0FB_N0Cf94,6921
spandrel_extra_arches/architectures/DDColor/arch/unet.py,sha256=87Ov22BrIOUF0jlP0PodW8KbVKzCZ6oLSalgZLYDfeM,7535
spandrel_extra_arches/architectures/DDColor/color.py,sha256=nyluQKi_5RUvizAXUBPLgwbJ-oVc3Qj9Os-5BqsFw5c,6522
spandrel_extra_arches/architectures/FeMaSR/__init__.py,sha256=iqWMhXgvRbV2e18rC5jeobGVZouQxfMbByw3hyuc7_s,5318
spandrel_extra_arches/architectures/FeMaSR/__pycache__/__init__.cpython-310.pyc,,
spandrel_extra_arches/architectures/FeMaSR/arch/__pycache__/fema_utils.cpython-310.pyc,,
spandrel_extra_arches/architectures/FeMaSR/arch/__pycache__/femasr.cpython-310.pyc,,
spandrel_extra_arches/architectures/FeMaSR/arch/fema_utils.py,sha256=bE9u0xl9E72ldwUcBMdQg0m8VecdrkUcDeRvbpgxk7s,3258
spandrel_extra_arches/architectures/FeMaSR/arch/femasr.py,sha256=v3wWyL66opewlcwXKJrsw2i-x1bD63n1VocN3pP_oQo,10370
spandrel_extra_arches/architectures/M3SNet/__init__.py,sha256=g-GL__UphVzsisSjADHzQEkzy1q1Laa4R0QL2GFsE2I,3393
spandrel_extra_arches/architectures/M3SNet/__pycache__/__init__.cpython-310.pyc,,
spandrel_extra_arches/architectures/M3SNet/arch/M3SNet.py,sha256=x0bQtESzh0PzztRdflv_hGNWtXdwqjCT0OQg8WCZE_A,8788
spandrel_extra_arches/architectures/M3SNet/arch/__pycache__/M3SNet.cpython-310.pyc,,
spandrel_extra_arches/architectures/M3SNet/arch/__pycache__/arch_utils.cpython-310.pyc,,
spandrel_extra_arches/architectures/M3SNet/arch/__pycache__/local_arch.cpython-310.pyc,,
spandrel_extra_arches/architectures/M3SNet/arch/arch_utils.py,sha256=vliZb3cXrjjAp7f5gVEWILBjw7Z3uW17Ub2l4P8UBrM,1516
spandrel_extra_arches/architectures/M3SNet/arch/local_arch.py,sha256=ykaPdnRzJE_l8ytsyYJeVEq2qlyZVH4Yvci9QuG2wW4,4256
spandrel_extra_arches/architectures/MAT/__init__.py,sha256=UgCDPAtEF4xV39gJ6c8VwC_WyDh_ABFkMUW274Qn1Dg,1259
spandrel_extra_arches/architectures/MAT/__pycache__/__init__.cpython-310.pyc,,
spandrel_extra_arches/architectures/MAT/arch/MAT.py,sha256=aN3CzEUZlt3vSbjBJ-JQzOWl8JgWqDrav31hVirAk2w,52087
spandrel_extra_arches/architectures/MAT/arch/__pycache__/MAT.cpython-310.pyc,,
spandrel_extra_arches/architectures/MAT/arch/__pycache__/utils.cpython-310.pyc,,
spandrel_extra_arches/architectures/MAT/arch/utils.py,sha256=xLMcqImYixCUB4LPjZCc6DZmRfkaxmg8fXDyT5vUG1U,25622
spandrel_extra_arches/architectures/MIRNet2/__init__.py,sha256=vRiar8EgAAeGJQCpC0LVJHeYFd91SMg26gDxUT7pUO0,3489
spandrel_extra_arches/architectures/MIRNet2/__pycache__/__init__.cpython-310.pyc,,
spandrel_extra_arches/architectures/MIRNet2/arch/__pycache__/mirnet_v2_arch.cpython-310.pyc,,
spandrel_extra_arches/architectures/MIRNet2/arch/mirnet_v2_arch.py,sha256=o0OlShEo3wKw7i0aCiHPhiksftqpcY6WnNTiVQxv6gA,10737
spandrel_extra_arches/architectures/MPRNet/__init__.py,sha256=au2VOOy-DkcENHgJFm89K7fVqvH0YV0qPlPJHNuUx_Q,4091
spandrel_extra_arches/architectures/MPRNet/__pycache__/__init__.cpython-310.pyc,,
spandrel_extra_arches/architectures/MPRNet/arch/MPRNet.py,sha256=UbWSjvZzfSdn8pRntmHrHVuLlLuGSYHMg-BAsoplCSE,17643
spandrel_extra_arches/architectures/MPRNet/arch/__pycache__/MPRNet.cpython-310.pyc,,
spandrel_extra_arches/architectures/Restormer/__init__.py,sha256=Msg6N49DYm5IAUVxZV5huogi0S8LBDN1k3OTJ2_-76s,4499
spandrel_extra_arches/architectures/Restormer/__pycache__/__init__.cpython-310.pyc,,
spandrel_extra_arches/architectures/Restormer/arch/__pycache__/restormer_arch.cpython-310.pyc,,
spandrel_extra_arches/architectures/Restormer/arch/restormer_arch.py,sha256=j3qW59uB2ABTOvigy0gjlT9zlQt9FZQR94fwSlcDVJ0,13141
spandrel_extra_arches/architectures/SRFormer/__init__.py,sha256=76eAJI4_XM_aokiwYkYZDvRqG-HGiXadIn3VGM3K-SU,6859
spandrel_extra_arches/architectures/SRFormer/__pycache__/__init__.cpython-310.pyc,,
spandrel_extra_arches/architectures/SRFormer/arch/SRFormer.py,sha256=2IURkg-OJutmuzeHG-b5vVyf3BauFedY-p6aQf4WMrA,44033
spandrel_extra_arches/architectures/SRFormer/arch/__pycache__/SRFormer.cpython-310.pyc,,
