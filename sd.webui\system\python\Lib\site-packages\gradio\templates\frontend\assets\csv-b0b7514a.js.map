{"version": 3, "file": "csv-b0b7514a.js", "sources": ["../../../../node_modules/.pnpm/d3-dsv@3.0.1/node_modules/d3-dsv/src/csv.js"], "sourcesContent": ["import dsv from \"./dsv.js\";\n\nvar csv = dsv(\",\");\n\nexport var csvParse = csv.parse;\nexport var csvParseRows = csv.parseRows;\nexport var csvFormat = csv.format;\nexport var csvFormatBody = csv.formatBody;\nexport var csvFormatRows = csv.formatRows;\nexport var csvFormatRow = csv.formatRow;\nexport var csvFormatValue = csv.formatValue;\n"], "names": ["csv", "dsv", "csvParse", "csvParseRows"], "mappings": "sCAEA,IAAIA,EAAMC,EAAI,GAAG,EAENC,EAAWF,EAAI,MACfG,EAAeH,EAAI", "x_google_ignoreList": [0]}