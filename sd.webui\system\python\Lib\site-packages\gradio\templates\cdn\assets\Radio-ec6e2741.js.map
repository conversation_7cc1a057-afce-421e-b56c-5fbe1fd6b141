{"version": 3, "file": "Radio-ec6e2741.js", "sources": ["../../../../js/radio/shared/Radio.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher, afterUpdate } from \"svelte\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\timport type { SelectData } from \"@gradio/utils\";\n\n\texport let value: string | number | null;\n\texport let value_is_output = false;\n\texport let choices: [string, number][];\n\texport let disabled = false;\n\texport let label: string;\n\texport let info: string | undefined = undefined;\n\texport let show_label = true;\n\texport let elem_id: string;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: string | number | null;\n\t\tinput: undefined;\n\t\tselect: SelectData;\n\t}>();\n\n\tfunction handle_change(): void {\n\t\tdispatch(\"change\", value);\n\t\tif (!value_is_output) {\n\t\t\tdispatch(\"input\");\n\t\t}\n\t}\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\t$: value, handle_change();\n</script>\n\n<BlockTitle {show_label} {info}>{label}</BlockTitle>\n\n<div class=\"wrap\">\n\t{#each choices as choice, i (i)}\n\t\t<label\n\t\t\tclass:disabled\n\t\t\tclass:selected={value === choice[1]}\n\t\t\tdata-testid={`${choice[1]}-radio-label`}\n\t\t>\n\t\t\t<input\n\t\t\t\t{disabled}\n\t\t\t\tbind:group={value}\n\t\t\t\ton:input={() => dispatch(\"select\", { value: choice[1], index: i })}\n\t\t\t\ttype=\"radio\"\n\t\t\t\tname=\"radio-{elem_id}\"\n\t\t\t\tvalue={choice[1]}\n\t\t\t/>\n\t\t\t<span class=\"ml-2\">{choice[0]}</span>\n\t\t</label>\n\t{/each}\n</div>\n\n<style>\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: var(--checkbox-label-gap);\n\t}\n\tlabel {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\ttransition: var(--button-transition);\n\t\tcursor: pointer;\n\t\tbox-shadow: var(--checkbox-label-shadow);\n\t\tborder: var(--checkbox-label-border-width) solid\n\t\t\tvar(--checkbox-label-border-color);\n\t\tborder-radius: var(--button-small-radius);\n\t\tbackground: var(--checkbox-label-background-fill);\n\t\tpadding: var(--checkbox-label-padding);\n\t\tcolor: var(--checkbox-label-text-color);\n\t\tfont-weight: var(--checkbox-label-text-weight);\n\t\tfont-size: var(--checkbox-label-text-size);\n\t\tline-height: var(--line-md);\n\t}\n\n\tlabel:hover {\n\t\tbackground: var(--checkbox-label-background-fill-hover);\n\t}\n\tlabel:focus {\n\t\tbackground: var(--checkbox-label-background-fill-focus);\n\t}\n\tlabel.selected {\n\t\tbackground: var(--checkbox-label-background-fill-selected);\n\t\tcolor: var(--checkbox-label-text-color-selected);\n\t}\n\n\tlabel > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\tinput {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\tbox-shadow: var(--checkbox-shadow);\n\t\tborder: var(--checkbox-border-width) solid var(--checkbox-border-color);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground-color: var(--checkbox-background-color);\n\t\tline-height: var(--line-sm);\n\t}\n\n\tinput:checked,\n\tinput:checked:hover,\n\tinput:checked:focus {\n\t\tborder-color: var(--checkbox-border-color-selected);\n\t\tbackground-image: var(--radio-circle);\n\t\tbackground-color: var(--checkbox-background-color-selected);\n\t}\n\n\tinput:hover {\n\t\tborder-color: var(--checkbox-border-color-hover);\n\t\tbackground-color: var(--checkbox-background-color-hover);\n\t}\n\n\tinput:focus {\n\t\tborder-color: var(--checkbox-border-color-focus);\n\t\tbackground-color: var(--checkbox-background-color-focus);\n\t}\n\n\tinput[disabled],\n\t.disabled {\n\t\tcursor: not-allowed;\n\t}\n</style>\n"], "names": ["ctx", "t1_value", "input", "input_value_value", "attr", "label_1", "label_1_data_testid_value", "insert", "target", "anchor", "append", "span", "dirty", "set_data", "t1", "i", "div", "value", "$$props", "value_is_output", "choices", "disabled", "label", "info", "show_label", "elem_id", "dispatch", "createEventDispatcher", "handle_change", "afterUpdate", "$$invalidate", "choice"], "mappings": "wYAgCiCA,EAAK,CAAA,CAAA,oCAALA,EAAK,CAAA,CAAA,uDAiBfC,EAAAD,MAAO,CAAC,EAAA,oNAHdA,EAAO,CAAA,CAAA,EACbE,EAAA,QAAAC,EAAAH,MAAO,CAAC,kFARAI,EAAAC,EAAA,cAAAC,EAAA,GAAAN,MAAO,CAAC,eAAA,oEADRA,EAAK,CAAA,IAAKA,EAAM,EAAA,EAAC,CAAC,CAAA,8BAFnCO,EAcOC,EAAAH,EAAAI,CAAA,EATNC,EAOCL,EAAAH,CAAA,wBALYF,EAAK,CAAA,SAMlBU,EAAoCL,EAAAM,CAAA,yHAHtBX,EAAO,CAAA,kBACbY,EAAA,GAAAT,KAAAA,EAAAH,MAAO,CAAC,uEAJHA,EAAK,CAAA,GAMEY,EAAA,GAAAX,KAAAA,EAAAD,MAAO,CAAC,EAAA,KAAAa,EAAAC,EAAAb,CAAA,EAVZW,EAAA,GAAAN,KAAAA,EAAA,GAAAN,MAAO,CAAC,qFADRA,EAAK,CAAA,IAAKA,EAAM,EAAA,EAAC,CAAC,CAAA,sKAH7BA,EAAO,CAAA,CAAA,aAAeA,EAAC,EAAA,kBAA5B,OAAIe,GAAA,EAAA,2LADPR,EAkBKC,EAAAQ,EAAAP,CAAA,sLAjBGT,EAAO,CAAA,CAAA,6LA9BH,MAAAiB,CAA6B,EAAAC,EAC7B,CAAA,gBAAAC,EAAkB,EAAK,EAAAD,GACvB,QAAAE,CAA2B,EAAAF,EAC3B,CAAA,SAAAG,EAAW,EAAK,EAAAH,GAChB,MAAAI,CAAa,EAAAJ,EACb,CAAA,KAAAK,EAA2B,MAAS,EAAAL,EACpC,CAAA,WAAAM,EAAa,EAAI,EAAAN,GACjB,QAAAO,CAAe,EAAAP,EAEpB,MAAAQ,EAAWC,aAMRC,GAAa,CACrBF,EAAS,SAAUT,CAAK,EACnBE,GACJO,EAAS,OAAO,EAGlBG,EAAW,IAAA,CACVC,EAAA,EAAAX,EAAkB,EAAK,8BAgBTF,EAAK,KAAA,8BACDS,EAAS,SAAQ,CAAI,MAAOK,EAAO,CAAC,EAAG,MAAOhB,CAAC,CAAA,+TAfxDa,EAAa"}