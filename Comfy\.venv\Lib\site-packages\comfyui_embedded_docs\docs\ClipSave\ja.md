`CLIPを保存`ノードは、CLIPテキストエンコーダーモデルをSafeTensors形式で保存するために設計されています。このノードは高度なモデル結合ワークフローの一部であり、通常は`CLIPMergeSimple`や`CLIPMergeAdd`などのノードと組み合わせて使用されます。保存されるファイルはセキュリティと互換性を確保するためにSafeTensors形式を使用します。

## 入力

| パラメータ | データ型 | 必須 | デフォルト値 | 説明 |
|-----------|-------------|----------|------------|------|
| clip | CLIP | はい | - | 保存するCLIPモデル |
| ファイル名プレフィックス | STRING | はい | "clip/ComfyUI" | 保存するファイルのプレフィックスパス |
| prompt | PROMPT | 非表示 | - | ワークフローのプロンプト情報（メタデータ用） |
| extra_pnginfo | EXTRA_PNGINFO | 非表示 | - | 追加のPNG情報（メタデータ用） |

## 出力

このノードには定義された出力タイプはありません。処理されたファイルは`ComfyUI/output/`フォルダに保存されます。

### 複数ファイル保存戦略

このノードはCLIPモデルのタイプに応じて異なるコンポーネントを保存します：

| プレフィックスタイプ | ファイル接尾辞 | 説明 |
|------------------|----------------|------|
| `clip_l.` | `_clip_l` | CLIP-Lテキストエンコーダー |
| `clip_g.` | `_clip_g` | CLIP-Gテキストエンコーダー |
| プレフィックスなし | 接尾辞なし | その他のCLIPコンポーネント |

## 使用上の注意

1. **ファイルの場所**: すべてのファイルは`ComfyUI/output/`ディレクトリに保存されます
2. **ファイル形式**: モデルはセキュリティのためにSafeTensors形式で保存されます
3. **メタデータ**: 利用可能な場合、ワークフロー情報とPNGメタデータが含まれます
4. **命名規則**: モデルタイプに応じて、指定されたプレフィックスと適切な接尾辞を使用します
