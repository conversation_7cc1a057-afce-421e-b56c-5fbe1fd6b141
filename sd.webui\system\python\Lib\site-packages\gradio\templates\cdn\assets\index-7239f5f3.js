import{S as Q,e as W,s as Y,f as le,g as u,h as b,j as H,n as M,k as v,m as C,o as E,p as D,w as h,r as T,u as g,v as N,aj as He,F as q,G as L,H as U,ae as Se,C as Me,ak as Te,al as Ne,I as O,Q as te,O as ie,E as z,R as Ee,N as I,M as F,t as pe,x as ye,B as Be,Z as qe,ad as Le,V as Ue,W as Ae}from"./index-afe51b5b.js";import{u as ae,c as Pe}from"./utils-c3e3db58.js";import{g as ne,B as Ve,n as se}from"./Button-b4eb936e.js";import{S as ze}from"./ShareButton-34cde537.js";import{M as De}from"./StaticMarkdown-15b756e1.js";import{C as Fe,a as Ie}from"./Copy-df6acdb9.js";import{B as Oe}from"./BlockLabel-4ae26392.js";import"./IconButton-12dccad1.js";function Re(n){let e,i,t;return{c(){e=le("svg"),i=le("path"),t=le("path"),u(i,"fill","currentColor"),u(i,"d","M17.74 30L16 29l4-7h6a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h9v2H6a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4h20a4 4 0 0 1 4 4v12a4 4 0 0 1-4 4h-4.84Z"),u(t,"fill","currentColor"),u(t,"d","M8 10h16v2H8zm0 6h10v2H8z"),u(e,"xmlns","http://www.w3.org/2000/svg"),u(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),u(e,"aria-hidden","true"),u(e,"role","img"),u(e,"class","iconify iconify--carbon"),u(e,"width","100%"),u(e,"height","100%"),u(e,"preserveAspectRatio","xMidYMid meet"),u(e,"viewBox","0 0 32 32")},m(l,r){b(l,e,r),H(e,i),H(e,t)},p:M,i:M,o:M,d(l){l&&v(e)}}}class Ze extends Q{constructor(e){super(),W(this,e,null,Re,Y,{})}}const Ge=async n=>(await Promise.all(n.map(async i=>await Promise.all(i.map(async(t,l)=>{if(t===null)return"";let r=l===0?"😃":"🤖",a="";if(typeof t=="string"){const o={audio:/<audio.*?src="(\/file=.*?)"/g,video:/<video.*?src="(\/file=.*?)"/g,image:/<img.*?src="(\/file=.*?)".*?\/>|!\[.*?\]\((\/file=.*?)\)/g};a=t;for(let[s,f]of Object.entries(o)){let m;for(;(m=f.exec(t))!==null;){const d=m[1]||m[2],k=await ae(d,"url");a=a.replace(d,k)}}}else{const o=await ae(t.data,"url");t.mime_type?.includes("audio")?a=`<audio controls src="${o}"></audio>`:t.mime_type?.includes("video")?a=o:t.mime_type?.includes("image")&&(a=`<img src="${o}" />`)}return`${r}: ${a}`}))))).map(i=>i.join(i[0]!==""&&i[1]!==""?`
`:"")).join(`
`);function oe(n){let e,i,t;return i=new Fe({}),{c(){e=C("span"),q(i.$$.fragment)},m(l,r){b(l,e,r),L(i,e,null),t=!0},i(l){t||(h(i.$$.fragment,l),t=!0)},o(l){g(i.$$.fragment,l),t=!1},d(l){l&&v(e),U(i)}}}function re(n){let e,i,t;return i=new Ie({}),{c(){e=C("span"),q(i.$$.fragment)},m(l,r){b(l,e,r),L(i,e,null),t=!0},i(l){t||(h(i.$$.fragment,l),t=!0)},o(l){g(i.$$.fragment,l),t=!1},d(l){l&&v(e),U(i)}}}function Qe(n){let e,i,t,l,r,a=!n[0]&&oe(),o=n[0]&&re();return{c(){e=C("button"),a&&a.c(),i=E(),o&&o.c(),u(e,"title","copy"),u(e,"class","svelte-jdgjqu")},m(s,f){b(s,e,f),a&&a.m(e,null),H(e,i),o&&o.m(e,null),t=!0,l||(r=D(e,"click",n[1]),l=!0)},p(s,[f]){s[0]?a&&(T(),g(a,1,1,()=>{a=null}),N()):a?f&1&&h(a,1):(a=oe(),a.c(),h(a,1),a.m(e,i)),s[0]?o?f&1&&h(o,1):(o=re(),o.c(),h(o,1),o.m(e,null)):o&&(T(),g(o,1,1,()=>{o=null}),N())},i(s){t||(h(a),h(o),t=!0)},o(s){g(a),g(o),t=!1},d(s){s&&v(e),a&&a.d(),o&&o.d(),l=!1,r()}}}function We(n,e,i){let t=!1,{value:l}=e,r;function a(){i(0,t=!0),r&&clearTimeout(r),r=setTimeout(()=>{i(0,t=!1)},2e3)}async function o(){if("clipboard"in navigator)await navigator.clipboard.writeText(l),a();else{const s=document.createElement("textarea");s.value=l,s.style.position="absolute",s.style.left="-999999px",document.body.prepend(s),s.select();try{document.execCommand("copy"),a()}catch(f){console.error(f)}finally{s.remove()}}}return He(()=>{r&&clearTimeout(r)}),n.$$set=s=>{"value"in s&&i(2,l=s.value)},[t,o,l]}class Ye extends Q{constructor(e){super(),W(this,e,We,Qe,Y,{value:2})}}function fe(n,e,i){const t=n.slice();return t[27]=e[i],t[29]=i,t}function ue(n,e,i){const t=n.slice();return t[30]=e[i],t[32]=i,t}function _e(n,e,i){const t=n.slice();return t[33]=e[i],t}function ce(n){let e,i,t;return i=new ze({props:{formatter:Ge,value:n[0]}}),i.$on("error",n[21]),i.$on("share",n[22]),{c(){e=C("div"),q(i.$$.fragment),u(e,"class","icon-button svelte-1uvwjgr")},m(l,r){b(l,e,r),L(i,e,null),t=!0},p(l,r){const a={};r[0]&1&&(a.value=l[0]),i.$set(a)},i(l){t||(h(i.$$.fragment,l),t=!0)},o(l){g(i.$$.fragment,l),t=!1},d(l){l&&v(e),U(i)}}}function me(n){let e,i,t=O(n[0]),l=[];for(let a=0;a<t.length;a+=1)l[a]=we(fe(n,t,a));const r=a=>g(l[a],1,1,()=>{l[a]=null});return{c(){for(let a=0;a<l.length;a+=1)l[a].c();e=te()},m(a,o){for(let s=0;s<l.length;s+=1)l[s]&&l[s].m(a,o);b(a,e,o),i=!0},p(a,o){if(o[0]&14299){t=O(a[0]);let s;for(s=0;s<t.length;s+=1){const f=fe(a,t,s);l[s]?(l[s].p(f,o),h(l[s],1)):(l[s]=we(f),l[s].c(),h(l[s],1),l[s].m(e.parentNode,e))}for(T(),s=t.length;s<l.length;s+=1)r(s);N()}},i(a){if(!i){for(let o=0;o<t.length;o+=1)h(l[o]);i=!0}},o(a){l=l.filter(Boolean);for(let o=0;o<l.length;o+=1)g(l[o]);i=!1},d(a){a&&v(e),ie(l,a)}}}function de(n){let e,i,t;return{c(){e=C("div"),i=C("img"),u(i,"class","avatar-image svelte-1uvwjgr"),F(i.src,t=ne(n[8][n[32]],n[9],n[10]))||u(i,"src",t),u(i,"alt","avatar-"+(n[32]==0?"user":"bot")),u(e,"class","avatar-container svelte-1uvwjgr")},m(l,r){b(l,e,r),H(e,i)},p(l,r){r[0]&1792&&!F(i.src,t=ne(l[8][l[32]],l[9],l[10]))&&u(i,"src",t)},d(l){l&&v(e)}}}function Je(n){let e,i=(n[30].orig_name||n[30].name)+"",t,l,r;return{c(){e=C("a"),t=pe(i),u(e,"data-testid","chatbot-file"),u(e,"href",l=n[30].data),u(e,"target","_blank"),u(e,"download",r=window.__is_colab__?null:n[30].orig_name||n[30].name),u(e,"class","svelte-1uvwjgr")},m(a,o){b(a,e,o),H(e,t)},p(a,o){o[0]&1&&i!==(i=(a[30].orig_name||a[30].name)+"")&&ye(t,i),o[0]&1&&l!==(l=a[30].data)&&u(e,"href",l),o[0]&1&&r!==(r=window.__is_colab__?null:a[30].orig_name||a[30].name)&&u(e,"download",r)},i:M,o:M,d(a){a&&v(e)}}}function Ke(n){let e,i,t;return{c(){e=C("img"),u(e,"data-testid","chatbot-image"),F(e.src,i=n[30].data)||u(e,"src",i),u(e,"alt",t=n[30].alt_text),u(e,"class","svelte-1uvwjgr")},m(l,r){b(l,e,r)},p(l,r){r[0]&1&&!F(e.src,i=l[30].data)&&u(e,"src",i),r[0]&1&&t!==(t=l[30].alt_text)&&u(e,"alt",t)},i:M,o:M,d(l){l&&v(e)}}}function Xe(n){let e,i,t,l,r,a;return{c(){e=C("video"),i=C("track"),u(i,"kind","captions"),u(i,"class","svelte-1uvwjgr"),u(e,"data-testid","chatbot-video"),e.controls=!0,F(e.src,t=n[30].data)||u(e,"src",t),u(e,"title",l=n[30].alt_text),u(e,"preload","auto"),u(e,"class","svelte-1uvwjgr")},m(o,s){b(o,e,s),H(e,i),r||(a=[D(e,"play",n[18]),D(e,"pause",n[19]),D(e,"ended",n[20])],r=!0)},p(o,s){s[0]&1&&!F(e.src,t=o[30].data)&&u(e,"src",t),s[0]&1&&l!==(l=o[30].alt_text)&&u(e,"title",l)},i:M,o:M,d(o){o&&v(e),r=!1,Be(a)}}}function xe(n){let e,i,t,l,r;return{c(){e=C("audio"),u(e,"data-testid","chatbot-audio"),e.controls=!0,u(e,"preload","metadata"),F(e.src,i=n[30].data)||u(e,"src",i),u(e,"title",t=n[30].alt_text),u(e,"class","svelte-1uvwjgr")},m(a,o){b(a,e,o),l||(r=[D(e,"play",n[15]),D(e,"pause",n[16]),D(e,"ended",n[17])],l=!0)},p(a,o){o[0]&1&&!F(e.src,i=a[30].data)&&u(e,"src",i),o[0]&1&&t!==(t=a[30].alt_text)&&u(e,"title",t)},i:M,o:M,d(a){a&&v(e),l=!1,Be(r)}}}function $e(n){let e,i,t,l,r;e=new De({props:{message:n[30],latex_delimiters:n[1]}}),e.$on("load",n[12]);let a=n[3]&&n[32]==1&&he(n),o=n[7]&&n[30]&&be(n);return{c(){q(e.$$.fragment),i=E(),a&&a.c(),t=E(),o&&o.c(),l=te()},m(s,f){L(e,s,f),b(s,i,f),a&&a.m(s,f),b(s,t,f),o&&o.m(s,f),b(s,l,f),r=!0},p(s,f){const m={};f[0]&1&&(m.message=s[30]),f[0]&2&&(m.latex_delimiters=s[1]),e.$set(m),s[3]&&s[32]==1?a?a.p(s,f):(a=he(s),a.c(),a.m(t.parentNode,t)):a&&(a.d(1),a=null),s[7]&&s[30]?o?(o.p(s,f),f[0]&129&&h(o,1)):(o=be(s),o.c(),h(o,1),o.m(l.parentNode,l)):o&&(T(),g(o,1,1,()=>{o=null}),N())},i(s){r||(h(e.$$.fragment,s),h(o),r=!0)},o(s){g(e.$$.fragment,s),g(o),r=!1},d(s){s&&(v(i),v(t),v(l)),U(e,s),a&&a.d(s),o&&o.d(s)}}}function he(n){let e,i=O(n[3]),t=[];for(let l=0;l<i.length;l+=1)t[l]=ge(_e(n,i,l));return{c(){e=C("div");for(let l=0;l<t.length;l+=1)t[l].c();u(e,"class","feedback svelte-1uvwjgr")},m(l,r){b(l,e,r);for(let a=0;a<t.length;a+=1)t[a]&&t[a].m(e,null)},p(l,r){if(r[0]&8){i=O(l[3]);let a;for(a=0;a<i.length;a+=1){const o=_e(l,i,a);t[a]?t[a].p(o,r):(t[a]=ge(o),t[a].c(),t[a].m(e,null))}for(;a<t.length;a+=1)t[a].d(1);t.length=i.length}},d(l){l&&v(e),ie(t,l)}}}function ge(n){let e,i=n[33]+"",t;return{c(){e=C("button"),t=pe(i),u(e,"class","svelte-1uvwjgr")},m(l,r){b(l,e,r),H(e,t)},p(l,r){r[0]&8&&i!==(i=l[33]+"")&&ye(t,i)},d(l){l&&v(e)}}}function be(n){let e,i,t;return i=new Ye({props:{value:n[30]}}),{c(){e=C("div"),q(i.$$.fragment),u(e,"class","icon-button svelte-1uvwjgr")},m(l,r){b(l,e,r),L(i,e,null),t=!0},p(l,r){const a={};r[0]&1&&(a.value=l[30]),i.$set(a)},i(l){t||(h(i.$$.fragment,l),t=!0)},o(l){g(i.$$.fragment,l),t=!1},d(l){l&&v(e),U(i)}}}function ve(n){let e,i,t,l,r,a,o,s,f,m,d,k,A,j=n[8][n[32]]!==null&&de(n);const y=[$e,xe,Xe,Ke,Je],B=[];function P(w,p){return p[0]&1&&(l=null),p[0]&1&&(r=null),p[0]&1&&(a=null),typeof w[30]=="string"?0:(l==null&&(l=!!(w[30]!==null&&w[30].mime_type?.includes("audio"))),l?1:(r==null&&(r=!!(w[30]!==null&&w[30].mime_type?.includes("video"))),r?2:(a==null&&(a=!!(w[30]!==null&&w[30].mime_type?.includes("image"))),a?3:w[30]!==null&&w[30].data!==null?4:-1)))}~(o=P(n,[-1,-1]))&&(s=B[o]=y[o](n));function S(){return n[23](n[29],n[32],n[30])}return{c(){e=C("div"),j&&j.c(),i=E(),t=C("div"),s&&s.c(),m=E(),u(t,"data-testid",n[32]==0?"user":"bot"),u(t,"class","message "+(n[32]==0?"user":"bot")+" svelte-1uvwjgr"),u(t,"dir",f=n[6]?"rtl":"ltr"),I(t,"latest",n[29]===n[0].length-1),I(t,"hide",n[30]===null),I(t,"selectable",n[4]),u(e,"class","message-row "+(n[32]==0?"user-row":"bot-row")+" svelte-1uvwjgr")},m(w,p){b(w,e,p),j&&j.m(e,null),H(e,i),H(e,t),~o&&B[o].m(t,null),H(e,m),d=!0,k||(A=D(t,"click",S),k=!0)},p(w,p){n=w,n[8][n[32]]!==null?j?j.p(n,p):(j=de(n),j.c(),j.m(e,i)):j&&(j.d(1),j=null);let V=o;o=P(n,p),o===V?~o&&B[o].p(n,p):(s&&(T(),g(B[V],1,1,()=>{B[V]=null}),N()),~o?(s=B[o],s?s.p(n,p):(s=B[o]=y[o](n),s.c()),h(s,1),s.m(t,null)):s=null),(!d||p[0]&64&&f!==(f=n[6]?"rtl":"ltr"))&&u(t,"dir",f),(!d||p[0]&1)&&I(t,"latest",n[29]===n[0].length-1),(!d||p[0]&1)&&I(t,"hide",n[30]===null),(!d||p[0]&16)&&I(t,"selectable",n[4])},i(w){d||(h(s),d=!0)},o(w){g(s),d=!1},d(w){w&&v(e),j&&j.d(),~o&&B[o].d(),k=!1,A()}}}function we(n){let e,i,t=O(n[27]),l=[];for(let a=0;a<t.length;a+=1)l[a]=ve(ue(n,t,a));const r=a=>g(l[a],1,1,()=>{l[a]=null});return{c(){for(let a=0;a<l.length;a+=1)l[a].c();e=te()},m(a,o){for(let s=0;s<l.length;s+=1)l[s]&&l[s].m(a,o);b(a,e,o),i=!0},p(a,o){if(o[0]&14299){t=O(a[27]);let s;for(s=0;s<t.length;s+=1){const f=ue(a,t,s);l[s]?(l[s].p(f,o),h(l[s],1)):(l[s]=ve(f),l[s].c(),h(l[s],1),l[s].m(e.parentNode,e))}for(T(),s=t.length;s<l.length;s+=1)r(s);N()}},i(a){if(!i){for(let o=0;o<t.length;o+=1)h(l[o]);i=!0}},o(a){l=l.filter(Boolean);for(let o=0;o<l.length;o+=1)g(l[o]);i=!1},d(a){a&&v(e),ie(l,a)}}}function ke(n){let e;return{c(){e=C("div"),e.innerHTML=`<div class="dot-flashing svelte-1uvwjgr"></div>
				 
				<div class="dot-flashing svelte-1uvwjgr"></div>
				 
				<div class="dot-flashing svelte-1uvwjgr"></div>`,u(e,"class","message pending svelte-1uvwjgr")},m(i,t){b(i,e,t)},d(i){i&&v(e)}}}function el(n){let e,i,t,l,r,a,o,s=n[5]&&n[0]!==null&&n[0].length>0&&ce(n),f=n[0]!==null&&me(n),m=n[2]&&ke();return{c(){s&&s.c(),e=E(),i=C("div"),t=C("div"),f&&f.c(),l=E(),m&&m.c(),u(t,"class","message-wrap svelte-1uvwjgr"),u(i,"class","wrap svelte-1uvwjgr")},m(d,k){s&&s.m(d,k),b(d,e,k),b(d,i,k),H(i,t),f&&f.m(t,null),H(t,l),m&&m.m(t,null),n[24](i),r=!0,a||(o=Se(Pe.call(null,t)),a=!0)},p(d,k){d[5]&&d[0]!==null&&d[0].length>0?s?(s.p(d,k),k[0]&33&&h(s,1)):(s=ce(d),s.c(),h(s,1),s.m(e.parentNode,e)):s&&(T(),g(s,1,1,()=>{s=null}),N()),d[0]!==null?f?(f.p(d,k),k[0]&1&&h(f,1)):(f=me(d),f.c(),h(f,1),f.m(t,l)):f&&(T(),g(f,1,1,()=>{f=null}),N()),d[2]?m||(m=ke(),m.c(),m.m(t,null)):m&&(m.d(1),m=null)},i(d){r||(h(s),h(f),r=!0)},o(d){g(s),g(f),r=!1},d(d){d&&(v(e),v(i)),s&&s.d(d),f&&f.d(),m&&m.d(),n[24](null),a=!1,o()}}}function ll(n,e,i){let{value:t}=e,l=null,{latex_delimiters:r}=e,{pending_message:a=!1}=e,{feedback:o=null}=e,{selectable:s=!1}=e,{show_share_button:f=!1}=e,{rtl:m=!1}=e,{show_copy_button:d=!1}=e,{avatar_images:k=[null,null]}=e,{root:A}=e,{root_url:j}=e,y,B;const P=Me();Te(()=>{B=y&&y.offsetHeight+y.scrollTop>y.scrollHeight-100});const S=()=>{B&&y.scrollTo(0,y.scrollHeight)};Ne(()=>{B&&(S(),y.querySelectorAll("img").forEach(c=>{c.addEventListener("load",()=>{S()})}))});function w(c,$,ee){P("select",{index:[c,$],value:ee})}function p(c){z.call(this,n,c)}function V(c){z.call(this,n,c)}function Z(c){z.call(this,n,c)}function G(c){z.call(this,n,c)}function J(c){z.call(this,n,c)}function K(c){z.call(this,n,c)}function X(c){z.call(this,n,c)}function x(c){z.call(this,n,c)}const _=(c,$,ee)=>w(c,$,ee);function R(c){Ee[c?"unshift":"push"](()=>{y=c,i(11,y)})}return n.$$set=c=>{"value"in c&&i(0,t=c.value),"latex_delimiters"in c&&i(1,r=c.latex_delimiters),"pending_message"in c&&i(2,a=c.pending_message),"feedback"in c&&i(3,o=c.feedback),"selectable"in c&&i(4,s=c.selectable),"show_share_button"in c&&i(5,f=c.show_share_button),"rtl"in c&&i(6,m=c.rtl),"show_copy_button"in c&&i(7,d=c.show_copy_button),"avatar_images"in c&&i(8,k=c.avatar_images),"root"in c&&i(9,A=c.root),"root_url"in c&&i(10,j=c.root_url)},n.$$.update=()=>{n.$$.dirty[0]&16385&&t!==l&&(i(14,l=t),P("change"))},[t,r,a,o,s,f,m,d,k,A,j,y,S,w,l,p,V,Z,G,J,K,X,x,_,R]}class tl extends Q{constructor(e){super(),W(this,e,ll,el,Y,{value:0,latex_delimiters:1,pending_message:2,feedback:3,selectable:4,show_share_button:5,rtl:6,show_copy_button:7,avatar_images:8,root:9,root_url:10},null,[-1,-1])}}function je(n){let e,i;const t=[n[17],{show_progress:n[17].show_progress==="hidden"?"hidden":"minimal"}];let l={};for(let r=0;r<t.length;r+=1)l=qe(l,t[r]);return e=new Le({props:l}),{c(){q(e.$$.fragment)},m(r,a){L(e,r,a),i=!0},p(r,a){const o=a&131072?Ue(t,[Ae(r[17]),{show_progress:r[17].show_progress==="hidden"?"hidden":"minimal"}]):{};e.$set(o)},i(r){i||(h(e.$$.fragment,r),i=!0)},o(r){g(e.$$.fragment,r),i=!1},d(r){U(e,r)}}}function Ce(n){let e,i;return e=new Oe({props:{show_label:n[7],Icon:Ze,float:!1,label:n[6]||"Chatbot"}}),{c(){q(e.$$.fragment)},m(t,l){L(e,t,l),i=!0},p(t,l){const r={};l&128&&(r.show_label=t[7]),l&64&&(r.label=t[6]||"Chatbot"),e.$set(r)},i(t){i||(h(e.$$.fragment,t),i=!0)},o(t){g(e.$$.fragment,t),i=!1},d(t){U(e,t)}}}function il(n){let e,i,t,l,r,a=n[17]&&je(n),o=n[7]&&Ce(n);return l=new tl({props:{selectable:n[10],show_share_button:n[11],value:n[19],latex_delimiters:n[14],pending_message:n[17]?.status==="pending",rtl:n[12],show_copy_button:n[13],avatar_images:n[16],root_url:n[9],root:n[8]}}),l.$on("change",n[20]),l.$on("select",n[21]),l.$on("share",n[22]),l.$on("error",n[23]),{c(){a&&a.c(),e=E(),i=C("div"),o&&o.c(),t=E(),q(l.$$.fragment),u(i,"class","wrapper svelte-nab2ao")},m(s,f){a&&a.m(s,f),b(s,e,f),b(s,i,f),o&&o.m(i,null),H(i,t),L(l,i,null),r=!0},p(s,f){s[17]?a?(a.p(s,f),f&131072&&h(a,1)):(a=je(s),a.c(),h(a,1),a.m(e.parentNode,e)):a&&(T(),g(a,1,1,()=>{a=null}),N()),s[7]?o?(o.p(s,f),f&128&&h(o,1)):(o=Ce(s),o.c(),h(o,1),o.m(i,t)):o&&(T(),g(o,1,1,()=>{o=null}),N());const m={};f&1024&&(m.selectable=s[10]),f&2048&&(m.show_share_button=s[11]),f&524288&&(m.value=s[19]),f&16384&&(m.latex_delimiters=s[14]),f&131072&&(m.pending_message=s[17]?.status==="pending"),f&4096&&(m.rtl=s[12]),f&8192&&(m.show_copy_button=s[13]),f&65536&&(m.avatar_images=s[16]),f&512&&(m.root_url=s[9]),f&256&&(m.root=s[8]),l.$set(m)},i(s){r||(h(a),h(o),h(l.$$.fragment,s),r=!0)},o(s){g(a),g(o),g(l.$$.fragment,s),r=!1},d(s){s&&(v(e),v(i)),a&&a.d(s),o&&o.d(),U(l)}}}function al(n){let e,i;return e=new Ve({props:{elem_id:n[0],elem_classes:n[1],visible:n[2],padding:!1,scale:n[4],min_width:n[5],height:n[18],allow_overflow:!1,$$slots:{default:[il]},$$scope:{ctx:n}}}),{c(){q(e.$$.fragment)},m(t,l){L(e,t,l),i=!0},p(t,[l]){const r={};l&1&&(r.elem_id=t[0]),l&2&&(r.elem_classes=t[1]),l&4&&(r.visible=t[2]),l&16&&(r.scale=t[4]),l&32&&(r.min_width=t[5]),l&262144&&(r.height=t[18]),l&34340808&&(r.$$scope={dirty:l,ctx:t}),e.$set(r)},i(t){i||(h(e.$$.fragment,t),i=!0)},o(t){g(e.$$.fragment,t),i=!1},d(t){U(e,t)}}}function nl(n,e,i){let{elem_id:t=""}=e,{elem_classes:l=[]}=e,{visible:r=!0}=e,{value:a=[]}=e,{scale:o=null}=e,{min_width:s=void 0}=e,{label:f}=e,{show_label:m=!0}=e,{root:d}=e,{root_url:k}=e,{selectable:A=!1}=e,{show_share_button:j=!1}=e,{rtl:y=!1}=e,{show_copy_button:B=!1}=e,{latex_delimiters:P}=e,{gradio:S}=e,{avatar_images:w=[null,null]}=e,p;const V=_=>_.replace('src="/file',`src="${d}file`);let{loading_status:Z=void 0}=e,{height:G=400}=e;const J=()=>S.dispatch("change",a),K=_=>S.dispatch("select",_.detail),X=_=>S.dispatch("share",_.detail),x=_=>S.dispatch("error",_.detail);return n.$$set=_=>{"elem_id"in _&&i(0,t=_.elem_id),"elem_classes"in _&&i(1,l=_.elem_classes),"visible"in _&&i(2,r=_.visible),"value"in _&&i(3,a=_.value),"scale"in _&&i(4,o=_.scale),"min_width"in _&&i(5,s=_.min_width),"label"in _&&i(6,f=_.label),"show_label"in _&&i(7,m=_.show_label),"root"in _&&i(8,d=_.root),"root_url"in _&&i(9,k=_.root_url),"selectable"in _&&i(10,A=_.selectable),"show_share_button"in _&&i(11,j=_.show_share_button),"rtl"in _&&i(12,y=_.rtl),"show_copy_button"in _&&i(13,B=_.show_copy_button),"latex_delimiters"in _&&i(14,P=_.latex_delimiters),"gradio"in _&&i(15,S=_.gradio),"avatar_images"in _&&i(16,w=_.avatar_images),"loading_status"in _&&i(17,Z=_.loading_status),"height"in _&&i(18,G=_.height)},n.$$.update=()=>{n.$$.dirty&776&&i(19,p=a?a.map(([_,R])=>[typeof _=="string"?V(_):se(_,d,k),typeof R=="string"?V(R):se(R,d,k)]):[])},[t,l,r,a,o,s,f,m,d,k,A,j,y,B,P,S,w,Z,G,p,J,K,X,x]}class sl extends Q{constructor(e){super(),W(this,e,nl,al,Y,{elem_id:0,elem_classes:1,visible:2,value:3,scale:4,min_width:5,label:6,show_label:7,root:8,root_url:9,selectable:10,show_share_button:11,rtl:12,show_copy_button:13,latex_delimiters:14,gradio:15,avatar_images:16,loading_status:17,height:18})}}const hl=sl;export{hl as default};
//# sourceMappingURL=index-7239f5f3.js.map
