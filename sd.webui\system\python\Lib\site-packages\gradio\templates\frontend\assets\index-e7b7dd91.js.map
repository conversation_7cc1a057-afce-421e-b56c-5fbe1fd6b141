{"version": 3, "file": "index-e7b7dd91.js", "sources": ["../../../../js/code/shared/Copy.svelte", "../../../../js/code/shared/Download.svelte", "../../../../js/code/shared/Widgets.svelte", "../../../../js/code/static/StaticCode.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { onD<PERSON>roy } from \"svelte\";\n\timport { fade } from \"svelte/transition\";\n\timport { Copy, Check } from \"@gradio/icons\";\n\n\tlet copied = false;\n\texport let value: string;\n\tlet timer: NodeJS.Timeout;\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 2000);\n\t}\n\n\tasync function handle_copy(): Promise<void> {\n\t\tif (\"clipboard\" in navigator) {\n\t\t\tawait navigator.clipboard.writeText(value);\n\t\t\tcopy_feedback();\n\t\t}\n\t}\n\n\tonDestroy(() => {\n\t\tif (timer) clearTimeout(timer);\n\t});\n</script>\n\n<button on:click={handle_copy} title=\"copy\">\n\t<!-- {#if !copied} -->\n\t<span class=\"copy-text\" class:copied><Copy /> </span>\n\t<!-- {/if} -->\n\t{#if copied}\n\t\t<span class=\"check\" transition:fade><Check /></span>\n\t{/if}\n</button>\n\n<style>\n\tbutton {\n\t\tposition: relative;\n\t\tcursor: pointer;\n\t\tpadding: 5px;\n\t\twidth: 22px;\n\t\theight: 22px;\n\t}\n\n\t.check {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tz-index: var(--layer-top);\n\t\tbackground: var(--background-fill-primary);\n\t\tpadding: var(--size-1);\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tcolor: var(--body-text-color);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onD<PERSON>roy } from \"svelte\";\n\timport { fade } from \"svelte/transition\";\n\timport { Download, Check } from \"@gradio/icons\";\n\n\texport let value: string;\n\texport let language: string;\n\n\t$: ext = get_ext_for_type(language);\n\n\tfunction get_ext_for_type(type: string): string {\n\t\tconst exts: Record<string, string> = {\n\t\t\tpy: \"py\",\n\t\t\tpython: \"py\",\n\t\t\tmd: \"md\",\n\t\t\tmarkdown: \"md\",\n\t\t\tjson: \"json\",\n\t\t\thtml: \"html\",\n\t\t\tcss: \"css\",\n\t\t\tjs: \"js\",\n\t\t\tjavascript: \"js\",\n\t\t\tts: \"ts\",\n\t\t\ttypescript: \"ts\",\n\t\t\tyaml: \"yaml\",\n\t\t\tyml: \"yml\",\n\t\t\tdockerfile: \"dockerfile\",\n\t\t\tsh: \"sh\",\n\t\t\tshell: \"sh\",\n\t\t\tr: \"r\"\n\t\t};\n\n\t\treturn exts[type] || \"txt\";\n\t}\n\n\tlet copied = false;\n\tlet timer: NodeJS.Timeout;\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 2000);\n\t}\n\n\t$: download_value = URL.createObjectURL(new Blob([value]));\n\n\tonDestroy(() => {\n\t\tif (timer) clearTimeout(timer);\n\t});\n</script>\n\n<a\n\tdownload=\"file.{ext}\"\n\thref={download_value}\n\tclass:copied\n\ton:click={copy_feedback}\n>\n\t<Download />\n\t{#if copied}\n\t\t<span class=\"check\" transition:fade><Check /></span>\n\t{/if}\n</a>\n\n<style>\n\ta {\n\t\tposition: relative;\n\t\tcursor: pointer;\n\t\tpadding: 5px;\n\n\t\twidth: 22px;\n\t\theight: 22px;\n\t}\n\n\t.copied {\n\t\tcolor: var(--color-green-500);\n\t}\n\n\t.check {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tz-index: var(--layer-top);\n\t\tbackground: var(--background-fill-primary);\n\t\tpadding: var(--size-1);\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tcolor: var(--body-text-color);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport Copy from \"./Copy.svelte\";\n\timport Download from \"./Download.svelte\";\n\n\texport let value: string;\n\texport let language: string;\n</script>\n\n<div>\n\t<Download {value} {language} />\n\t<Copy {value} />\n</div>\n\n<style>\n\tdiv {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: var(--block-label-margin);\n\t\tright: var(--block-label-margin);\n\t\talign-items: center;\n\n\t\tz-index: var(--layer-2);\n\t\ttransition: 150ms;\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-top: none;\n\t\tborder-right: none;\n\t\tborder-radius: var(--block-label-right-radius);\n\t\tbackground: var(--block-label-background-fill);\n\t\toverflow: hidden;\n\t\tcolor: var(--block-label-text-color);\n\t\tfont: var(--font);\n\t\tfont-size: var(--button-small-text-size);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport { afterUpdate } from \"svelte\";\n\timport { _ } from \"svelte-i18n\";\n\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\timport Code, { Widget } from \"../shared\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport { Block, BlockLabel, Empty } from \"@gradio/atoms\";\n\timport { Code as CodeIcon } from \"@gradio/icons\";\n\n\texport let value = \"\";\n\texport let value_is_output = false;\n\texport let language = \"\";\n\texport let lines = 5;\n\texport let target: HTMLElement;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let label = $_(\"code.code\");\n\texport let show_label = true;\n\texport let loading_status: LoadingStatus;\n\texport let gradio: Gradio<{\n\t\tchange: typeof value;\n\t\tinput: never;\n\t}>;\n\n\tlet dark_mode = target.classList.contains(\"dark\");\n\n\tfunction handle_change(): void {\n\t\tgradio.dispatch(\"change\", value);\n\t\tif (!value_is_output) {\n\t\t\tgradio.dispatch(\"input\");\n\t\t}\n\t}\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\t$: value, handle_change();\n</script>\n\n<Block variant={\"solid\"} padding={false} {elem_id} {elem_classes} {visible}>\n\t<StatusTracker {...loading_status} />\n\n\t<BlockLabel Icon={CodeIcon} {show_label} {label} float={false} />\n\n\t{#if !value}\n\t\t<Empty unpadded_box={true} size=\"large\">\n\t\t\t<CodeIcon />\n\t\t</Empty>\n\t{:else}\n\t\t<Widget {language} {value} />\n\n\t\t<Code bind:value {language} {lines} {dark_mode} readonly />\n\t{/if}\n</Block>\n"], "names": ["insert", "target", "span", "anchor", "ctx", "create_if_block", "button", "append", "copied", "value", "$$props", "timer", "copy_feedback", "$$invalidate", "handle_copy", "onDestroy", "a", "get_ext_for_type", "type", "language", "ext", "download_value", "div", "CodeIcon", "value_is_output", "lines", "elem_id", "elem_classes", "visible", "label", "$_", "show_label", "loading_status", "gradio", "dark_mode", "handle_change", "afterUpdate"], "mappings": "urBAkCEA,EAAmDC,EAAAC,EAAAC,CAAA,2PAD/CC,EAAM,CAAA,GAAAC,EAAA,4KAJZL,EAOQC,EAAAK,EAAAH,CAAA,EALPI,EAAoDD,EAAAJ,CAAA,2DAFnCE,EAAW,CAAA,CAAA,gDAIvBA,EAAM,CAAA,yNA5BP,IAAAI,EAAS,IACF,MAAAC,CAAa,EAAAC,EACpBC,WAEKC,GAAa,CACrBC,EAAA,EAAAL,EAAS,EAAI,EACTG,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,gBACPE,EAAA,EAAAL,EAAS,EAAK,GACZ,oBAGWM,GAAW,CACrB,cAAe,YACZ,MAAA,UAAU,UAAU,UAAUL,CAAK,EACzCG,KAIF,OAAAG,EAAS,IAAA,CACJJ,GAAO,aAAaA,CAAK,uPCmC7BX,EAAmDC,EAAAC,EAAAC,CAAA,2PAD/CC,EAAM,CAAA,GAAAC,EAAA,+EANKD,EAAG,CAAA,CAAA,aACbA,EAAc,CAAA,CAAA,2DAFrBJ,EAUGC,EAAAe,EAAAb,CAAA,2DANQC,EAAa,CAAA,CAAA,kBAGlBA,EAAM,CAAA,6GANKA,EAAG,CAAA,6CACbA,EAAc,CAAA,CAAA,kJA5CX,SAAAa,GAAiBC,EAAY,OAC3B,CACT,GAAI,KACJ,OAAQ,KACR,GAAI,KACJ,SAAU,KACV,KAAM,OACN,KAAM,OACN,IAAK,MACL,GAAI,KACJ,WAAY,KACZ,GAAI,KACJ,WAAY,KACZ,KAAM,OACN,IAAK,MACL,WAAY,aACZ,GAAI,KACJ,MAAO,KACP,EAAG,KAGQA,CAAI,GAAK,kCA1BX,MAAAT,CAAa,EAAAC,GACb,SAAAS,CAAgB,EAAAT,EA4BvBF,EAAS,GACTG,WAEKC,GAAa,CACrBC,EAAA,EAAAL,EAAS,EAAI,EACTG,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,gBACPE,EAAA,EAAAL,EAAS,EAAK,GACZ,KAKJ,OAAAO,EAAS,IAAA,CACJJ,GAAO,aAAaA,CAAK,+GAxC3BE,EAAA,EAAAO,EAAMH,GAAiBE,CAAQ,CAAA,iBAqCjCN,EAAA,EAAEQ,EAAiB,IAAI,gBAAe,IAAK,KAAI,CAAEZ,CAAK,CAAA,CAAA,CAAA,iTCrCxDT,EAGKC,EAAAqB,EAAAnB,CAAA,iTAPO,MAAAM,CAAa,EAAAC,GACb,SAAAS,CAAgB,EAAAT,u2BC2CL,0cALHN,EAAc,CAAA,CAAA,4FAEfmB,mCAAsC,kDAElDnB,EAAK,CAAA,IAAA,iMAJQA,EAAc,CAAA,CAAA,CAAA,CAAA,2bADlB,gBAAkB,+YA9BtB,GAAA,CAAA,MAAAK,EAAQ,EAAE,EAAAC,EACV,CAAA,gBAAAc,EAAkB,EAAK,EAAAd,EACvB,CAAA,SAAAS,EAAW,EAAE,EAAAT,EACb,CAAA,MAAAe,EAAQ,CAAC,EAAAf,GACT,OAAAT,CAAmB,EAAAS,EACnB,CAAA,QAAAgB,EAAU,EAAE,EAAAhB,GACZ,aAAAiB,EAAY,EAAA,EAAAjB,EACZ,CAAA,QAAAkB,EAAU,EAAI,EAAAlB,GACd,MAAAmB,EAAQC,EAAG,WAAW,CAAA,EAAApB,EACtB,CAAA,WAAAqB,EAAa,EAAI,EAAArB,GACjB,eAAAsB,CAA6B,EAAAtB,GAC7B,OAAAuB,CAGT,EAAAvB,EAEEwB,EAAYjC,EAAO,UAAU,SAAS,MAAM,WAEvCkC,GAAa,CACrBF,EAAO,SAAS,SAAUxB,CAAK,EAC1Be,GACJS,EAAO,SAAS,OAAO,EAGzBG,EAAW,IAAA,CACVvB,EAAA,GAAAW,EAAkB,EAAK,mfAEdW,EAAa"}