`CLIP 저장` 노드는 CLIP 텍스트 인코더 모델을 SafeTensors 형식으로 저장하도록 설계되었습니다. 이 노드는 고급 모델 병합 워크플로우의 일부이며, 일반적으로 `CLIPMergeSimple` 및 `CLIPMergeAdd`와 같은 노드와 함께 사용됩니다. 저장된 파일은 보안 및 호환성을 보장하기 위해 SafeTensors 형식을 사용합니다.

## 입력

| 매개변수 | 데이터 유형 | 필수 | 기본값 | 설명 |
|----------|------------|-------|--------|------|
| clip | CLIP | 예 | - | 저장할 CLIP 모델 |
| 파일명 접두사 | STRING | 예 | "clip/ComfyUI" | 저장할 파일의 접두사 경로 |
| prompt | PROMPT | 숨김 | - | 워크플로우 프롬프트 정보 (메타데이터용) |
| extra_pnginfo | EXTRA_PNGINFO | 숨김 | - | 추가 PNG 정보 (메타데이터용) |

## 출력

이 노드는 정의된 출력 유형이 없습니다. 처리된 파일은 `ComfyUI/output/` 폴더에 저장됩니다.

### 다중 파일 저장 전략

이 노드는 CLIP 모델 유형에 따라 다른 구성 요소를 저장합니다:

| 접두사 유형 | 파일 접미사 | 설명 |
|------------|------------|------|
| `clip_l.` | `_clip_l` | CLIP-L 텍스트 인코더 |
| `clip_g.` | `_clip_g` | CLIP-G 텍스트 인코더 |
| 접두사 없음 | 접미사 없음 | 기타 CLIP 구성 요소 |

## 사용 참고사항

1. **파일 위치**: 모든 파일은 `ComfyUI/output/` 디렉토리에 저장됩니다
2. **파일 형식**: 모델은 보안을 위해 SafeTensors 형식으로 저장됩니다
3. **메타데이터**: 워크플로우 정보 및 사용 가능한 경우 PNG 메타데이터가 포함됩니다
4. **명명 규칙**: 모델 유형에 따라 지정된 접두사와 적절한 접미사를 사용합니다
