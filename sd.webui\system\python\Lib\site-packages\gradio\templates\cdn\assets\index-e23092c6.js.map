{"version": 3, "file": "index-e23092c6.js", "sources": ["../../../../js/icons/src/LineChart.svelte", "../../../../js/label/static/Label.svelte", "../../../../js/label/static/StaticLabel.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\taria-hidden=\"true\"\n\trole=\"img\"\n\tclass=\"iconify iconify--carbon\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tpreserveAspectRatio=\"xMidYMid meet\"\n\tviewBox=\"0 0 32 32\"\n>\n\t<path\n\t\tfill=\"currentColor\"\n\t\td=\"M4 2H2v26a2 2 0 0 0 2 2h26v-2H4v-3h22v-8H4v-4h14V5H4Zm20 17v4H4v-4ZM16 7v4H4V7Z\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\texport let value: {\n\t\tlabel?: string;\n\t\tconfidences?: { label: string; confidence: number }[];\n\t};\n\n\tconst dispatch = createEventDispatcher<{ select: SelectData }>();\n\n\texport let color: string | undefined = undefined;\n\texport let selectable = false;\n</script>\n\n<div class=\"container\">\n\t<div\n\t\tclass=\"output-class\"\n\t\tdata-testid=\"label-output-value\"\n\t\tclass:no-confidence={!(\"confidences\" in value)}\n\t\tstyle:background-color={color || \"transparent\"}\n\t>\n\t\t{value.label}\n\t</div>\n\n\t<!-- TODO: fix -->\n\t<!-- svelte-ignore a11y-click-events-have-key-events-->\n\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t{#if typeof value === \"object\" && value.confidences}\n\t\t{#each value.confidences as confidence_set, i}\n\t\t\t<div\n\t\t\t\tclass=\"confidence-set group\"\n\t\t\t\tdata-testid={`${confidence_set.label}-confidence-set`}\n\t\t\t\tclass:selectable\n\t\t\t\ton:click={() => {\n\t\t\t\t\tdispatch(\"select\", { index: i, value: confidence_set.label });\n\t\t\t\t}}\n\t\t\t>\n\t\t\t\t<div class=\"inner-wrap\">\n\t\t\t\t\t<div class=\"bar\" style=\"width: {confidence_set.confidence * 100}%\" />\n\t\t\t\t\t<div class=\"label\">\n\t\t\t\t\t\t<div class=\"text\">{confidence_set.label}</div>\n\t\t\t\t\t\t{#if value.confidences}\n\t\t\t\t\t\t\t<div class=\"line\" />\n\t\t\t\t\t\t\t<div class=\"confidence\">\n\t\t\t\t\t\t\t\t{Math.round(confidence_set.confidence * 100)}%\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t{/each}\n\t{/if}\n</div>\n\n<style>\n\t.container {\n\t\tpadding: var(--block-padding);\n\t}\n\t.output-class {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tpadding: var(--size-6) var(--size-4);\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-bold);\n\t\tfont-size: var(--text-xxl);\n\t}\n\n\t.confidence-set {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: flex-start;\n\t\tmargin-bottom: var(--size-2);\n\t\tcolor: var(--body-text-color);\n\t\tline-height: var(--line-none);\n\t\tfont-family: var(--font-mono);\n\t}\n\n\t.confidence-set:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\n\t.inner-wrap {\n\t\tflex: 1 1 0%;\n\t}\n\n\t.bar {\n\t\tmargin-bottom: var(--size-1);\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--stat-background-fill);\n\t\theight: var(--size-1);\n\t}\n\n\t.label {\n\t\tdisplay: flex;\n\t\talign-items: baseline;\n\t}\n\n\t.label > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\t.confidence-set:hover .label {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.text {\n\t\tline-height: var(--line-md);\n\t}\n\n\t.line {\n\t\tflex: 1 1 0%;\n\t\tborder: 1px dashed var(--border-color-primary);\n\t\tpadding-right: var(--size-4);\n\t\tpadding-left: var(--size-4);\n\t}\n\n\t.confidence {\n\t\tmargin-left: auto;\n\t\ttext-align: right;\n\t}\n\t.selectable {\n\t\tcursor: pointer;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport Label from \"./Label.svelte\";\n\timport { LineChart as LabelIcon } from \"@gradio/icons\";\n\timport { Block, BlockLabel, Empty } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let color: undefined | string = undefined;\n\texport let value: {\n\t\tlabel?: string;\n\t\tconfidences?: { label: string; confidence: number }[];\n\t} = {};\n\texport let label = $_(\"label.label\");\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let show_label = true;\n\texport let selectable = false;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tselect: SelectData;\n\t}>;\n\n\t$: ({ confidences, label: _label } = value);\n\t$: _label, confidences, gradio.dispatch(\"change\");\n</script>\n\n<Block\n\ttest_id=\"label\"\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n\tpadding={false}\n>\n\t<StatusTracker {...loading_status} />\n\t{#if show_label}\n\t\t<BlockLabel Icon={LabelIcon} {label} disable={container === false} />\n\t{/if}\n\t{#if _label !== undefined && _label !== null}\n\t\t<Label\n\t\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\t\t{selectable}\n\t\t\t{value}\n\t\t\t{color}\n\t\t/>\n\t{:else}\n\t\t<Empty unpadded_box={true}><LabelIcon /></Empty>\n\t{/if}\n</Block>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "each_value", "ensure_array_like", "ctx", "i", "t1_value", "div0", "div1", "dirty", "set_data", "t1", "if_block", "create_if_block_1", "attr", "div4", "div4_data_testid_value", "div3", "div2", "t0_value", "create_if_block", "toggle_class", "set_style", "t0", "value", "$$props", "dispatch", "createEventDispatcher", "color", "selectable", "confidence_set", "LabelIcon", "blocklabel_changes", "elem_id", "elem_classes", "visible", "label", "$_", "container", "scale", "min_width", "loading_status", "show_label", "gradio", "select_handler", "detail", "confidences", "_label", "$$invalidate"], "mappings": "u2BAAAA,EAeKC,EAAAC,EAAAC,CAAA,EAJJC,EAGCF,EAAAG,CAAA,iLCeOC,EAAAC,EAAAC,KAAM,WAAW,uBAAtB,OAAIC,GAAA,yJAACH,EAAAC,EAAAC,KAAM,WAAW,oBAAtB,OAAI,GAAA,EAAA,yHAAJ,uDAgBKE,EAAA,KAAK,MAAMF,EAAe,CAAA,EAAA,WAAa,GAAG,EAAA,yDAAE,GAC9C,oFAHAR,EAAmBC,EAAAU,EAAAR,CAAA,WACnBH,EAEKC,EAAAW,EAAAT,CAAA,wBADHU,EAAA,GAAAH,KAAAA,EAAA,KAAK,MAAMF,EAAe,CAAA,EAAA,WAAa,GAAG,EAAA,KAAAM,EAAAC,EAAAL,CAAA,4DAJ1BA,EAAAF,KAAe,MAAK,eAClCQ,EAAAR,KAAM,aAAWS,EAAAT,CAAA,uLAHSA,EAAc,CAAA,EAAC,WAAa,IAAG,GAAA,iKAPhDU,EAAAC,EAAA,cAAAC,EAAA,GAAAZ,KAAe,sBAAK,iCAFrCR,EAoBKC,EAAAkB,EAAAhB,CAAA,EAZJC,EAWKe,EAAAE,CAAA,EAVJjB,EAAoEiB,EAAAV,CAAA,SACpEP,EAQKiB,EAAAC,CAAA,EAPJlB,EAA6CkB,EAAAV,CAAA,8FAFdJ,EAAc,CAAA,EAAC,WAAa,IAAG,GAAA,EAE3CK,EAAA,GAAAH,KAAAA,EAAAF,KAAe,MAAK,KAAAM,EAAAC,EAAAL,CAAA,EAClCF,KAAM,qEAVGK,EAAA,GAAAO,KAAAA,EAAA,GAAAZ,KAAe,mIAVhCe,EAAAf,KAAM,MAAK,OAMDQ,EAAA,OAAAR,EAAU,CAAA,GAAA,UAAYA,KAAM,aAAWgB,EAAAhB,CAAA,2IAT3BiB,EAAAd,EAAA,gBAAA,EAAA,gBAAiBH,EAAK,CAAA,EAAA,EACrBkB,EAAAf,EAAA,mBAAAH,MAAS,aAAa,iDALhDR,EAsCKC,EAAAW,EAAAT,CAAA,EArCJC,EAOKQ,EAAAD,CAAA,yCADHE,EAAA,GAAAU,KAAAA,EAAAf,KAAM,MAAK,KAAAM,EAAAa,EAAAJ,CAAA,OAHWE,EAAAd,EAAA,gBAAA,EAAA,gBAAiBH,EAAK,CAAA,EAAA,OACrBkB,EAAAf,EAAA,mBAAAH,MAAS,aAAa,EAQnC,OAAAA,EAAU,CAAA,GAAA,UAAYA,KAAM,6HAxB7B,MAAAoB,CAGV,EAAAC,EAEK,MAAAC,EAAWC,IAEN,GAAA,CAAA,MAAAC,EAA4B,MAAS,EAAAH,EACrC,CAAA,WAAAI,EAAa,EAAK,EAAAJ,kBAuBzBC,EAAS,SAAQ,CAAI,MAAOrB,EAAG,MAAOyB,EAAe,KAAK,CAAA,gRCU3CC,aAA4B,QAAA3B,OAAc,0FAAdK,EAAA,KAAAuB,EAAA,QAAA5B,OAAc,0JAUvC,0wBAZHA,EAAc,CAAA,CAAA,8EAC5BA,EAAU,EAAA,GAAAS,EAAAT,CAAA,uCAGV,OAAAA,EAAW,EAAA,IAAA,QAAaA,QAAW,KAAI,gMAJzBA,EAAc,CAAA,CAAA,CAAA,CAAA,eAC5BA,EAAU,EAAA,8hBAHN,ibAhCE,GAAA,CAAA,QAAA6B,EAAU,EAAE,EAAAR,GACZ,aAAAS,EAAY,EAAA,EAAAT,EACZ,CAAA,QAAAU,EAAU,EAAI,EAAAV,EACd,CAAA,MAAAG,EAA4B,MAAS,EAAAH,GACrC,MAAAD,EAAK,EAAA,EAAAC,GAIL,MAAAW,EAAQC,EAAG,aAAa,CAAA,EAAAZ,EACxB,CAAA,UAAAa,EAAY,EAAI,EAAAb,EAChB,CAAA,MAAAc,EAAuB,IAAI,EAAAd,EAC3B,CAAA,UAAAe,EAAgC,MAAS,EAAAf,GACzC,eAAAgB,CAA6B,EAAAhB,EAC7B,CAAA,WAAAiB,EAAa,EAAI,EAAAjB,EACjB,CAAA,WAAAI,EAAa,EAAK,EAAAJ,GAClB,OAAAkB,CAGT,EAAAlB,EAsBc,MAAAmB,EAAA,CAAA,CAAA,OAAAC,KAAaF,EAAO,SAAS,SAAUE,CAAM,yfApBvD,YAAAC,EAAa,MAAOC,GAAWvB,EAAKsB,GAAAE,EAAA,GAAAD,CAAA,EAAAC,EAAA,EAAAxB,CAAA,sBAClBmB,EAAO,SAAS,QAAQ"}