Failed to execute startup-script: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Manager\prestartup_script.py / Failed to initialize: Bad git executable.
The git executable must be specified in one of the following ways:
    - be included in your $PATH
    - be set via $GIT_PYTHON_GIT_EXECUTABLE
    - explicitly set via git.refresh(<full-path-to-git-executable>)

All git commands will error until this is rectified.

This initial message can be silenced or aggravated in the future by setting the
$GIT_PYTHON_REFRESH environment variable. Use one of the following values:
    - quiet|q|silence|s|silent|none|n|0: for no message or exception
    - warn|w|warning|log|l|1: for a warning message (logging level CRITICAL, displayed by default)
    - error|e|exception|raise|r|2: for a raised exception

Example:
    export GIT_PYTHON_REFRESH=quiet

[2025-06-28 16:28:07.821] 
Prestartup times for custom nodes:
[2025-06-28 16:28:07.821]    1.2 seconds (PRESTARTUP FAILED): C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Manager
[2025-06-28 16:28:07.821] 
[2025-06-28 16:28:09.146] Checkpoint files will always be loaded safely.
[2025-06-28 16:28:09.263] Total VRAM 24463 MB, total RAM 64957 MB
[2025-06-28 16:28:09.263] pytorch version: 2.7.1+cu128
[2025-06-28 16:28:09.263] Set vram state to: NORMAL_VRAM
[2025-06-28 16:28:09.264] Device: cuda:0 NVIDIA GeForce RTX 5090 Laptop GPU : cudaMallocAsync
[2025-06-28 16:28:10.206] Using pytorch attention
[2025-06-28 16:28:11.497] Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 16:28:11.497] ComfyUI version: 0.3.43
[2025-06-28 16:28:11.545] ComfyUI frontend version: 1.23.4
[2025-06-28 16:28:11.547] [Prompt Server] web root: C:\Users\<USER>\ai\ComfiGit\comfyui_env\Lib\site-packages\comfyui_frontend_package\static
### Loading: ComfyUI-Impact-Pack (V8.17.1)
[2025-06-28 16:28:14.882] [Impact Pack] Wildcards loading done.
[2025-06-28 16:28:14.915] Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\comfyui_env\Lib\site-packages\git\__init__.py", line 296, in <module>
    refresh()
    ~~~~~~~^^
  File "C:\Users\<USER>\ai\ComfiGit\comfyui_env\Lib\site-packages\git\__init__.py", line 287, in refresh
    if not Git.refresh(path=path):
           ~~~~~~~~~~~^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\comfyui_env\Lib\site-packages\git\cmd.py", line 631, in refresh
    raise ImportError(err)
ImportError: Bad git executable.
The git executable must be specified in one of the following ways:
    - be included in your $PATH
    - be set via $GIT_PYTHON_GIT_EXECUTABLE
    - explicitly set via git.refresh(<full-path-to-git-executable>)

All git commands will error until this is rectified.

This initial message can be silenced or aggravated in the future by setting the
$GIT_PYTHON_REFRESH environment variable. Use one of the following values:
    - quiet|q|silence|s|silent|none|n|0: for no message or exception
    - warn|w|warning|log|l|1: for a warning message (logging level CRITICAL, displayed by default)
    - error|e|exception|raise|r|2: for a raised exception

Example:
    export GIT_PYTHON_REFRESH=quiet


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Manager\__init__.py", line 12, in <module>
    import manager_server  # noqa: F401
    ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Manager\glob\manager_server.py", line 13, in <module>
    import git
  File "C:\Users\<USER>\ai\ComfiGit\comfyui_env\Lib\site-packages\git\__init__.py", line 298, in <module>
    raise ImportError("Failed to initialize: {0}".format(_exc)) from _exc
ImportError: Failed to initialize: Bad git executable.
The git executable must be specified in one of the following ways:
    - be included in your $PATH
    - be set via $GIT_PYTHON_GIT_EXECUTABLE
    - explicitly set via git.refresh(<full-path-to-git-executable>)

All git commands will error until this is rectified.

This initial message can be silenced or aggravated in the future by setting the
$GIT_PYTHON_REFRESH environment variable. Use one of the following values:
    - quiet|q|silence|s|silent|none|n|0: for no message or exception
    - warn|w|warning|log|l|1: for a warning message (logging level CRITICAL, displayed by default)
    - error|e|exception|raise|r|2: for a raised exception

Example:
    export GIT_PYTHON_REFRESH=quiet


[2025-06-28 16:28:14.916] Cannot import C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Manager module for custom nodes: Failed to initialize: Bad git executable.
The git executable must be specified in one of the following ways:
    - be included in your $PATH
    - be set via $GIT_PYTHON_GIT_EXECUTABLE
    - explicitly set via git.refresh(<full-path-to-git-executable>)

All git commands will error until this is rectified.

This initial message can be silenced or aggravated in the future by setting the
$GIT_PYTHON_REFRESH environment variable. Use one of the following values:
    - quiet|q|silence|s|silent|none|n|0: for no message or exception
    - warn|w|warning|log|l|1: for a warning message (logging level CRITICAL, displayed by default)
    - error|e|exception|raise|r|2: for a raised exception

Example:
    export GIT_PYTHON_REFRESH=quiet

[2025-06-28 16:28:15.038] 
Import times for custom nodes:
[2025-06-28 16:28:15.038]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\websocket_image_save.py
[2025-06-28 16:28:15.039]    0.0 seconds (IMPORT FAILED): C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Manager
[2025-06-28 16:28:15.039]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Custom-Scripts
[2025-06-28 16:28:15.039]    0.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Advanced-ControlNet
[2025-06-28 16:28:15.039]    0.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\efficiency-nodes-comfyui
[2025-06-28 16:28:15.039]    2.5 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Impact-Pack
[2025-06-28 16:28:15.040] 
[2025-06-28 16:28:15.498] Context impl SQLiteImpl.
[2025-06-28 16:28:15.499] Will assume non-transactional DDL.
[2025-06-28 16:28:15.500] No target revision found.
[2025-06-28 16:28:15.511] Starting server

[2025-06-28 16:28:15.511] To see the GUI go to: http://0.0.0.0:8188
