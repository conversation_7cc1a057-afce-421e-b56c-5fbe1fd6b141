import{n as X,i as rn,a as sn,l as an,c as _n,d as cn,b as fn,S as re,e as se,s as ae,f as me,g as u,h as k,j as p,k as b,m as T,o as M,t as O,p as pe,q as Fe,r as ne,u as C,v as le,w as I,x as H,y as un,z as mn,A as dn,B as pn,C as je,D as Pe,E as ve,F as G,G as W,H as J,I as te,J as St,K as ue,L as gn,_ as N,M as Ae,N as Le,O as he,P as hn,Q as fe,R as ye,T as He,U as Ge,V as vn,W as bn,X as kn,Y as wn,Z as En,$ as yn,a0 as We,a1 as jn,a2 as An,a3 as On,a4 as Ln,a5 as Tn,a6 as Pn}from"./index-2519a27e.js";import{c as In,f as Je,B as Ve,a as Rn}from"./Button-748313a7.js";function Dn(l,e,t,n){if(!e)return X;const i=l.getBoundingClientRect();if(e.left===i.left&&e.right===i.right&&e.top===i.top&&e.bottom===i.bottom)return X;const{delay:o=0,duration:s=300,easing:r=rn,start:a=sn()+o,end:_=a+s,tick:c=X,css:f}=t(l,{from:e,to:i},n);let m=!0,h=!1,j;function A(){f&&(j=_n(l,0,1,s,o,r,f)),o||(h=!0)}function y(){f&&cn(l,j),m=!1}return an(g=>{if(!h&&g>=a&&(h=!0),h&&g>=_&&(c(1,0),y()),!m)return!1;if(h){const E=g-a,L=0+1*r(E/s);c(L,1-L)}return!0}),A(),c(0,1),y}function Vn(l){const e=getComputedStyle(l);if(e.position!=="absolute"&&e.position!=="fixed"){const{width:t,height:n}=e,i=l.getBoundingClientRect();l.style.position="absolute",l.style.width=t,l.style.height=n,qn(l,i)}}function qn(l,e){const t=l.getBoundingClientRect();if(e.left!==t.left||e.top!==t.top){const n=getComputedStyle(l),i=n.transform==="none"?"":n.transform;l.style.transform=`${i} translate(${e.left-t.left}px, ${e.top-t.top}px)`}}function Cn(l,{from:e,to:t},n={}){const i=getComputedStyle(l),o=i.transform==="none"?"":i.transform,[s,r]=i.transformOrigin.split(" ").map(parseFloat),a=e.left+e.width*s/t.width-(t.left+s),_=e.top+e.height*r/t.height-(t.top+r),{delay:c=0,duration:f=h=>Math.sqrt(h)*120,easing:m=In}=n;return{delay:c,duration:fn(f)?f(Math.sqrt(a*a+_*_)):f,easing:m,css:(h,j)=>{const A=j*a,y=j*_,g=h+j*e.width/t.width,E=h+j*e.height/t.height;return`transform: ${o} translate(${A}px, ${y}px) scale(${g}, ${E});`}}}function Nn(l){let e,t;return{c(){e=me("svg"),t=me("path"),u(t,"stroke-linecap","round"),u(t,"stroke-linejoin","round"),u(t,"d","M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"),u(e,"fill","none"),u(e,"stroke","currentColor"),u(e,"viewBox","0 0 24 24"),u(e,"width","100%"),u(e,"height","100%"),u(e,"xmlns","http://www.w3.org/2000/svg"),u(e,"aria-hidden","true"),u(e,"stroke-width","2"),u(e,"stroke-linecap","round"),u(e,"stroke-linejoin","round")},m(n,i){k(n,e,i),p(e,t)},p:X,i:X,o:X,d(n){n&&b(e)}}}let Sn=class extends re{constructor(e){super(),se(this,e,null,Nn,ae,{})}};function zn(l){let e,t;return{c(){e=me("svg"),t=me("path"),u(t,"stroke-linecap","round"),u(t,"stroke-linejoin","round"),u(t,"d","M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"),u(e,"fill","none"),u(e,"stroke","currentColor"),u(e,"viewBox","0 0 24 24"),u(e,"width","100%"),u(e,"height","100%"),u(e,"xmlns","http://www.w3.org/2000/svg"),u(e,"aria-hidden","true"),u(e,"stroke-width","2"),u(e,"stroke-linecap","round"),u(e,"stroke-linejoin","round")},m(n,i){k(n,e,i),p(e,t)},p:X,i:X,o:X,d(n){n&&b(e)}}}class Mn extends re{constructor(e){super(),se(this,e,null,zn,ae,{})}}function Bn(l){let e,t;return{c(){e=me("svg"),t=me("path"),u(t,"stroke-linecap","round"),u(t,"stroke-linejoin","round"),u(t,"d","M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"),u(e,"fill","none"),u(e,"stroke","currentColor"),u(e,"stroke-width","2"),u(e,"viewBox","0 0 24 24"),u(e,"width","100%"),u(e,"height","100%"),u(e,"xmlns","http://www.w3.org/2000/svg"),u(e,"aria-hidden","true"),u(e,"stroke-linecap","round"),u(e,"stroke-linejoin","round")},m(n,i){k(n,e,i),p(e,t)},p:X,i:X,o:X,d(n){n&&b(e)}}}class Un extends re{constructor(e){super(),se(this,e,null,Bn,ae,{})}}function Fn(l){let e,t;return e=new Sn({}),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Hn(l){let e,t;return e=new Mn({}),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Gn(l){let e,t;return e=new Un({}),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Wn(l){let e,t,n,i,o,s,r,a,_,c,f,m,h,j,A,y,g,E,L,v,w,D,q,F,Q,V,_e,P;const U=[Gn,Hn,Fn],K=[];function de(R,Z){return R[1]==="warning"?0:R[1]==="info"?1:R[1]==="error"?2:-1}return~(n=de(l))&&(i=K[n]=U[n](l)),{c(){e=T("div"),t=T("div"),i&&i.c(),s=M(),r=T("div"),a=T("div"),_=O(l[1]),f=M(),m=T("div"),h=O(l[0]),y=M(),g=T("button"),E=T("span"),E.textContent="×",v=M(),w=T("div"),u(t,"class",o="toast-icon "+l[1]+" svelte-z3l7qj"),u(a,"class",c="toast-title "+l[1]+" svelte-z3l7qj"),u(m,"class",j="toast-text "+l[1]+" svelte-z3l7qj"),u(r,"class",A="toast-details "+l[1]+" svelte-z3l7qj"),u(E,"aria-hidden","true"),u(g,"class",L="toast-close "+l[1]+" svelte-z3l7qj"),u(g,"type","button"),u(g,"aria-label","Close"),u(g,"data-testid","toast-close"),u(w,"class",D="timer "+l[1]+" svelte-z3l7qj"),u(e,"class",q="toast-body "+l[1]+" svelte-z3l7qj"),u(e,"role","alert"),u(e,"data-testid","toast-body")},m(R,Z){k(R,e,Z),p(e,t),~n&&K[n].m(t,null),p(e,s),p(e,r),p(r,a),p(a,_),p(r,f),p(r,m),p(m,h),p(e,y),p(e,g),p(g,E),p(e,v),p(e,w),V=!0,_e||(P=[pe(g,"click",l[2]),pe(e,"click",Fe(l[4])),pe(e,"keydown",Fe(l[5]))],_e=!0)},p(R,[Z]){let ge=n;n=de(R),n!==ge&&(i&&(ne(),C(K[ge],1,1,()=>{K[ge]=null}),le()),~n?(i=K[n],i||(i=K[n]=U[n](R),i.c()),I(i,1),i.m(t,null)):i=null),(!V||Z&2&&o!==(o="toast-icon "+R[1]+" svelte-z3l7qj"))&&u(t,"class",o),(!V||Z&2)&&H(_,R[1]),(!V||Z&2&&c!==(c="toast-title "+R[1]+" svelte-z3l7qj"))&&u(a,"class",c),(!V||Z&1)&&H(h,R[0]),(!V||Z&2&&j!==(j="toast-text "+R[1]+" svelte-z3l7qj"))&&u(m,"class",j),(!V||Z&2&&A!==(A="toast-details "+R[1]+" svelte-z3l7qj"))&&u(r,"class",A),(!V||Z&2&&L!==(L="toast-close "+R[1]+" svelte-z3l7qj"))&&u(g,"class",L),(!V||Z&2&&D!==(D="timer "+R[1]+" svelte-z3l7qj"))&&u(w,"class",D),(!V||Z&2&&q!==(q="toast-body "+R[1]+" svelte-z3l7qj"))&&u(e,"class",q)},i(R){V||(I(i),R&&un(()=>{V&&(Q&&Q.end(1),F=mn(e,Je,{duration:200,delay:100}),F.start())}),V=!0)},o(R){C(i),F&&F.invalidate(),R&&(Q=dn(e,Je,{duration:200})),V=!1},d(R){R&&b(e),~n&&K[n].d(),R&&Q&&Q.end(),_e=!1,pn(P)}}}function Jn(l,e,t){let{message:n=""}=e,{type:i}=e,{id:o}=e;const s=je();function r(){s("close",o)}Pe(()=>{setTimeout(()=>{r()},1e4)});function a(c){ve.call(this,l,c)}function _(c){ve.call(this,l,c)}return l.$$set=c=>{"message"in c&&t(0,n=c.message),"type"in c&&t(1,i=c.type),"id"in c&&t(3,o=c.id)},[n,i,r,o,a,_]}class Qn extends re{constructor(e){super(),se(this,e,Jn,Wn,ae,{message:0,type:1,id:3})}}function Qe(l,e,t){const n=l.slice();return n[2]=e[t].type,n[3]=e[t].message,n[4]=e[t].id,n}function Ke(l,e){let t,n,i,o,s=X,r;return n=new Qn({props:{type:e[2],message:e[3],id:e[4]}}),n.$on("close",e[1]),{key:l,first:null,c(){t=T("div"),G(n.$$.fragment),i=M(),ue(t,"width","100%"),this.first=t},m(a,_){k(a,t,_),W(n,t,null),p(t,i),r=!0},p(a,_){e=a;const c={};_&1&&(c.type=e[2]),_&1&&(c.message=e[3]),_&1&&(c.id=e[4]),n.$set(c)},r(){o=t.getBoundingClientRect()},f(){Vn(t),s()},a(){s(),s=Dn(t,o,Cn,{duration:300})},i(a){r||(I(n.$$.fragment,a),r=!0)},o(a){C(n.$$.fragment,a),r=!1},d(a){a&&b(t),J(n)}}}function Kn(l){let e,t=[],n=new Map,i,o=te(l[0]);const s=r=>r[4];for(let r=0;r<o.length;r+=1){let a=Qe(l,o,r),_=s(a);n.set(_,t[r]=Ke(_,a))}return{c(){e=T("div");for(let r=0;r<t.length;r+=1)t[r].c();u(e,"class","toast-wrap svelte-pu0yf1")},m(r,a){k(r,e,a);for(let _=0;_<t.length;_+=1)t[_]&&t[_].m(e,null);i=!0},p(r,[a]){if(a&1){o=te(r[0]),ne();for(let _=0;_<t.length;_+=1)t[_].r();t=St(t,a,s,1,r,o,n,e,gn,Ke,null,Qe);for(let _=0;_<t.length;_+=1)t[_].a();le()}},i(r){if(!i){for(let a=0;a<o.length;a+=1)I(t[a]);i=!0}},o(r){for(let a=0;a<t.length;a+=1)C(t[a]);i=!1},d(r){r&&b(e);for(let a=0;a<t.length;a+=1)t[a].d()}}}function Zn(l){l.length>0&&"parentIFrame"in window&&window.parentIFrame?.scrollTo(0,0)}function Xn(l,e,t){let{messages:n=[]}=e;function i(o){ve.call(this,l,o)}return l.$$set=o=>{"messages"in o&&t(0,n=o.messages)},l.$$.update=()=>{l.$$.dirty&1&&Zn(n)},[n,i]}class Yn extends re{constructor(e){super(),se(this,e,Xn,Kn,ae,{messages:0})}}const Ze={accordion:{static:()=>N(()=>import("./index-ebbc89b6.js"),["./index-ebbc89b6.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css","./StaticColumn-6bd0cb0d.js","./StaticColumn-2853eb31.css","./index-8f1feca1.css"],import.meta.url)},annotatedimage:{static:()=>N(()=>import("./index-a1ea7e02.js"),["./index-a1ea7e02.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css","./BlockLabel-ddfceeb6.js","./Empty-23f73391.js","./Image-4f63353a.js","./index-f0e43e7d.css"],import.meta.url)},audio:{static:()=>N(()=>import("./index-54ba3fd2.js"),["./index-54ba3fd2.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css","./BlockLabel-ddfceeb6.js","./IconButton-f5e53901.js","./Empty-23f73391.js","./ShareButton-33bb4648.js","./utils-c3e3db58.js","./Download-43879c5c.js","./utils-65590fab.js","./index-4e2a7646.css"],import.meta.url),interactive:()=>N(()=>import("./index-8bbd76aa.js"),["./index-8bbd76aa.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css","./UploadText-87afcd1e.js","./Upload-b0a38490.js","./ModifyUpload-87a335b2.js","./IconButton-f5e53901.js","./BlockLabel-ddfceeb6.js","./utils-65590fab.js","./index-4a131291.css"],import.meta.url)},box:{static:()=>N(()=>import("./index-51054ff7.js"),["./index-51054ff7.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css"],import.meta.url)},button:{static:()=>N(()=>import("./index-d48aa930.js"),["./index-d48aa930.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css"],import.meta.url)},chatbot:{static:()=>N(()=>import("./index-c2548338.js"),["./index-c2548338.js","./index-2519a27e.js","./index-29fa5a20.css","./utils-c3e3db58.js","./Button-748313a7.js","./Button-620848cb.css","./ShareButton-33bb4648.js","./IconButton-f5e53901.js","./StaticMarkdown-e77fff97.js","./StaticMarkdown-7c13eb76.css","./Copy-754acc5f.js","./BlockLabel-ddfceeb6.js","./index-25f94022.css"],import.meta.url)},checkbox:{static:()=>N(()=>import("./index-656d9d84.js"),["./index-656d9d84.js","./index-2519a27e.js","./index-29fa5a20.css","./Checkbox-3e2588fc.js","./Checkbox-dc375626.css","./Button-748313a7.js","./Button-620848cb.css","./Info-02b862eb.js"],import.meta.url),interactive:()=>N(()=>import("./index-600fc6f0.js"),["./index-600fc6f0.js","./index-2519a27e.js","./index-29fa5a20.css","./Checkbox-3e2588fc.js","./Checkbox-dc375626.css","./Button-748313a7.js","./Button-620848cb.css","./Info-02b862eb.js"],import.meta.url)},checkboxgroup:{static:()=>N(()=>import("./index-bfa83c87.js"),["./index-bfa83c87.js","./index-2519a27e.js","./index-29fa5a20.css","./Checkboxgroup-73d55393.js","./Button-748313a7.js","./Button-620848cb.css","./BlockTitle-de7b2d6e.js","./Info-02b862eb.js","./Checkboxgroup-e557d23a.css"],import.meta.url),interactive:()=>N(()=>import("./index-bafb2d1e.js"),["./index-bafb2d1e.js","./index-2519a27e.js","./index-29fa5a20.css","./Checkboxgroup-73d55393.js","./Button-748313a7.js","./Button-620848cb.css","./BlockTitle-de7b2d6e.js","./Info-02b862eb.js","./Checkboxgroup-e557d23a.css"],import.meta.url)},code:{static:()=>N(()=>import("./index-e7b7dd91.js"),["./index-e7b7dd91.js","./index-2519a27e.js","./index-29fa5a20.css","./Widgets.svelte_svelte_type_style_lang-66f20987.js","./Widgets-4ccfb72c.css","./Button-748313a7.js","./Button-620848cb.css","./Copy-754acc5f.js","./Download-43879c5c.js","./BlockLabel-ddfceeb6.js","./Empty-23f73391.js"],import.meta.url),interactive:()=>N(()=>import("./index-ec313a35.js"),["./index-ec313a35.js","./index-2519a27e.js","./index-29fa5a20.css","./Widgets.svelte_svelte_type_style_lang-66f20987.js","./Widgets-4ccfb72c.css","./Button-748313a7.js","./Button-620848cb.css","./BlockLabel-ddfceeb6.js"],import.meta.url)},colorpicker:{static:()=>N(()=>import("./index-25f908cf.js"),["./index-25f908cf.js","./index-2519a27e.js","./index-29fa5a20.css","./Colorpicker-84d86033.js","./Button-748313a7.js","./Button-620848cb.css","./BlockTitle-de7b2d6e.js","./Info-02b862eb.js","./Colorpicker-cd311153.css"],import.meta.url),interactive:()=>N(()=>import("./index-8848d2c9.js"),["./index-8848d2c9.js","./index-2519a27e.js","./index-29fa5a20.css","./Colorpicker-84d86033.js","./Button-748313a7.js","./Button-620848cb.css","./BlockTitle-de7b2d6e.js","./Info-02b862eb.js","./Colorpicker-cd311153.css"],import.meta.url)},column:{static:()=>N(()=>import("./index-81a4ec15.js"),["./index-81a4ec15.js","./StaticColumn-6bd0cb0d.js","./index-2519a27e.js","./index-29fa5a20.css","./StaticColumn-2853eb31.css"],import.meta.url)},dataframe:{static:()=>N(()=>import("./index-a3e055a1.js"),["./index-a3e055a1.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css","./Table-be00801a.js","./utils-c3e3db58.js","./Upload-b0a38490.js","./StaticMarkdown-e77fff97.js","./StaticMarkdown-7c13eb76.css","./dsv-576afacd.js","./Table-77433dc3.css"],import.meta.url),interactive:()=>N(()=>import("./index-7d175e82.js"),["./index-7d175e82.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css","./Table-be00801a.js","./utils-c3e3db58.js","./Upload-b0a38490.js","./StaticMarkdown-e77fff97.js","./StaticMarkdown-7c13eb76.css","./dsv-576afacd.js","./Table-77433dc3.css"],import.meta.url)},dataset:{static:()=>N(()=>import("./index-8cfbebba.js"),["./index-8cfbebba.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css","./csv-b0b7514a.js","./dsv-576afacd.js","./index-77812ee4.css","./Player-1e00f554.css"],import.meta.url)},dropdown:{static:()=>N(()=>import("./index-3739d845.js"),["./index-3739d845.js","./index-2519a27e.js","./index-29fa5a20.css","./Dropdown-bb2d071f.js","./Button-748313a7.js","./Button-620848cb.css","./BlockTitle-de7b2d6e.js","./Info-02b862eb.js","./Dropdown-1d75348c.css"],import.meta.url),interactive:()=>N(()=>import("./index-22627d22.js"),["./index-22627d22.js","./index-2519a27e.js","./index-29fa5a20.css","./Dropdown-bb2d071f.js","./Button-748313a7.js","./Button-620848cb.css","./BlockTitle-de7b2d6e.js","./Info-02b862eb.js","./Dropdown-1d75348c.css"],import.meta.url)},file:{static:()=>N(()=>import("./index-4588fc96.js"),["./index-4588fc96.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css","./BlockLabel-ddfceeb6.js","./Empty-23f73391.js","./File-50244b53.js","./FilePreview-282650e0.js","./FilePreview-f49dff58.css"],import.meta.url),interactive:()=>N(()=>import("./index-2e98bba4.js"),["./index-2e98bba4.js","./index-2519a27e.js","./index-29fa5a20.css","./Upload-b0a38490.js","./Button-748313a7.js","./Button-620848cb.css","./ModifyUpload-87a335b2.js","./IconButton-f5e53901.js","./BlockLabel-ddfceeb6.js","./File-50244b53.js","./FilePreview-282650e0.js","./FilePreview-f49dff58.css","./UploadText-87afcd1e.js"],import.meta.url)},form:{static:()=>N(()=>import("./index-42d38290.js"),["./index-42d38290.js","./StaticForm-23a48556.js","./index-2519a27e.js","./index-29fa5a20.css","./StaticForm-3812b7f1.css"],import.meta.url)},gallery:{static:()=>N(()=>import("./index-a024de33.js"),["./index-a024de33.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css","./BlockLabel-ddfceeb6.js","./IconButton-f5e53901.js","./Empty-23f73391.js","./ShareButton-33bb4648.js","./utils-c3e3db58.js","./ModifyUpload-87a335b2.js","./Download-43879c5c.js","./Image-4f63353a.js","./index-bc19ffad.css"],import.meta.url)},group:{static:()=>N(()=>import("./index-70a3ef59.js"),["./index-70a3ef59.js","./index-2519a27e.js","./index-29fa5a20.css","./index-37519934.css"],import.meta.url)},highlightedtext:{static:()=>N(()=>import("./index-f626b427.js"),["./index-f626b427.js","./index-2519a27e.js","./index-29fa5a20.css","./color-ba58c3cc.js","./Button-748313a7.js","./Button-620848cb.css","./BlockLabel-ddfceeb6.js","./Empty-23f73391.js","./index-9d08c7d8.css"],import.meta.url)},html:{static:()=>N(()=>import("./index-98949b8a.js"),["./index-98949b8a.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css","./index-329f8260.css"],import.meta.url)},image:{static:()=>N(()=>import("./index-06a5ac05.js"),["./index-06a5ac05.js","./index-2519a27e.js","./index-29fa5a20.css","./utils-c3e3db58.js","./Button-748313a7.js","./Button-620848cb.css","./BlockLabel-ddfceeb6.js","./IconButton-f5e53901.js","./Empty-23f73391.js","./ShareButton-33bb4648.js","./Download-43879c5c.js","./Image-4f63353a.js","./utils-90f3612b.js","./index-f62e764d.css"],import.meta.url),interactive:()=>N(()=>import("./index-2541585c.js"),["./index-2541585c.js","./InteractiveImage-870bae3a.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css","./BlockLabel-ddfceeb6.js","./Image-4f63353a.js","./utils-90f3612b.js","./IconButton-f5e53901.js","./ModifyUpload-87a335b2.js","./Undo-6ef95523.js","./Upload-b0a38490.js","./UploadText-87afcd1e.js","./InteractiveImage-b496c98d.css"],import.meta.url)},interpretation:{static:()=>N(()=>import("./index-8a93944d.js"),["./index-8a93944d.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css","./BlockTitle-de7b2d6e.js","./Info-02b862eb.js","./index-6acaa952.css"],import.meta.url),interactive:()=>N(()=>import("./index-8a93944d.js"),["./index-8a93944d.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css","./BlockTitle-de7b2d6e.js","./Info-02b862eb.js","./index-6acaa952.css"],import.meta.url)},json:{static:()=>N(()=>import("./index-a6bfcb35.js"),["./index-a6bfcb35.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css","./Copy-754acc5f.js","./Empty-23f73391.js","./BlockLabel-ddfceeb6.js","./index-3ca142e0.css"],import.meta.url)},label:{static:()=>N(()=>import("./index-256984e8.js"),["./index-256984e8.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css","./BlockLabel-ddfceeb6.js","./Empty-23f73391.js","./index-cc2431f4.css"],import.meta.url)},markdown:{static:()=>N(()=>import("./index-d178b6e1.js"),["./index-d178b6e1.js","./StaticMarkdown-e77fff97.js","./index-2519a27e.js","./index-29fa5a20.css","./utils-c3e3db58.js","./Button-748313a7.js","./Button-620848cb.css","./StaticMarkdown-7c13eb76.css"],import.meta.url)},model3d:{static:()=>N(()=>import("./index-f34451e3.js"),["./index-f34451e3.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css","./BlockLabel-ddfceeb6.js","./Empty-23f73391.js","./File-50244b53.js","./IconButton-f5e53901.js","./Download-43879c5c.js","./babylonjs.loaders.min-330fe675.js","./index-d5d74218.css"],import.meta.url),interactive:()=>N(()=>import("./index-952b6c50.js"),["./index-952b6c50.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css","./UploadText-87afcd1e.js","./Upload-b0a38490.js","./ModifyUpload-87a335b2.js","./IconButton-f5e53901.js","./BlockLabel-ddfceeb6.js","./File-50244b53.js","./babylonjs.loaders.min-330fe675.js","./index-3a72a5f5.css"],import.meta.url)},number:{static:()=>N(()=>import("./index-5bfd6075.js"),["./index-5bfd6075.js","./index-2519a27e.js","./index-29fa5a20.css","./Number-ef0e738a.js","./Button-748313a7.js","./Button-620848cb.css","./BlockTitle-de7b2d6e.js","./Info-02b862eb.js","./Number-76c3ee3f.css"],import.meta.url),interactive:()=>N(()=>import("./index-6e807821.js"),["./index-6e807821.js","./index-2519a27e.js","./index-29fa5a20.css","./Number-ef0e738a.js","./Button-748313a7.js","./Button-620848cb.css","./BlockTitle-de7b2d6e.js","./Info-02b862eb.js","./Number-76c3ee3f.css"],import.meta.url)},plot:{static:()=>N(()=>import("./index-a9692bd2.js"),["./index-a9692bd2.js","./index-2519a27e.js","./index-29fa5a20.css","./color-ba58c3cc.js","./linear-bcbcf466.js","./dsv-576afacd.js","./Button-748313a7.js","./Button-620848cb.css","./Empty-23f73391.js","./BlockLabel-ddfceeb6.js","./index-31d5c487.css"],import.meta.url)},radio:{static:()=>N(()=>import("./index-9fe9b571.js"),["./index-9fe9b571.js","./index-2519a27e.js","./index-29fa5a20.css","./Radio-295ff847.js","./Button-748313a7.js","./Button-620848cb.css","./BlockTitle-de7b2d6e.js","./Info-02b862eb.js","./Radio-b7554727.css"],import.meta.url),interactive:()=>N(()=>import("./index-6a48cfe0.js"),["./index-6a48cfe0.js","./index-2519a27e.js","./index-29fa5a20.css","./Radio-295ff847.js","./Button-748313a7.js","./Button-620848cb.css","./BlockTitle-de7b2d6e.js","./Info-02b862eb.js","./Radio-b7554727.css"],import.meta.url)},row:{static:()=>N(()=>import("./index-cd201bdb.js"),["./index-cd201bdb.js","./index-2519a27e.js","./index-29fa5a20.css","./index-93c91554.css"],import.meta.url)},slider:{static:()=>N(()=>import("./index-3c4a57e3.js"),["./index-3c4a57e3.js","./index-2519a27e.js","./index-29fa5a20.css","./Range-4159083f.js","./Button-748313a7.js","./Button-620848cb.css","./BlockTitle-de7b2d6e.js","./Info-02b862eb.js","./Range-49c152ed.css"],import.meta.url),interactive:()=>N(()=>import("./index-309055b4.js"),["./index-309055b4.js","./index-2519a27e.js","./index-29fa5a20.css","./Range-4159083f.js","./Button-748313a7.js","./Button-620848cb.css","./BlockTitle-de7b2d6e.js","./Info-02b862eb.js","./Range-49c152ed.css"],import.meta.url)},state:{static:()=>N(()=>import("./index-1b05448f.js"),["./index-1b05448f.js","./index-2519a27e.js","./index-29fa5a20.css"],import.meta.url)},statustracker:{static:()=>N(()=>import("./index-c3b089d1.js"),["./index-c3b089d1.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css"],import.meta.url)},tabs:{static:()=>N(()=>import("./index-30aeeca6.js"),["./index-30aeeca6.js","./StaticTabs-7f435bd9.js","./index-2519a27e.js","./index-29fa5a20.css","./StaticTabs-42a53876.css"],import.meta.url)},tabitem:{static:()=>N(()=>import("./index-6d795fa7.js"),["./index-6d795fa7.js","./index-2519a27e.js","./index-29fa5a20.css","./StaticTabs-7f435bd9.js","./StaticTabs-42a53876.css","./StaticColumn-6bd0cb0d.js","./StaticColumn-2853eb31.css","./index-d43fcb36.css"],import.meta.url)},textbox:{static:()=>N(()=>import("./index-cb99d206.js"),["./index-cb99d206.js","./index-2519a27e.js","./index-29fa5a20.css","./Textbox-c3e160c8.js","./Button-748313a7.js","./Button-620848cb.css","./BlockTitle-de7b2d6e.js","./Info-02b862eb.js","./Copy-754acc5f.js","./Textbox-dde6f8cc.css"],import.meta.url),interactive:()=>N(()=>import("./index-e90af093.js"),["./index-e90af093.js","./InteractiveTextbox-6c073f5e.js","./index-2519a27e.js","./index-29fa5a20.css","./Textbox-c3e160c8.js","./Button-748313a7.js","./Button-620848cb.css","./BlockTitle-de7b2d6e.js","./Info-02b862eb.js","./Copy-754acc5f.js","./Textbox-dde6f8cc.css"],import.meta.url)},timeseries:{static:()=>N(()=>import("./index-89782997.js"),["./index-89782997.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css","./BlockLabel-ddfceeb6.js","./Empty-23f73391.js","./Chart-afe4e9b4.js","./color-ba58c3cc.js","./csv-b0b7514a.js","./dsv-576afacd.js","./linear-bcbcf466.js","./Chart-02ddc6a9.css"],import.meta.url),interactive:()=>N(()=>import("./index-f6ce7697.js"),["./index-f6ce7697.js","./index-2519a27e.js","./index-29fa5a20.css","./Upload-b0a38490.js","./Button-748313a7.js","./Button-620848cb.css","./ModifyUpload-87a335b2.js","./IconButton-f5e53901.js","./BlockLabel-ddfceeb6.js","./Chart-afe4e9b4.js","./color-ba58c3cc.js","./csv-b0b7514a.js","./dsv-576afacd.js","./linear-bcbcf466.js","./Chart-02ddc6a9.css","./UploadText-87afcd1e.js","./index-69616613.css"],import.meta.url)},uploadbutton:{static:()=>N(()=>import("./index-f92b63b0.js"),["./index-f92b63b0.js","./index-2519a27e.js","./index-29fa5a20.css","./UploadButton-92a68e12.js","./Button-748313a7.js","./Button-620848cb.css","./UploadButton-03d58ab8.css"],import.meta.url),interactive:()=>N(()=>import("./index-c427f156.js"),["./index-c427f156.js","./index-2519a27e.js","./index-29fa5a20.css","./UploadButton-92a68e12.js","./Button-748313a7.js","./Button-620848cb.css","./UploadButton-03d58ab8.css"],import.meta.url)},video:{static:()=>N(()=>import("./index-35508349.js"),["./index-35508349.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css","./BlockLabel-ddfceeb6.js","./IconButton-f5e53901.js","./Empty-23f73391.js","./ShareButton-33bb4648.js","./utils-c3e3db58.js","./Download-43879c5c.js","./Player-ff6505f7.js","./Undo-6ef95523.js","./Player-1e00f554.css","./index-022688c7.css"],import.meta.url),interactive:()=>N(()=>import("./index-e85ad7fd.js"),["./index-e85ad7fd.js","./index-2519a27e.js","./index-29fa5a20.css","./Button-748313a7.js","./Button-620848cb.css","./UploadText-87afcd1e.js","./Upload-b0a38490.js","./ModifyUpload-87a335b2.js","./IconButton-f5e53901.js","./BlockLabel-ddfceeb6.js","./Player-ff6505f7.js","./Undo-6ef95523.js","./Player-1e00f554.css","./InteractiveImage-870bae3a.js","./Image-4f63353a.js","./utils-90f3612b.js","./InteractiveImage-b496c98d.css","./index-633cd86d.css"],import.meta.url)}};function $n(l){let e,t,n,i;return{c(){e=me("svg"),t=me("g"),n=me("path"),i=me("path"),u(n,"d","M3.789,0.09C3.903,-0.024 4.088,-0.024 4.202,0.09L4.817,0.705C4.931,0.819 4.931,1.004 4.817,1.118L1.118,4.817C1.004,4.931 0.819,4.931 0.705,4.817L0.09,4.202C-0.024,4.088 -0.024,3.903 0.09,3.789L3.789,0.09Z"),u(i,"d","M4.825,3.797C4.934,3.907 4.934,4.084 4.825,4.193L4.193,4.825C4.084,4.934 3.907,4.934 3.797,4.825L0.082,1.11C-0.027,1.001 -0.027,0.823 0.082,0.714L0.714,0.082C0.823,-0.027 1.001,-0.027 1.11,0.082L4.825,3.797Z"),u(e,"width","100%"),u(e,"height","100%"),u(e,"viewBox","0 0 5 5"),u(e,"version","1.1"),u(e,"xmlns","http://www.w3.org/2000/svg"),u(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),u(e,"xml:space","preserve"),ue(e,"fill","currentColor"),ue(e,"fill-rule","evenodd"),ue(e,"clip-rule","evenodd"),ue(e,"stroke-linejoin","round"),ue(e,"stroke-miterlimit","2")},m(o,s){k(o,e,s),p(e,t),p(t,n),p(t,i)},p:X,i:X,o:X,d(o){o&&b(e)}}}class zt extends re{constructor(e){super(),se(this,e,null,$n,ae,{})}}function xn(l){let e,t,n,i,o,s,r,a,_,c,f,m,h,j,A;return m=new zt({}),{c(){e=T("div"),t=T("h1"),t.textContent="API Docs",n=M(),i=T("p"),o=O(`No API Routes found for
		`),s=T("code"),r=O(l[0]),a=M(),_=T("p"),_.innerHTML=`To expose an API endpoint of your app in this page, set the <code>api_name</code>
		parameter of the event listener.
		<br/>
		For more information, visit the
		<a href="https://gradio.app/sharing_your_app/#api-page" target="_blank">API Page guide</a>
		. To hide the API documentation button and this page, set
		<code>show_api=False</code>
		in the
		<code>Blocks.launch()</code>
		method.`,c=M(),f=T("button"),G(m.$$.fragment),u(s,"class","svelte-e1ha0f"),u(i,"class","attention svelte-e1ha0f"),u(e,"class","wrap prose svelte-e1ha0f"),u(f,"class","svelte-e1ha0f")},m(y,g){k(y,e,g),p(e,t),p(e,n),p(e,i),p(i,o),p(i,s),p(s,r),p(e,a),p(e,_),k(y,c,g),k(y,f,g),W(m,f,null),h=!0,j||(A=pe(f,"click",l[2]),j=!0)},p(y,[g]){(!h||g&1)&&H(r,y[0])},i(y){h||(I(m.$$.fragment,y),h=!0)},o(y){C(m.$$.fragment,y),h=!1},d(y){y&&(b(e),b(c),b(f)),J(m),j=!1,A()}}}function el(l,e,t){const n=je();let{root:i}=e;const o=()=>n("close");return l.$$set=s=>{"root"in s&&t(0,i=s.root)},[i,n,o]}class tl extends re{constructor(e){super(),se(this,e,el,xn,ae,{root:0})}}function Te(l,e,t=null){return e===void 0?t==="py"?"None":null:e==="string"||e==="str"?t===null?l:'"'+l+'"':e==="number"?t===null?parseFloat(l):l:e==="boolean"||e=="bool"?t==="py"?(l=String(l),l==="true"?"True":"False"):t==="js"?l:l==="true":e==="List[str]"?(l=JSON.stringify(l),l):t===null?l===""?null:JSON.parse(l):typeof l=="string"?l===""?t==="py"?"None":"null":l:JSON.stringify(l)}const Mt=""+new URL("api-logo-5346f193.svg",import.meta.url).href;function Xe(l){let e;return{c(){e=O("s")},m(t,n){k(t,e,n)},d(t){t&&b(e)}}}function nl(l){let e,t,n,i,o,s,r,a,_,c,f,m,h,j,A,y,g,E,L,v=l[1]>1&&Xe();return y=new zt({}),{c(){e=T("h2"),t=T("img"),i=M(),o=T("div"),s=O(`API documentation
		`),r=T("div"),a=O(l[0]),_=M(),c=T("span"),f=T("span"),m=O(l[1]),h=O(" API endpoint"),v&&v.c(),j=M(),A=T("button"),G(y.$$.fragment),Ae(t.src,n=Mt)||u(t,"src",n),u(t,"alt",""),u(t,"class","svelte-3n2nxs"),u(r,"class","url svelte-3n2nxs"),u(f,"class","url svelte-3n2nxs"),u(c,"class","counts svelte-3n2nxs"),u(e,"class","svelte-3n2nxs"),u(A,"class","svelte-3n2nxs")},m(w,D){k(w,e,D),p(e,t),p(e,i),p(e,o),p(o,s),p(o,r),p(r,a),p(e,_),p(e,c),p(c,f),p(f,m),p(c,h),v&&v.m(c,null),k(w,j,D),k(w,A,D),W(y,A,null),g=!0,E||(L=pe(A,"click",l[3]),E=!0)},p(w,[D]){(!g||D&1)&&H(a,w[0]),(!g||D&2)&&H(m,w[1]),w[1]>1?v||(v=Xe(),v.c(),v.m(c,null)):v&&(v.d(1),v=null)},i(w){g||(I(y.$$.fragment,w),g=!0)},o(w){C(y.$$.fragment,w),g=!1},d(w){w&&(b(e),b(j),b(A)),v&&v.d(),J(y),E=!1,L()}}}function ll(l,e,t){let{root:n}=e,{api_count:i}=e;const o=je(),s=()=>o("close");return l.$$set=r=>{"root"in r&&t(0,n=r.root),"api_count"in r&&t(1,i=r.api_count)},[n,i,o,s]}class il extends re{constructor(e){super(),se(this,e,ll,nl,ae,{root:0,api_count:1})}}class Ye{#e;#t;constructor(e,t,n,i,o){this.#e=e,this.theme=n,this.version=i,this.#t=t,this.root=o}dispatch(e,t){const n=new CustomEvent("gradio",{bubbles:!0,detail:{data:t,id:this.#e,event:e}});this.#t.dispatchEvent(n)}}function $e(l,e,t){const n=l.slice();return n[4]=e[t].label,n[5]=e[t].type,n[6]=e[t].python_type,n[7]=e[t].component,n[8]=e[t].serializer,n[10]=t,n}function xe(l){let e;return{c(){e=O("(")},m(t,n){k(t,e,n)},d(t){t&&b(e)}}}function ol(l){let e=l[2][l[10]].type+"",t;return{c(){t=O(e)},m(n,i){k(n,t,i)},p(n,i){i&4&&e!==(e=n[2][n[10]].type+"")&&H(t,e)},d(n){n&&b(t)}}}function rl(l){let e=l[6].type+"",t;return{c(){t=O(e)},m(n,i){k(n,t,i)},p(n,i){i&2&&e!==(e=n[6].type+"")&&H(t,e)},d(n){n&&b(t)}}}function et(l){let e;return{c(){e=O(",")},m(t,n){k(t,e,n)},d(t){t&&b(e)}}}function tt(l){let e,t,n,i,o=l[4]+"",s,r,a=l[7]+"",_,c;function f(A,y){return A[3]==="python"?rl:ol}let m=f(l),h=m(l),j=l[1].length>1&&et();return{c(){e=T("div"),t=T("span"),n=O("# "),h.c(),i=O(`
						representing output in '`),s=O(o),r=O("' "),_=O(a),c=O(`
						component`),j&&j.c(),u(t,"class","desc svelte-1c7hj3i"),u(e,"class","svelte-1c7hj3i"),Le(e,"second-level",l[1].length>1)},m(A,y){k(A,e,y),p(e,t),p(t,n),h.m(t,null),p(t,i),p(t,s),p(t,r),p(t,_),p(t,c),j&&j.m(e,null)},p(A,y){m===(m=f(A))&&h?h.p(A,y):(h.d(1),h=m(A),h&&(h.c(),h.m(t,i))),y&2&&o!==(o=A[4]+"")&&H(s,o),y&2&&a!==(a=A[7]+"")&&H(_,a),A[1].length>1?j||(j=et(),j.c(),j.m(e,null)):j&&(j.d(1),j=null),y&2&&Le(e,"second-level",A[1].length>1)},d(A){A&&b(e),h.d(),j&&j.d()}}}function nt(l){let e;return{c(){e=O(")")},m(t,n){k(t,e,n)},d(t){t&&b(e)}}}function lt(l){let e,t,n;return t=new hn({props:{margin:!1}}),{c(){e=T("div"),G(t.$$.fragment),u(e,"class","load-wrap svelte-1c7hj3i")},m(i,o){k(i,e,o),W(t,e,null),n=!0},i(i){n||(I(t.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),n=!1},d(i){i&&b(e),J(t)}}}function sl(l){let e,t,n,i,o,s,r=l[1].length>1&&xe(),a=te(l[1]),_=[];for(let m=0;m<a.length;m+=1)_[m]=tt($e(l,a,m));let c=l[1].length>1&&nt(),f=l[0]&&lt();return{c(){e=T("div"),t=T("div"),r&&r.c(),n=M();for(let m=0;m<_.length;m+=1)_[m].c();i=M(),c&&c.c(),o=M(),f&&f.c(),u(t,"class","svelte-1c7hj3i"),Le(t,"hide",l[0]),u(e,"class","response-wrap svelte-1c7hj3i")},m(m,h){k(m,e,h),p(e,t),r&&r.m(t,null),p(t,n);for(let j=0;j<_.length;j+=1)_[j]&&_[j].m(t,null);p(t,i),c&&c.m(t,null),p(e,o),f&&f.m(e,null),s=!0},p(m,h){if(m[1].length>1?r||(r=xe(),r.c(),r.m(t,n)):r&&(r.d(1),r=null),h&14){a=te(m[1]);let j;for(j=0;j<a.length;j+=1){const A=$e(m,a,j);_[j]?_[j].p(A,h):(_[j]=tt(A),_[j].c(),_[j].m(t,i))}for(;j<_.length;j+=1)_[j].d(1);_.length=a.length}m[1].length>1?c||(c=nt(),c.c(),c.m(t,null)):c&&(c.d(1),c=null),(!s||h&1)&&Le(t,"hide",m[0]),m[0]?f?h&1&&I(f,1):(f=lt(),f.c(),I(f,1),f.m(e,null)):f&&(ne(),C(f,1,1,()=>{f=null}),le())},i(m){s||(I(f),s=!0)},o(m){C(f),s=!1},d(m){m&&b(e),r&&r.d(),he(_,m),c&&c.d(),f&&f.d()}}}function al(l){let e,t,n,i;return n=new Ve({props:{$$slots:{default:[sl]},$$scope:{ctx:l}}}),{c(){e=T("h4"),e.innerHTML=`<div class="toggle-icon svelte-1c7hj3i"><div class="toggle-dot svelte-1c7hj3i"></div></div>
	Return Type(s)`,t=M(),G(n.$$.fragment),u(e,"class","svelte-1c7hj3i")},m(o,s){k(o,e,s),k(o,t,s),W(n,o,s),i=!0},p(o,[s]){const r={};s&2063&&(r.$$scope={dirty:s,ctx:o}),n.$set(r)},i(o){i||(I(n.$$.fragment,o),i=!0)},o(o){C(n.$$.fragment,o),i=!1},d(o){o&&(b(e),b(t)),J(n,o)}}}function _l(l,e,t){let{is_running:n}=e,{endpoint_returns:i}=e,{js_returns:o}=e,{current_language:s}=e;return l.$$set=r=>{"is_running"in r&&t(0,n=r.is_running),"endpoint_returns"in r&&t(1,i=r.endpoint_returns),"js_returns"in r&&t(2,o=r.js_returns),"current_language"in r&&t(3,s=r.current_language)},[n,i,o,s]}class Bt extends re{constructor(e){super(),se(this,e,_l,al,ae,{is_running:0,endpoint_returns:1,js_returns:2,current_language:3})}}function cl(l){let e;return{c(){e=O(l[0])},m(t,n){k(t,e,n)},p(t,n){n&1&&H(e,t[0])},d(t){t&&b(e)}}}function fl(l){let e,t;return e=new Rn({props:{size:"sm",$$slots:{default:[cl]},$$scope:{ctx:l}}}),e.$on("click",l[1]),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},p(n,[i]){const o={};i&9&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function ul(l,e,t){let{code:n}=e,i="copy";function o(){navigator.clipboard.writeText(n),t(0,i="copied!"),setTimeout(()=>{t(0,i="copy")},1500)}return l.$$set=s=>{"code"in s&&t(2,n=s.code)},[i,o,n]}class Ie extends re{constructor(e){super(),se(this,e,ul,fl,ae,{code:2})}}function ml(l){let e,t,n,i,o,s;return t=new Ie({props:{code:ot}}),{c(){e=T("div"),G(t.$$.fragment),n=M(),i=T("div"),o=T("pre"),o.textContent=`$ ${ot}`,u(e,"class","copy svelte-hq8ezf"),u(o,"class","svelte-hq8ezf")},m(r,a){k(r,e,a),W(t,e,null),k(r,n,a),k(r,i,a),p(i,o),s=!0},p:X,i(r){s||(I(t.$$.fragment,r),s=!0)},o(r){C(t.$$.fragment,r),s=!1},d(r){r&&(b(e),b(n),b(i)),J(t)}}}function dl(l){let e,t,n,i,o,s;return t=new Ie({props:{code:it}}),{c(){e=T("div"),G(t.$$.fragment),n=M(),i=T("div"),o=T("pre"),o.textContent=`$ ${it}`,u(e,"class","copy svelte-hq8ezf"),u(o,"class","svelte-hq8ezf")},m(r,a){k(r,e,a),W(t,e,null),k(r,n,a),k(r,i,a),p(i,o),s=!0},p:X,i(r){s||(I(t.$$.fragment,r),s=!0)},o(r){C(t.$$.fragment,r),s=!1},d(r){r&&(b(e),b(n),b(i)),J(t)}}}function pl(l){let e,t,n,i;const o=[dl,ml],s=[];function r(a,_){return a[0]==="python"?0:a[0]==="javascript"?1:-1}return~(t=r(l))&&(n=s[t]=o[t](l)),{c(){e=T("code"),n&&n.c(),u(e,"class","svelte-hq8ezf")},m(a,_){k(a,e,_),~t&&s[t].m(e,null),i=!0},p(a,_){let c=t;t=r(a),t===c?~t&&s[t].p(a,_):(n&&(ne(),C(s[c],1,1,()=>{s[c]=null}),le()),~t?(n=s[t],n?n.p(a,_):(n=s[t]=o[t](a),n.c()),I(n,1),n.m(e,null)):n=null)},i(a){i||(I(n),i=!0)},o(a){C(n),i=!1},d(a){a&&b(e),~t&&s[t].d()}}}function gl(l){let e,t;return e=new Ve({props:{$$slots:{default:[pl]},$$scope:{ctx:l}}}),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},p(n,[i]){const o={};i&3&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}let it="pip install gradio_client",ot="npm i -D @gradio/client";function hl(l,e,t){let{current_language:n}=e;return l.$$set=i=>{"current_language"in i&&t(0,n=i.current_language)},[n]}class vl extends re{constructor(e){super(),se(this,e,hl,gl,ae,{current_language:0})}}function bl(l){let e,t,n,i;return{c(){e=T("h3"),t=O(`fn_index:
		`),n=T("span"),i=O(l[1]),u(n,"class","post svelte-41kcm6"),u(e,"class","svelte-41kcm6")},m(o,s){k(o,e,s),p(e,t),p(e,n),p(n,i)},p(o,s){s&2&&H(i,o[1])},d(o){o&&b(e)}}}function kl(l){let e,t,n,i="/"+l[0],o;return{c(){e=T("h3"),t=O(`api_name:
		`),n=T("span"),o=O(i),u(n,"class","post svelte-41kcm6"),u(e,"class","svelte-41kcm6")},m(s,r){k(s,e,r),p(e,t),p(e,n),p(n,o)},p(s,r){r&1&&i!==(i="/"+s[0])&&H(o,i)},d(s){s&&b(e)}}}function wl(l){let e;function t(o,s){return o[2]?kl:bl}let n=t(l),i=n(l);return{c(){i.c(),e=fe()},m(o,s){i.m(o,s),k(o,e,s)},p(o,[s]){n===(n=t(o))&&i?i.p(o,s):(i.d(1),i=n(o),i&&(i.c(),i.m(e.parentNode,e)))},i:X,o:X,d(o){o&&b(e),i.d(o)}}}function El(l,e,t){let{api_name:n=null}=e,{fn_index:i=null}=e,{named:o}=e;return l.$$set=s=>{"api_name"in s&&t(0,n=s.api_name),"fn_index"in s&&t(1,i=s.fn_index),"named"in s&&t(2,o=s.named)},[n,i,o]}class Ut extends re{constructor(e){super(),se(this,e,El,wl,ae,{api_name:0,fn_index:1,named:2})}}function rt(l,e,t){const n=l.slice();return n[14]=e[t].label,n[15]=e[t].type,n[16]=e[t].python_type,n[17]=e[t].component,n[18]=e[t].example_input,n[19]=e[t].serializer,n[21]=t,n}function st(l,e,t){const n=l.slice();return n[14]=e[t].label,n[15]=e[t].type,n[16]=e[t].python_type,n[17]=e[t].component,n[18]=e[t].example_input,n[19]=e[t].serializer,n[21]=t,n}function at(l,e,t){const n=l.slice();return n[14]=e[t].label,n[15]=e[t].type,n[16]=e[t].python_type,n[17]=e[t].component,n[18]=e[t].example_input,n[19]=e[t].serializer,n[21]=t,n}function yl(l){let e,t;return e=new Ut({props:{named:l[6],fn_index:l[1]}}),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},p(n,i){const o={};i&64&&(o.named=n[6]),i&2&&(o.fn_index=n[1]),e.$set(o)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function jl(l){let e,t;return e=new Ut({props:{named:l[6],api_name:l[0].api_name}}),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},p(n,i){const o={};i&64&&(o.named=n[6]),i&1&&(o.api_name=n[0].api_name),e.$set(o)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Al(l){let e,t,n,i,o,s,r,a,_,c,f,m,h,j,A;t=new Ie({props:{code:l[9]?.innerText}});let y=te(l[11]),g=[];for(let q=0;q<y.length;q+=1)g[q]=_t(st(l,y,q));function E(q,F){return q[6]?Tl:Ll}let L=E(l),v=L(l),w=te(l[4]),D=[];for(let q=0;q<w.length;q+=1)D[q]=ft(rt(l,w,q));return{c(){e=T("div"),G(t.$$.fragment),n=M(),i=T("div"),o=T("pre"),s=O(`import { client } from "@gradio/client";
`);for(let q=0;q<g.length;q+=1)g[q].c();r=O(`
const app = await client(`),a=T("span"),_=O('"'),c=O(l[2]),f=O('"'),m=O(`);
const result = await app.predict(`),v.c(),h=O(", [");for(let q=0;q<D.length;q+=1)D[q].c();j=O(`
	]);

console.log(result.data);
`),u(e,"class","copy svelte-1d98qmk"),u(a,"class","token string svelte-1d98qmk"),u(o,"class","svelte-1d98qmk")},m(q,F){k(q,e,F),W(t,e,null),k(q,n,F),k(q,i,F),p(i,o),p(o,s);for(let Q=0;Q<g.length;Q+=1)g[Q]&&g[Q].m(o,null);p(o,r),p(o,a),p(a,_),p(a,c),p(a,f),p(o,m),v.m(o,null),p(o,h);for(let Q=0;Q<D.length;Q+=1)D[Q]&&D[Q].m(o,null);p(o,j),l[13](i),A=!0},p(q,F){const Q={};if(F&512&&(Q.code=q[9]?.innerText),t.$set(Q),F&2048){y=te(q[11]);let V;for(V=0;V<y.length;V+=1){const _e=st(q,y,V);g[V]?g[V].p(_e,F):(g[V]=_t(_e),g[V].c(),g[V].m(o,r))}for(;V<g.length;V+=1)g[V].d(1);g.length=y.length}if((!A||F&4)&&H(c,q[2]),L===(L=E(q))&&v?v.p(q,F):(v.d(1),v=L(q),v&&(v.c(),v.m(o,h))),F&1072){w=te(q[4]);let V;for(V=0;V<w.length;V+=1){const _e=rt(q,w,V);D[V]?D[V].p(_e,F):(D[V]=ft(_e),D[V].c(),D[V].m(o,j))}for(;V<D.length;V+=1)D[V].d(1);D.length=w.length}},i(q){A||(I(t.$$.fragment,q),A=!0)},o(q){C(t.$$.fragment,q),A=!1},d(q){q&&(b(e),b(n),b(i)),J(t),he(g,q),v.d(),he(D,q),l[13](null)}}}function Ol(l){let e,t,n,i,o,s,r,a,_,c,f,m,h,j;t=new Ie({props:{code:l[8]?.innerText}});let A=te(l[4]),y=[];for(let v=0;v<A.length;v+=1)y[v]=dt(at(l,A,v));function g(v,w){return v[6]?Dl:Rl}let E=g(l),L=E(l);return{c(){e=T("div"),G(t.$$.fragment),n=M(),i=T("div"),o=T("pre"),s=O(`from gradio_client import Client

client = Client(`),r=T("span"),a=O('"'),_=O(l[2]),c=O('"'),f=O(`)
result = client.predict(`);for(let v=0;v<y.length;v+=1)y[v].c();m=O(`
				`),L.c(),h=O(`
)
print(result)`),u(e,"class","copy svelte-1d98qmk"),u(r,"class","token string svelte-1d98qmk"),u(o,"class","svelte-1d98qmk")},m(v,w){k(v,e,w),W(t,e,null),k(v,n,w),k(v,i,w),p(i,o),p(o,s),p(o,r),p(r,a),p(r,_),p(r,c),p(o,f);for(let D=0;D<y.length;D+=1)y[D]&&y[D].m(o,null);p(o,m),L.m(o,null),p(o,h),l[12](i),j=!0},p(v,w){const D={};if(w&256&&(D.code=v[8]?.innerText),t.$set(D),(!j||w&4)&&H(_,v[2]),w&26){A=te(v[4]);let q;for(q=0;q<A.length;q+=1){const F=at(v,A,q);y[q]?y[q].p(F,w):(y[q]=dt(F),y[q].c(),y[q].m(o,m))}for(;q<y.length;q+=1)y[q].d(1);y.length=A.length}E===(E=g(v))&&L?L.p(v,w):(L.d(1),L=E(v),L&&(L.c(),L.m(o,h)))},i(v){j||(I(t.$$.fragment,v),j=!0)},o(v){C(t.$$.fragment,v),j=!1},d(v){v&&(b(e),b(n),b(i)),J(t),he(y,v),L.d(),l[12](null)}}}function _t(l){let e,t,n,i=l[18]+"",o,s,r=l[17]+"",a,_,c,f;return{c(){e=O(`
const response_`),t=O(l[21]),n=O(' = await fetch("'),o=O(i),s=O(`");
const example`),a=O(r),_=O(" = await response_"),c=O(l[21]),f=O(`.blob();
						`)},m(m,h){k(m,e,h),k(m,t,h),k(m,n,h),k(m,o,h),k(m,s,h),k(m,a,h),k(m,_,h),k(m,c,h),k(m,f,h)},p:X,d(m){m&&(b(e),b(t),b(n),b(o),b(s),b(a),b(_),b(c),b(f))}}}function Ll(l){let e;return{c(){e=O(l[1])},m(t,n){k(t,e,n)},p(t,n){n&2&&H(e,t[1])},d(t){t&&b(e)}}}function Tl(l){let e,t=l[0].api_name+"",n,i;return{c(){e=O('"/'),n=O(t),i=O('"')},m(o,s){k(o,e,s),k(o,n,s),k(o,i,s)},p(o,s){s&1&&t!==(t=o[0].api_name+"")&&H(n,t)},d(o){o&&(b(e),b(n),b(i))}}}function Pl(l){let e,t,n=Te(l[18],l[16].type,"js")+"",i,o,s,r,a=l[5][l[21]].type+"",_,c,f,m=l[14]+"",h,j,A=l[17]+"",y,g,E=l[5][l[21]].description&&ct(l);return{c(){e=O(`		
				`),t=T("span"),i=O(n),o=O(", "),s=T("span"),r=O("// "),_=O(a),c=O(" "),E&&E.c(),f=O(" in '"),h=O(m),j=O("' "),y=O(A),g=O(" component"),u(t,"class","example-inputs svelte-1d98qmk"),u(s,"class","desc svelte-1d98qmk")},m(L,v){k(L,e,v),k(L,t,v),p(t,i),k(L,o,v),k(L,s,v),p(s,r),p(s,_),p(s,c),E&&E.m(s,null),p(s,f),p(s,h),p(s,j),p(s,y),p(s,g)},p(L,v){v&16&&n!==(n=Te(L[18],L[16].type,"js")+"")&&H(i,n),v&32&&a!==(a=L[5][L[21]].type+"")&&H(_,a),L[5][L[21]].description?E?E.p(L,v):(E=ct(L),E.c(),E.m(s,f)):E&&(E.d(1),E=null),v&16&&m!==(m=L[14]+"")&&H(h,m),v&16&&A!==(A=L[17]+"")&&H(y,A)},d(L){L&&(b(e),b(t),b(o),b(s)),E&&E.d()}}}function Il(l){let e,t,n,i=l[17]+"",o,s,r,a,_=l[14]+"",c,f,m=l[17]+"",h,j;return{c(){e=O(`
				`),t=T("span"),n=O("example"),o=O(i),s=O(", "),r=T("span"),a=O("	// blob in '"),c=O(_),f=O("' "),h=O(m),j=O(" component"),u(t,"class","example-inputs svelte-1d98qmk"),u(r,"class","desc svelte-1d98qmk")},m(A,y){k(A,e,y),k(A,t,y),p(t,n),p(t,o),k(A,s,y),k(A,r,y),p(r,a),p(r,c),p(r,f),p(r,h),p(r,j)},p(A,y){y&16&&i!==(i=A[17]+"")&&H(o,i),y&16&&_!==(_=A[14]+"")&&H(c,_),y&16&&m!==(m=A[17]+"")&&H(h,m)},d(A){A&&(b(e),b(t),b(s),b(r))}}}function ct(l){let e,t=l[5][l[21]].description+"",n,i;return{c(){e=O("("),n=O(t),i=O(")")},m(o,s){k(o,e,s),k(o,n,s),k(o,i,s)},p(o,s){s&32&&t!==(t=o[5][o[21]].description+"")&&H(n,t)},d(o){o&&(b(e),b(n),b(i))}}}function ft(l){let e,t;function n(s,r){return r&16&&(e=null),e==null&&(e=!!s[10].includes(s[17])),e?Il:Pl}let i=n(l,-1),o=i(l);return{c(){o.c(),t=fe()},m(s,r){o.m(s,r),k(s,t,r)},p(s,r){i===(i=n(s,r))&&o?o.p(s,r):(o.d(1),o=i(s),o&&(o.c(),o.m(t.parentNode,t)))},d(s){s&&b(t),o.d(s)}}}function ut(l){let e;return{c(){e=T("span"),e.textContent="ERROR",u(e,"class","error svelte-1d98qmk")},m(t,n){k(t,e,n)},d(t){t&&b(e)}}}function mt(l){let e,t=l[16].description+"",n,i;return{c(){e=O("("),n=O(t),i=O(`)
								`)},m(o,s){k(o,e,s),k(o,n,s),k(o,i,s)},p(o,s){s&16&&t!==(t=o[16].description+"")&&H(n,t)},d(o){o&&(b(e),b(n),b(i))}}}function dt(l){let e,t,n=Te(l[18],l[16].type,"py")+"",i,o,s,r,a=l[16].type+"",_,c,f,m=l[14]+"",h,j,A=l[17]+"",y,g,E=l[3][l[1]][l[21]]&&ut(),L=l[16].description&&mt(l);return{c(){e=O(`
				`),t=T("span"),i=O(n),o=O(","),E&&E.c(),s=T("span"),r=O("	# "),_=O(a),c=O(" "),L&&L.c(),f=O("in '"),h=O(m),j=O("' "),y=O(A),g=O(" component"),u(t,"class","example-inputs svelte-1d98qmk"),u(s,"class","desc svelte-1d98qmk")},m(v,w){k(v,e,w),k(v,t,w),p(t,i),k(v,o,w),E&&E.m(v,w),k(v,s,w),p(s,r),p(s,_),p(s,c),L&&L.m(s,null),p(s,f),p(s,h),p(s,j),p(s,y),p(s,g)},p(v,w){w&16&&n!==(n=Te(v[18],v[16].type,"py")+"")&&H(i,n),v[3][v[1]][v[21]]?E||(E=ut(),E.c(),E.m(s.parentNode,s)):E&&(E.d(1),E=null),w&16&&a!==(a=v[16].type+"")&&H(_,a),v[16].description?L?L.p(v,w):(L=mt(v),L.c(),L.m(s,f)):L&&(L.d(1),L=null),w&16&&m!==(m=v[14]+"")&&H(h,m),w&16&&A!==(A=v[17]+"")&&H(y,A)},d(v){v&&(b(e),b(t),b(o),b(s)),E&&E.d(v),L&&L.d()}}}function Rl(l){let e,t;return{c(){e=O("fn_index="),t=O(l[1])},m(n,i){k(n,e,i),k(n,t,i)},p(n,i){i&2&&H(t,n[1])},d(n){n&&(b(e),b(t))}}}function Dl(l){let e,t=l[0].api_name+"",n,i;return{c(){e=O('api_name="/'),n=O(t),i=O('"')},m(o,s){k(o,e,s),k(o,n,s),k(o,i,s)},p(o,s){s&1&&t!==(t=o[0].api_name+"")&&H(n,t)},d(o){o&&(b(e),b(n),b(i))}}}function Vl(l){let e,t,n,i;const o=[Ol,Al],s=[];function r(a,_){return a[7]==="python"?0:a[7]==="javascript"?1:-1}return~(t=r(l))&&(n=s[t]=o[t](l)),{c(){e=T("code"),n&&n.c(),u(e,"class","svelte-1d98qmk")},m(a,_){k(a,e,_),~t&&s[t].m(e,null),i=!0},p(a,_){let c=t;t=r(a),t===c?~t&&s[t].p(a,_):(n&&(ne(),C(s[c],1,1,()=>{s[c]=null}),le()),~t?(n=s[t],n?n.p(a,_):(n=s[t]=o[t](a),n.c()),I(n,1),n.m(e,null)):n=null)},i(a){i||(I(n),i=!0)},o(a){C(n),i=!1},d(a){a&&b(e),~t&&s[t].d()}}}function ql(l){let e,t,n,i,o,s;const r=[jl,yl],a=[];function _(c,f){return c[6]?0:1}return t=_(l),n=a[t]=r[t](l),o=new Ve({props:{$$slots:{default:[Vl]},$$scope:{ctx:l}}}),{c(){e=T("div"),n.c(),i=M(),G(o.$$.fragment),u(e,"class","container svelte-1d98qmk")},m(c,f){k(c,e,f),a[t].m(e,null),p(e,i),W(o,e,null),s=!0},p(c,[f]){let m=t;t=_(c),t===m?a[t].p(c,f):(ne(),C(a[m],1,1,()=>{a[m]=null}),le(),n=a[t],n?n.p(c,f):(n=a[t]=r[t](c),n.c()),I(n,1),n.m(e,i));const h={};f&16778239&&(h.$$scope={dirty:f,ctx:c}),o.$set(h)},i(c){s||(I(n),I(o.$$.fragment,c),s=!0)},o(c){C(n),C(o.$$.fragment,c),s=!1},d(c){c&&b(e),a[t].d(),J(o)}}}function Cl(l,e,t){let{dependency:n}=e,{dependency_index:i}=e,{root:o}=e,{dependency_failures:s}=e,{endpoint_parameters:r}=e,{js_parameters:a}=e,{named:_}=e,{current_language:c}=e,f,m,h=["Audio","File","Image","Video"],j=r.filter(g=>h.includes(g.component));function A(g){ye[g?"unshift":"push"](()=>{f=g,t(8,f)})}function y(g){ye[g?"unshift":"push"](()=>{m=g,t(9,m)})}return l.$$set=g=>{"dependency"in g&&t(0,n=g.dependency),"dependency_index"in g&&t(1,i=g.dependency_index),"root"in g&&t(2,o=g.root),"dependency_failures"in g&&t(3,s=g.dependency_failures),"endpoint_parameters"in g&&t(4,r=g.endpoint_parameters),"js_parameters"in g&&t(5,a=g.js_parameters),"named"in g&&t(6,_=g.named),"current_language"in g&&t(7,c=g.current_language)},[n,i,o,s,r,a,_,c,f,m,h,j,A,y]}class Ft extends re{constructor(e){super(),se(this,e,Cl,ql,ae,{dependency:0,dependency_index:1,root:2,dependency_failures:3,endpoint_parameters:4,js_parameters:5,named:6,current_language:7})}}const Nl=""+new URL("python-20e39c92.svg",import.meta.url).href,Sl=""+new URL("javascript-850cf94b.svg",import.meta.url).href;function pt(l,e,t){const n=l.slice();return n[18]=e[t],n[20]=t,n}function gt(l,e,t){const n=l.slice();return n[18]=e[t],n[20]=t,n}function ht(l,e,t){const n=l.slice();return n[22]=e[t][0],n[23]=e[t][1],n}function vt(l){let e,t,n,i,o;const s=[Ml,zl],r=[];function a(_,c){return c&32&&(e=null),e==null&&(e=!!(Object.keys(_[5].named_endpoints).length+Object.keys(_[5].unnamed_endpoints).length)),e?0:1}return t=a(l,-1),n=r[t]=s[t](l),{c(){n.c(),i=fe()},m(_,c){r[t].m(_,c),k(_,i,c),o=!0},p(_,c){let f=t;t=a(_,c),t===f?r[t].p(_,c):(ne(),C(r[f],1,1,()=>{r[f]=null}),le(),n=r[t],n?n.p(_,c):(n=r[t]=s[t](_),n.c()),I(n,1),n.m(i.parentNode,i))},i(_){o||(I(n),o=!0)},o(_){C(n),o=!1},d(_){_&&b(i),r[t].d(_)}}}function zl(l){let e,t;return e=new tl({props:{root:l[0]}}),e.$on("close",l[12]),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},p(n,i){const o={};i&1&&(o.root=n[0]),e.$set(o)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Ml(l){let e,t,n,i,o,s,r,a,_,c,f,m=Object.keys(l[5].named_endpoints).length,h,j,A=Object.keys(l[5].unnamed_endpoints).length,y,g;t=new il({props:{root:l[0],api_count:Object.keys(l[5].named_endpoints).length+Object.keys(l[5].unnamed_endpoints).length}}),t.$on("close",l[10]);let E=te(l[7]),L=[];for(let P=0;P<E.length;P+=1)L[P]=bt(ht(l,E,P));c=new vl({props:{current_language:l[2]}});let v=m&&kt(),w=te(l[1]),D=[];for(let P=0;P<w.length;P+=1)D[P]=Et(gt(l,w,P));const q=P=>C(D[P],1,1,()=>{D[P]=null});let F=A&&yt(),Q=te(l[1]),V=[];for(let P=0;P<Q.length;P+=1)V[P]=At(pt(l,Q,P));const _e=P=>C(V[P],1,1,()=>{V[P]=null});return{c(){e=T("div"),G(t.$$.fragment),n=M(),i=T("div"),o=T("div"),o.innerHTML=`<p>Use the <a href="https://gradio.app/docs/#python-client" target="_blank"><code class="library svelte-bdjvpc">gradio_client</code></a>
					Python library or the
					<a href="https://gradio.app/docs/#javascript-client" target="_blank"><code class="library svelte-bdjvpc">@gradio/client</code></a> Javascript package to query the demo via API.</p>`,s=M(),r=T("div"),a=T("div");for(let P=0;P<L.length;P+=1)L[P].c();_=M(),G(c.$$.fragment),f=M(),v&&v.c(),h=M();for(let P=0;P<D.length;P+=1)D[P].c();j=M(),F&&F.c(),y=M();for(let P=0;P<V.length;P+=1)V[P].c();u(e,"class","banner-wrap svelte-bdjvpc"),u(o,"class","client-doc svelte-bdjvpc"),u(a,"class","snippets svelte-bdjvpc"),u(r,"class","endpoint svelte-bdjvpc"),u(i,"class","docs-wrap svelte-bdjvpc")},m(P,U){k(P,e,U),W(t,e,null),k(P,n,U),k(P,i,U),p(i,o),p(i,s),p(i,r),p(r,a);for(let K=0;K<L.length;K+=1)L[K]&&L[K].m(a,null);p(r,_),W(c,r,null),p(r,f),v&&v.m(r,null),p(r,h);for(let K=0;K<D.length;K+=1)D[K]&&D[K].m(r,null);p(r,j),F&&F.m(r,null),p(r,y);for(let K=0;K<V.length;K+=1)V[K]&&V[K].m(r,null);g=!0},p(P,U){const K={};if(U&1&&(K.root=P[0]),U&32&&(K.api_count=Object.keys(P[5].named_endpoints).length+Object.keys(P[5].unnamed_endpoints).length),t.$set(K),U&132){E=te(P[7]);let R;for(R=0;R<E.length;R+=1){const Z=ht(P,E,R);L[R]?L[R].p(Z,U):(L[R]=bt(Z),L[R].c(),L[R].m(a,null))}for(;R<L.length;R+=1)L[R].d(1);L.length=E.length}const de={};if(U&4&&(de.current_language=P[2]),c.$set(de),U&32&&(m=Object.keys(P[5].named_endpoints).length),m?v||(v=kt(),v.c(),v.m(r,h)):v&&(v.d(1),v=null),U&127){w=te(P[1]);let R;for(R=0;R<w.length;R+=1){const Z=gt(P,w,R);D[R]?(D[R].p(Z,U),I(D[R],1)):(D[R]=Et(Z),D[R].c(),I(D[R],1),D[R].m(r,j))}for(ne(),R=w.length;R<D.length;R+=1)q(R);le()}if(U&32&&(A=Object.keys(P[5].unnamed_endpoints).length),A?F||(F=yt(),F.c(),F.m(r,y)):F&&(F.d(1),F=null),U&127){Q=te(P[1]);let R;for(R=0;R<Q.length;R+=1){const Z=pt(P,Q,R);V[R]?(V[R].p(Z,U),I(V[R],1)):(V[R]=At(Z),V[R].c(),I(V[R],1),V[R].m(r,null))}for(ne(),R=Q.length;R<V.length;R+=1)_e(R);le()}},i(P){if(!g){I(t.$$.fragment,P),I(c.$$.fragment,P);for(let U=0;U<w.length;U+=1)I(D[U]);for(let U=0;U<Q.length;U+=1)I(V[U]);g=!0}},o(P){C(t.$$.fragment,P),C(c.$$.fragment,P),D=D.filter(Boolean);for(let U=0;U<D.length;U+=1)C(D[U]);V=V.filter(Boolean);for(let U=0;U<V.length;U+=1)C(V[U]);g=!1},d(P){P&&(b(e),b(n),b(i)),J(t),he(L,P),J(c),v&&v.d(),he(D,P),F&&F.d(),he(V,P)}}}function bt(l){let e,t,n,i,o=l[22]+"",s,r,a,_,c;function f(){return l[11](l[22])}return{c(){e=T("li"),t=T("img"),i=M(),s=O(o),r=M(),Ae(t.src,n=l[23])||u(t,"src",n),u(t,"alt",""),u(t,"class","svelte-bdjvpc"),u(e,"class",a="snippet "+(l[2]===l[22]?"current-lang":"inactive-lang")+" svelte-bdjvpc")},m(m,h){k(m,e,h),p(e,t),p(e,i),p(e,s),p(e,r),_||(c=pe(e,"click",f),_=!0)},p(m,h){l=m,h&4&&a!==(a="snippet "+(l[2]===l[22]?"current-lang":"inactive-lang")+" svelte-bdjvpc")&&u(e,"class",a)},d(m){m&&b(e),_=!1,c()}}}function kt(l){let e;return{c(){e=T("h2"),e.textContent="Named Endpoints",u(e,"class","header svelte-bdjvpc")},m(t,n){k(t,e,n)},d(t){t&&b(e)}}}function wt(l){let e,t,n,i,o;return t=new Ft({props:{named:!0,endpoint_parameters:l[5].named_endpoints["/"+l[18].api_name].parameters,js_parameters:l[6].named_endpoints["/"+l[18].api_name].parameters,dependency:l[18],dependency_index:l[20],current_language:l[2],root:l[0],dependency_failures:l[4]}}),i=new Bt({props:{endpoint_returns:l[5].named_endpoints["/"+l[18].api_name].returns,js_returns:l[6].named_endpoints["/"+l[18].api_name].returns,is_running:l[3],current_language:l[2]}}),{c(){e=T("div"),G(t.$$.fragment),n=M(),G(i.$$.fragment),u(e,"class","endpoint-container svelte-bdjvpc")},m(s,r){k(s,e,r),W(t,e,null),p(e,n),W(i,e,null),o=!0},p(s,r){const a={};r&34&&(a.endpoint_parameters=s[5].named_endpoints["/"+s[18].api_name].parameters),r&66&&(a.js_parameters=s[6].named_endpoints["/"+s[18].api_name].parameters),r&2&&(a.dependency=s[18]),r&4&&(a.current_language=s[2]),r&1&&(a.root=s[0]),r&16&&(a.dependency_failures=s[4]),t.$set(a);const _={};r&34&&(_.endpoint_returns=s[5].named_endpoints["/"+s[18].api_name].returns),r&66&&(_.js_returns=s[6].named_endpoints["/"+s[18].api_name].returns),r&8&&(_.is_running=s[3]),r&4&&(_.current_language=s[2]),i.$set(_)},i(s){o||(I(t.$$.fragment,s),I(i.$$.fragment,s),o=!0)},o(s){C(t.$$.fragment,s),C(i.$$.fragment,s),o=!1},d(s){s&&b(e),J(t),J(i)}}}function Et(l){let e,t,n=l[18].api_name&&wt(l);return{c(){n&&n.c(),e=fe()},m(i,o){n&&n.m(i,o),k(i,e,o),t=!0},p(i,o){i[18].api_name?n?(n.p(i,o),o&2&&I(n,1)):(n=wt(i),n.c(),I(n,1),n.m(e.parentNode,e)):n&&(ne(),C(n,1,1,()=>{n=null}),le())},i(i){t||(I(n),t=!0)},o(i){C(n),t=!1},d(i){i&&b(e),n&&n.d(i)}}}function yt(l){let e;return{c(){e=T("h2"),e.textContent="Unnamed Endpoints",u(e,"class","header svelte-bdjvpc")},m(t,n){k(t,e,n)},d(t){t&&b(e)}}}function jt(l){let e,t,n,i,o,s;return t=new Ft({props:{named:!1,endpoint_parameters:l[5].unnamed_endpoints[l[20]].parameters,js_parameters:l[6].unnamed_endpoints[l[20]].parameters,dependency:l[18],dependency_index:l[20],current_language:l[2],root:l[0],dependency_failures:l[4]}}),i=new Bt({props:{endpoint_returns:l[5].unnamed_endpoints[l[20]].returns,js_returns:l[6].unnamed_endpoints[l[20]].returns,is_running:l[3],current_language:l[2]}}),{c(){e=T("div"),G(t.$$.fragment),n=M(),G(i.$$.fragment),o=M(),u(e,"class","endpoint-container svelte-bdjvpc")},m(r,a){k(r,e,a),W(t,e,null),p(e,n),W(i,e,null),p(e,o),s=!0},p(r,a){const _={};a&32&&(_.endpoint_parameters=r[5].unnamed_endpoints[r[20]].parameters),a&64&&(_.js_parameters=r[6].unnamed_endpoints[r[20]].parameters),a&2&&(_.dependency=r[18]),a&4&&(_.current_language=r[2]),a&1&&(_.root=r[0]),a&16&&(_.dependency_failures=r[4]),t.$set(_);const c={};a&32&&(c.endpoint_returns=r[5].unnamed_endpoints[r[20]].returns),a&64&&(c.js_returns=r[6].unnamed_endpoints[r[20]].returns),a&8&&(c.is_running=r[3]),a&4&&(c.current_language=r[2]),i.$set(c)},i(r){s||(I(t.$$.fragment,r),I(i.$$.fragment,r),s=!0)},o(r){C(t.$$.fragment,r),C(i.$$.fragment,r),s=!1},d(r){r&&b(e),J(t),J(i)}}}function At(l){let e,t,n=l[5].unnamed_endpoints[l[20]]&&jt(l);return{c(){n&&n.c(),e=fe()},m(i,o){n&&n.m(i,o),k(i,e,o),t=!0},p(i,o){i[5].unnamed_endpoints[i[20]]?n?(n.p(i,o),o&32&&I(n,1)):(n=jt(i),n.c(),I(n,1),n.m(e.parentNode,e)):n&&(ne(),C(n,1,1,()=>{n=null}),le())},i(i){t||(I(n),t=!0)},o(i){C(n),t=!1},d(i){i&&b(e),n&&n.d(i)}}}function Bl(l){let e,t,n=l[5]&&vt(l);return{c(){n&&n.c(),e=fe()},m(i,o){n&&n.m(i,o),k(i,e,o),t=!0},p(i,[o]){i[5]?n?(n.p(i,o),o&32&&I(n,1)):(n=vt(i),n.c(),I(n,1),n.m(e.parentNode,e)):n&&(ne(),C(n,1,1,()=>{n=null}),le())},i(i){t||(I(n),t=!0)},o(i){C(n),t=!1},d(i){i&&b(e),n&&n.d(i)}}}function Ul(l,e,t){let{instance_map:n}=e,{dependencies:i}=e,{root:o}=e,{app:s}=e;o===""&&(o=location.protocol+"//"+location.host+location.pathname),o.endsWith("/")||(o+="/");let r="python";const a=[["python",Nl],["javascript",Sl]];let _=!1;i.map(E=>E.inputs.map(L=>{let v=n[L].documentation?.example_data;return v===void 0?v="":typeof v=="object"&&(v=JSON.stringify(v)),v})),i.map(E=>new Array(E.outputs.length));let c=i.map(E=>new Array(E.inputs.length).fill(!1));async function f(){return await(await fetch(o+"info")).json()}async function m(){return await s.view_api()}let h,j;f().then(E=>t(5,h=E)),m().then(E=>t(6,j=E)),Pe(()=>(document.body.style.overflow="hidden","parentIFrame"in window&&window.parentIFrame?.scrollTo(0,0),()=>{document.body.style.overflow="auto"}));function A(E){ve.call(this,l,E)}const y=E=>t(2,r=E);function g(E){ve.call(this,l,E)}return l.$$set=E=>{"instance_map"in E&&t(8,n=E.instance_map),"dependencies"in E&&t(1,i=E.dependencies),"root"in E&&t(0,o=E.root),"app"in E&&t(9,s=E.app)},[o,i,r,_,c,h,j,a,n,s,A,y,g]}class Fl extends re{constructor(e){super(),se(this,e,Ul,Bl,ae,{instance_map:8,dependencies:1,root:0,app:9})}}function Ot(l,e,t){const n=l.slice();return n[9]=e[t].component,n[18]=e[t].id,n[2]=e[t].props,n[19]=e[t].children,n[20]=e[t].has_modes,n}function Lt(l){let e=[],t=new Map,n,i,o=te(l[1]);const s=r=>r[18];for(let r=0;r<o.length;r+=1){let a=Ot(l,o,r),_=s(a);t.set(_,e[r]=Tt(_,a))}return{c(){for(let r=0;r<e.length;r+=1)e[r].c();n=fe()},m(r,a){for(let _=0;_<e.length;_+=1)e[_]&&e[_].m(r,a);k(r,n,a),i=!0},p(r,a){a&235&&(o=te(r[1]),ne(),e=St(e,a,s,1,r,o,t,n.parentNode,yn,Tt,n,Ot),le())},i(r){if(!i){for(let a=0;a<o.length;a+=1)I(e[a]);i=!0}},o(r){for(let a=0;a<e.length;a+=1)C(e[a]);i=!1},d(r){r&&b(n);for(let a=0;a<e.length;a+=1)e[a].d(r)}}}function Tt(l,e){let t,n,i;return n=new Ht({props:{component:e[9],target:e[6],id:e[18],props:e[2],root:e[3],instance_map:e[0],children:e[19],dynamic_ids:e[5],has_modes:e[20],theme_mode:e[7]}}),n.$on("destroy",e[12]),n.$on("mount",e[13]),{key:l,first:null,c(){t=fe(),G(n.$$.fragment),this.first=t},m(o,s){k(o,t,s),W(n,o,s),i=!0},p(o,s){e=o;const r={};s&2&&(r.component=e[9]),s&64&&(r.target=e[6]),s&2&&(r.id=e[18]),s&2&&(r.props=e[2]),s&8&&(r.root=e[3]),s&1&&(r.instance_map=e[0]),s&2&&(r.children=e[19]),s&32&&(r.dynamic_ids=e[5]),s&2&&(r.has_modes=e[20]),s&128&&(r.theme_mode=e[7]),n.$set(r)},i(o){i||(I(n.$$.fragment,o),i=!0)},o(o){C(n.$$.fragment,o),i=!1},d(o){o&&b(t),J(n,o)}}}function Hl(l){let e,t,n=l[1]&&l[1].length&&Lt(l);return{c(){n&&n.c(),e=fe()},m(i,o){n&&n.m(i,o),k(i,e,o),t=!0},p(i,o){i[1]&&i[1].length?n?(n.p(i,o),o&2&&I(n,1)):(n=Lt(i),n.c(),I(n,1),n.m(e.parentNode,e)):n&&(ne(),C(n,1,1,()=>{n=null}),le())},i(i){t||(I(n),t=!0)},o(i){C(n),t=!1},d(i){i&&b(e),n&&n.d(i)}}}function Gl(l){let e,t,n,i;const o=[{elem_id:"elem_id"in l[2]&&l[2].elem_id||`component-${l[4]}`},{elem_classes:"elem_classes"in l[2]&&l[2].elem_classes||[]},{target:l[6]},l[2],{theme_mode:l[7]},{root:l[3]},{gradio:new Ye(l[4],l[6],l[7],l[8],l[3])}];function s(_){l[15](_)}var r=l[9];function a(_){let c={$$slots:{default:[Hl]},$$scope:{ctx:_}};for(let f=0;f<o.length;f+=1)c=En(c,o[f]);return _[0][_[4]].props.value!==void 0&&(c.value=_[0][_[4]].props.value),{props:c}}return r&&(e=He(r,a(l)),l[14](e),ye.push(()=>Ge(e,"value",s)),e.$on("prop_change",l[10])),{c(){e&&G(e.$$.fragment),n=fe()},m(_,c){e&&W(e,_,c),k(_,n,c),i=!0},p(_,[c]){const f=c&476?vn(o,[c&20&&{elem_id:"elem_id"in _[2]&&_[2].elem_id||`component-${_[4]}`},c&4&&{elem_classes:"elem_classes"in _[2]&&_[2].elem_classes||[]},c&64&&{target:_[6]},c&4&&bn(_[2]),c&128&&{theme_mode:_[7]},c&8&&{root:_[3]},c&472&&{gradio:new Ye(_[4],_[6],_[7],_[8],_[3])}]):{};if(c&8388843&&(f.$$scope={dirty:c,ctx:_}),!t&&c&17&&(t=!0,f.value=_[0][_[4]].props.value,kn(()=>t=!1)),c&512&&r!==(r=_[9])){if(e){ne();const m=e;C(m.$$.fragment,1,0,()=>{J(m,1)}),le()}r?(e=He(r,a(_)),_[14](e),ye.push(()=>Ge(e,"value",s)),e.$on("prop_change",_[10]),G(e.$$.fragment),I(e.$$.fragment,1),W(e,n.parentNode,n)):e=null}else r&&e.$set(f)},i(_){i||(e&&I(e.$$.fragment,_),i=!0)},o(_){e&&C(e.$$.fragment,_),i=!1},d(_){_&&b(n),l[14](null),e&&J(e,_)}}}function Wl(l,e,t){let{root:n}=e,{component:i}=e,{instance_map:o}=e,{id:s}=e,{props:r}=e,{children:a}=e,{dynamic_ids:_}=e,{parent:c=null}=e,{target:f}=e,{theme_mode:m}=e,{version:h}=e;const j=je();let A=[];Pe(()=>{j("mount",s);for(const w of A)j("mount",w.id);return()=>{j("destroy",s);for(const w of A)j("mount",w.id)}}),wn("BLOCK_KEY",c);function y(w){for(const D in w.detail)t(0,o[s].props[D]=w.detail[D],o)}function g(w){ve.call(this,l,w)}function E(w){ve.call(this,l,w)}function L(w){ye[w?"unshift":"push"](()=>{o[s].instance=w,t(0,o)})}function v(w){l.$$.not_equal(o[s].props.value,w)&&(o[s].props.value=w,t(0,o))}return l.$$set=w=>{"root"in w&&t(3,n=w.root),"component"in w&&t(9,i=w.component),"instance_map"in w&&t(0,o=w.instance_map),"id"in w&&t(4,s=w.id),"props"in w&&t(2,r=w.props),"children"in w&&t(1,a=w.children),"dynamic_ids"in w&&t(5,_=w.dynamic_ids),"parent"in w&&t(11,c=w.parent),"target"in w&&t(6,f=w.target),"theme_mode"in w&&t(7,m=w.theme_mode),"version"in w&&t(8,h=w.version)},l.$$.update=()=>{l.$$.dirty&3&&t(1,a=a&&a.filter(w=>{const D=o[w.id].type!=="statustracker";return D||A.push(w),D})),l.$$.dirty&19&&o[s].type==="form"&&(a?.every(w=>!w.props.visible)?t(2,r.visible=!1,r):t(2,r.visible=!0,r))},[o,a,r,n,s,_,f,m,h,i,y,c,g,E,L,v]}class Ht extends re{constructor(e){super(),se(this,e,Wl,Gl,ae,{root:3,component:9,instance_map:0,id:4,props:2,children:1,dynamic_ids:5,parent:11,target:6,theme_mode:7,version:8})}}function Jl(l){let e,t;return e=new Ht({props:{component:l[0].component,id:l[0].id,props:l[0].props,children:l[0].children,dynamic_ids:l[1],instance_map:l[2],root:l[3],target:l[4],theme_mode:l[5],version:l[6]}}),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},p(n,[i]){const o={};i&1&&(o.component=n[0].component),i&1&&(o.id=n[0].id),i&1&&(o.props=n[0].props),i&1&&(o.children=n[0].children),i&2&&(o.dynamic_ids=n[1]),i&4&&(o.instance_map=n[2]),i&8&&(o.root=n[3]),i&16&&(o.target=n[4]),i&32&&(o.theme_mode=n[5]),i&64&&(o.version=n[6]),e.$set(o)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Ql(l,e,t){let{rootNode:n}=e,{dynamic_ids:i}=e,{instance_map:o}=e,{root:s}=e,{target:r}=e,{theme_mode:a}=e,{version:_}=e;const c=je();return Pe(()=>{c("mount")}),l.$$set=f=>{"rootNode"in f&&t(0,n=f.rootNode),"dynamic_ids"in f&&t(1,i=f.dynamic_ids),"instance_map"in f&&t(2,o=f.instance_map),"root"in f&&t(3,s=f.root),"target"in f&&t(4,r=f.target),"theme_mode"in f&&t(5,a=f.theme_mode),"version"in f&&t(6,_=f.version)},[n,i,o,s,r,a,_]}class Kl extends re{constructor(e){super(),se(this,e,Ql,Jl,ae,{rootNode:0,dynamic_ids:1,instance_map:2,root:3,target:4,theme_mode:5,version:6})}}const Zl=""+new URL("logo-0a070fcf.svg",import.meta.url).href;const{document:Ee}=Tn;function Pt(l){return Ee.title=l[3],{c:X,m:X,d:X}}function It(l){let e,t,n,i;return{c(){e=T("script"),e.innerHTML="",n=M(),i=T("script"),i.textContent=`window.dataLayer = window.dataLayer || [];
			function gtag() {
				dataLayer.push(arguments);
			}
			gtag("js", new Date());
			gtag("config", "UA-156449732-1");`,e.async=!0,e.defer=!0,Ae(e.src,t="https://www.googletagmanager.com/gtag/js?id=UA-156449732-1")||u(e,"src",t)},m(o,s){k(o,e,s),k(o,n,s),k(o,i,s)},d(o){o&&(b(e),b(n),b(i))}}}function Rt(l){let e,t;return e=new Kl({props:{rootNode:l[13],dynamic_ids:l[19],instance_map:l[20],root:l[1],target:l[5],theme_mode:l[10],version:l[12]}}),e.$on("mount",l[22]),e.$on("destroy",l[30]),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},p(n,i){const o={};i[0]&8192&&(o.rootNode=n[13]),i[0]&2&&(o.root=n[1]),i[0]&32&&(o.target=n[5]),i[0]&1024&&(o.theme_mode=n[10]),i[0]&4096&&(o.version=n[12]),e.$set(o)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Dt(l){let e,t,n,i=l[16]("common.built_with_gradio")+"",o,s,r,a,_,c=l[6]&&Vt(l);return{c(){e=T("footer"),c&&c.c(),t=M(),n=T("a"),o=O(i),s=M(),r=T("img"),Ae(r.src,a=Zl)||u(r,"src",a),u(r,"alt",_=l[16]("common.logo")),u(r,"class","svelte-1ax1toq"),u(n,"href","https://gradio.app"),u(n,"class","built-with svelte-1ax1toq"),u(n,"target","_blank"),u(n,"rel","noreferrer"),u(e,"class","svelte-1ax1toq")},m(f,m){k(f,e,m),c&&c.m(e,null),p(e,t),p(e,n),p(n,o),p(n,s),p(n,r)},p(f,m){f[6]?c?c.p(f,m):(c=Vt(f),c.c(),c.m(e,t)):c&&(c.d(1),c=null),m[0]&65536&&i!==(i=f[16]("common.built_with_gradio")+"")&&H(o,i),m[0]&65536&&_!==(_=f[16]("common.logo"))&&u(r,"alt",_)},d(f){f&&b(e),c&&c.d()}}}function Vt(l){let e,t=l[16]("errors.use_via_api")+"",n,i,o,s,r,a,_,c,f;return{c(){e=T("button"),n=O(t),i=M(),o=T("img"),a=M(),_=T("div"),_.textContent="·",Ae(o.src,s=Mt)||u(o,"src",s),u(o,"alt",r=l[16]("common.logo")),u(o,"class","svelte-1ax1toq"),u(e,"class","show-api svelte-1ax1toq"),u(_,"class","svelte-1ax1toq")},m(m,h){k(m,e,h),p(e,n),p(e,i),p(e,o),k(m,a,h),k(m,_,h),c||(f=pe(e,"click",l[31]),c=!0)},p(m,h){h[0]&65536&&t!==(t=m[16]("errors.use_via_api")+"")&&H(n,t),h[0]&65536&&r!==(r=m[16]("common.logo"))&&u(o,"alt",r)},d(m){m&&(b(e),b(a),b(_)),c=!1,f()}}}function qt(l){let e,t,n,i,o,s,r,a;return o=new Fl({props:{instance_map:l[20],dependencies:l[2],root:l[1],app:l[11]}}),o.$on("close",l[33]),{c(){e=T("div"),t=T("div"),n=M(),i=T("div"),G(o.$$.fragment),u(t,"class","backdrop svelte-1ax1toq"),u(i,"class","api-docs-wrap svelte-1ax1toq"),u(e,"class","api-docs svelte-1ax1toq")},m(_,c){k(_,e,c),p(e,t),p(e,n),p(e,i),W(o,i,null),s=!0,r||(a=pe(t,"click",l[32]),r=!0)},p(_,c){const f={};c[0]&4&&(f.dependencies=_[2]),c[0]&2&&(f.root=_[1]),c[0]&2048&&(f.app=_[11]),o.$set(f)},i(_){s||(I(o.$$.fragment,_),s=!0)},o(_){C(o.$$.fragment,_),s=!1},d(_){_&&b(e),J(o),r=!1,a()}}}function Ct(l){let e,t;return e=new Yn({props:{messages:l[15]}}),e.$on("close",l[21]),{c(){G(e.$$.fragment)},m(n,i){W(e,n,i),t=!0},p(n,i){const o={};i[0]&32768&&(o.messages=n[15]),e.$set(o)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Xl(l){let e,t,n,i,o,s,r,a,_,c,f=l[8]&&Pt(l),m=l[4]&&It(),h=l[0]&&Rt(l),j=l[7]&&Dt(l),A=l[14]&&l[0]&&qt(l),y=l[15]&&Ct(l);return{c(){f&&f.c(),e=fe(),m&&m.c(),t=fe(),n=M(),i=T("div"),o=T("div"),h&&h.c(),s=M(),j&&j.c(),r=M(),A&&A.c(),a=M(),y&&y.c(),_=fe(),u(o,"class","contain"),ue(o,"flex-grow",l[9]?"1":"auto"),u(i,"class","wrap svelte-1ax1toq"),ue(i,"min-height",l[9]?"100%":"auto")},m(g,E){f&&f.m(Ee.head,null),p(Ee.head,e),m&&m.m(Ee.head,null),p(Ee.head,t),k(g,n,E),k(g,i,E),p(i,o),h&&h.m(o,null),p(i,s),j&&j.m(i,null),k(g,r,E),A&&A.m(g,E),k(g,a,E),y&&y.m(g,E),k(g,_,E),c=!0},p(g,E){g[8]?f||(f=Pt(g),f.c(),f.m(e.parentNode,e)):f&&(f.d(1),f=null),g[4]?m||(m=It(),m.c(),m.m(t.parentNode,t)):m&&(m.d(1),m=null),g[0]?h?(h.p(g,E),E[0]&1&&I(h,1)):(h=Rt(g),h.c(),I(h,1),h.m(o,null)):h&&(ne(),C(h,1,1,()=>{h=null}),le()),E[0]&512&&ue(o,"flex-grow",g[9]?"1":"auto"),g[7]?j?j.p(g,E):(j=Dt(g),j.c(),j.m(i,null)):j&&(j.d(1),j=null),E[0]&512&&ue(i,"min-height",g[9]?"100%":"auto"),g[14]&&g[0]?A?(A.p(g,E),E[0]&16385&&I(A,1)):(A=qt(g),A.c(),I(A,1),A.m(a.parentNode,a)):A&&(ne(),C(A,1,1,()=>{A=null}),le()),g[15]?y?(y.p(g,E),E[0]&32768&&I(y,1)):(y=Ct(g),y.c(),I(y,1),y.m(_.parentNode,_)):y&&(ne(),C(y,1,1,()=>{y=null}),le())},i(g){c||(I(h),I(A),I(y),c=!0)},o(g){C(h),C(A),C(y),c=!1},d(g){g&&(b(n),b(i),b(r),b(a),b(_)),f&&f.d(g),b(e),m&&m.d(g),b(t),h&&h.d(),j&&j.d(),A&&A.d(g),y&&y.d(g)}}}const Yl=/^'([^]+)'$/,$l=15,xl=10;function Nt(l,e,t){for(const n of t)for(const i of n[e])if(i===l)return!0;return!1}function ei(l){return Array.isArray(l)&&l.length===0||l===""||l===0||!l}function ti(l){return"detail"in l}function ni(l,e,t){let n,i;We(l,jn,d=>t(16,i=d)),An();let{root:o}=e,{components:s}=e,{layout:r}=e,{dependencies:a}=e,{title:_="Gradio"}=e,{analytics_enabled:c=!1}=e,{target:f}=e,{autoscroll:m}=e,{show_api:h=!0}=e,{show_footer:j=!0}=e,{control_page_title:A=!1}=e,{app_mode:y}=e,{theme_mode:g}=e,{app:E}=e,{space_id:L}=e,{version:v}=e,w=On();We(l,w,d=>t(29,n=d));let D={id:r.id,type:"column",props:{mode:"static"},has_modes:!1,instance:{},component:{}};s.push(D);const q=Object.getPrototypeOf(async function(){}).constructor;a.forEach(d=>{if(d.js){const z=d.backend_fn?d.inputs.length===1:d.outputs.length===1;try{d.frontend_fn=new q("__fn_args",`let result = await (${d.js})(...__fn_args);
					return (${z} && !Array.isArray(result)) ? [result] : result;`)}catch(S){console.error("Could not parse custom js method."),console.error(S)}}});let Q=new URLSearchParams(window.location.search).get("view")==="api"&&h;function V(d){t(14,Q=d);let z=new URLSearchParams(window.location.search);d?z.set("view","api"):z.delete("view"),history.replaceState(null,"","?"+z.toString())}const _e=new Set;for(const d of s){const{id:z,props:S}=d;(Nt(z,"inputs",a)||!Nt(z,"outputs",a)&&ei(S?.value))&&_e.add(z)}let P=s.reduce((d,z)=>(d[z.id]=z,d),{});async function U(d,z){try{const S=await Ze[d][z]();return{name:d,component:S}}catch(S){if(z==="interactive")try{const B=await Ze[d].static();return{name:d,component:B}}catch(B){throw console.error(`failed to load: ${d}`),console.error(B),B}else throw console.error(`failed to load: ${d}`),console.error(S),S}}const K=new Set,de=new Map,R=new Map;async function Z(d){let z=P[d.id];const S=(await de.get(`${z.type}_${R.get(d.id)||"static"}`)).component;z.component=S.default,d.children&&(z.children=d.children.map(B=>P[B.id]),await Promise.all(d.children.map(B=>Z(B))))}s.forEach(d=>{d.props.interactive===!1?d.props.mode="static":d.props.interactive===!0||_e.has(d.id)?d.props.mode="interactive":d.props.mode="static",R.set(d.id,d.props.mode);const z=U(d.type,d.props.mode);K.add(z),de.set(`${d.type}_${d.props.mode}`,z)});let{ready:ge=!1}=e,{render_complete:Re=!1}=e;Promise.all(Array.from(K)).then(()=>{Z(r).then(async()=>{t(0,ge=!0)}).catch(d=>{console.error(d)})});async function Gt(d,z){let S=z==="dynamic"?"interactive":z;if(d.props.mode===S)return;d.props.mode=S;const B=U(d.type,d.props.mode);K.add(B),de.set(`${d.type}_${d.props.mode}`,B),B.then(ee=>{d.component=ee.component.default,t(13,D)})}function qe(d,z){const S=a[z].outputs;d?.forEach((B,ee)=>{const ce=P[S[ee]];if(ce.props.value_is_output=!0,typeof B=="object"&&B!==null&&B.__type__==="update")for(const[x,Y]of Object.entries(B))x!=="__type__"&&(x==="mode"&&Gt(ce,Y),ce.props[x]=Y);else ce.props.value=B}),t(13,D)}let Ce=new Map;function Ne(d,z,S){d?.props||(d.props={}),d.props[z]=S,t(13,D)}let Se=[],ie=[];function be(d,z,S){return{message:d,fn_index:z,type:S,id:++Wt}}let Wt=-1,De=!1;document.addEventListener("visibilitychange",function(){document.visibilityState==="hidden"&&(De=!0)});const Jt=i("blocks.long_requests_queue"),Qt=i("blocks.connection_can_break"),Kt=i("blocks.lost_connection"),ze=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);let Me=!1,Be=!1;async function we(d,z=null){let S=a[d];const B=w.get_status_for_fn(d);if(t(15,ie=ie.filter(({fn_index:x})=>x!==d)),S.cancels&&await Promise.all(S.cancels.map(async x=>{const Y=Ce.get(x);return Y?.cancel(),Y})),B==="pending"||B==="generating")return;let ee={fn_index:d,data:S.inputs.map(x=>P[x].props.value),event_data:S.collects_event_data?z:null};S.frontend_fn?S.frontend_fn(ee.data.concat(S.outputs.map(x=>P[x].props.value))).then(x=>{S.backend_fn?(ee.data=x,ce()):qe(x,d)}):S.backend_fn&&ce();function ce(){const x=E.submit(ee.fn_index,ee.data,ee.event_data).on("data",({data:Y,fn_index:$})=>{qe(Y,$)}).on("status",({fn_index:Y,...$})=>{if(w.update({...$,status:$.stage,progress:$.progress_data,fn_index:Y}),!Me&&L!==null&&$.position!==void 0&&$.position>=2&&$.eta!==void 0&&$.eta>$l&&(Me=!0,t(15,ie=[be(Jt,Y,"warning"),...ie])),!Be&&ze&&$.eta!==void 0&&$.eta>xl&&(Be=!0,t(15,ie=[be(Qt,Y,"warning"),...ie])),$.stage==="complete"&&(a.map(async(oe,Oe)=>{oe.trigger_after===Y&&we(Oe)}),x.destroy()),$.broken&&ze&&De)window.setTimeout(()=>{t(15,ie=[be(Kt,Y,"error"),...ie])},0),we(d,z),De=!1;else if($.stage==="error"){if($.message){const oe=$.message.replace(Yl,(Oe,on)=>on);t(15,ie=[be(oe,Y,"error"),...ie])}a.map(async(oe,Oe)=>{oe.trigger_after===Y&&!oe.trigger_only_on_success&&we(Oe)}),x.destroy()}}).on("log",({log:Y,fn_index:$,level:oe})=>{t(15,ie=[be(Y,$,oe),...ie])});Ce.set(d,x)}}function Zt(d,z){if(L===null)return;const S=new URL(`https://huggingface.co/spaces/${L}/discussions/new`);d!==void 0&&d.length>0&&S.searchParams.set("title",d),S.searchParams.set("description",z),window.open(S.toString(),"_blank")}function Xt(d){const z=d.detail;t(15,ie=ie.filter(S=>S.id!==z))}const Yt=d=>!!(d&&new URL(d,location.href).origin!==location.origin);async function $t(){await Pn();for(var d=f.getElementsByTagName("a"),z=0;z<d.length;z++){const S=d[z].getAttribute("target"),B=d[z].getAttribute("href");Yt(B)&&S!=="_blank"&&d[z].setAttribute("target","_blank")}a.forEach((S,B)=>{S.targets.length===0&&S.trigger==="load"&&we(B)}),a.forEach((S,B)=>{let{targets:ee,trigger:ce,inputs:x,outputs:Y}=S;ee.map(oe=>[oe,P[oe]]).forEach(([oe])=>{ke[oe]||(ke[oe]={}),ke[oe]?.[ce]?ke[oe][ce].push(B):ke[oe][ce]=[B]})}),f.addEventListener("gradio",S=>{if(!ti(S))throw new Error("not a custom event");const{id:B,event:ee,data:ce}=S.detail;if(ee==="share"){const{title:Y,description:$}=ce;Zt(Y,$)}else ee==="error"&&t(15,ie=[be(ce,-1,"error"),...ie]);ke[B]?.[ee]?.forEach(Y=>{we(Y,ce)})}),t(24,Re=!0)}function Ue(d){Se=Se.map(z=>z.filter(S=>S!==d))}a.forEach((d,z)=>{w.register(z,d.inputs,d.outputs)});function xt(d){for(const S in d){let B=d[S],ee=a[B.fn_index];B.scroll_to_output=ee.scroll_to_output,B.show_progress=ee.show_progress,Ne(P[S],"loading_status",B)}const z=w.get_inputs_to_update();for(const[S,B]of z)Ne(P[S],"pending",B==="pending")}const ke={},en=({detail:d})=>Ue(d),tn=()=>{V(!Q)},nn=()=>{V(!1)},ln=()=>{V(!1)};return l.$$set=d=>{"root"in d&&t(1,o=d.root),"components"in d&&t(25,s=d.components),"layout"in d&&t(26,r=d.layout),"dependencies"in d&&t(2,a=d.dependencies),"title"in d&&t(3,_=d.title),"analytics_enabled"in d&&t(4,c=d.analytics_enabled),"target"in d&&t(5,f=d.target),"autoscroll"in d&&t(27,m=d.autoscroll),"show_api"in d&&t(6,h=d.show_api),"show_footer"in d&&t(7,j=d.show_footer),"control_page_title"in d&&t(8,A=d.control_page_title),"app_mode"in d&&t(9,y=d.app_mode),"theme_mode"in d&&t(10,g=d.theme_mode),"app"in d&&t(11,E=d.app),"space_id"in d&&t(28,L=d.space_id),"version"in d&&t(12,v=d.version),"ready"in d&&t(0,ge=d.ready),"render_complete"in d&&t(24,Re=d.render_complete)},l.$$.update=()=>{l.$$.dirty[0]&134217728&&Ln.update(d=>({...d,autoscroll:m})),l.$$.dirty[0]&536870912&&xt(n)},[ge,o,a,_,c,f,h,j,A,y,g,E,v,D,Q,ie,i,w,V,_e,P,Xt,$t,Ue,Re,s,r,m,L,n,en,tn,nn,ln]}class li extends re{constructor(e){super(),se(this,e,ni,Xl,ae,{root:1,components:25,layout:26,dependencies:2,title:3,analytics_enabled:4,target:5,autoscroll:27,show_api:6,show_footer:7,control_page_title:8,app_mode:9,theme_mode:10,app:11,space_id:28,version:12,ready:0,render_complete:24},null,[-1,-1])}}const si=Object.freeze(Object.defineProperty({__proto__:null,default:li},Symbol.toStringTag,{value:"Module"}));export{si as B,Yn as T};
//# sourceMappingURL=Blocks-b3881047.js.map
