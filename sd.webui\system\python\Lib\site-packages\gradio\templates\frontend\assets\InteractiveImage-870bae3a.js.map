{"version": 3, "file": "InteractiveImage-870bae3a.js", "sources": ["../../../../js/icons/src/Brush.svelte", "../../../../js/icons/src/Camera.svelte", "../../../../js/icons/src/Circle.svelte", "../../../../js/icons/src/Color.svelte", "../../../../js/icons/src/Erase.svelte", "../../../../js/icons/src/Sketch.svelte", "../../../../js/icons/src/Square.svelte", "../../../../js/image/interactive/Webcam.svelte", "../../../../node_modules/.pnpm/cropperjs@1.5.12/node_modules/cropperjs/dist/cropper.esm.js", "../../../../js/image/interactive/Cropper.svelte", "../../../../node_modules/.pnpm/lazy-brush@1.0.1/node_modules/lazy-brush/src/Point.js", "../../../../node_modules/.pnpm/lazy-brush@1.0.1/node_modules/lazy-brush/src/LazyPoint.js", "../../../../node_modules/.pnpm/lazy-brush@1.0.1/node_modules/lazy-brush/src/LazyBrush.js", "../../../../node_modules/.pnpm/resize-observer-polyfill@1.5.1/node_modules/resize-observer-polyfill/dist/ResizeObserver.es.js", "../../../../js/image/interactive/Sketch.svelte", "../../../../js/image/interactive/ModifySketch.svelte", "../../../../js/image/interactive/SketchSettings.svelte", "../../../../js/image/interactive/Image.svelte", "../../../../js/image/interactive/InteractiveImage.svelte"], "sourcesContent": ["<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 32 32\">\n\t<path\n\t\td=\"M28.828 3.172a4.094 4.094 0 0 0-5.656 0L4.05 22.292A6.954 6.954 0 0 0 2 27.242V30h2.756a6.952 6.952 0 0 0 4.95-2.05L28.828 8.829a3.999 3.999 0 0 0 0-5.657zM10.91 18.26l2.829 2.829l-2.122 2.121l-2.828-2.828zm-2.619 8.276A4.966 4.966 0 0 1 4.756 28H4v-.759a4.967 4.967 0 0 1 1.464-3.535l1.91-1.91l2.829 2.828zM27.415 7.414l-12.261 12.26l-2.829-2.828l12.262-12.26a2.047 2.047 0 0 1 2.828 0a2 2 0 0 1 0 2.828z\"\n\t\tfill=\"currentColor\"\n\t/>\n\t<path\n\t\td=\"M6.5 15a3.5 3.5 0 0 1-2.475-5.974l3.5-3.5a1.502 1.502 0 0 0 0-2.121a1.537 1.537 0 0 0-2.121 0L3.415 5.394L2 3.98l1.99-1.988a3.585 3.585 0 0 1 4.95 0a3.504 3.504 0 0 1 0 4.949L5.439 10.44a1.502 1.502 0 0 0 0 2.121a1.537 1.537 0 0 0 2.122 0l4.024-4.024L13 9.95l-4.025 4.024A3.475 3.475 0 0 1 6.5 15z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-camera\"\n>\n\t<path\n\t\td=\"M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z\"\n\t/>\n\t<circle cx=\"12\" cy=\"13\" r=\"4\" />\n</svg>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"red\"\n\tstroke=\"red\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-circle\"\n>\n\t<circle cx=\"12\" cy=\"12\" r=\"10\" />\n</svg>\n", "<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 32 32\">\n\t<circle cx=\"10\" cy=\"12\" r=\"2\" fill=\"currentColor\" />\n\t<circle cx=\"16\" cy=\"9\" r=\"2\" fill=\"currentColor\" />\n\t<circle cx=\"22\" cy=\"12\" r=\"2\" fill=\"currentColor\" />\n\t<circle cx=\"23\" cy=\"18\" r=\"2\" fill=\"currentColor\" />\n\t<circle cx=\"19\" cy=\"23\" r=\"2\" fill=\"currentColor\" />\n\t<path\n\t\tfill=\"currentColor\"\n\t\td=\"M16.54 2A14 14 0 0 0 2 16a4.82 4.82 0 0 0 6.09 4.65l1.12-.31a3 3 0 0 1 3.79 2.9V27a3 3 0 0 0 3 3a14 14 0 0 0 14-14.54A14.05 14.05 0 0 0 16.54 2Zm8.11 22.31A11.93 11.93 0 0 1 16 28a1 1 0 0 1-1-1v-3.76a5 5 0 0 0-5-5a5.07 5.07 0 0 0-1.33.18l-1.12.31A2.82 2.82 0 0 1 4 16A12 12 0 0 1 16.47 4A12.18 12.18 0 0 1 28 15.53a11.89 11.89 0 0 1-3.35 8.79Z\"\n\t/>\n</svg>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 32 32\"\n\t><path\n\t\tfill=\"currentColor\"\n\t\td=\"M7 27h23v2H7zm20.38-16.49l-7.93-7.92a2 2 0 0 0-2.83 0l-14 14a2 2 0 0 0 0 2.83L7.13 24h9.59l10.66-10.66a2 2 0 0 0 0-2.83zM15.89 22H8l-4-4l6.31-6.31l7.93 7.92zm3.76-3.76l-7.92-7.93L18 4l8 7.93z\"\n\t/></svg\n>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-edit-2\"\n>\n\t<path d=\"M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z\" />\n</svg>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"red\"\n\tstroke=\"red\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-square\"\n>\n\t<rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\" />\n</svg>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher, onMount } from \"svelte\";\n\timport { Camera, Circle, Square } from \"@gradio/icons\";\n\timport { _ } from \"svelte-i18n\";\n\n\tlet video_source: HTMLVideoElement;\n\tlet canvas: HTMLCanvasElement;\n\texport let streaming = false;\n\texport let pending = false;\n\n\texport let mode: \"image\" | \"video\" = \"image\";\n\texport let mirror_webcam: boolean;\n\texport let include_audio: boolean;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tstream: undefined;\n\t\tcapture:\n\t\t\t| {\n\t\t\t\t\tdata: FileReader[\"result\"];\n\t\t\t\t\tname: string;\n\t\t\t\t\tis_example?: boolean;\n\t\t\t  }\n\t\t\t| string;\n\t\terror: string;\n\t\tstart_recording: undefined;\n\t\tstop_recording: undefined;\n\t}>();\n\n\tonMount(() => (canvas = document.createElement(\"canvas\")));\n\n\tasync function access_webcam(): Promise<void> {\n\t\ttry {\n\t\t\tstream = await navigator.mediaDevices.getUserMedia({\n\t\t\t\tvideo: true,\n\t\t\t\taudio: include_audio\n\t\t\t});\n\t\t\tvideo_source.srcObject = stream;\n\t\t\tvideo_source.muted = true;\n\t\t\tvideo_source.play();\n\t\t} catch (err) {\n\t\t\tif (err instanceof DOMException && err.name == \"NotAllowedError\") {\n\t\t\t\tdispatch(\"error\", $_(\"image.allow_webcam_access\"));\n\t\t\t} else {\n\t\t\t\tthrow err;\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction take_picture(): void {\n\t\tvar context = canvas.getContext(\"2d\")!;\n\n\t\tif (video_source.videoWidth && video_source.videoHeight) {\n\t\t\tcanvas.width = video_source.videoWidth;\n\t\t\tcanvas.height = video_source.videoHeight;\n\t\t\tcontext.drawImage(\n\t\t\t\tvideo_source,\n\t\t\t\t0,\n\t\t\t\t0,\n\t\t\t\tvideo_source.videoWidth,\n\t\t\t\tvideo_source.videoHeight\n\t\t\t);\n\n\t\t\tvar data = canvas.toDataURL(\"image/png\");\n\t\t\tdispatch(streaming ? \"stream\" : \"capture\", data);\n\t\t}\n\t}\n\n\tlet recording = false;\n\tlet recorded_blobs: BlobPart[] = [];\n\tlet stream: MediaStream;\n\tlet mimeType: string;\n\tlet media_recorder: MediaRecorder;\n\n\tfunction take_recording(): void {\n\t\tif (recording) {\n\t\t\tmedia_recorder.stop();\n\t\t\tlet video_blob = new Blob(recorded_blobs, { type: mimeType });\n\t\t\tlet ReaderObj = new FileReader();\n\t\t\tReaderObj.onload = function (e): void {\n\t\t\t\tif (e.target) {\n\t\t\t\t\tdispatch(\"capture\", {\n\t\t\t\t\t\tdata: e.target.result,\n\t\t\t\t\t\tname: \"sample.\" + mimeType.substring(6),\n\t\t\t\t\t\tis_example: false\n\t\t\t\t\t});\n\t\t\t\t\tdispatch(\"stop_recording\");\n\t\t\t\t}\n\t\t\t};\n\t\t\tReaderObj.readAsDataURL(video_blob);\n\t\t} else {\n\t\t\tdispatch(\"start_recording\");\n\t\t\trecorded_blobs = [];\n\t\t\tlet validMimeTypes = [\"video/webm\", \"video/mp4\"];\n\t\t\tfor (let validMimeType of validMimeTypes) {\n\t\t\t\tif (MediaRecorder.isTypeSupported(validMimeType)) {\n\t\t\t\t\tmimeType = validMimeType;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (mimeType === null) {\n\t\t\t\tconsole.error(\"No supported MediaRecorder mimeType\");\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tmedia_recorder = new MediaRecorder(stream, {\n\t\t\t\tmimeType: mimeType\n\t\t\t});\n\t\t\tmedia_recorder.addEventListener(\"dataavailable\", function (e) {\n\t\t\t\trecorded_blobs.push(e.data);\n\t\t\t});\n\t\t\tmedia_recorder.start(200);\n\t\t}\n\t\trecording = !recording;\n\t}\n\n\taccess_webcam();\n\n\tif (streaming && mode === \"image\") {\n\t\twindow.setInterval(() => {\n\t\t\tif (video_source && !pending) {\n\t\t\t\ttake_picture();\n\t\t\t}\n\t\t}, 500);\n\t}\n</script>\n\n<div class=\"wrap\">\n\t<!-- svelte-ignore a11y-media-has-caption -->\n\t<video bind:this={video_source} class:flip={mirror_webcam} />\n\t{#if !streaming}\n\t\t<button on:click={mode === \"image\" ? take_picture : take_recording}>\n\t\t\t{#if mode === \"video\"}\n\t\t\t\t{#if recording}\n\t\t\t\t\t<div class=\"icon\">\n\t\t\t\t\t\t<Square />\n\t\t\t\t\t</div>\n\t\t\t\t{:else}\n\t\t\t\t\t<div class=\"icon\">\n\t\t\t\t\t\t<Circle />\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t{:else}\n\t\t\t\t<div class=\"icon\">\n\t\t\t\t\t<Camera />\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t</button>\n\t{/if}\n</div>\n\n<style>\n\t.wrap {\n\t\tposition: relative;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tmin-height: var(--size-60);\n\t}\n\n\tvideo {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\tbutton {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tright: 0px;\n\t\tbottom: var(--size-2);\n\t\tleft: 0px;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmargin: auto;\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tborder-radius: var(--radius-xl);\n\t\tbackground-color: rgba(0, 0, 0, 0.9);\n\t\twidth: var(--size-10);\n\t\theight: var(--size-10);\n\t}\n\n\t@media (--screen-md) {\n\t\tbutton {\n\t\t\tbottom: var(--size-4);\n\t\t}\n\t}\n\n\t@media (--screen-xl) {\n\t\tbutton {\n\t\t\tbottom: var(--size-8);\n\t\t}\n\t}\n\n\t.icon {\n\t\topacity: 0.8;\n\t\twidth: 50%;\n\t\theight: 50%;\n\t\tcolor: white;\n\t}\n\n\t.flip {\n\t\ttransform: scaleX(-1);\n\t}\n</style>\n", "/*!\n * Cropper.js v1.5.12\n * https://fengyuanchen.github.io/cropperjs\n *\n * Copyright 2015-present <PERSON>\n * Released under the MIT license\n *\n * Date: 2021-06-12T08:00:17.411Z\n */\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar IS_BROWSER = typeof window !== 'undefined' && typeof window.document !== 'undefined';\nvar WINDOW = IS_BROWSER ? window : {};\nvar IS_TOUCH_DEVICE = IS_BROWSER && WINDOW.document.documentElement ? 'ontouchstart' in WINDOW.document.documentElement : false;\nvar HAS_POINTER_EVENT = IS_BROWSER ? 'PointerEvent' in WINDOW : false;\nvar NAMESPACE = 'cropper'; // Actions\n\nvar ACTION_ALL = 'all';\nvar ACTION_CROP = 'crop';\nvar ACTION_MOVE = 'move';\nvar ACTION_ZOOM = 'zoom';\nvar ACTION_EAST = 'e';\nvar ACTION_WEST = 'w';\nvar ACTION_SOUTH = 's';\nvar ACTION_NORTH = 'n';\nvar ACTION_NORTH_EAST = 'ne';\nvar ACTION_NORTH_WEST = 'nw';\nvar ACTION_SOUTH_EAST = 'se';\nvar ACTION_SOUTH_WEST = 'sw'; // Classes\n\nvar CLASS_CROP = \"\".concat(NAMESPACE, \"-crop\");\nvar CLASS_DISABLED = \"\".concat(NAMESPACE, \"-disabled\");\nvar CLASS_HIDDEN = \"\".concat(NAMESPACE, \"-hidden\");\nvar CLASS_HIDE = \"\".concat(NAMESPACE, \"-hide\");\nvar CLASS_INVISIBLE = \"\".concat(NAMESPACE, \"-invisible\");\nvar CLASS_MODAL = \"\".concat(NAMESPACE, \"-modal\");\nvar CLASS_MOVE = \"\".concat(NAMESPACE, \"-move\"); // Data keys\n\nvar DATA_ACTION = \"\".concat(NAMESPACE, \"Action\");\nvar DATA_PREVIEW = \"\".concat(NAMESPACE, \"Preview\"); // Drag modes\n\nvar DRAG_MODE_CROP = 'crop';\nvar DRAG_MODE_MOVE = 'move';\nvar DRAG_MODE_NONE = 'none'; // Events\n\nvar EVENT_CROP = 'crop';\nvar EVENT_CROP_END = 'cropend';\nvar EVENT_CROP_MOVE = 'cropmove';\nvar EVENT_CROP_START = 'cropstart';\nvar EVENT_DBLCLICK = 'dblclick';\nvar EVENT_TOUCH_START = IS_TOUCH_DEVICE ? 'touchstart' : 'mousedown';\nvar EVENT_TOUCH_MOVE = IS_TOUCH_DEVICE ? 'touchmove' : 'mousemove';\nvar EVENT_TOUCH_END = IS_TOUCH_DEVICE ? 'touchend touchcancel' : 'mouseup';\nvar EVENT_POINTER_DOWN = HAS_POINTER_EVENT ? 'pointerdown' : EVENT_TOUCH_START;\nvar EVENT_POINTER_MOVE = HAS_POINTER_EVENT ? 'pointermove' : EVENT_TOUCH_MOVE;\nvar EVENT_POINTER_UP = HAS_POINTER_EVENT ? 'pointerup pointercancel' : EVENT_TOUCH_END;\nvar EVENT_READY = 'ready';\nvar EVENT_RESIZE = 'resize';\nvar EVENT_WHEEL = 'wheel';\nvar EVENT_ZOOM = 'zoom'; // Mime types\n\nvar MIME_TYPE_JPEG = 'image/jpeg'; // RegExps\n\nvar REGEXP_ACTIONS = /^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/;\nvar REGEXP_DATA_URL = /^data:/;\nvar REGEXP_DATA_URL_JPEG = /^data:image\\/jpeg;base64,/;\nvar REGEXP_TAG_NAME = /^img|canvas$/i; // Misc\n// Inspired by the default width and height of a canvas element.\n\nvar MIN_CONTAINER_WIDTH = 200;\nvar MIN_CONTAINER_HEIGHT = 100;\n\nvar DEFAULTS = {\n  // Define the view mode of the cropper\n  viewMode: 0,\n  // 0, 1, 2, 3\n  // Define the dragging mode of the cropper\n  dragMode: DRAG_MODE_CROP,\n  // 'crop', 'move' or 'none'\n  // Define the initial aspect ratio of the crop box\n  initialAspectRatio: NaN,\n  // Define the aspect ratio of the crop box\n  aspectRatio: NaN,\n  // An object with the previous cropping result data\n  data: null,\n  // A selector for adding extra containers to preview\n  preview: '',\n  // Re-render the cropper when resize the window\n  responsive: true,\n  // Restore the cropped area after resize the window\n  restore: true,\n  // Check if the current image is a cross-origin image\n  checkCrossOrigin: true,\n  // Check the current image's Exif Orientation information\n  checkOrientation: true,\n  // Show the black modal\n  modal: true,\n  // Show the dashed lines for guiding\n  guides: true,\n  // Show the center indicator for guiding\n  center: true,\n  // Show the white modal to highlight the crop box\n  highlight: true,\n  // Show the grid background\n  background: true,\n  // Enable to crop the image automatically when initialize\n  autoCrop: true,\n  // Define the percentage of automatic cropping area when initializes\n  autoCropArea: 0.8,\n  // Enable to move the image\n  movable: true,\n  // Enable to rotate the image\n  rotatable: true,\n  // Enable to scale the image\n  scalable: true,\n  // Enable to zoom the image\n  zoomable: true,\n  // Enable to zoom the image by dragging touch\n  zoomOnTouch: true,\n  // Enable to zoom the image by wheeling mouse\n  zoomOnWheel: true,\n  // Define zoom ratio when zoom the image by wheeling mouse\n  wheelZoomRatio: 0.1,\n  // Enable to move the crop box\n  cropBoxMovable: true,\n  // Enable to resize the crop box\n  cropBoxResizable: true,\n  // Toggle drag mode between \"crop\" and \"move\" when click twice on the cropper\n  toggleDragModeOnDblclick: true,\n  // Size limitation\n  minCanvasWidth: 0,\n  minCanvasHeight: 0,\n  minCropBoxWidth: 0,\n  minCropBoxHeight: 0,\n  minContainerWidth: MIN_CONTAINER_WIDTH,\n  minContainerHeight: MIN_CONTAINER_HEIGHT,\n  // Shortcuts of events\n  ready: null,\n  cropstart: null,\n  cropmove: null,\n  cropend: null,\n  crop: null,\n  zoom: null\n};\n\nvar TEMPLATE = '<div class=\"cropper-container\" touch-action=\"none\">' + '<div class=\"cropper-wrap-box\">' + '<div class=\"cropper-canvas\"></div>' + '</div>' + '<div class=\"cropper-drag-box\"></div>' + '<div class=\"cropper-crop-box\">' + '<span class=\"cropper-view-box\"></span>' + '<span class=\"cropper-dashed dashed-h\"></span>' + '<span class=\"cropper-dashed dashed-v\"></span>' + '<span class=\"cropper-center\"></span>' + '<span class=\"cropper-face\"></span>' + '<span class=\"cropper-line line-e\" data-cropper-action=\"e\"></span>' + '<span class=\"cropper-line line-n\" data-cropper-action=\"n\"></span>' + '<span class=\"cropper-line line-w\" data-cropper-action=\"w\"></span>' + '<span class=\"cropper-line line-s\" data-cropper-action=\"s\"></span>' + '<span class=\"cropper-point point-e\" data-cropper-action=\"e\"></span>' + '<span class=\"cropper-point point-n\" data-cropper-action=\"n\"></span>' + '<span class=\"cropper-point point-w\" data-cropper-action=\"w\"></span>' + '<span class=\"cropper-point point-s\" data-cropper-action=\"s\"></span>' + '<span class=\"cropper-point point-ne\" data-cropper-action=\"ne\"></span>' + '<span class=\"cropper-point point-nw\" data-cropper-action=\"nw\"></span>' + '<span class=\"cropper-point point-sw\" data-cropper-action=\"sw\"></span>' + '<span class=\"cropper-point point-se\" data-cropper-action=\"se\"></span>' + '</div>' + '</div>';\n\n/**\n * Check if the given value is not a number.\n */\n\nvar isNaN = Number.isNaN || WINDOW.isNaN;\n/**\n * Check if the given value is a number.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a number, else `false`.\n */\n\nfunction isNumber(value) {\n  return typeof value === 'number' && !isNaN(value);\n}\n/**\n * Check if the given value is a positive number.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a positive number, else `false`.\n */\n\nvar isPositiveNumber = function isPositiveNumber(value) {\n  return value > 0 && value < Infinity;\n};\n/**\n * Check if the given value is undefined.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is undefined, else `false`.\n */\n\nfunction isUndefined(value) {\n  return typeof value === 'undefined';\n}\n/**\n * Check if the given value is an object.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is an object, else `false`.\n */\n\nfunction isObject(value) {\n  return _typeof(value) === 'object' && value !== null;\n}\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n/**\n * Check if the given value is a plain object.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a plain object, else `false`.\n */\n\nfunction isPlainObject(value) {\n  if (!isObject(value)) {\n    return false;\n  }\n\n  try {\n    var _constructor = value.constructor;\n    var prototype = _constructor.prototype;\n    return _constructor && prototype && hasOwnProperty.call(prototype, 'isPrototypeOf');\n  } catch (error) {\n    return false;\n  }\n}\n/**\n * Check if the given value is a function.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a function, else `false`.\n */\n\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\nvar slice = Array.prototype.slice;\n/**\n * Convert array-like or iterable object to an array.\n * @param {*} value - The value to convert.\n * @returns {Array} Returns a new array.\n */\n\nfunction toArray(value) {\n  return Array.from ? Array.from(value) : slice.call(value);\n}\n/**\n * Iterate the given data.\n * @param {*} data - The data to iterate.\n * @param {Function} callback - The process function for each element.\n * @returns {*} The original data.\n */\n\nfunction forEach(data, callback) {\n  if (data && isFunction(callback)) {\n    if (Array.isArray(data) || isNumber(data.length)\n    /* array-like */\n    ) {\n        toArray(data).forEach(function (value, key) {\n          callback.call(data, value, key, data);\n        });\n      } else if (isObject(data)) {\n      Object.keys(data).forEach(function (key) {\n        callback.call(data, data[key], key, data);\n      });\n    }\n  }\n\n  return data;\n}\n/**\n * Extend the given object.\n * @param {*} target - The target object to extend.\n * @param {*} args - The rest objects for merging to the target object.\n * @returns {Object} The extended object.\n */\n\nvar assign = Object.assign || function assign(target) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n\n  if (isObject(target) && args.length > 0) {\n    args.forEach(function (arg) {\n      if (isObject(arg)) {\n        Object.keys(arg).forEach(function (key) {\n          target[key] = arg[key];\n        });\n      }\n    });\n  }\n\n  return target;\n};\nvar REGEXP_DECIMALS = /\\.\\d*(?:0|9){12}\\d*$/;\n/**\n * Normalize decimal number.\n * Check out {@link https://0.30000000000000004.com/}\n * @param {number} value - The value to normalize.\n * @param {number} [times=100000000000] - The times for normalizing.\n * @returns {number} Returns the normalized number.\n */\n\nfunction normalizeDecimalNumber(value) {\n  var times = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 100000000000;\n  return REGEXP_DECIMALS.test(value) ? Math.round(value * times) / times : value;\n}\nvar REGEXP_SUFFIX = /^width|height|left|top|marginLeft|marginTop$/;\n/**\n * Apply styles to the given element.\n * @param {Element} element - The target element.\n * @param {Object} styles - The styles for applying.\n */\n\nfunction setStyle(element, styles) {\n  var style = element.style;\n  forEach(styles, function (value, property) {\n    if (REGEXP_SUFFIX.test(property) && isNumber(value)) {\n      value = \"\".concat(value, \"px\");\n    }\n\n    style[property] = value;\n  });\n}\n/**\n * Check if the given element has a special class.\n * @param {Element} element - The element to check.\n * @param {string} value - The class to search.\n * @returns {boolean} Returns `true` if the special class was found.\n */\n\nfunction hasClass(element, value) {\n  return element.classList ? element.classList.contains(value) : element.className.indexOf(value) > -1;\n}\n/**\n * Add classes to the given element.\n * @param {Element} element - The target element.\n * @param {string} value - The classes to be added.\n */\n\nfunction addClass(element, value) {\n  if (!value) {\n    return;\n  }\n\n  if (isNumber(element.length)) {\n    forEach(element, function (elem) {\n      addClass(elem, value);\n    });\n    return;\n  }\n\n  if (element.classList) {\n    element.classList.add(value);\n    return;\n  }\n\n  var className = element.className.trim();\n\n  if (!className) {\n    element.className = value;\n  } else if (className.indexOf(value) < 0) {\n    element.className = \"\".concat(className, \" \").concat(value);\n  }\n}\n/**\n * Remove classes from the given element.\n * @param {Element} element - The target element.\n * @param {string} value - The classes to be removed.\n */\n\nfunction removeClass(element, value) {\n  if (!value) {\n    return;\n  }\n\n  if (isNumber(element.length)) {\n    forEach(element, function (elem) {\n      removeClass(elem, value);\n    });\n    return;\n  }\n\n  if (element.classList) {\n    element.classList.remove(value);\n    return;\n  }\n\n  if (element.className.indexOf(value) >= 0) {\n    element.className = element.className.replace(value, '');\n  }\n}\n/**\n * Add or remove classes from the given element.\n * @param {Element} element - The target element.\n * @param {string} value - The classes to be toggled.\n * @param {boolean} added - Add only.\n */\n\nfunction toggleClass(element, value, added) {\n  if (!value) {\n    return;\n  }\n\n  if (isNumber(element.length)) {\n    forEach(element, function (elem) {\n      toggleClass(elem, value, added);\n    });\n    return;\n  } // IE10-11 doesn't support the second parameter of `classList.toggle`\n\n\n  if (added) {\n    addClass(element, value);\n  } else {\n    removeClass(element, value);\n  }\n}\nvar REGEXP_CAMEL_CASE = /([a-z\\d])([A-Z])/g;\n/**\n * Transform the given string from camelCase to kebab-case\n * @param {string} value - The value to transform.\n * @returns {string} The transformed value.\n */\n\nfunction toParamCase(value) {\n  return value.replace(REGEXP_CAMEL_CASE, '$1-$2').toLowerCase();\n}\n/**\n * Get data from the given element.\n * @param {Element} element - The target element.\n * @param {string} name - The data key to get.\n * @returns {string} The data value.\n */\n\nfunction getData(element, name) {\n  if (isObject(element[name])) {\n    return element[name];\n  }\n\n  if (element.dataset) {\n    return element.dataset[name];\n  }\n\n  return element.getAttribute(\"data-\".concat(toParamCase(name)));\n}\n/**\n * Set data to the given element.\n * @param {Element} element - The target element.\n * @param {string} name - The data key to set.\n * @param {string} data - The data value.\n */\n\nfunction setData(element, name, data) {\n  if (isObject(data)) {\n    element[name] = data;\n  } else if (element.dataset) {\n    element.dataset[name] = data;\n  } else {\n    element.setAttribute(\"data-\".concat(toParamCase(name)), data);\n  }\n}\n/**\n * Remove data from the given element.\n * @param {Element} element - The target element.\n * @param {string} name - The data key to remove.\n */\n\nfunction removeData(element, name) {\n  if (isObject(element[name])) {\n    try {\n      delete element[name];\n    } catch (error) {\n      element[name] = undefined;\n    }\n  } else if (element.dataset) {\n    // #128 Safari not allows to delete dataset property\n    try {\n      delete element.dataset[name];\n    } catch (error) {\n      element.dataset[name] = undefined;\n    }\n  } else {\n    element.removeAttribute(\"data-\".concat(toParamCase(name)));\n  }\n}\nvar REGEXP_SPACES = /\\s\\s*/;\n\nvar onceSupported = function () {\n  var supported = false;\n\n  if (IS_BROWSER) {\n    var once = false;\n\n    var listener = function listener() {};\n\n    var options = Object.defineProperty({}, 'once', {\n      get: function get() {\n        supported = true;\n        return once;\n      },\n\n      /**\n       * This setter can fix a `TypeError` in strict mode\n       * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Errors/Getter_only}\n       * @param {boolean} value - The value to set\n       */\n      set: function set(value) {\n        once = value;\n      }\n    });\n    WINDOW.addEventListener('test', listener, options);\n    WINDOW.removeEventListener('test', listener, options);\n  }\n\n  return supported;\n}();\n/**\n * Remove event listener from the target element.\n * @param {Element} element - The event target.\n * @param {string} type - The event type(s).\n * @param {Function} listener - The event listener.\n * @param {Object} options - The event options.\n */\n\n\nfunction removeListener(element, type, listener) {\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  var handler = listener;\n  type.trim().split(REGEXP_SPACES).forEach(function (event) {\n    if (!onceSupported) {\n      var listeners = element.listeners;\n\n      if (listeners && listeners[event] && listeners[event][listener]) {\n        handler = listeners[event][listener];\n        delete listeners[event][listener];\n\n        if (Object.keys(listeners[event]).length === 0) {\n          delete listeners[event];\n        }\n\n        if (Object.keys(listeners).length === 0) {\n          delete element.listeners;\n        }\n      }\n    }\n\n    element.removeEventListener(event, handler, options);\n  });\n}\n/**\n * Add event listener to the target element.\n * @param {Element} element - The event target.\n * @param {string} type - The event type(s).\n * @param {Function} listener - The event listener.\n * @param {Object} options - The event options.\n */\n\nfunction addListener(element, type, listener) {\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  var _handler = listener;\n  type.trim().split(REGEXP_SPACES).forEach(function (event) {\n    if (options.once && !onceSupported) {\n      var _element$listeners = element.listeners,\n          listeners = _element$listeners === void 0 ? {} : _element$listeners;\n\n      _handler = function handler() {\n        delete listeners[event][listener];\n        element.removeEventListener(event, _handler, options);\n\n        for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n          args[_key2] = arguments[_key2];\n        }\n\n        listener.apply(element, args);\n      };\n\n      if (!listeners[event]) {\n        listeners[event] = {};\n      }\n\n      if (listeners[event][listener]) {\n        element.removeEventListener(event, listeners[event][listener], options);\n      }\n\n      listeners[event][listener] = _handler;\n      element.listeners = listeners;\n    }\n\n    element.addEventListener(event, _handler, options);\n  });\n}\n/**\n * Dispatch event on the target element.\n * @param {Element} element - The event target.\n * @param {string} type - The event type(s).\n * @param {Object} data - The additional event data.\n * @returns {boolean} Indicate if the event is default prevented or not.\n */\n\nfunction dispatchEvent(element, type, data) {\n  var event; // Event and CustomEvent on IE9-11 are global objects, not constructors\n\n  if (isFunction(Event) && isFunction(CustomEvent)) {\n    event = new CustomEvent(type, {\n      detail: data,\n      bubbles: true,\n      cancelable: true\n    });\n  } else {\n    event = document.createEvent('CustomEvent');\n    event.initCustomEvent(type, true, true, data);\n  }\n\n  return element.dispatchEvent(event);\n}\n/**\n * Get the offset base on the document.\n * @param {Element} element - The target element.\n * @returns {Object} The offset data.\n */\n\nfunction getOffset(element) {\n  var box = element.getBoundingClientRect();\n  return {\n    left: box.left + (window.pageXOffset - document.documentElement.clientLeft),\n    top: box.top + (window.pageYOffset - document.documentElement.clientTop)\n  };\n}\nvar location = WINDOW.location;\nvar REGEXP_ORIGINS = /^(\\w+:)\\/\\/([^:/?#]*):?(\\d*)/i;\n/**\n * Check if the given URL is a cross origin URL.\n * @param {string} url - The target URL.\n * @returns {boolean} Returns `true` if the given URL is a cross origin URL, else `false`.\n */\n\nfunction isCrossOriginURL(url) {\n  var parts = url.match(REGEXP_ORIGINS);\n  return parts !== null && (parts[1] !== location.protocol || parts[2] !== location.hostname || parts[3] !== location.port);\n}\n/**\n * Add timestamp to the given URL.\n * @param {string} url - The target URL.\n * @returns {string} The result URL.\n */\n\nfunction addTimestamp(url) {\n  var timestamp = \"timestamp=\".concat(new Date().getTime());\n  return url + (url.indexOf('?') === -1 ? '?' : '&') + timestamp;\n}\n/**\n * Get transforms base on the given object.\n * @param {Object} obj - The target object.\n * @returns {string} A string contains transform values.\n */\n\nfunction getTransforms(_ref) {\n  var rotate = _ref.rotate,\n      scaleX = _ref.scaleX,\n      scaleY = _ref.scaleY,\n      translateX = _ref.translateX,\n      translateY = _ref.translateY;\n  var values = [];\n\n  if (isNumber(translateX) && translateX !== 0) {\n    values.push(\"translateX(\".concat(translateX, \"px)\"));\n  }\n\n  if (isNumber(translateY) && translateY !== 0) {\n    values.push(\"translateY(\".concat(translateY, \"px)\"));\n  } // Rotate should come first before scale to match orientation transform\n\n\n  if (isNumber(rotate) && rotate !== 0) {\n    values.push(\"rotate(\".concat(rotate, \"deg)\"));\n  }\n\n  if (isNumber(scaleX) && scaleX !== 1) {\n    values.push(\"scaleX(\".concat(scaleX, \")\"));\n  }\n\n  if (isNumber(scaleY) && scaleY !== 1) {\n    values.push(\"scaleY(\".concat(scaleY, \")\"));\n  }\n\n  var transform = values.length ? values.join(' ') : 'none';\n  return {\n    WebkitTransform: transform,\n    msTransform: transform,\n    transform: transform\n  };\n}\n/**\n * Get the max ratio of a group of pointers.\n * @param {string} pointers - The target pointers.\n * @returns {number} The result ratio.\n */\n\nfunction getMaxZoomRatio(pointers) {\n  var pointers2 = _objectSpread2({}, pointers);\n\n  var maxRatio = 0;\n  forEach(pointers, function (pointer, pointerId) {\n    delete pointers2[pointerId];\n    forEach(pointers2, function (pointer2) {\n      var x1 = Math.abs(pointer.startX - pointer2.startX);\n      var y1 = Math.abs(pointer.startY - pointer2.startY);\n      var x2 = Math.abs(pointer.endX - pointer2.endX);\n      var y2 = Math.abs(pointer.endY - pointer2.endY);\n      var z1 = Math.sqrt(x1 * x1 + y1 * y1);\n      var z2 = Math.sqrt(x2 * x2 + y2 * y2);\n      var ratio = (z2 - z1) / z1;\n\n      if (Math.abs(ratio) > Math.abs(maxRatio)) {\n        maxRatio = ratio;\n      }\n    });\n  });\n  return maxRatio;\n}\n/**\n * Get a pointer from an event object.\n * @param {Object} event - The target event object.\n * @param {boolean} endOnly - Indicates if only returns the end point coordinate or not.\n * @returns {Object} The result pointer contains start and/or end point coordinates.\n */\n\nfunction getPointer(_ref2, endOnly) {\n  var pageX = _ref2.pageX,\n      pageY = _ref2.pageY;\n  var end = {\n    endX: pageX,\n    endY: pageY\n  };\n  return endOnly ? end : _objectSpread2({\n    startX: pageX,\n    startY: pageY\n  }, end);\n}\n/**\n * Get the center point coordinate of a group of pointers.\n * @param {Object} pointers - The target pointers.\n * @returns {Object} The center point coordinate.\n */\n\nfunction getPointersCenter(pointers) {\n  var pageX = 0;\n  var pageY = 0;\n  var count = 0;\n  forEach(pointers, function (_ref3) {\n    var startX = _ref3.startX,\n        startY = _ref3.startY;\n    pageX += startX;\n    pageY += startY;\n    count += 1;\n  });\n  pageX /= count;\n  pageY /= count;\n  return {\n    pageX: pageX,\n    pageY: pageY\n  };\n}\n/**\n * Get the max sizes in a rectangle under the given aspect ratio.\n * @param {Object} data - The original sizes.\n * @param {string} [type='contain'] - The adjust type.\n * @returns {Object} The result sizes.\n */\n\nfunction getAdjustedSizes(_ref4) // or 'cover'\n{\n  var aspectRatio = _ref4.aspectRatio,\n      height = _ref4.height,\n      width = _ref4.width;\n  var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'contain';\n  var isValidWidth = isPositiveNumber(width);\n  var isValidHeight = isPositiveNumber(height);\n\n  if (isValidWidth && isValidHeight) {\n    var adjustedWidth = height * aspectRatio;\n\n    if (type === 'contain' && adjustedWidth > width || type === 'cover' && adjustedWidth < width) {\n      height = width / aspectRatio;\n    } else {\n      width = height * aspectRatio;\n    }\n  } else if (isValidWidth) {\n    height = width / aspectRatio;\n  } else if (isValidHeight) {\n    width = height * aspectRatio;\n  }\n\n  return {\n    width: width,\n    height: height\n  };\n}\n/**\n * Get the new sizes of a rectangle after rotated.\n * @param {Object} data - The original sizes.\n * @returns {Object} The result sizes.\n */\n\nfunction getRotatedSizes(_ref5) {\n  var width = _ref5.width,\n      height = _ref5.height,\n      degree = _ref5.degree;\n  degree = Math.abs(degree) % 180;\n\n  if (degree === 90) {\n    return {\n      width: height,\n      height: width\n    };\n  }\n\n  var arc = degree % 90 * Math.PI / 180;\n  var sinArc = Math.sin(arc);\n  var cosArc = Math.cos(arc);\n  var newWidth = width * cosArc + height * sinArc;\n  var newHeight = width * sinArc + height * cosArc;\n  return degree > 90 ? {\n    width: newHeight,\n    height: newWidth\n  } : {\n    width: newWidth,\n    height: newHeight\n  };\n}\n/**\n * Get a canvas which drew the given image.\n * @param {HTMLImageElement} image - The image for drawing.\n * @param {Object} imageData - The image data.\n * @param {Object} canvasData - The canvas data.\n * @param {Object} options - The options.\n * @returns {HTMLCanvasElement} The result canvas.\n */\n\nfunction getSourceCanvas(image, _ref6, _ref7, _ref8) {\n  var imageAspectRatio = _ref6.aspectRatio,\n      imageNaturalWidth = _ref6.naturalWidth,\n      imageNaturalHeight = _ref6.naturalHeight,\n      _ref6$rotate = _ref6.rotate,\n      rotate = _ref6$rotate === void 0 ? 0 : _ref6$rotate,\n      _ref6$scaleX = _ref6.scaleX,\n      scaleX = _ref6$scaleX === void 0 ? 1 : _ref6$scaleX,\n      _ref6$scaleY = _ref6.scaleY,\n      scaleY = _ref6$scaleY === void 0 ? 1 : _ref6$scaleY;\n  var aspectRatio = _ref7.aspectRatio,\n      naturalWidth = _ref7.naturalWidth,\n      naturalHeight = _ref7.naturalHeight;\n  var _ref8$fillColor = _ref8.fillColor,\n      fillColor = _ref8$fillColor === void 0 ? 'transparent' : _ref8$fillColor,\n      _ref8$imageSmoothingE = _ref8.imageSmoothingEnabled,\n      imageSmoothingEnabled = _ref8$imageSmoothingE === void 0 ? true : _ref8$imageSmoothingE,\n      _ref8$imageSmoothingQ = _ref8.imageSmoothingQuality,\n      imageSmoothingQuality = _ref8$imageSmoothingQ === void 0 ? 'low' : _ref8$imageSmoothingQ,\n      _ref8$maxWidth = _ref8.maxWidth,\n      maxWidth = _ref8$maxWidth === void 0 ? Infinity : _ref8$maxWidth,\n      _ref8$maxHeight = _ref8.maxHeight,\n      maxHeight = _ref8$maxHeight === void 0 ? Infinity : _ref8$maxHeight,\n      _ref8$minWidth = _ref8.minWidth,\n      minWidth = _ref8$minWidth === void 0 ? 0 : _ref8$minWidth,\n      _ref8$minHeight = _ref8.minHeight,\n      minHeight = _ref8$minHeight === void 0 ? 0 : _ref8$minHeight;\n  var canvas = document.createElement('canvas');\n  var context = canvas.getContext('2d');\n  var maxSizes = getAdjustedSizes({\n    aspectRatio: aspectRatio,\n    width: maxWidth,\n    height: maxHeight\n  });\n  var minSizes = getAdjustedSizes({\n    aspectRatio: aspectRatio,\n    width: minWidth,\n    height: minHeight\n  }, 'cover');\n  var width = Math.min(maxSizes.width, Math.max(minSizes.width, naturalWidth));\n  var height = Math.min(maxSizes.height, Math.max(minSizes.height, naturalHeight)); // Note: should always use image's natural sizes for drawing as\n  // imageData.naturalWidth === canvasData.naturalHeight when rotate % 180 === 90\n\n  var destMaxSizes = getAdjustedSizes({\n    aspectRatio: imageAspectRatio,\n    width: maxWidth,\n    height: maxHeight\n  });\n  var destMinSizes = getAdjustedSizes({\n    aspectRatio: imageAspectRatio,\n    width: minWidth,\n    height: minHeight\n  }, 'cover');\n  var destWidth = Math.min(destMaxSizes.width, Math.max(destMinSizes.width, imageNaturalWidth));\n  var destHeight = Math.min(destMaxSizes.height, Math.max(destMinSizes.height, imageNaturalHeight));\n  var params = [-destWidth / 2, -destHeight / 2, destWidth, destHeight];\n  canvas.width = normalizeDecimalNumber(width);\n  canvas.height = normalizeDecimalNumber(height);\n  context.fillStyle = fillColor;\n  context.fillRect(0, 0, width, height);\n  context.save();\n  context.translate(width / 2, height / 2);\n  context.rotate(rotate * Math.PI / 180);\n  context.scale(scaleX, scaleY);\n  context.imageSmoothingEnabled = imageSmoothingEnabled;\n  context.imageSmoothingQuality = imageSmoothingQuality;\n  context.drawImage.apply(context, [image].concat(_toConsumableArray(params.map(function (param) {\n    return Math.floor(normalizeDecimalNumber(param));\n  }))));\n  context.restore();\n  return canvas;\n}\nvar fromCharCode = String.fromCharCode;\n/**\n * Get string from char code in data view.\n * @param {DataView} dataView - The data view for read.\n * @param {number} start - The start index.\n * @param {number} length - The read length.\n * @returns {string} The read result.\n */\n\nfunction getStringFromCharCode(dataView, start, length) {\n  var str = '';\n  length += start;\n\n  for (var i = start; i < length; i += 1) {\n    str += fromCharCode(dataView.getUint8(i));\n  }\n\n  return str;\n}\nvar REGEXP_DATA_URL_HEAD = /^data:.*,/;\n/**\n * Transform Data URL to array buffer.\n * @param {string} dataURL - The Data URL to transform.\n * @returns {ArrayBuffer} The result array buffer.\n */\n\nfunction dataURLToArrayBuffer(dataURL) {\n  var base64 = dataURL.replace(REGEXP_DATA_URL_HEAD, '');\n  var binary = atob(base64);\n  var arrayBuffer = new ArrayBuffer(binary.length);\n  var uint8 = new Uint8Array(arrayBuffer);\n  forEach(uint8, function (value, i) {\n    uint8[i] = binary.charCodeAt(i);\n  });\n  return arrayBuffer;\n}\n/**\n * Transform array buffer to Data URL.\n * @param {ArrayBuffer} arrayBuffer - The array buffer to transform.\n * @param {string} mimeType - The mime type of the Data URL.\n * @returns {string} The result Data URL.\n */\n\nfunction arrayBufferToDataURL(arrayBuffer, mimeType) {\n  var chunks = []; // Chunk Typed Array for better performance (#435)\n\n  var chunkSize = 8192;\n  var uint8 = new Uint8Array(arrayBuffer);\n\n  while (uint8.length > 0) {\n    // XXX: Babel's `toConsumableArray` helper will throw error in IE or Safari 9\n    // eslint-disable-next-line prefer-spread\n    chunks.push(fromCharCode.apply(null, toArray(uint8.subarray(0, chunkSize))));\n    uint8 = uint8.subarray(chunkSize);\n  }\n\n  return \"data:\".concat(mimeType, \";base64,\").concat(btoa(chunks.join('')));\n}\n/**\n * Get orientation value from given array buffer.\n * @param {ArrayBuffer} arrayBuffer - The array buffer to read.\n * @returns {number} The read orientation value.\n */\n\nfunction resetAndGetOrientation(arrayBuffer) {\n  var dataView = new DataView(arrayBuffer);\n  var orientation; // Ignores range error when the image does not have correct Exif information\n\n  try {\n    var littleEndian;\n    var app1Start;\n    var ifdStart; // Only handle JPEG image (start by 0xFFD8)\n\n    if (dataView.getUint8(0) === 0xFF && dataView.getUint8(1) === 0xD8) {\n      var length = dataView.byteLength;\n      var offset = 2;\n\n      while (offset + 1 < length) {\n        if (dataView.getUint8(offset) === 0xFF && dataView.getUint8(offset + 1) === 0xE1) {\n          app1Start = offset;\n          break;\n        }\n\n        offset += 1;\n      }\n    }\n\n    if (app1Start) {\n      var exifIDCode = app1Start + 4;\n      var tiffOffset = app1Start + 10;\n\n      if (getStringFromCharCode(dataView, exifIDCode, 4) === 'Exif') {\n        var endianness = dataView.getUint16(tiffOffset);\n        littleEndian = endianness === 0x4949;\n\n        if (littleEndian || endianness === 0x4D4D\n        /* bigEndian */\n        ) {\n            if (dataView.getUint16(tiffOffset + 2, littleEndian) === 0x002A) {\n              var firstIFDOffset = dataView.getUint32(tiffOffset + 4, littleEndian);\n\n              if (firstIFDOffset >= 0x00000008) {\n                ifdStart = tiffOffset + firstIFDOffset;\n              }\n            }\n          }\n      }\n    }\n\n    if (ifdStart) {\n      var _length = dataView.getUint16(ifdStart, littleEndian);\n\n      var _offset;\n\n      var i;\n\n      for (i = 0; i < _length; i += 1) {\n        _offset = ifdStart + i * 12 + 2;\n\n        if (dataView.getUint16(_offset, littleEndian) === 0x0112\n        /* Orientation */\n        ) {\n            // 8 is the offset of the current tag's value\n            _offset += 8; // Get the original orientation value\n\n            orientation = dataView.getUint16(_offset, littleEndian); // Override the orientation with its default value\n\n            dataView.setUint16(_offset, 1, littleEndian);\n            break;\n          }\n      }\n    }\n  } catch (error) {\n    orientation = 1;\n  }\n\n  return orientation;\n}\n/**\n * Parse Exif Orientation value.\n * @param {number} orientation - The orientation to parse.\n * @returns {Object} The parsed result.\n */\n\nfunction parseOrientation(orientation) {\n  var rotate = 0;\n  var scaleX = 1;\n  var scaleY = 1;\n\n  switch (orientation) {\n    // Flip horizontal\n    case 2:\n      scaleX = -1;\n      break;\n    // Rotate left 180°\n\n    case 3:\n      rotate = -180;\n      break;\n    // Flip vertical\n\n    case 4:\n      scaleY = -1;\n      break;\n    // Flip vertical and rotate right 90°\n\n    case 5:\n      rotate = 90;\n      scaleY = -1;\n      break;\n    // Rotate right 90°\n\n    case 6:\n      rotate = 90;\n      break;\n    // Flip horizontal and rotate right 90°\n\n    case 7:\n      rotate = 90;\n      scaleX = -1;\n      break;\n    // Rotate left 90°\n\n    case 8:\n      rotate = -90;\n      break;\n  }\n\n  return {\n    rotate: rotate,\n    scaleX: scaleX,\n    scaleY: scaleY\n  };\n}\n\nvar render = {\n  render: function render() {\n    this.initContainer();\n    this.initCanvas();\n    this.initCropBox();\n    this.renderCanvas();\n\n    if (this.cropped) {\n      this.renderCropBox();\n    }\n  },\n  initContainer: function initContainer() {\n    var element = this.element,\n        options = this.options,\n        container = this.container,\n        cropper = this.cropper;\n    var minWidth = Number(options.minContainerWidth);\n    var minHeight = Number(options.minContainerHeight);\n    addClass(cropper, CLASS_HIDDEN);\n    removeClass(element, CLASS_HIDDEN);\n    var containerData = {\n      width: Math.max(container.offsetWidth, minWidth >= 0 ? minWidth : MIN_CONTAINER_WIDTH),\n      height: Math.max(container.offsetHeight, minHeight >= 0 ? minHeight : MIN_CONTAINER_HEIGHT)\n    };\n    this.containerData = containerData;\n    setStyle(cropper, {\n      width: containerData.width,\n      height: containerData.height\n    });\n    addClass(element, CLASS_HIDDEN);\n    removeClass(cropper, CLASS_HIDDEN);\n  },\n  // Canvas (image wrapper)\n  initCanvas: function initCanvas() {\n    var containerData = this.containerData,\n        imageData = this.imageData;\n    var viewMode = this.options.viewMode;\n    var rotated = Math.abs(imageData.rotate) % 180 === 90;\n    var naturalWidth = rotated ? imageData.naturalHeight : imageData.naturalWidth;\n    var naturalHeight = rotated ? imageData.naturalWidth : imageData.naturalHeight;\n    var aspectRatio = naturalWidth / naturalHeight;\n    var canvasWidth = containerData.width;\n    var canvasHeight = containerData.height;\n\n    if (containerData.height * aspectRatio > containerData.width) {\n      if (viewMode === 3) {\n        canvasWidth = containerData.height * aspectRatio;\n      } else {\n        canvasHeight = containerData.width / aspectRatio;\n      }\n    } else if (viewMode === 3) {\n      canvasHeight = containerData.width / aspectRatio;\n    } else {\n      canvasWidth = containerData.height * aspectRatio;\n    }\n\n    var canvasData = {\n      aspectRatio: aspectRatio,\n      naturalWidth: naturalWidth,\n      naturalHeight: naturalHeight,\n      width: canvasWidth,\n      height: canvasHeight\n    };\n    this.canvasData = canvasData;\n    this.limited = viewMode === 1 || viewMode === 2;\n    this.limitCanvas(true, true);\n    canvasData.width = Math.min(Math.max(canvasData.width, canvasData.minWidth), canvasData.maxWidth);\n    canvasData.height = Math.min(Math.max(canvasData.height, canvasData.minHeight), canvasData.maxHeight);\n    canvasData.left = (containerData.width - canvasData.width) / 2;\n    canvasData.top = (containerData.height - canvasData.height) / 2;\n    canvasData.oldLeft = canvasData.left;\n    canvasData.oldTop = canvasData.top;\n    this.initialCanvasData = assign({}, canvasData);\n  },\n  limitCanvas: function limitCanvas(sizeLimited, positionLimited) {\n    var options = this.options,\n        containerData = this.containerData,\n        canvasData = this.canvasData,\n        cropBoxData = this.cropBoxData;\n    var viewMode = options.viewMode;\n    var aspectRatio = canvasData.aspectRatio;\n    var cropped = this.cropped && cropBoxData;\n\n    if (sizeLimited) {\n      var minCanvasWidth = Number(options.minCanvasWidth) || 0;\n      var minCanvasHeight = Number(options.minCanvasHeight) || 0;\n\n      if (viewMode > 1) {\n        minCanvasWidth = Math.max(minCanvasWidth, containerData.width);\n        minCanvasHeight = Math.max(minCanvasHeight, containerData.height);\n\n        if (viewMode === 3) {\n          if (minCanvasHeight * aspectRatio > minCanvasWidth) {\n            minCanvasWidth = minCanvasHeight * aspectRatio;\n          } else {\n            minCanvasHeight = minCanvasWidth / aspectRatio;\n          }\n        }\n      } else if (viewMode > 0) {\n        if (minCanvasWidth) {\n          minCanvasWidth = Math.max(minCanvasWidth, cropped ? cropBoxData.width : 0);\n        } else if (minCanvasHeight) {\n          minCanvasHeight = Math.max(minCanvasHeight, cropped ? cropBoxData.height : 0);\n        } else if (cropped) {\n          minCanvasWidth = cropBoxData.width;\n          minCanvasHeight = cropBoxData.height;\n\n          if (minCanvasHeight * aspectRatio > minCanvasWidth) {\n            minCanvasWidth = minCanvasHeight * aspectRatio;\n          } else {\n            minCanvasHeight = minCanvasWidth / aspectRatio;\n          }\n        }\n      }\n\n      var _getAdjustedSizes = getAdjustedSizes({\n        aspectRatio: aspectRatio,\n        width: minCanvasWidth,\n        height: minCanvasHeight\n      });\n\n      minCanvasWidth = _getAdjustedSizes.width;\n      minCanvasHeight = _getAdjustedSizes.height;\n      canvasData.minWidth = minCanvasWidth;\n      canvasData.minHeight = minCanvasHeight;\n      canvasData.maxWidth = Infinity;\n      canvasData.maxHeight = Infinity;\n    }\n\n    if (positionLimited) {\n      if (viewMode > (cropped ? 0 : 1)) {\n        var newCanvasLeft = containerData.width - canvasData.width;\n        var newCanvasTop = containerData.height - canvasData.height;\n        canvasData.minLeft = Math.min(0, newCanvasLeft);\n        canvasData.minTop = Math.min(0, newCanvasTop);\n        canvasData.maxLeft = Math.max(0, newCanvasLeft);\n        canvasData.maxTop = Math.max(0, newCanvasTop);\n\n        if (cropped && this.limited) {\n          canvasData.minLeft = Math.min(cropBoxData.left, cropBoxData.left + (cropBoxData.width - canvasData.width));\n          canvasData.minTop = Math.min(cropBoxData.top, cropBoxData.top + (cropBoxData.height - canvasData.height));\n          canvasData.maxLeft = cropBoxData.left;\n          canvasData.maxTop = cropBoxData.top;\n\n          if (viewMode === 2) {\n            if (canvasData.width >= containerData.width) {\n              canvasData.minLeft = Math.min(0, newCanvasLeft);\n              canvasData.maxLeft = Math.max(0, newCanvasLeft);\n            }\n\n            if (canvasData.height >= containerData.height) {\n              canvasData.minTop = Math.min(0, newCanvasTop);\n              canvasData.maxTop = Math.max(0, newCanvasTop);\n            }\n          }\n        }\n      } else {\n        canvasData.minLeft = -canvasData.width;\n        canvasData.minTop = -canvasData.height;\n        canvasData.maxLeft = containerData.width;\n        canvasData.maxTop = containerData.height;\n      }\n    }\n  },\n  renderCanvas: function renderCanvas(changed, transformed) {\n    var canvasData = this.canvasData,\n        imageData = this.imageData;\n\n    if (transformed) {\n      var _getRotatedSizes = getRotatedSizes({\n        width: imageData.naturalWidth * Math.abs(imageData.scaleX || 1),\n        height: imageData.naturalHeight * Math.abs(imageData.scaleY || 1),\n        degree: imageData.rotate || 0\n      }),\n          naturalWidth = _getRotatedSizes.width,\n          naturalHeight = _getRotatedSizes.height;\n\n      var width = canvasData.width * (naturalWidth / canvasData.naturalWidth);\n      var height = canvasData.height * (naturalHeight / canvasData.naturalHeight);\n      canvasData.left -= (width - canvasData.width) / 2;\n      canvasData.top -= (height - canvasData.height) / 2;\n      canvasData.width = width;\n      canvasData.height = height;\n      canvasData.aspectRatio = naturalWidth / naturalHeight;\n      canvasData.naturalWidth = naturalWidth;\n      canvasData.naturalHeight = naturalHeight;\n      this.limitCanvas(true, false);\n    }\n\n    if (canvasData.width > canvasData.maxWidth || canvasData.width < canvasData.minWidth) {\n      canvasData.left = canvasData.oldLeft;\n    }\n\n    if (canvasData.height > canvasData.maxHeight || canvasData.height < canvasData.minHeight) {\n      canvasData.top = canvasData.oldTop;\n    }\n\n    canvasData.width = Math.min(Math.max(canvasData.width, canvasData.minWidth), canvasData.maxWidth);\n    canvasData.height = Math.min(Math.max(canvasData.height, canvasData.minHeight), canvasData.maxHeight);\n    this.limitCanvas(false, true);\n    canvasData.left = Math.min(Math.max(canvasData.left, canvasData.minLeft), canvasData.maxLeft);\n    canvasData.top = Math.min(Math.max(canvasData.top, canvasData.minTop), canvasData.maxTop);\n    canvasData.oldLeft = canvasData.left;\n    canvasData.oldTop = canvasData.top;\n    setStyle(this.canvas, assign({\n      width: canvasData.width,\n      height: canvasData.height\n    }, getTransforms({\n      translateX: canvasData.left,\n      translateY: canvasData.top\n    })));\n    this.renderImage(changed);\n\n    if (this.cropped && this.limited) {\n      this.limitCropBox(true, true);\n    }\n  },\n  renderImage: function renderImage(changed) {\n    var canvasData = this.canvasData,\n        imageData = this.imageData;\n    var width = imageData.naturalWidth * (canvasData.width / canvasData.naturalWidth);\n    var height = imageData.naturalHeight * (canvasData.height / canvasData.naturalHeight);\n    assign(imageData, {\n      width: width,\n      height: height,\n      left: (canvasData.width - width) / 2,\n      top: (canvasData.height - height) / 2\n    });\n    setStyle(this.image, assign({\n      width: imageData.width,\n      height: imageData.height\n    }, getTransforms(assign({\n      translateX: imageData.left,\n      translateY: imageData.top\n    }, imageData))));\n\n    if (changed) {\n      this.output();\n    }\n  },\n  initCropBox: function initCropBox() {\n    var options = this.options,\n        canvasData = this.canvasData;\n    var aspectRatio = options.aspectRatio || options.initialAspectRatio;\n    var autoCropArea = Number(options.autoCropArea) || 0.8;\n    var cropBoxData = {\n      width: canvasData.width,\n      height: canvasData.height\n    };\n\n    if (aspectRatio) {\n      if (canvasData.height * aspectRatio > canvasData.width) {\n        cropBoxData.height = cropBoxData.width / aspectRatio;\n      } else {\n        cropBoxData.width = cropBoxData.height * aspectRatio;\n      }\n    }\n\n    this.cropBoxData = cropBoxData;\n    this.limitCropBox(true, true); // Initialize auto crop area\n\n    cropBoxData.width = Math.min(Math.max(cropBoxData.width, cropBoxData.minWidth), cropBoxData.maxWidth);\n    cropBoxData.height = Math.min(Math.max(cropBoxData.height, cropBoxData.minHeight), cropBoxData.maxHeight); // The width/height of auto crop area must large than \"minWidth/Height\"\n\n    cropBoxData.width = Math.max(cropBoxData.minWidth, cropBoxData.width * autoCropArea);\n    cropBoxData.height = Math.max(cropBoxData.minHeight, cropBoxData.height * autoCropArea);\n    cropBoxData.left = canvasData.left + (canvasData.width - cropBoxData.width) / 2;\n    cropBoxData.top = canvasData.top + (canvasData.height - cropBoxData.height) / 2;\n    cropBoxData.oldLeft = cropBoxData.left;\n    cropBoxData.oldTop = cropBoxData.top;\n    this.initialCropBoxData = assign({}, cropBoxData);\n  },\n  limitCropBox: function limitCropBox(sizeLimited, positionLimited) {\n    var options = this.options,\n        containerData = this.containerData,\n        canvasData = this.canvasData,\n        cropBoxData = this.cropBoxData,\n        limited = this.limited;\n    var aspectRatio = options.aspectRatio;\n\n    if (sizeLimited) {\n      var minCropBoxWidth = Number(options.minCropBoxWidth) || 0;\n      var minCropBoxHeight = Number(options.minCropBoxHeight) || 0;\n      var maxCropBoxWidth = limited ? Math.min(containerData.width, canvasData.width, canvasData.width + canvasData.left, containerData.width - canvasData.left) : containerData.width;\n      var maxCropBoxHeight = limited ? Math.min(containerData.height, canvasData.height, canvasData.height + canvasData.top, containerData.height - canvasData.top) : containerData.height; // The min/maxCropBoxWidth/Height must be less than container's width/height\n\n      minCropBoxWidth = Math.min(minCropBoxWidth, containerData.width);\n      minCropBoxHeight = Math.min(minCropBoxHeight, containerData.height);\n\n      if (aspectRatio) {\n        if (minCropBoxWidth && minCropBoxHeight) {\n          if (minCropBoxHeight * aspectRatio > minCropBoxWidth) {\n            minCropBoxHeight = minCropBoxWidth / aspectRatio;\n          } else {\n            minCropBoxWidth = minCropBoxHeight * aspectRatio;\n          }\n        } else if (minCropBoxWidth) {\n          minCropBoxHeight = minCropBoxWidth / aspectRatio;\n        } else if (minCropBoxHeight) {\n          minCropBoxWidth = minCropBoxHeight * aspectRatio;\n        }\n\n        if (maxCropBoxHeight * aspectRatio > maxCropBoxWidth) {\n          maxCropBoxHeight = maxCropBoxWidth / aspectRatio;\n        } else {\n          maxCropBoxWidth = maxCropBoxHeight * aspectRatio;\n        }\n      } // The minWidth/Height must be less than maxWidth/Height\n\n\n      cropBoxData.minWidth = Math.min(minCropBoxWidth, maxCropBoxWidth);\n      cropBoxData.minHeight = Math.min(minCropBoxHeight, maxCropBoxHeight);\n      cropBoxData.maxWidth = maxCropBoxWidth;\n      cropBoxData.maxHeight = maxCropBoxHeight;\n    }\n\n    if (positionLimited) {\n      if (limited) {\n        cropBoxData.minLeft = Math.max(0, canvasData.left);\n        cropBoxData.minTop = Math.max(0, canvasData.top);\n        cropBoxData.maxLeft = Math.min(containerData.width, canvasData.left + canvasData.width) - cropBoxData.width;\n        cropBoxData.maxTop = Math.min(containerData.height, canvasData.top + canvasData.height) - cropBoxData.height;\n      } else {\n        cropBoxData.minLeft = 0;\n        cropBoxData.minTop = 0;\n        cropBoxData.maxLeft = containerData.width - cropBoxData.width;\n        cropBoxData.maxTop = containerData.height - cropBoxData.height;\n      }\n    }\n  },\n  renderCropBox: function renderCropBox() {\n    var options = this.options,\n        containerData = this.containerData,\n        cropBoxData = this.cropBoxData;\n\n    if (cropBoxData.width > cropBoxData.maxWidth || cropBoxData.width < cropBoxData.minWidth) {\n      cropBoxData.left = cropBoxData.oldLeft;\n    }\n\n    if (cropBoxData.height > cropBoxData.maxHeight || cropBoxData.height < cropBoxData.minHeight) {\n      cropBoxData.top = cropBoxData.oldTop;\n    }\n\n    cropBoxData.width = Math.min(Math.max(cropBoxData.width, cropBoxData.minWidth), cropBoxData.maxWidth);\n    cropBoxData.height = Math.min(Math.max(cropBoxData.height, cropBoxData.minHeight), cropBoxData.maxHeight);\n    this.limitCropBox(false, true);\n    cropBoxData.left = Math.min(Math.max(cropBoxData.left, cropBoxData.minLeft), cropBoxData.maxLeft);\n    cropBoxData.top = Math.min(Math.max(cropBoxData.top, cropBoxData.minTop), cropBoxData.maxTop);\n    cropBoxData.oldLeft = cropBoxData.left;\n    cropBoxData.oldTop = cropBoxData.top;\n\n    if (options.movable && options.cropBoxMovable) {\n      // Turn to move the canvas when the crop box is equal to the container\n      setData(this.face, DATA_ACTION, cropBoxData.width >= containerData.width && cropBoxData.height >= containerData.height ? ACTION_MOVE : ACTION_ALL);\n    }\n\n    setStyle(this.cropBox, assign({\n      width: cropBoxData.width,\n      height: cropBoxData.height\n    }, getTransforms({\n      translateX: cropBoxData.left,\n      translateY: cropBoxData.top\n    })));\n\n    if (this.cropped && this.limited) {\n      this.limitCanvas(true, true);\n    }\n\n    if (!this.disabled) {\n      this.output();\n    }\n  },\n  output: function output() {\n    this.preview();\n    dispatchEvent(this.element, EVENT_CROP, this.getData());\n  }\n};\n\nvar preview = {\n  initPreview: function initPreview() {\n    var element = this.element,\n        crossOrigin = this.crossOrigin;\n    var preview = this.options.preview;\n    var url = crossOrigin ? this.crossOriginUrl : this.url;\n    var alt = element.alt || 'The image to preview';\n    var image = document.createElement('img');\n\n    if (crossOrigin) {\n      image.crossOrigin = crossOrigin;\n    }\n\n    image.src = url;\n    image.alt = alt;\n    this.viewBox.appendChild(image);\n    this.viewBoxImage = image;\n\n    if (!preview) {\n      return;\n    }\n\n    var previews = preview;\n\n    if (typeof preview === 'string') {\n      previews = element.ownerDocument.querySelectorAll(preview);\n    } else if (preview.querySelector) {\n      previews = [preview];\n    }\n\n    this.previews = previews;\n    forEach(previews, function (el) {\n      var img = document.createElement('img'); // Save the original size for recover\n\n      setData(el, DATA_PREVIEW, {\n        width: el.offsetWidth,\n        height: el.offsetHeight,\n        html: el.innerHTML\n      });\n\n      if (crossOrigin) {\n        img.crossOrigin = crossOrigin;\n      }\n\n      img.src = url;\n      img.alt = alt;\n      /**\n       * Override img element styles\n       * Add `display:block` to avoid margin top issue\n       * Add `height:auto` to override `height` attribute on IE8\n       * (Occur only when margin-top <= -height)\n       */\n\n      img.style.cssText = 'display:block;' + 'width:100%;' + 'height:auto;' + 'min-width:0!important;' + 'min-height:0!important;' + 'max-width:none!important;' + 'max-height:none!important;' + 'image-orientation:0deg!important;\"';\n      el.innerHTML = '';\n      el.appendChild(img);\n    });\n  },\n  resetPreview: function resetPreview() {\n    forEach(this.previews, function (element) {\n      var data = getData(element, DATA_PREVIEW);\n      setStyle(element, {\n        width: data.width,\n        height: data.height\n      });\n      element.innerHTML = data.html;\n      removeData(element, DATA_PREVIEW);\n    });\n  },\n  preview: function preview() {\n    var imageData = this.imageData,\n        canvasData = this.canvasData,\n        cropBoxData = this.cropBoxData;\n    var cropBoxWidth = cropBoxData.width,\n        cropBoxHeight = cropBoxData.height;\n    var width = imageData.width,\n        height = imageData.height;\n    var left = cropBoxData.left - canvasData.left - imageData.left;\n    var top = cropBoxData.top - canvasData.top - imageData.top;\n\n    if (!this.cropped || this.disabled) {\n      return;\n    }\n\n    setStyle(this.viewBoxImage, assign({\n      width: width,\n      height: height\n    }, getTransforms(assign({\n      translateX: -left,\n      translateY: -top\n    }, imageData))));\n    forEach(this.previews, function (element) {\n      var data = getData(element, DATA_PREVIEW);\n      var originalWidth = data.width;\n      var originalHeight = data.height;\n      var newWidth = originalWidth;\n      var newHeight = originalHeight;\n      var ratio = 1;\n\n      if (cropBoxWidth) {\n        ratio = originalWidth / cropBoxWidth;\n        newHeight = cropBoxHeight * ratio;\n      }\n\n      if (cropBoxHeight && newHeight > originalHeight) {\n        ratio = originalHeight / cropBoxHeight;\n        newWidth = cropBoxWidth * ratio;\n        newHeight = originalHeight;\n      }\n\n      setStyle(element, {\n        width: newWidth,\n        height: newHeight\n      });\n      setStyle(element.getElementsByTagName('img')[0], assign({\n        width: width * ratio,\n        height: height * ratio\n      }, getTransforms(assign({\n        translateX: -left * ratio,\n        translateY: -top * ratio\n      }, imageData))));\n    });\n  }\n};\n\nvar events = {\n  bind: function bind() {\n    var element = this.element,\n        options = this.options,\n        cropper = this.cropper;\n\n    if (isFunction(options.cropstart)) {\n      addListener(element, EVENT_CROP_START, options.cropstart);\n    }\n\n    if (isFunction(options.cropmove)) {\n      addListener(element, EVENT_CROP_MOVE, options.cropmove);\n    }\n\n    if (isFunction(options.cropend)) {\n      addListener(element, EVENT_CROP_END, options.cropend);\n    }\n\n    if (isFunction(options.crop)) {\n      addListener(element, EVENT_CROP, options.crop);\n    }\n\n    if (isFunction(options.zoom)) {\n      addListener(element, EVENT_ZOOM, options.zoom);\n    }\n\n    addListener(cropper, EVENT_POINTER_DOWN, this.onCropStart = this.cropStart.bind(this));\n\n    if (options.zoomable && options.zoomOnWheel) {\n      addListener(cropper, EVENT_WHEEL, this.onWheel = this.wheel.bind(this), {\n        passive: false,\n        capture: true\n      });\n    }\n\n    if (options.toggleDragModeOnDblclick) {\n      addListener(cropper, EVENT_DBLCLICK, this.onDblclick = this.dblclick.bind(this));\n    }\n\n    addListener(element.ownerDocument, EVENT_POINTER_MOVE, this.onCropMove = this.cropMove.bind(this));\n    addListener(element.ownerDocument, EVENT_POINTER_UP, this.onCropEnd = this.cropEnd.bind(this));\n\n    if (options.responsive) {\n      addListener(window, EVENT_RESIZE, this.onResize = this.resize.bind(this));\n    }\n  },\n  unbind: function unbind() {\n    var element = this.element,\n        options = this.options,\n        cropper = this.cropper;\n\n    if (isFunction(options.cropstart)) {\n      removeListener(element, EVENT_CROP_START, options.cropstart);\n    }\n\n    if (isFunction(options.cropmove)) {\n      removeListener(element, EVENT_CROP_MOVE, options.cropmove);\n    }\n\n    if (isFunction(options.cropend)) {\n      removeListener(element, EVENT_CROP_END, options.cropend);\n    }\n\n    if (isFunction(options.crop)) {\n      removeListener(element, EVENT_CROP, options.crop);\n    }\n\n    if (isFunction(options.zoom)) {\n      removeListener(element, EVENT_ZOOM, options.zoom);\n    }\n\n    removeListener(cropper, EVENT_POINTER_DOWN, this.onCropStart);\n\n    if (options.zoomable && options.zoomOnWheel) {\n      removeListener(cropper, EVENT_WHEEL, this.onWheel, {\n        passive: false,\n        capture: true\n      });\n    }\n\n    if (options.toggleDragModeOnDblclick) {\n      removeListener(cropper, EVENT_DBLCLICK, this.onDblclick);\n    }\n\n    removeListener(element.ownerDocument, EVENT_POINTER_MOVE, this.onCropMove);\n    removeListener(element.ownerDocument, EVENT_POINTER_UP, this.onCropEnd);\n\n    if (options.responsive) {\n      removeListener(window, EVENT_RESIZE, this.onResize);\n    }\n  }\n};\n\nvar handlers = {\n  resize: function resize() {\n    if (this.disabled) {\n      return;\n    }\n\n    var options = this.options,\n        container = this.container,\n        containerData = this.containerData;\n    var ratioX = container.offsetWidth / containerData.width;\n    var ratioY = container.offsetHeight / containerData.height;\n    var ratio = Math.abs(ratioX - 1) > Math.abs(ratioY - 1) ? ratioX : ratioY; // Resize when width changed or height changed\n\n    if (ratio !== 1) {\n      var canvasData;\n      var cropBoxData;\n\n      if (options.restore) {\n        canvasData = this.getCanvasData();\n        cropBoxData = this.getCropBoxData();\n      }\n\n      this.render();\n\n      if (options.restore) {\n        this.setCanvasData(forEach(canvasData, function (n, i) {\n          canvasData[i] = n * ratio;\n        }));\n        this.setCropBoxData(forEach(cropBoxData, function (n, i) {\n          cropBoxData[i] = n * ratio;\n        }));\n      }\n    }\n  },\n  dblclick: function dblclick() {\n    if (this.disabled || this.options.dragMode === DRAG_MODE_NONE) {\n      return;\n    }\n\n    this.setDragMode(hasClass(this.dragBox, CLASS_CROP) ? DRAG_MODE_MOVE : DRAG_MODE_CROP);\n  },\n  wheel: function wheel(event) {\n    var _this = this;\n\n    var ratio = Number(this.options.wheelZoomRatio) || 0.1;\n    var delta = 1;\n\n    if (this.disabled) {\n      return;\n    }\n\n    event.preventDefault(); // Limit wheel speed to prevent zoom too fast (#21)\n\n    if (this.wheeling) {\n      return;\n    }\n\n    this.wheeling = true;\n    setTimeout(function () {\n      _this.wheeling = false;\n    }, 50);\n\n    if (event.deltaY) {\n      delta = event.deltaY > 0 ? 1 : -1;\n    } else if (event.wheelDelta) {\n      delta = -event.wheelDelta / 120;\n    } else if (event.detail) {\n      delta = event.detail > 0 ? 1 : -1;\n    }\n\n    this.zoom(-delta * ratio, event);\n  },\n  cropStart: function cropStart(event) {\n    var buttons = event.buttons,\n        button = event.button;\n\n    if (this.disabled // Handle mouse event and pointer event and ignore touch event\n    || (event.type === 'mousedown' || event.type === 'pointerdown' && event.pointerType === 'mouse') && ( // No primary button (Usually the left button)\n    isNumber(buttons) && buttons !== 1 || isNumber(button) && button !== 0 // Open context menu\n    || event.ctrlKey)) {\n      return;\n    }\n\n    var options = this.options,\n        pointers = this.pointers;\n    var action;\n\n    if (event.changedTouches) {\n      // Handle touch event\n      forEach(event.changedTouches, function (touch) {\n        pointers[touch.identifier] = getPointer(touch);\n      });\n    } else {\n      // Handle mouse event and pointer event\n      pointers[event.pointerId || 0] = getPointer(event);\n    }\n\n    if (Object.keys(pointers).length > 1 && options.zoomable && options.zoomOnTouch) {\n      action = ACTION_ZOOM;\n    } else {\n      action = getData(event.target, DATA_ACTION);\n    }\n\n    if (!REGEXP_ACTIONS.test(action)) {\n      return;\n    }\n\n    if (dispatchEvent(this.element, EVENT_CROP_START, {\n      originalEvent: event,\n      action: action\n    }) === false) {\n      return;\n    } // This line is required for preventing page zooming in iOS browsers\n\n\n    event.preventDefault();\n    this.action = action;\n    this.cropping = false;\n\n    if (action === ACTION_CROP) {\n      this.cropping = true;\n      addClass(this.dragBox, CLASS_MODAL);\n    }\n  },\n  cropMove: function cropMove(event) {\n    var action = this.action;\n\n    if (this.disabled || !action) {\n      return;\n    }\n\n    var pointers = this.pointers;\n    event.preventDefault();\n\n    if (dispatchEvent(this.element, EVENT_CROP_MOVE, {\n      originalEvent: event,\n      action: action\n    }) === false) {\n      return;\n    }\n\n    if (event.changedTouches) {\n      forEach(event.changedTouches, function (touch) {\n        // The first parameter should not be undefined (#432)\n        assign(pointers[touch.identifier] || {}, getPointer(touch, true));\n      });\n    } else {\n      assign(pointers[event.pointerId || 0] || {}, getPointer(event, true));\n    }\n\n    this.change(event);\n  },\n  cropEnd: function cropEnd(event) {\n    if (this.disabled) {\n      return;\n    }\n\n    var action = this.action,\n        pointers = this.pointers;\n\n    if (event.changedTouches) {\n      forEach(event.changedTouches, function (touch) {\n        delete pointers[touch.identifier];\n      });\n    } else {\n      delete pointers[event.pointerId || 0];\n    }\n\n    if (!action) {\n      return;\n    }\n\n    event.preventDefault();\n\n    if (!Object.keys(pointers).length) {\n      this.action = '';\n    }\n\n    if (this.cropping) {\n      this.cropping = false;\n      toggleClass(this.dragBox, CLASS_MODAL, this.cropped && this.options.modal);\n    }\n\n    dispatchEvent(this.element, EVENT_CROP_END, {\n      originalEvent: event,\n      action: action\n    });\n  }\n};\n\nvar change = {\n  change: function change(event) {\n    var options = this.options,\n        canvasData = this.canvasData,\n        containerData = this.containerData,\n        cropBoxData = this.cropBoxData,\n        pointers = this.pointers;\n    var action = this.action;\n    var aspectRatio = options.aspectRatio;\n    var left = cropBoxData.left,\n        top = cropBoxData.top,\n        width = cropBoxData.width,\n        height = cropBoxData.height;\n    var right = left + width;\n    var bottom = top + height;\n    var minLeft = 0;\n    var minTop = 0;\n    var maxWidth = containerData.width;\n    var maxHeight = containerData.height;\n    var renderable = true;\n    var offset; // Locking aspect ratio in \"free mode\" by holding shift key\n\n    if (!aspectRatio && event.shiftKey) {\n      aspectRatio = width && height ? width / height : 1;\n    }\n\n    if (this.limited) {\n      minLeft = cropBoxData.minLeft;\n      minTop = cropBoxData.minTop;\n      maxWidth = minLeft + Math.min(containerData.width, canvasData.width, canvasData.left + canvasData.width);\n      maxHeight = minTop + Math.min(containerData.height, canvasData.height, canvasData.top + canvasData.height);\n    }\n\n    var pointer = pointers[Object.keys(pointers)[0]];\n    var range = {\n      x: pointer.endX - pointer.startX,\n      y: pointer.endY - pointer.startY\n    };\n\n    var check = function check(side) {\n      switch (side) {\n        case ACTION_EAST:\n          if (right + range.x > maxWidth) {\n            range.x = maxWidth - right;\n          }\n\n          break;\n\n        case ACTION_WEST:\n          if (left + range.x < minLeft) {\n            range.x = minLeft - left;\n          }\n\n          break;\n\n        case ACTION_NORTH:\n          if (top + range.y < minTop) {\n            range.y = minTop - top;\n          }\n\n          break;\n\n        case ACTION_SOUTH:\n          if (bottom + range.y > maxHeight) {\n            range.y = maxHeight - bottom;\n          }\n\n          break;\n      }\n    };\n\n    switch (action) {\n      // Move crop box\n      case ACTION_ALL:\n        left += range.x;\n        top += range.y;\n        break;\n      // Resize crop box\n\n      case ACTION_EAST:\n        if (range.x >= 0 && (right >= maxWidth || aspectRatio && (top <= minTop || bottom >= maxHeight))) {\n          renderable = false;\n          break;\n        }\n\n        check(ACTION_EAST);\n        width += range.x;\n\n        if (width < 0) {\n          action = ACTION_WEST;\n          width = -width;\n          left -= width;\n        }\n\n        if (aspectRatio) {\n          height = width / aspectRatio;\n          top += (cropBoxData.height - height) / 2;\n        }\n\n        break;\n\n      case ACTION_NORTH:\n        if (range.y <= 0 && (top <= minTop || aspectRatio && (left <= minLeft || right >= maxWidth))) {\n          renderable = false;\n          break;\n        }\n\n        check(ACTION_NORTH);\n        height -= range.y;\n        top += range.y;\n\n        if (height < 0) {\n          action = ACTION_SOUTH;\n          height = -height;\n          top -= height;\n        }\n\n        if (aspectRatio) {\n          width = height * aspectRatio;\n          left += (cropBoxData.width - width) / 2;\n        }\n\n        break;\n\n      case ACTION_WEST:\n        if (range.x <= 0 && (left <= minLeft || aspectRatio && (top <= minTop || bottom >= maxHeight))) {\n          renderable = false;\n          break;\n        }\n\n        check(ACTION_WEST);\n        width -= range.x;\n        left += range.x;\n\n        if (width < 0) {\n          action = ACTION_EAST;\n          width = -width;\n          left -= width;\n        }\n\n        if (aspectRatio) {\n          height = width / aspectRatio;\n          top += (cropBoxData.height - height) / 2;\n        }\n\n        break;\n\n      case ACTION_SOUTH:\n        if (range.y >= 0 && (bottom >= maxHeight || aspectRatio && (left <= minLeft || right >= maxWidth))) {\n          renderable = false;\n          break;\n        }\n\n        check(ACTION_SOUTH);\n        height += range.y;\n\n        if (height < 0) {\n          action = ACTION_NORTH;\n          height = -height;\n          top -= height;\n        }\n\n        if (aspectRatio) {\n          width = height * aspectRatio;\n          left += (cropBoxData.width - width) / 2;\n        }\n\n        break;\n\n      case ACTION_NORTH_EAST:\n        if (aspectRatio) {\n          if (range.y <= 0 && (top <= minTop || right >= maxWidth)) {\n            renderable = false;\n            break;\n          }\n\n          check(ACTION_NORTH);\n          height -= range.y;\n          top += range.y;\n          width = height * aspectRatio;\n        } else {\n          check(ACTION_NORTH);\n          check(ACTION_EAST);\n\n          if (range.x >= 0) {\n            if (right < maxWidth) {\n              width += range.x;\n            } else if (range.y <= 0 && top <= minTop) {\n              renderable = false;\n            }\n          } else {\n            width += range.x;\n          }\n\n          if (range.y <= 0) {\n            if (top > minTop) {\n              height -= range.y;\n              top += range.y;\n            }\n          } else {\n            height -= range.y;\n            top += range.y;\n          }\n        }\n\n        if (width < 0 && height < 0) {\n          action = ACTION_SOUTH_WEST;\n          height = -height;\n          width = -width;\n          top -= height;\n          left -= width;\n        } else if (width < 0) {\n          action = ACTION_NORTH_WEST;\n          width = -width;\n          left -= width;\n        } else if (height < 0) {\n          action = ACTION_SOUTH_EAST;\n          height = -height;\n          top -= height;\n        }\n\n        break;\n\n      case ACTION_NORTH_WEST:\n        if (aspectRatio) {\n          if (range.y <= 0 && (top <= minTop || left <= minLeft)) {\n            renderable = false;\n            break;\n          }\n\n          check(ACTION_NORTH);\n          height -= range.y;\n          top += range.y;\n          width = height * aspectRatio;\n          left += cropBoxData.width - width;\n        } else {\n          check(ACTION_NORTH);\n          check(ACTION_WEST);\n\n          if (range.x <= 0) {\n            if (left > minLeft) {\n              width -= range.x;\n              left += range.x;\n            } else if (range.y <= 0 && top <= minTop) {\n              renderable = false;\n            }\n          } else {\n            width -= range.x;\n            left += range.x;\n          }\n\n          if (range.y <= 0) {\n            if (top > minTop) {\n              height -= range.y;\n              top += range.y;\n            }\n          } else {\n            height -= range.y;\n            top += range.y;\n          }\n        }\n\n        if (width < 0 && height < 0) {\n          action = ACTION_SOUTH_EAST;\n          height = -height;\n          width = -width;\n          top -= height;\n          left -= width;\n        } else if (width < 0) {\n          action = ACTION_NORTH_EAST;\n          width = -width;\n          left -= width;\n        } else if (height < 0) {\n          action = ACTION_SOUTH_WEST;\n          height = -height;\n          top -= height;\n        }\n\n        break;\n\n      case ACTION_SOUTH_WEST:\n        if (aspectRatio) {\n          if (range.x <= 0 && (left <= minLeft || bottom >= maxHeight)) {\n            renderable = false;\n            break;\n          }\n\n          check(ACTION_WEST);\n          width -= range.x;\n          left += range.x;\n          height = width / aspectRatio;\n        } else {\n          check(ACTION_SOUTH);\n          check(ACTION_WEST);\n\n          if (range.x <= 0) {\n            if (left > minLeft) {\n              width -= range.x;\n              left += range.x;\n            } else if (range.y >= 0 && bottom >= maxHeight) {\n              renderable = false;\n            }\n          } else {\n            width -= range.x;\n            left += range.x;\n          }\n\n          if (range.y >= 0) {\n            if (bottom < maxHeight) {\n              height += range.y;\n            }\n          } else {\n            height += range.y;\n          }\n        }\n\n        if (width < 0 && height < 0) {\n          action = ACTION_NORTH_EAST;\n          height = -height;\n          width = -width;\n          top -= height;\n          left -= width;\n        } else if (width < 0) {\n          action = ACTION_SOUTH_EAST;\n          width = -width;\n          left -= width;\n        } else if (height < 0) {\n          action = ACTION_NORTH_WEST;\n          height = -height;\n          top -= height;\n        }\n\n        break;\n\n      case ACTION_SOUTH_EAST:\n        if (aspectRatio) {\n          if (range.x >= 0 && (right >= maxWidth || bottom >= maxHeight)) {\n            renderable = false;\n            break;\n          }\n\n          check(ACTION_EAST);\n          width += range.x;\n          height = width / aspectRatio;\n        } else {\n          check(ACTION_SOUTH);\n          check(ACTION_EAST);\n\n          if (range.x >= 0) {\n            if (right < maxWidth) {\n              width += range.x;\n            } else if (range.y >= 0 && bottom >= maxHeight) {\n              renderable = false;\n            }\n          } else {\n            width += range.x;\n          }\n\n          if (range.y >= 0) {\n            if (bottom < maxHeight) {\n              height += range.y;\n            }\n          } else {\n            height += range.y;\n          }\n        }\n\n        if (width < 0 && height < 0) {\n          action = ACTION_NORTH_WEST;\n          height = -height;\n          width = -width;\n          top -= height;\n          left -= width;\n        } else if (width < 0) {\n          action = ACTION_SOUTH_WEST;\n          width = -width;\n          left -= width;\n        } else if (height < 0) {\n          action = ACTION_NORTH_EAST;\n          height = -height;\n          top -= height;\n        }\n\n        break;\n      // Move canvas\n\n      case ACTION_MOVE:\n        this.move(range.x, range.y);\n        renderable = false;\n        break;\n      // Zoom canvas\n\n      case ACTION_ZOOM:\n        this.zoom(getMaxZoomRatio(pointers), event);\n        renderable = false;\n        break;\n      // Create crop box\n\n      case ACTION_CROP:\n        if (!range.x || !range.y) {\n          renderable = false;\n          break;\n        }\n\n        offset = getOffset(this.cropper);\n        left = pointer.startX - offset.left;\n        top = pointer.startY - offset.top;\n        width = cropBoxData.minWidth;\n        height = cropBoxData.minHeight;\n\n        if (range.x > 0) {\n          action = range.y > 0 ? ACTION_SOUTH_EAST : ACTION_NORTH_EAST;\n        } else if (range.x < 0) {\n          left -= width;\n          action = range.y > 0 ? ACTION_SOUTH_WEST : ACTION_NORTH_WEST;\n        }\n\n        if (range.y < 0) {\n          top -= height;\n        } // Show the crop box if is hidden\n\n\n        if (!this.cropped) {\n          removeClass(this.cropBox, CLASS_HIDDEN);\n          this.cropped = true;\n\n          if (this.limited) {\n            this.limitCropBox(true, true);\n          }\n        }\n\n        break;\n    }\n\n    if (renderable) {\n      cropBoxData.width = width;\n      cropBoxData.height = height;\n      cropBoxData.left = left;\n      cropBoxData.top = top;\n      this.action = action;\n      this.renderCropBox();\n    } // Override\n\n\n    forEach(pointers, function (p) {\n      p.startX = p.endX;\n      p.startY = p.endY;\n    });\n  }\n};\n\nvar methods = {\n  // Show the crop box manually\n  crop: function crop() {\n    if (this.ready && !this.cropped && !this.disabled) {\n      this.cropped = true;\n      this.limitCropBox(true, true);\n\n      if (this.options.modal) {\n        addClass(this.dragBox, CLASS_MODAL);\n      }\n\n      removeClass(this.cropBox, CLASS_HIDDEN);\n      this.setCropBoxData(this.initialCropBoxData);\n    }\n\n    return this;\n  },\n  // Reset the image and crop box to their initial states\n  reset: function reset() {\n    if (this.ready && !this.disabled) {\n      this.imageData = assign({}, this.initialImageData);\n      this.canvasData = assign({}, this.initialCanvasData);\n      this.cropBoxData = assign({}, this.initialCropBoxData);\n      this.renderCanvas();\n\n      if (this.cropped) {\n        this.renderCropBox();\n      }\n    }\n\n    return this;\n  },\n  // Clear the crop box\n  clear: function clear() {\n    if (this.cropped && !this.disabled) {\n      assign(this.cropBoxData, {\n        left: 0,\n        top: 0,\n        width: 0,\n        height: 0\n      });\n      this.cropped = false;\n      this.renderCropBox();\n      this.limitCanvas(true, true); // Render canvas after crop box rendered\n\n      this.renderCanvas();\n      removeClass(this.dragBox, CLASS_MODAL);\n      addClass(this.cropBox, CLASS_HIDDEN);\n    }\n\n    return this;\n  },\n\n  /**\n   * Replace the image's src and rebuild the cropper\n   * @param {string} url - The new URL.\n   * @param {boolean} [hasSameSize] - Indicate if the new image has the same size as the old one.\n   * @returns {Cropper} this\n   */\n  replace: function replace(url) {\n    var hasSameSize = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n    if (!this.disabled && url) {\n      if (this.isImg) {\n        this.element.src = url;\n      }\n\n      if (hasSameSize) {\n        this.url = url;\n        this.image.src = url;\n\n        if (this.ready) {\n          this.viewBoxImage.src = url;\n          forEach(this.previews, function (element) {\n            element.getElementsByTagName('img')[0].src = url;\n          });\n        }\n      } else {\n        if (this.isImg) {\n          this.replaced = true;\n        }\n\n        this.options.data = null;\n        this.uncreate();\n        this.load(url);\n      }\n    }\n\n    return this;\n  },\n  // Enable (unfreeze) the cropper\n  enable: function enable() {\n    if (this.ready && this.disabled) {\n      this.disabled = false;\n      removeClass(this.cropper, CLASS_DISABLED);\n    }\n\n    return this;\n  },\n  // Disable (freeze) the cropper\n  disable: function disable() {\n    if (this.ready && !this.disabled) {\n      this.disabled = true;\n      addClass(this.cropper, CLASS_DISABLED);\n    }\n\n    return this;\n  },\n\n  /**\n   * Destroy the cropper and remove the instance from the image\n   * @returns {Cropper} this\n   */\n  destroy: function destroy() {\n    var element = this.element;\n\n    if (!element[NAMESPACE]) {\n      return this;\n    }\n\n    element[NAMESPACE] = undefined;\n\n    if (this.isImg && this.replaced) {\n      element.src = this.originalUrl;\n    }\n\n    this.uncreate();\n    return this;\n  },\n\n  /**\n   * Move the canvas with relative offsets\n   * @param {number} offsetX - The relative offset distance on the x-axis.\n   * @param {number} [offsetY=offsetX] - The relative offset distance on the y-axis.\n   * @returns {Cropper} this\n   */\n  move: function move(offsetX) {\n    var offsetY = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : offsetX;\n    var _this$canvasData = this.canvasData,\n        left = _this$canvasData.left,\n        top = _this$canvasData.top;\n    return this.moveTo(isUndefined(offsetX) ? offsetX : left + Number(offsetX), isUndefined(offsetY) ? offsetY : top + Number(offsetY));\n  },\n\n  /**\n   * Move the canvas to an absolute point\n   * @param {number} x - The x-axis coordinate.\n   * @param {number} [y=x] - The y-axis coordinate.\n   * @returns {Cropper} this\n   */\n  moveTo: function moveTo(x) {\n    var y = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : x;\n    var canvasData = this.canvasData;\n    var changed = false;\n    x = Number(x);\n    y = Number(y);\n\n    if (this.ready && !this.disabled && this.options.movable) {\n      if (isNumber(x)) {\n        canvasData.left = x;\n        changed = true;\n      }\n\n      if (isNumber(y)) {\n        canvasData.top = y;\n        changed = true;\n      }\n\n      if (changed) {\n        this.renderCanvas(true);\n      }\n    }\n\n    return this;\n  },\n\n  /**\n   * Zoom the canvas with a relative ratio\n   * @param {number} ratio - The target ratio.\n   * @param {Event} _originalEvent - The original event if any.\n   * @returns {Cropper} this\n   */\n  zoom: function zoom(ratio, _originalEvent) {\n    var canvasData = this.canvasData;\n    ratio = Number(ratio);\n\n    if (ratio < 0) {\n      ratio = 1 / (1 - ratio);\n    } else {\n      ratio = 1 + ratio;\n    }\n\n    return this.zoomTo(canvasData.width * ratio / canvasData.naturalWidth, null, _originalEvent);\n  },\n\n  /**\n   * Zoom the canvas to an absolute ratio\n   * @param {number} ratio - The target ratio.\n   * @param {Object} pivot - The zoom pivot point coordinate.\n   * @param {Event} _originalEvent - The original event if any.\n   * @returns {Cropper} this\n   */\n  zoomTo: function zoomTo(ratio, pivot, _originalEvent) {\n    var options = this.options,\n        canvasData = this.canvasData;\n    var width = canvasData.width,\n        height = canvasData.height,\n        naturalWidth = canvasData.naturalWidth,\n        naturalHeight = canvasData.naturalHeight;\n    ratio = Number(ratio);\n\n    if (ratio >= 0 && this.ready && !this.disabled && options.zoomable) {\n      var newWidth = naturalWidth * ratio;\n      var newHeight = naturalHeight * ratio;\n\n      if (dispatchEvent(this.element, EVENT_ZOOM, {\n        ratio: ratio,\n        oldRatio: width / naturalWidth,\n        originalEvent: _originalEvent\n      }) === false) {\n        return this;\n      }\n\n      if (_originalEvent) {\n        var pointers = this.pointers;\n        var offset = getOffset(this.cropper);\n        var center = pointers && Object.keys(pointers).length ? getPointersCenter(pointers) : {\n          pageX: _originalEvent.pageX,\n          pageY: _originalEvent.pageY\n        }; // Zoom from the triggering point of the event\n\n        canvasData.left -= (newWidth - width) * ((center.pageX - offset.left - canvasData.left) / width);\n        canvasData.top -= (newHeight - height) * ((center.pageY - offset.top - canvasData.top) / height);\n      } else if (isPlainObject(pivot) && isNumber(pivot.x) && isNumber(pivot.y)) {\n        canvasData.left -= (newWidth - width) * ((pivot.x - canvasData.left) / width);\n        canvasData.top -= (newHeight - height) * ((pivot.y - canvasData.top) / height);\n      } else {\n        // Zoom from the center of the canvas\n        canvasData.left -= (newWidth - width) / 2;\n        canvasData.top -= (newHeight - height) / 2;\n      }\n\n      canvasData.width = newWidth;\n      canvasData.height = newHeight;\n      this.renderCanvas(true);\n    }\n\n    return this;\n  },\n\n  /**\n   * Rotate the canvas with a relative degree\n   * @param {number} degree - The rotate degree.\n   * @returns {Cropper} this\n   */\n  rotate: function rotate(degree) {\n    return this.rotateTo((this.imageData.rotate || 0) + Number(degree));\n  },\n\n  /**\n   * Rotate the canvas to an absolute degree\n   * @param {number} degree - The rotate degree.\n   * @returns {Cropper} this\n   */\n  rotateTo: function rotateTo(degree) {\n    degree = Number(degree);\n\n    if (isNumber(degree) && this.ready && !this.disabled && this.options.rotatable) {\n      this.imageData.rotate = degree % 360;\n      this.renderCanvas(true, true);\n    }\n\n    return this;\n  },\n\n  /**\n   * Scale the image on the x-axis.\n   * @param {number} scaleX - The scale ratio on the x-axis.\n   * @returns {Cropper} this\n   */\n  scaleX: function scaleX(_scaleX) {\n    var scaleY = this.imageData.scaleY;\n    return this.scale(_scaleX, isNumber(scaleY) ? scaleY : 1);\n  },\n\n  /**\n   * Scale the image on the y-axis.\n   * @param {number} scaleY - The scale ratio on the y-axis.\n   * @returns {Cropper} this\n   */\n  scaleY: function scaleY(_scaleY) {\n    var scaleX = this.imageData.scaleX;\n    return this.scale(isNumber(scaleX) ? scaleX : 1, _scaleY);\n  },\n\n  /**\n   * Scale the image\n   * @param {number} scaleX - The scale ratio on the x-axis.\n   * @param {number} [scaleY=scaleX] - The scale ratio on the y-axis.\n   * @returns {Cropper} this\n   */\n  scale: function scale(scaleX) {\n    var scaleY = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : scaleX;\n    var imageData = this.imageData;\n    var transformed = false;\n    scaleX = Number(scaleX);\n    scaleY = Number(scaleY);\n\n    if (this.ready && !this.disabled && this.options.scalable) {\n      if (isNumber(scaleX)) {\n        imageData.scaleX = scaleX;\n        transformed = true;\n      }\n\n      if (isNumber(scaleY)) {\n        imageData.scaleY = scaleY;\n        transformed = true;\n      }\n\n      if (transformed) {\n        this.renderCanvas(true, true);\n      }\n    }\n\n    return this;\n  },\n\n  /**\n   * Get the cropped area position and size data (base on the original image)\n   * @param {boolean} [rounded=false] - Indicate if round the data values or not.\n   * @returns {Object} The result cropped data.\n   */\n  getData: function getData() {\n    var rounded = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var options = this.options,\n        imageData = this.imageData,\n        canvasData = this.canvasData,\n        cropBoxData = this.cropBoxData;\n    var data;\n\n    if (this.ready && this.cropped) {\n      data = {\n        x: cropBoxData.left - canvasData.left,\n        y: cropBoxData.top - canvasData.top,\n        width: cropBoxData.width,\n        height: cropBoxData.height\n      };\n      var ratio = imageData.width / imageData.naturalWidth;\n      forEach(data, function (n, i) {\n        data[i] = n / ratio;\n      });\n\n      if (rounded) {\n        // In case rounding off leads to extra 1px in right or bottom border\n        // we should round the top-left corner and the dimension (#343).\n        var bottom = Math.round(data.y + data.height);\n        var right = Math.round(data.x + data.width);\n        data.x = Math.round(data.x);\n        data.y = Math.round(data.y);\n        data.width = right - data.x;\n        data.height = bottom - data.y;\n      }\n    } else {\n      data = {\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n      };\n    }\n\n    if (options.rotatable) {\n      data.rotate = imageData.rotate || 0;\n    }\n\n    if (options.scalable) {\n      data.scaleX = imageData.scaleX || 1;\n      data.scaleY = imageData.scaleY || 1;\n    }\n\n    return data;\n  },\n\n  /**\n   * Set the cropped area position and size with new data\n   * @param {Object} data - The new data.\n   * @returns {Cropper} this\n   */\n  setData: function setData(data) {\n    var options = this.options,\n        imageData = this.imageData,\n        canvasData = this.canvasData;\n    var cropBoxData = {};\n\n    if (this.ready && !this.disabled && isPlainObject(data)) {\n      var transformed = false;\n\n      if (options.rotatable) {\n        if (isNumber(data.rotate) && data.rotate !== imageData.rotate) {\n          imageData.rotate = data.rotate;\n          transformed = true;\n        }\n      }\n\n      if (options.scalable) {\n        if (isNumber(data.scaleX) && data.scaleX !== imageData.scaleX) {\n          imageData.scaleX = data.scaleX;\n          transformed = true;\n        }\n\n        if (isNumber(data.scaleY) && data.scaleY !== imageData.scaleY) {\n          imageData.scaleY = data.scaleY;\n          transformed = true;\n        }\n      }\n\n      if (transformed) {\n        this.renderCanvas(true, true);\n      }\n\n      var ratio = imageData.width / imageData.naturalWidth;\n\n      if (isNumber(data.x)) {\n        cropBoxData.left = data.x * ratio + canvasData.left;\n      }\n\n      if (isNumber(data.y)) {\n        cropBoxData.top = data.y * ratio + canvasData.top;\n      }\n\n      if (isNumber(data.width)) {\n        cropBoxData.width = data.width * ratio;\n      }\n\n      if (isNumber(data.height)) {\n        cropBoxData.height = data.height * ratio;\n      }\n\n      this.setCropBoxData(cropBoxData);\n    }\n\n    return this;\n  },\n\n  /**\n   * Get the container size data.\n   * @returns {Object} The result container data.\n   */\n  getContainerData: function getContainerData() {\n    return this.ready ? assign({}, this.containerData) : {};\n  },\n\n  /**\n   * Get the image position and size data.\n   * @returns {Object} The result image data.\n   */\n  getImageData: function getImageData() {\n    return this.sized ? assign({}, this.imageData) : {};\n  },\n\n  /**\n   * Get the canvas position and size data.\n   * @returns {Object} The result canvas data.\n   */\n  getCanvasData: function getCanvasData() {\n    var canvasData = this.canvasData;\n    var data = {};\n\n    if (this.ready) {\n      forEach(['left', 'top', 'width', 'height', 'naturalWidth', 'naturalHeight'], function (n) {\n        data[n] = canvasData[n];\n      });\n    }\n\n    return data;\n  },\n\n  /**\n   * Set the canvas position and size with new data.\n   * @param {Object} data - The new canvas data.\n   * @returns {Cropper} this\n   */\n  setCanvasData: function setCanvasData(data) {\n    var canvasData = this.canvasData;\n    var aspectRatio = canvasData.aspectRatio;\n\n    if (this.ready && !this.disabled && isPlainObject(data)) {\n      if (isNumber(data.left)) {\n        canvasData.left = data.left;\n      }\n\n      if (isNumber(data.top)) {\n        canvasData.top = data.top;\n      }\n\n      if (isNumber(data.width)) {\n        canvasData.width = data.width;\n        canvasData.height = data.width / aspectRatio;\n      } else if (isNumber(data.height)) {\n        canvasData.height = data.height;\n        canvasData.width = data.height * aspectRatio;\n      }\n\n      this.renderCanvas(true);\n    }\n\n    return this;\n  },\n\n  /**\n   * Get the crop box position and size data.\n   * @returns {Object} The result crop box data.\n   */\n  getCropBoxData: function getCropBoxData() {\n    var cropBoxData = this.cropBoxData;\n    var data;\n\n    if (this.ready && this.cropped) {\n      data = {\n        left: cropBoxData.left,\n        top: cropBoxData.top,\n        width: cropBoxData.width,\n        height: cropBoxData.height\n      };\n    }\n\n    return data || {};\n  },\n\n  /**\n   * Set the crop box position and size with new data.\n   * @param {Object} data - The new crop box data.\n   * @returns {Cropper} this\n   */\n  setCropBoxData: function setCropBoxData(data) {\n    var cropBoxData = this.cropBoxData;\n    var aspectRatio = this.options.aspectRatio;\n    var widthChanged;\n    var heightChanged;\n\n    if (this.ready && this.cropped && !this.disabled && isPlainObject(data)) {\n      if (isNumber(data.left)) {\n        cropBoxData.left = data.left;\n      }\n\n      if (isNumber(data.top)) {\n        cropBoxData.top = data.top;\n      }\n\n      if (isNumber(data.width) && data.width !== cropBoxData.width) {\n        widthChanged = true;\n        cropBoxData.width = data.width;\n      }\n\n      if (isNumber(data.height) && data.height !== cropBoxData.height) {\n        heightChanged = true;\n        cropBoxData.height = data.height;\n      }\n\n      if (aspectRatio) {\n        if (widthChanged) {\n          cropBoxData.height = cropBoxData.width / aspectRatio;\n        } else if (heightChanged) {\n          cropBoxData.width = cropBoxData.height * aspectRatio;\n        }\n      }\n\n      this.renderCropBox();\n    }\n\n    return this;\n  },\n\n  /**\n   * Get a canvas drawn the cropped image.\n   * @param {Object} [options={}] - The config options.\n   * @returns {HTMLCanvasElement} - The result canvas.\n   */\n  getCroppedCanvas: function getCroppedCanvas() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n    if (!this.ready || !window.HTMLCanvasElement) {\n      return null;\n    }\n\n    var canvasData = this.canvasData;\n    var source = getSourceCanvas(this.image, this.imageData, canvasData, options); // Returns the source canvas if it is not cropped.\n\n    if (!this.cropped) {\n      return source;\n    }\n\n    var _this$getData = this.getData(),\n        initialX = _this$getData.x,\n        initialY = _this$getData.y,\n        initialWidth = _this$getData.width,\n        initialHeight = _this$getData.height;\n\n    var ratio = source.width / Math.floor(canvasData.naturalWidth);\n\n    if (ratio !== 1) {\n      initialX *= ratio;\n      initialY *= ratio;\n      initialWidth *= ratio;\n      initialHeight *= ratio;\n    }\n\n    var aspectRatio = initialWidth / initialHeight;\n    var maxSizes = getAdjustedSizes({\n      aspectRatio: aspectRatio,\n      width: options.maxWidth || Infinity,\n      height: options.maxHeight || Infinity\n    });\n    var minSizes = getAdjustedSizes({\n      aspectRatio: aspectRatio,\n      width: options.minWidth || 0,\n      height: options.minHeight || 0\n    }, 'cover');\n\n    var _getAdjustedSizes = getAdjustedSizes({\n      aspectRatio: aspectRatio,\n      width: options.width || (ratio !== 1 ? source.width : initialWidth),\n      height: options.height || (ratio !== 1 ? source.height : initialHeight)\n    }),\n        width = _getAdjustedSizes.width,\n        height = _getAdjustedSizes.height;\n\n    width = Math.min(maxSizes.width, Math.max(minSizes.width, width));\n    height = Math.min(maxSizes.height, Math.max(minSizes.height, height));\n    var canvas = document.createElement('canvas');\n    var context = canvas.getContext('2d');\n    canvas.width = normalizeDecimalNumber(width);\n    canvas.height = normalizeDecimalNumber(height);\n    context.fillStyle = options.fillColor || 'transparent';\n    context.fillRect(0, 0, width, height);\n    var _options$imageSmoothi = options.imageSmoothingEnabled,\n        imageSmoothingEnabled = _options$imageSmoothi === void 0 ? true : _options$imageSmoothi,\n        imageSmoothingQuality = options.imageSmoothingQuality;\n    context.imageSmoothingEnabled = imageSmoothingEnabled;\n\n    if (imageSmoothingQuality) {\n      context.imageSmoothingQuality = imageSmoothingQuality;\n    } // https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D.drawImage\n\n\n    var sourceWidth = source.width;\n    var sourceHeight = source.height; // Source canvas parameters\n\n    var srcX = initialX;\n    var srcY = initialY;\n    var srcWidth;\n    var srcHeight; // Destination canvas parameters\n\n    var dstX;\n    var dstY;\n    var dstWidth;\n    var dstHeight;\n\n    if (srcX <= -initialWidth || srcX > sourceWidth) {\n      srcX = 0;\n      srcWidth = 0;\n      dstX = 0;\n      dstWidth = 0;\n    } else if (srcX <= 0) {\n      dstX = -srcX;\n      srcX = 0;\n      srcWidth = Math.min(sourceWidth, initialWidth + srcX);\n      dstWidth = srcWidth;\n    } else if (srcX <= sourceWidth) {\n      dstX = 0;\n      srcWidth = Math.min(initialWidth, sourceWidth - srcX);\n      dstWidth = srcWidth;\n    }\n\n    if (srcWidth <= 0 || srcY <= -initialHeight || srcY > sourceHeight) {\n      srcY = 0;\n      srcHeight = 0;\n      dstY = 0;\n      dstHeight = 0;\n    } else if (srcY <= 0) {\n      dstY = -srcY;\n      srcY = 0;\n      srcHeight = Math.min(sourceHeight, initialHeight + srcY);\n      dstHeight = srcHeight;\n    } else if (srcY <= sourceHeight) {\n      dstY = 0;\n      srcHeight = Math.min(initialHeight, sourceHeight - srcY);\n      dstHeight = srcHeight;\n    }\n\n    var params = [srcX, srcY, srcWidth, srcHeight]; // Avoid \"IndexSizeError\"\n\n    if (dstWidth > 0 && dstHeight > 0) {\n      var scale = width / initialWidth;\n      params.push(dstX * scale, dstY * scale, dstWidth * scale, dstHeight * scale);\n    } // All the numerical parameters should be integer for `drawImage`\n    // https://github.com/fengyuanchen/cropper/issues/476\n\n\n    context.drawImage.apply(context, [source].concat(_toConsumableArray(params.map(function (param) {\n      return Math.floor(normalizeDecimalNumber(param));\n    }))));\n    return canvas;\n  },\n\n  /**\n   * Change the aspect ratio of the crop box.\n   * @param {number} aspectRatio - The new aspect ratio.\n   * @returns {Cropper} this\n   */\n  setAspectRatio: function setAspectRatio(aspectRatio) {\n    var options = this.options;\n\n    if (!this.disabled && !isUndefined(aspectRatio)) {\n      // 0 -> NaN\n      options.aspectRatio = Math.max(0, aspectRatio) || NaN;\n\n      if (this.ready) {\n        this.initCropBox();\n\n        if (this.cropped) {\n          this.renderCropBox();\n        }\n      }\n    }\n\n    return this;\n  },\n\n  /**\n   * Change the drag mode.\n   * @param {string} mode - The new drag mode.\n   * @returns {Cropper} this\n   */\n  setDragMode: function setDragMode(mode) {\n    var options = this.options,\n        dragBox = this.dragBox,\n        face = this.face;\n\n    if (this.ready && !this.disabled) {\n      var croppable = mode === DRAG_MODE_CROP;\n      var movable = options.movable && mode === DRAG_MODE_MOVE;\n      mode = croppable || movable ? mode : DRAG_MODE_NONE;\n      options.dragMode = mode;\n      setData(dragBox, DATA_ACTION, mode);\n      toggleClass(dragBox, CLASS_CROP, croppable);\n      toggleClass(dragBox, CLASS_MOVE, movable);\n\n      if (!options.cropBoxMovable) {\n        // Sync drag mode to crop box when it is not movable\n        setData(face, DATA_ACTION, mode);\n        toggleClass(face, CLASS_CROP, croppable);\n        toggleClass(face, CLASS_MOVE, movable);\n      }\n    }\n\n    return this;\n  }\n};\n\nvar AnotherCropper = WINDOW.Cropper;\n\nvar Cropper = /*#__PURE__*/function () {\n  /**\n   * Create a new Cropper.\n   * @param {Element} element - The target element for cropping.\n   * @param {Object} [options={}] - The configuration options.\n   */\n  function Cropper(element) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n    _classCallCheck(this, Cropper);\n\n    if (!element || !REGEXP_TAG_NAME.test(element.tagName)) {\n      throw new Error('The first argument is required and must be an <img> or <canvas> element.');\n    }\n\n    this.element = element;\n    this.options = assign({}, DEFAULTS, isPlainObject(options) && options);\n    this.cropped = false;\n    this.disabled = false;\n    this.pointers = {};\n    this.ready = false;\n    this.reloading = false;\n    this.replaced = false;\n    this.sized = false;\n    this.sizing = false;\n    this.init();\n  }\n\n  _createClass(Cropper, [{\n    key: \"init\",\n    value: function init() {\n      var element = this.element;\n      var tagName = element.tagName.toLowerCase();\n      var url;\n\n      if (element[NAMESPACE]) {\n        return;\n      }\n\n      element[NAMESPACE] = this;\n\n      if (tagName === 'img') {\n        this.isImg = true; // e.g.: \"img/picture.jpg\"\n\n        url = element.getAttribute('src') || '';\n        this.originalUrl = url; // Stop when it's a blank image\n\n        if (!url) {\n          return;\n        } // e.g.: \"https://example.com/img/picture.jpg\"\n\n\n        url = element.src;\n      } else if (tagName === 'canvas' && window.HTMLCanvasElement) {\n        url = element.toDataURL();\n      }\n\n      this.load(url);\n    }\n  }, {\n    key: \"load\",\n    value: function load(url) {\n      var _this = this;\n\n      if (!url) {\n        return;\n      }\n\n      this.url = url;\n      this.imageData = {};\n      var element = this.element,\n          options = this.options;\n\n      if (!options.rotatable && !options.scalable) {\n        options.checkOrientation = false;\n      } // Only IE10+ supports Typed Arrays\n\n\n      if (!options.checkOrientation || !window.ArrayBuffer) {\n        this.clone();\n        return;\n      } // Detect the mime type of the image directly if it is a Data URL\n\n\n      if (REGEXP_DATA_URL.test(url)) {\n        // Read ArrayBuffer from Data URL of JPEG images directly for better performance\n        if (REGEXP_DATA_URL_JPEG.test(url)) {\n          this.read(dataURLToArrayBuffer(url));\n        } else {\n          // Only a JPEG image may contains Exif Orientation information,\n          // the rest types of Data URLs are not necessary to check orientation at all.\n          this.clone();\n        }\n\n        return;\n      } // 1. Detect the mime type of the image by a XMLHttpRequest.\n      // 2. Load the image as ArrayBuffer for reading orientation if its a JPEG image.\n\n\n      var xhr = new XMLHttpRequest();\n      var clone = this.clone.bind(this);\n      this.reloading = true;\n      this.xhr = xhr; // 1. Cross origin requests are only supported for protocol schemes:\n      // http, https, data, chrome, chrome-extension.\n      // 2. Access to XMLHttpRequest from a Data URL will be blocked by CORS policy\n      // in some browsers as IE11 and Safari.\n\n      xhr.onabort = clone;\n      xhr.onerror = clone;\n      xhr.ontimeout = clone;\n\n      xhr.onprogress = function () {\n        // Abort the request directly if it not a JPEG image for better performance\n        if (xhr.getResponseHeader('content-type') !== MIME_TYPE_JPEG) {\n          xhr.abort();\n        }\n      };\n\n      xhr.onload = function () {\n        _this.read(xhr.response);\n      };\n\n      xhr.onloadend = function () {\n        _this.reloading = false;\n        _this.xhr = null;\n      }; // Bust cache when there is a \"crossOrigin\" property to avoid browser cache error\n\n\n      if (options.checkCrossOrigin && isCrossOriginURL(url) && element.crossOrigin) {\n        url = addTimestamp(url);\n      } // The third parameter is required for avoiding side-effect (#682)\n\n\n      xhr.open('GET', url, true);\n      xhr.responseType = 'arraybuffer';\n      xhr.withCredentials = element.crossOrigin === 'use-credentials';\n      xhr.send();\n    }\n  }, {\n    key: \"read\",\n    value: function read(arrayBuffer) {\n      var options = this.options,\n          imageData = this.imageData; // Reset the orientation value to its default value 1\n      // as some iOS browsers will render image with its orientation\n\n      var orientation = resetAndGetOrientation(arrayBuffer);\n      var rotate = 0;\n      var scaleX = 1;\n      var scaleY = 1;\n\n      if (orientation > 1) {\n        // Generate a new URL which has the default orientation value\n        this.url = arrayBufferToDataURL(arrayBuffer, MIME_TYPE_JPEG);\n\n        var _parseOrientation = parseOrientation(orientation);\n\n        rotate = _parseOrientation.rotate;\n        scaleX = _parseOrientation.scaleX;\n        scaleY = _parseOrientation.scaleY;\n      }\n\n      if (options.rotatable) {\n        imageData.rotate = rotate;\n      }\n\n      if (options.scalable) {\n        imageData.scaleX = scaleX;\n        imageData.scaleY = scaleY;\n      }\n\n      this.clone();\n    }\n  }, {\n    key: \"clone\",\n    value: function clone() {\n      var element = this.element,\n          url = this.url;\n      var crossOrigin = element.crossOrigin;\n      var crossOriginUrl = url;\n\n      if (this.options.checkCrossOrigin && isCrossOriginURL(url)) {\n        if (!crossOrigin) {\n          crossOrigin = 'anonymous';\n        } // Bust cache when there is not a \"crossOrigin\" property (#519)\n\n\n        crossOriginUrl = addTimestamp(url);\n      }\n\n      this.crossOrigin = crossOrigin;\n      this.crossOriginUrl = crossOriginUrl;\n      var image = document.createElement('img');\n\n      if (crossOrigin) {\n        image.crossOrigin = crossOrigin;\n      }\n\n      image.src = crossOriginUrl || url;\n      image.alt = element.alt || 'The image to crop';\n      this.image = image;\n      image.onload = this.start.bind(this);\n      image.onerror = this.stop.bind(this);\n      addClass(image, CLASS_HIDE);\n      element.parentNode.insertBefore(image, element.nextSibling);\n    }\n  }, {\n    key: \"start\",\n    value: function start() {\n      var _this2 = this;\n\n      var image = this.image;\n      image.onload = null;\n      image.onerror = null;\n      this.sizing = true; // Match all browsers that use WebKit as the layout engine in iOS devices,\n      // such as Safari for iOS, Chrome for iOS, and in-app browsers.\n\n      var isIOSWebKit = WINDOW.navigator && /(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(WINDOW.navigator.userAgent);\n\n      var done = function done(naturalWidth, naturalHeight) {\n        assign(_this2.imageData, {\n          naturalWidth: naturalWidth,\n          naturalHeight: naturalHeight,\n          aspectRatio: naturalWidth / naturalHeight\n        });\n        _this2.initialImageData = assign({}, _this2.imageData);\n        _this2.sizing = false;\n        _this2.sized = true;\n\n        _this2.build();\n      }; // Most modern browsers (excepts iOS WebKit)\n\n\n      if (image.naturalWidth && !isIOSWebKit) {\n        done(image.naturalWidth, image.naturalHeight);\n        return;\n      }\n\n      var sizingImage = document.createElement('img');\n      var body = document.body || document.documentElement;\n      this.sizingImage = sizingImage;\n\n      sizingImage.onload = function () {\n        done(sizingImage.width, sizingImage.height);\n\n        if (!isIOSWebKit) {\n          body.removeChild(sizingImage);\n        }\n      };\n\n      sizingImage.src = image.src; // iOS WebKit will convert the image automatically\n      // with its orientation once append it into DOM (#279)\n\n      if (!isIOSWebKit) {\n        sizingImage.style.cssText = 'left:0;' + 'max-height:none!important;' + 'max-width:none!important;' + 'min-height:0!important;' + 'min-width:0!important;' + 'opacity:0;' + 'position:absolute;' + 'top:0;' + 'z-index:-1;';\n        body.appendChild(sizingImage);\n      }\n    }\n  }, {\n    key: \"stop\",\n    value: function stop() {\n      var image = this.image;\n      image.onload = null;\n      image.onerror = null;\n      image.parentNode.removeChild(image);\n      this.image = null;\n    }\n  }, {\n    key: \"build\",\n    value: function build() {\n      if (!this.sized || this.ready) {\n        return;\n      }\n\n      var element = this.element,\n          options = this.options,\n          image = this.image; // Create cropper elements\n\n      var container = element.parentNode;\n      var template = document.createElement('div');\n      template.innerHTML = TEMPLATE;\n      var cropper = template.querySelector(\".\".concat(NAMESPACE, \"-container\"));\n      var canvas = cropper.querySelector(\".\".concat(NAMESPACE, \"-canvas\"));\n      var dragBox = cropper.querySelector(\".\".concat(NAMESPACE, \"-drag-box\"));\n      var cropBox = cropper.querySelector(\".\".concat(NAMESPACE, \"-crop-box\"));\n      var face = cropBox.querySelector(\".\".concat(NAMESPACE, \"-face\"));\n      this.container = container;\n      this.cropper = cropper;\n      this.canvas = canvas;\n      this.dragBox = dragBox;\n      this.cropBox = cropBox;\n      this.viewBox = cropper.querySelector(\".\".concat(NAMESPACE, \"-view-box\"));\n      this.face = face;\n      canvas.appendChild(image); // Hide the original image\n\n      addClass(element, CLASS_HIDDEN); // Inserts the cropper after to the current image\n\n      container.insertBefore(cropper, element.nextSibling); // Show the image if is hidden\n\n      if (!this.isImg) {\n        removeClass(image, CLASS_HIDE);\n      }\n\n      this.initPreview();\n      this.bind();\n      options.initialAspectRatio = Math.max(0, options.initialAspectRatio) || NaN;\n      options.aspectRatio = Math.max(0, options.aspectRatio) || NaN;\n      options.viewMode = Math.max(0, Math.min(3, Math.round(options.viewMode))) || 0;\n      addClass(cropBox, CLASS_HIDDEN);\n\n      if (!options.guides) {\n        addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-dashed\")), CLASS_HIDDEN);\n      }\n\n      if (!options.center) {\n        addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-center\")), CLASS_HIDDEN);\n      }\n\n      if (options.background) {\n        addClass(cropper, \"\".concat(NAMESPACE, \"-bg\"));\n      }\n\n      if (!options.highlight) {\n        addClass(face, CLASS_INVISIBLE);\n      }\n\n      if (options.cropBoxMovable) {\n        addClass(face, CLASS_MOVE);\n        setData(face, DATA_ACTION, ACTION_ALL);\n      }\n\n      if (!options.cropBoxResizable) {\n        addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-line\")), CLASS_HIDDEN);\n        addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-point\")), CLASS_HIDDEN);\n      }\n\n      this.render();\n      this.ready = true;\n      this.setDragMode(options.dragMode);\n\n      if (options.autoCrop) {\n        this.crop();\n      }\n\n      this.setData(options.data);\n\n      if (isFunction(options.ready)) {\n        addListener(element, EVENT_READY, options.ready, {\n          once: true\n        });\n      }\n\n      dispatchEvent(element, EVENT_READY);\n    }\n  }, {\n    key: \"unbuild\",\n    value: function unbuild() {\n      if (!this.ready) {\n        return;\n      }\n\n      this.ready = false;\n      this.unbind();\n      this.resetPreview();\n      this.cropper.parentNode.removeChild(this.cropper);\n      removeClass(this.element, CLASS_HIDDEN);\n    }\n  }, {\n    key: \"uncreate\",\n    value: function uncreate() {\n      if (this.ready) {\n        this.unbuild();\n        this.ready = false;\n        this.cropped = false;\n      } else if (this.sizing) {\n        this.sizingImage.onload = null;\n        this.sizing = false;\n        this.sized = false;\n      } else if (this.reloading) {\n        this.xhr.onabort = null;\n        this.xhr.abort();\n      } else if (this.image) {\n        this.stop();\n      }\n    }\n    /**\n     * Get the no conflict cropper class.\n     * @returns {Cropper} The cropper class.\n     */\n\n  }], [{\n    key: \"noConflict\",\n    value: function noConflict() {\n      window.Cropper = AnotherCropper;\n      return Cropper;\n    }\n    /**\n     * Change the default options.\n     * @param {Object} options - The new default options.\n     */\n\n  }, {\n    key: \"setDefaults\",\n    value: function setDefaults(options) {\n      assign(DEFAULTS, isPlainObject(options) && options);\n    }\n  }]);\n\n  return Cropper;\n}();\n\nassign(Cropper.prototype, render, preview, events, handlers, change, methods);\n\nexport default Cropper;\n", "<svelte:options accessors={true} />\n\n<script lang=\"ts\">\n\timport Cropper from \"cropperjs\";\n\timport { onMount, createEventDispatcher } from \"svelte\";\n\n\texport let image: string;\n\tlet el: HTMLImageElement;\n\n\tconst dispatch = createEventDispatcher();\n\tlet cropper: Cropper;\n\n\texport function destroy(): void {\n\t\tcropper.destroy();\n\t}\n\n\texport function create(): void {\n\t\tif (cropper) {\n\t\t\tdestroy();\n\t\t}\n\t\tcropper = new Cropper(el, {\n\t\t\tautoCropArea: 1,\n\t\t\tcropend(): void {\n\t\t\t\tconst image_data = cropper.getCroppedCanvas().toDataURL();\n\t\t\t\tdispatch(\"crop\", image_data);\n\t\t\t}\n\t\t});\n\n\t\tdispatch(\"crop\", image);\n\t}\n</script>\n\n<img src={image} bind:this={el} alt=\"\" />\n", "class Point {\n  /**\n   *\n   * @param {number} x\n   * @param {number} y\n   */\n  constructor(x, y) {\n    this.x = x\n    this.y = y\n  }\n}\n\nexport default Point\n", "import Point from './Point'\n\nclass LazyPoint extends Point {\n  /**\n   * Update the x and y values\n   *\n   * @param {Point} point\n   */\n  update (point) {\n    this.x = point.x\n    this.y = point.y\n  }\n\n  /**\n   * Move the point to another position using an angle and distance\n   *\n   * @param {number} angle The angle in radians\n   * @param {number} distance How much the point should be moved\n   */\n  moveByAngle (angle, distance) {\n    // Rotate the angle based on the browser coordinate system ([0,0] in the top left)\n    const angleRotated = angle + (Math.PI / 2)\n\n    this.x = this.x + (Math.sin(angleRotated) * distance),\n    this.y = this.y - (Math.cos(angleRotated) * distance)\n  }\n\n  /**\n   * Check if this point is the same as another point\n   *\n   * @param {Point} point\n   * @returns {boolean}\n   */\n  equalsTo (point) {\n    return this.x === point.x && this.y === point.y\n  }\n\n  /**\n   * Get the difference for x and y axis to another point\n   *\n   * @param {Point} point\n   * @returns {Point}\n   */\n  getDifferenceTo (point) {\n    return new Point(this.x - point.x, this.y - point.y)\n  }\n\n  /**\n   * Calculate distance to another point\n   *\n   * @param {Point} point\n   * @returns {Point}\n   */\n  getDistanceTo (point) {\n    const diff = this.getDifferenceTo(point)\n\n    return Math.sqrt(Math.pow(diff.x, 2) + Math.pow(diff.y, 2))\n  }\n\n  /**\n   * Calculate the angle to another point\n   *\n   * @param {Point} point\n   * @returns {Point}\n   */\n  getAngleTo (point) {\n    const diff = this.getDifferenceTo(point)\n\n    return Math.atan2(diff.y, diff.x)\n  }\n\n  /**\n   * Return a simple object with x and y properties\n   *\n   * @returns {object}\n   */\n  toObject () {\n    return {\n      x: this.x,\n      y: this.y\n    }\n  }\n}\n\nexport default LazyPoint\n", "import LazyPoint from './LazyPoint'\nconst RADIUS_DEFAULT = 30\n\nclass LazyBrush {\n  /**\n   * constructor\n   *\n   * @param {object} settings\n   * @param {number} settings.radius The radius for the lazy area\n   * @param {boolean} settings.enabled\n   */\n  constructor ({ radius = RADIUS_DEFAULT, enabled = true, initialPoint = { x: 0, y: 0 }} = {}) {\n    this.radius = radius\n    this._isEnabled = enabled\n\n    this.pointer = new LazyPoint(initialPoint.x, initialPoint.y)\n    this.brush = new LazyPoint(initialPoint.x, initialPoint.y)\n\n    this.angle = 0\n    this.distance = 0\n    this._hasMoved = false\n  }\n\n  /**\n   * Enable lazy brush calculations.\n   *\n   */\n  enable () {\n    this._isEnabled = true\n  }\n\n  /**\n   * Disable lazy brush calculations.\n   *\n   */\n  disable () {\n    this._isEnabled = false\n  }\n\n  /**\n   * @returns {boolean}\n   */\n  isEnabled () {\n    return this._isEnabled\n  }\n\n  /**\n   * Update the radius\n   *\n   * @param {number} radius\n   */\n  setRadius (radius) {\n    this.radius = radius\n  }\n\n  /**\n   * Return the current radius\n   *\n   * @returns {number}\n   */\n  getRadius () {\n    return this.radius\n  }\n\n  /**\n   * Return the brush coordinates as a simple object\n   *\n   * @returns {object}\n   */\n  getBrushCoordinates () {\n    return this.brush.toObject()\n  }\n\n  /**\n   * Return the pointer coordinates as a simple object\n   *\n   * @returns {object}\n   */\n  getPointerCoordinates () {\n    return this.pointer.toObject()\n  }\n\n  /**\n   * Return the brush as a LazyPoint\n   *\n   * @returns {LazyPoint}\n   */\n  getBrush () {\n    return this.brush\n  }\n\n  /**\n   * Return the pointer as a LazyPoint\n   *\n   * @returns {LazyPoint}\n   */\n  getPointer () {\n    return this.pointer\n  }\n\n  /**\n   * Return the angle between pointer and brush\n   *\n   * @returns {number} Angle in radians\n   */\n  getAngle () {\n    return this.angle\n  }\n\n  /**\n   * Return the distance between pointer and brush\n   *\n   * @returns {number} Distance in pixels\n   */\n  getDistance () {\n    return this.distance\n  }\n\n  /**\n   * Return if the previous update has moved the brush.\n   *\n   * @returns {boolean} Whether the brush moved previously.\n   */\n  brushHasMoved () {\n    return this._hasMoved\n  }\n\n  /**\n   * Updates the pointer point and calculates the new brush point.\n   *\n   * @param {Point} newPointerPoint\n   * @param {Object} options\n   * @param {Boolean} options.both Force update pointer and brush\n   * @returns {Boolean} Whether any of the two points changed\n   */\n  update (newPointerPoint, { both = false } = {}) {\n    this._hasMoved = false\n    if (this.pointer.equalsTo(newPointerPoint) && !both) {\n      return false\n    }\n\n    this.pointer.update(newPointerPoint)\n\n    if (both) {\n      this._hasMoved = true\n      this.brush.update(newPointerPoint)\n      return true\n    }\n\n    if (this._isEnabled) {\n      this.distance = this.pointer.getDistanceTo(this.brush)\n      this.angle = this.pointer.getAngleTo(this.brush)\n\n      if (this.distance > this.radius) {\n        this.brush.moveByAngle(this.angle, this.distance - this.radius)\n        this._hasMoved = true\n      }\n    } else {\n      this.distance = 0\n      this.angle = 0\n      this.brush.update(newPointerPoint)\n      this._hasMoved = true\n    }\n\n    return true\n  }\n}\n\nexport default LazyBrush\n\n", "/**\r\n * A collection of shims that provide minimal functionality of the ES6 collections.\r\n *\r\n * These implementations are not meant to be used outside of the ResizeObserver\r\n * modules as they cover only a limited range of use cases.\r\n */\r\n/* eslint-disable require-jsdoc, valid-jsdoc */\r\nvar MapShim = (function () {\r\n    if (typeof Map !== 'undefined') {\r\n        return Map;\r\n    }\r\n    /**\r\n     * Returns index in provided array that matches the specified key.\r\n     *\r\n     * @param {Array<Array>} arr\r\n     * @param {*} key\r\n     * @returns {number}\r\n     */\r\n    function getIndex(arr, key) {\r\n        var result = -1;\r\n        arr.some(function (entry, index) {\r\n            if (entry[0] === key) {\r\n                result = index;\r\n                return true;\r\n            }\r\n            return false;\r\n        });\r\n        return result;\r\n    }\r\n    return /** @class */ (function () {\r\n        function class_1() {\r\n            this.__entries__ = [];\r\n        }\r\n        Object.defineProperty(class_1.prototype, \"size\", {\r\n            /**\r\n             * @returns {boolean}\r\n             */\r\n            get: function () {\r\n                return this.__entries__.length;\r\n            },\r\n            enumerable: true,\r\n            configurable: true\r\n        });\r\n        /**\r\n         * @param {*} key\r\n         * @returns {*}\r\n         */\r\n        class_1.prototype.get = function (key) {\r\n            var index = getIndex(this.__entries__, key);\r\n            var entry = this.__entries__[index];\r\n            return entry && entry[1];\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @param {*} value\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.set = function (key, value) {\r\n            var index = getIndex(this.__entries__, key);\r\n            if (~index) {\r\n                this.__entries__[index][1] = value;\r\n            }\r\n            else {\r\n                this.__entries__.push([key, value]);\r\n            }\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.delete = function (key) {\r\n            var entries = this.__entries__;\r\n            var index = getIndex(entries, key);\r\n            if (~index) {\r\n                entries.splice(index, 1);\r\n            }\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.has = function (key) {\r\n            return !!~getIndex(this.__entries__, key);\r\n        };\r\n        /**\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.clear = function () {\r\n            this.__entries__.splice(0);\r\n        };\r\n        /**\r\n         * @param {Function} callback\r\n         * @param {*} [ctx=null]\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.forEach = function (callback, ctx) {\r\n            if (ctx === void 0) { ctx = null; }\r\n            for (var _i = 0, _a = this.__entries__; _i < _a.length; _i++) {\r\n                var entry = _a[_i];\r\n                callback.call(ctx, entry[1], entry[0]);\r\n            }\r\n        };\r\n        return class_1;\r\n    }());\r\n})();\n\n/**\r\n * Detects whether window and document objects are available in current environment.\r\n */\r\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && window.document === document;\n\n// Returns global object of a current environment.\r\nvar global$1 = (function () {\r\n    if (typeof global !== 'undefined' && global.Math === Math) {\r\n        return global;\r\n    }\r\n    if (typeof self !== 'undefined' && self.Math === Math) {\r\n        return self;\r\n    }\r\n    if (typeof window !== 'undefined' && window.Math === Math) {\r\n        return window;\r\n    }\r\n    // eslint-disable-next-line no-new-func\r\n    return Function('return this')();\r\n})();\n\n/**\r\n * A shim for the requestAnimationFrame which falls back to the setTimeout if\r\n * first one is not supported.\r\n *\r\n * @returns {number} Requests' identifier.\r\n */\r\nvar requestAnimationFrame$1 = (function () {\r\n    if (typeof requestAnimationFrame === 'function') {\r\n        // It's required to use a bounded function because IE sometimes throws\r\n        // an \"Invalid calling object\" error if rAF is invoked without the global\r\n        // object on the left hand side.\r\n        return requestAnimationFrame.bind(global$1);\r\n    }\r\n    return function (callback) { return setTimeout(function () { return callback(Date.now()); }, 1000 / 60); };\r\n})();\n\n// Defines minimum timeout before adding a trailing call.\r\nvar trailingTimeout = 2;\r\n/**\r\n * Creates a wrapper function which ensures that provided callback will be\r\n * invoked only once during the specified delay period.\r\n *\r\n * @param {Function} callback - Function to be invoked after the delay period.\r\n * @param {number} delay - Delay after which to invoke callback.\r\n * @returns {Function}\r\n */\r\nfunction throttle (callback, delay) {\r\n    var leadingCall = false, trailingCall = false, lastCallTime = 0;\r\n    /**\r\n     * Invokes the original callback function and schedules new invocation if\r\n     * the \"proxy\" was called during current request.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function resolvePending() {\r\n        if (leadingCall) {\r\n            leadingCall = false;\r\n            callback();\r\n        }\r\n        if (trailingCall) {\r\n            proxy();\r\n        }\r\n    }\r\n    /**\r\n     * Callback invoked after the specified delay. It will further postpone\r\n     * invocation of the original function delegating it to the\r\n     * requestAnimationFrame.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function timeoutCallback() {\r\n        requestAnimationFrame$1(resolvePending);\r\n    }\r\n    /**\r\n     * Schedules invocation of the original function.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function proxy() {\r\n        var timeStamp = Date.now();\r\n        if (leadingCall) {\r\n            // Reject immediately following calls.\r\n            if (timeStamp - lastCallTime < trailingTimeout) {\r\n                return;\r\n            }\r\n            // Schedule new call to be in invoked when the pending one is resolved.\r\n            // This is important for \"transitions\" which never actually start\r\n            // immediately so there is a chance that we might miss one if change\r\n            // happens amids the pending invocation.\r\n            trailingCall = true;\r\n        }\r\n        else {\r\n            leadingCall = true;\r\n            trailingCall = false;\r\n            setTimeout(timeoutCallback, delay);\r\n        }\r\n        lastCallTime = timeStamp;\r\n    }\r\n    return proxy;\r\n}\n\n// Minimum delay before invoking the update of observers.\r\nvar REFRESH_DELAY = 20;\r\n// A list of substrings of CSS properties used to find transition events that\r\n// might affect dimensions of observed elements.\r\nvar transitionKeys = ['top', 'right', 'bottom', 'left', 'width', 'height', 'size', 'weight'];\r\n// Check if MutationObserver is available.\r\nvar mutationObserverSupported = typeof MutationObserver !== 'undefined';\r\n/**\r\n * Singleton controller class which handles updates of ResizeObserver instances.\r\n */\r\nvar ResizeObserverController = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserverController.\r\n     *\r\n     * @private\r\n     */\r\n    function ResizeObserverController() {\r\n        /**\r\n         * Indicates whether DOM listeners have been added.\r\n         *\r\n         * @private {boolean}\r\n         */\r\n        this.connected_ = false;\r\n        /**\r\n         * Tells that controller has subscribed for Mutation Events.\r\n         *\r\n         * @private {boolean}\r\n         */\r\n        this.mutationEventsAdded_ = false;\r\n        /**\r\n         * Keeps reference to the instance of MutationObserver.\r\n         *\r\n         * @private {MutationObserver}\r\n         */\r\n        this.mutationsObserver_ = null;\r\n        /**\r\n         * A list of connected observers.\r\n         *\r\n         * @private {Array<ResizeObserverSPI>}\r\n         */\r\n        this.observers_ = [];\r\n        this.onTransitionEnd_ = this.onTransitionEnd_.bind(this);\r\n        this.refresh = throttle(this.refresh.bind(this), REFRESH_DELAY);\r\n    }\r\n    /**\r\n     * Adds observer to observers list.\r\n     *\r\n     * @param {ResizeObserverSPI} observer - Observer to be added.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.addObserver = function (observer) {\r\n        if (!~this.observers_.indexOf(observer)) {\r\n            this.observers_.push(observer);\r\n        }\r\n        // Add listeners if they haven't been added yet.\r\n        if (!this.connected_) {\r\n            this.connect_();\r\n        }\r\n    };\r\n    /**\r\n     * Removes observer from observers list.\r\n     *\r\n     * @param {ResizeObserverSPI} observer - Observer to be removed.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.removeObserver = function (observer) {\r\n        var observers = this.observers_;\r\n        var index = observers.indexOf(observer);\r\n        // Remove observer if it's present in registry.\r\n        if (~index) {\r\n            observers.splice(index, 1);\r\n        }\r\n        // Remove listeners if controller has no connected observers.\r\n        if (!observers.length && this.connected_) {\r\n            this.disconnect_();\r\n        }\r\n    };\r\n    /**\r\n     * Invokes the update of observers. It will continue running updates insofar\r\n     * it detects changes.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.refresh = function () {\r\n        var changesDetected = this.updateObservers_();\r\n        // Continue running updates if changes have been detected as there might\r\n        // be future ones caused by CSS transitions.\r\n        if (changesDetected) {\r\n            this.refresh();\r\n        }\r\n    };\r\n    /**\r\n     * Updates every observer from observers list and notifies them of queued\r\n     * entries.\r\n     *\r\n     * @private\r\n     * @returns {boolean} Returns \"true\" if any observer has detected changes in\r\n     *      dimensions of it's elements.\r\n     */\r\n    ResizeObserverController.prototype.updateObservers_ = function () {\r\n        // Collect observers that have active observations.\r\n        var activeObservers = this.observers_.filter(function (observer) {\r\n            return observer.gatherActive(), observer.hasActive();\r\n        });\r\n        // Deliver notifications in a separate cycle in order to avoid any\r\n        // collisions between observers, e.g. when multiple instances of\r\n        // ResizeObserver are tracking the same element and the callback of one\r\n        // of them changes content dimensions of the observed target. Sometimes\r\n        // this may result in notifications being blocked for the rest of observers.\r\n        activeObservers.forEach(function (observer) { return observer.broadcastActive(); });\r\n        return activeObservers.length > 0;\r\n    };\r\n    /**\r\n     * Initializes DOM listeners.\r\n     *\r\n     * @private\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.connect_ = function () {\r\n        // Do nothing if running in a non-browser environment or if listeners\r\n        // have been already added.\r\n        if (!isBrowser || this.connected_) {\r\n            return;\r\n        }\r\n        // Subscription to the \"Transitionend\" event is used as a workaround for\r\n        // delayed transitions. This way it's possible to capture at least the\r\n        // final state of an element.\r\n        document.addEventListener('transitionend', this.onTransitionEnd_);\r\n        window.addEventListener('resize', this.refresh);\r\n        if (mutationObserverSupported) {\r\n            this.mutationsObserver_ = new MutationObserver(this.refresh);\r\n            this.mutationsObserver_.observe(document, {\r\n                attributes: true,\r\n                childList: true,\r\n                characterData: true,\r\n                subtree: true\r\n            });\r\n        }\r\n        else {\r\n            document.addEventListener('DOMSubtreeModified', this.refresh);\r\n            this.mutationEventsAdded_ = true;\r\n        }\r\n        this.connected_ = true;\r\n    };\r\n    /**\r\n     * Removes DOM listeners.\r\n     *\r\n     * @private\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.disconnect_ = function () {\r\n        // Do nothing if running in a non-browser environment or if listeners\r\n        // have been already removed.\r\n        if (!isBrowser || !this.connected_) {\r\n            return;\r\n        }\r\n        document.removeEventListener('transitionend', this.onTransitionEnd_);\r\n        window.removeEventListener('resize', this.refresh);\r\n        if (this.mutationsObserver_) {\r\n            this.mutationsObserver_.disconnect();\r\n        }\r\n        if (this.mutationEventsAdded_) {\r\n            document.removeEventListener('DOMSubtreeModified', this.refresh);\r\n        }\r\n        this.mutationsObserver_ = null;\r\n        this.mutationEventsAdded_ = false;\r\n        this.connected_ = false;\r\n    };\r\n    /**\r\n     * \"Transitionend\" event handler.\r\n     *\r\n     * @private\r\n     * @param {TransitionEvent} event\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.onTransitionEnd_ = function (_a) {\r\n        var _b = _a.propertyName, propertyName = _b === void 0 ? '' : _b;\r\n        // Detect whether transition may affect dimensions of an element.\r\n        var isReflowProperty = transitionKeys.some(function (key) {\r\n            return !!~propertyName.indexOf(key);\r\n        });\r\n        if (isReflowProperty) {\r\n            this.refresh();\r\n        }\r\n    };\r\n    /**\r\n     * Returns instance of the ResizeObserverController.\r\n     *\r\n     * @returns {ResizeObserverController}\r\n     */\r\n    ResizeObserverController.getInstance = function () {\r\n        if (!this.instance_) {\r\n            this.instance_ = new ResizeObserverController();\r\n        }\r\n        return this.instance_;\r\n    };\r\n    /**\r\n     * Holds reference to the controller's instance.\r\n     *\r\n     * @private {ResizeObserverController}\r\n     */\r\n    ResizeObserverController.instance_ = null;\r\n    return ResizeObserverController;\r\n}());\n\n/**\r\n * Defines non-writable/enumerable properties of the provided target object.\r\n *\r\n * @param {Object} target - Object for which to define properties.\r\n * @param {Object} props - Properties to be defined.\r\n * @returns {Object} Target object.\r\n */\r\nvar defineConfigurable = (function (target, props) {\r\n    for (var _i = 0, _a = Object.keys(props); _i < _a.length; _i++) {\r\n        var key = _a[_i];\r\n        Object.defineProperty(target, key, {\r\n            value: props[key],\r\n            enumerable: false,\r\n            writable: false,\r\n            configurable: true\r\n        });\r\n    }\r\n    return target;\r\n});\n\n/**\r\n * Returns the global object associated with provided element.\r\n *\r\n * @param {Object} target\r\n * @returns {Object}\r\n */\r\nvar getWindowOf = (function (target) {\r\n    // Assume that the element is an instance of Node, which means that it\r\n    // has the \"ownerDocument\" property from which we can retrieve a\r\n    // corresponding global object.\r\n    var ownerGlobal = target && target.ownerDocument && target.ownerDocument.defaultView;\r\n    // Return the local global object if it's not possible extract one from\r\n    // provided element.\r\n    return ownerGlobal || global$1;\r\n});\n\n// Placeholder of an empty content rectangle.\r\nvar emptyRect = createRectInit(0, 0, 0, 0);\r\n/**\r\n * Converts provided string to a number.\r\n *\r\n * @param {number|string} value\r\n * @returns {number}\r\n */\r\nfunction toFloat(value) {\r\n    return parseFloat(value) || 0;\r\n}\r\n/**\r\n * Extracts borders size from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @param {...string} positions - Borders positions (top, right, ...)\r\n * @returns {number}\r\n */\r\nfunction getBordersSize(styles) {\r\n    var positions = [];\r\n    for (var _i = 1; _i < arguments.length; _i++) {\r\n        positions[_i - 1] = arguments[_i];\r\n    }\r\n    return positions.reduce(function (size, position) {\r\n        var value = styles['border-' + position + '-width'];\r\n        return size + toFloat(value);\r\n    }, 0);\r\n}\r\n/**\r\n * Extracts paddings sizes from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @returns {Object} Paddings box.\r\n */\r\nfunction getPaddings(styles) {\r\n    var positions = ['top', 'right', 'bottom', 'left'];\r\n    var paddings = {};\r\n    for (var _i = 0, positions_1 = positions; _i < positions_1.length; _i++) {\r\n        var position = positions_1[_i];\r\n        var value = styles['padding-' + position];\r\n        paddings[position] = toFloat(value);\r\n    }\r\n    return paddings;\r\n}\r\n/**\r\n * Calculates content rectangle of provided SVG element.\r\n *\r\n * @param {SVGGraphicsElement} target - Element content rectangle of which needs\r\n *      to be calculated.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getSVGContentRect(target) {\r\n    var bbox = target.getBBox();\r\n    return createRectInit(0, 0, bbox.width, bbox.height);\r\n}\r\n/**\r\n * Calculates content rectangle of provided HTMLElement.\r\n *\r\n * @param {HTMLElement} target - Element for which to calculate the content rectangle.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getHTMLElementContentRect(target) {\r\n    // Client width & height properties can't be\r\n    // used exclusively as they provide rounded values.\r\n    var clientWidth = target.clientWidth, clientHeight = target.clientHeight;\r\n    // By this condition we can catch all non-replaced inline, hidden and\r\n    // detached elements. Though elements with width & height properties less\r\n    // than 0.5 will be discarded as well.\r\n    //\r\n    // Without it we would need to implement separate methods for each of\r\n    // those cases and it's not possible to perform a precise and performance\r\n    // effective test for hidden elements. E.g. even jQuery's ':visible' filter\r\n    // gives wrong results for elements with width & height less than 0.5.\r\n    if (!clientWidth && !clientHeight) {\r\n        return emptyRect;\r\n    }\r\n    var styles = getWindowOf(target).getComputedStyle(target);\r\n    var paddings = getPaddings(styles);\r\n    var horizPad = paddings.left + paddings.right;\r\n    var vertPad = paddings.top + paddings.bottom;\r\n    // Computed styles of width & height are being used because they are the\r\n    // only dimensions available to JS that contain non-rounded values. It could\r\n    // be possible to utilize the getBoundingClientRect if only it's data wasn't\r\n    // affected by CSS transformations let alone paddings, borders and scroll bars.\r\n    var width = toFloat(styles.width), height = toFloat(styles.height);\r\n    // Width & height include paddings and borders when the 'border-box' box\r\n    // model is applied (except for IE).\r\n    if (styles.boxSizing === 'border-box') {\r\n        // Following conditions are required to handle Internet Explorer which\r\n        // doesn't include paddings and borders to computed CSS dimensions.\r\n        //\r\n        // We can say that if CSS dimensions + paddings are equal to the \"client\"\r\n        // properties then it's either IE, and thus we don't need to subtract\r\n        // anything, or an element merely doesn't have paddings/borders styles.\r\n        if (Math.round(width + horizPad) !== clientWidth) {\r\n            width -= getBordersSize(styles, 'left', 'right') + horizPad;\r\n        }\r\n        if (Math.round(height + vertPad) !== clientHeight) {\r\n            height -= getBordersSize(styles, 'top', 'bottom') + vertPad;\r\n        }\r\n    }\r\n    // Following steps can't be applied to the document's root element as its\r\n    // client[Width/Height] properties represent viewport area of the window.\r\n    // Besides, it's as well not necessary as the <html> itself neither has\r\n    // rendered scroll bars nor it can be clipped.\r\n    if (!isDocumentElement(target)) {\r\n        // In some browsers (only in Firefox, actually) CSS width & height\r\n        // include scroll bars size which can be removed at this step as scroll\r\n        // bars are the only difference between rounded dimensions + paddings\r\n        // and \"client\" properties, though that is not always true in Chrome.\r\n        var vertScrollbar = Math.round(width + horizPad) - clientWidth;\r\n        var horizScrollbar = Math.round(height + vertPad) - clientHeight;\r\n        // Chrome has a rather weird rounding of \"client\" properties.\r\n        // E.g. for an element with content width of 314.2px it sometimes gives\r\n        // the client width of 315px and for the width of 314.7px it may give\r\n        // 314px. And it doesn't happen all the time. So just ignore this delta\r\n        // as a non-relevant.\r\n        if (Math.abs(vertScrollbar) !== 1) {\r\n            width -= vertScrollbar;\r\n        }\r\n        if (Math.abs(horizScrollbar) !== 1) {\r\n            height -= horizScrollbar;\r\n        }\r\n    }\r\n    return createRectInit(paddings.left, paddings.top, width, height);\r\n}\r\n/**\r\n * Checks whether provided element is an instance of the SVGGraphicsElement.\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\r\nvar isSVGGraphicsElement = (function () {\r\n    // Some browsers, namely IE and Edge, don't have the SVGGraphicsElement\r\n    // interface.\r\n    if (typeof SVGGraphicsElement !== 'undefined') {\r\n        return function (target) { return target instanceof getWindowOf(target).SVGGraphicsElement; };\r\n    }\r\n    // If it's so, then check that element is at least an instance of the\r\n    // SVGElement and that it has the \"getBBox\" method.\r\n    // eslint-disable-next-line no-extra-parens\r\n    return function (target) { return (target instanceof getWindowOf(target).SVGElement &&\r\n        typeof target.getBBox === 'function'); };\r\n})();\r\n/**\r\n * Checks whether provided element is a document element (<html>).\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\r\nfunction isDocumentElement(target) {\r\n    return target === getWindowOf(target).document.documentElement;\r\n}\r\n/**\r\n * Calculates an appropriate content rectangle for provided html or svg element.\r\n *\r\n * @param {Element} target - Element content rectangle of which needs to be calculated.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getContentRect(target) {\r\n    if (!isBrowser) {\r\n        return emptyRect;\r\n    }\r\n    if (isSVGGraphicsElement(target)) {\r\n        return getSVGContentRect(target);\r\n    }\r\n    return getHTMLElementContentRect(target);\r\n}\r\n/**\r\n * Creates rectangle with an interface of the DOMRectReadOnly.\r\n * Spec: https://drafts.fxtf.org/geometry/#domrectreadonly\r\n *\r\n * @param {DOMRectInit} rectInit - Object with rectangle's x/y coordinates and dimensions.\r\n * @returns {DOMRectReadOnly}\r\n */\r\nfunction createReadOnlyRect(_a) {\r\n    var x = _a.x, y = _a.y, width = _a.width, height = _a.height;\r\n    // If DOMRectReadOnly is available use it as a prototype for the rectangle.\r\n    var Constr = typeof DOMRectReadOnly !== 'undefined' ? DOMRectReadOnly : Object;\r\n    var rect = Object.create(Constr.prototype);\r\n    // Rectangle's properties are not writable and non-enumerable.\r\n    defineConfigurable(rect, {\r\n        x: x, y: y, width: width, height: height,\r\n        top: y,\r\n        right: x + width,\r\n        bottom: height + y,\r\n        left: x\r\n    });\r\n    return rect;\r\n}\r\n/**\r\n * Creates DOMRectInit object based on the provided dimensions and the x/y coordinates.\r\n * Spec: https://drafts.fxtf.org/geometry/#dictdef-domrectinit\r\n *\r\n * @param {number} x - X coordinate.\r\n * @param {number} y - Y coordinate.\r\n * @param {number} width - Rectangle's width.\r\n * @param {number} height - Rectangle's height.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction createRectInit(x, y, width, height) {\r\n    return { x: x, y: y, width: width, height: height };\r\n}\n\n/**\r\n * Class that is responsible for computations of the content rectangle of\r\n * provided DOM element and for keeping track of it's changes.\r\n */\r\nvar ResizeObservation = /** @class */ (function () {\r\n    /**\r\n     * Creates an instance of ResizeObservation.\r\n     *\r\n     * @param {Element} target - Element to be observed.\r\n     */\r\n    function ResizeObservation(target) {\r\n        /**\r\n         * Broadcasted width of content rectangle.\r\n         *\r\n         * @type {number}\r\n         */\r\n        this.broadcastWidth = 0;\r\n        /**\r\n         * Broadcasted height of content rectangle.\r\n         *\r\n         * @type {number}\r\n         */\r\n        this.broadcastHeight = 0;\r\n        /**\r\n         * Reference to the last observed content rectangle.\r\n         *\r\n         * @private {DOMRectInit}\r\n         */\r\n        this.contentRect_ = createRectInit(0, 0, 0, 0);\r\n        this.target = target;\r\n    }\r\n    /**\r\n     * Updates content rectangle and tells whether it's width or height properties\r\n     * have changed since the last broadcast.\r\n     *\r\n     * @returns {boolean}\r\n     */\r\n    ResizeObservation.prototype.isActive = function () {\r\n        var rect = getContentRect(this.target);\r\n        this.contentRect_ = rect;\r\n        return (rect.width !== this.broadcastWidth ||\r\n            rect.height !== this.broadcastHeight);\r\n    };\r\n    /**\r\n     * Updates 'broadcastWidth' and 'broadcastHeight' properties with a data\r\n     * from the corresponding properties of the last observed content rectangle.\r\n     *\r\n     * @returns {DOMRectInit} Last observed content rectangle.\r\n     */\r\n    ResizeObservation.prototype.broadcastRect = function () {\r\n        var rect = this.contentRect_;\r\n        this.broadcastWidth = rect.width;\r\n        this.broadcastHeight = rect.height;\r\n        return rect;\r\n    };\r\n    return ResizeObservation;\r\n}());\n\nvar ResizeObserverEntry = /** @class */ (function () {\r\n    /**\r\n     * Creates an instance of ResizeObserverEntry.\r\n     *\r\n     * @param {Element} target - Element that is being observed.\r\n     * @param {DOMRectInit} rectInit - Data of the element's content rectangle.\r\n     */\r\n    function ResizeObserverEntry(target, rectInit) {\r\n        var contentRect = createReadOnlyRect(rectInit);\r\n        // According to the specification following properties are not writable\r\n        // and are also not enumerable in the native implementation.\r\n        //\r\n        // Property accessors are not being used as they'd require to define a\r\n        // private WeakMap storage which may cause memory leaks in browsers that\r\n        // don't support this type of collections.\r\n        defineConfigurable(this, { target: target, contentRect: contentRect });\r\n    }\r\n    return ResizeObserverEntry;\r\n}());\n\nvar ResizeObserverSPI = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserver.\r\n     *\r\n     * @param {ResizeObserverCallback} callback - Callback function that is invoked\r\n     *      when one of the observed elements changes it's content dimensions.\r\n     * @param {ResizeObserverController} controller - Controller instance which\r\n     *      is responsible for the updates of observer.\r\n     * @param {ResizeObserver} callbackCtx - Reference to the public\r\n     *      ResizeObserver instance which will be passed to callback function.\r\n     */\r\n    function ResizeObserverSPI(callback, controller, callbackCtx) {\r\n        /**\r\n         * Collection of resize observations that have detected changes in dimensions\r\n         * of elements.\r\n         *\r\n         * @private {Array<ResizeObservation>}\r\n         */\r\n        this.activeObservations_ = [];\r\n        /**\r\n         * Registry of the ResizeObservation instances.\r\n         *\r\n         * @private {Map<Element, ResizeObservation>}\r\n         */\r\n        this.observations_ = new MapShim();\r\n        if (typeof callback !== 'function') {\r\n            throw new TypeError('The callback provided as parameter 1 is not a function.');\r\n        }\r\n        this.callback_ = callback;\r\n        this.controller_ = controller;\r\n        this.callbackCtx_ = callbackCtx;\r\n    }\r\n    /**\r\n     * Starts observing provided element.\r\n     *\r\n     * @param {Element} target - Element to be observed.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.observe = function (target) {\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        // Do nothing if current environment doesn't have the Element interface.\r\n        if (typeof Element === 'undefined' || !(Element instanceof Object)) {\r\n            return;\r\n        }\r\n        if (!(target instanceof getWindowOf(target).Element)) {\r\n            throw new TypeError('parameter 1 is not of type \"Element\".');\r\n        }\r\n        var observations = this.observations_;\r\n        // Do nothing if element is already being observed.\r\n        if (observations.has(target)) {\r\n            return;\r\n        }\r\n        observations.set(target, new ResizeObservation(target));\r\n        this.controller_.addObserver(this);\r\n        // Force the update of observations.\r\n        this.controller_.refresh();\r\n    };\r\n    /**\r\n     * Stops observing provided element.\r\n     *\r\n     * @param {Element} target - Element to stop observing.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.unobserve = function (target) {\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        // Do nothing if current environment doesn't have the Element interface.\r\n        if (typeof Element === 'undefined' || !(Element instanceof Object)) {\r\n            return;\r\n        }\r\n        if (!(target instanceof getWindowOf(target).Element)) {\r\n            throw new TypeError('parameter 1 is not of type \"Element\".');\r\n        }\r\n        var observations = this.observations_;\r\n        // Do nothing if element is not being observed.\r\n        if (!observations.has(target)) {\r\n            return;\r\n        }\r\n        observations.delete(target);\r\n        if (!observations.size) {\r\n            this.controller_.removeObserver(this);\r\n        }\r\n    };\r\n    /**\r\n     * Stops observing all elements.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.disconnect = function () {\r\n        this.clearActive();\r\n        this.observations_.clear();\r\n        this.controller_.removeObserver(this);\r\n    };\r\n    /**\r\n     * Collects observation instances the associated element of which has changed\r\n     * it's content rectangle.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.gatherActive = function () {\r\n        var _this = this;\r\n        this.clearActive();\r\n        this.observations_.forEach(function (observation) {\r\n            if (observation.isActive()) {\r\n                _this.activeObservations_.push(observation);\r\n            }\r\n        });\r\n    };\r\n    /**\r\n     * Invokes initial callback function with a list of ResizeObserverEntry\r\n     * instances collected from active resize observations.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.broadcastActive = function () {\r\n        // Do nothing if observer doesn't have active observations.\r\n        if (!this.hasActive()) {\r\n            return;\r\n        }\r\n        var ctx = this.callbackCtx_;\r\n        // Create ResizeObserverEntry instance for every active observation.\r\n        var entries = this.activeObservations_.map(function (observation) {\r\n            return new ResizeObserverEntry(observation.target, observation.broadcastRect());\r\n        });\r\n        this.callback_.call(ctx, entries, ctx);\r\n        this.clearActive();\r\n    };\r\n    /**\r\n     * Clears the collection of active observations.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.clearActive = function () {\r\n        this.activeObservations_.splice(0);\r\n    };\r\n    /**\r\n     * Tells whether observer has active observations.\r\n     *\r\n     * @returns {boolean}\r\n     */\r\n    ResizeObserverSPI.prototype.hasActive = function () {\r\n        return this.activeObservations_.length > 0;\r\n    };\r\n    return ResizeObserverSPI;\r\n}());\n\n// Registry of internal observers. If WeakMap is not available use current shim\r\n// for the Map collection as it has all required methods and because WeakMap\r\n// can't be fully polyfilled anyway.\r\nvar observers = typeof WeakMap !== 'undefined' ? new WeakMap() : new MapShim();\r\n/**\r\n * ResizeObserver API. Encapsulates the ResizeObserver SPI implementation\r\n * exposing only those methods and properties that are defined in the spec.\r\n */\r\nvar ResizeObserver = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserver.\r\n     *\r\n     * @param {ResizeObserverCallback} callback - Callback that is invoked when\r\n     *      dimensions of the observed elements change.\r\n     */\r\n    function ResizeObserver(callback) {\r\n        if (!(this instanceof ResizeObserver)) {\r\n            throw new TypeError('Cannot call a class as a function.');\r\n        }\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        var controller = ResizeObserverController.getInstance();\r\n        var observer = new ResizeObserverSPI(callback, controller, this);\r\n        observers.set(this, observer);\r\n    }\r\n    return ResizeObserver;\r\n}());\r\n// Expose public methods of ResizeObserver.\r\n[\r\n    'observe',\r\n    'unobserve',\r\n    'disconnect'\r\n].forEach(function (method) {\r\n    ResizeObserver.prototype[method] = function () {\r\n        var _a;\r\n        return (_a = observers.get(this))[method].apply(_a, arguments);\r\n    };\r\n});\n\nvar index = (function () {\r\n    // Export existing implementation if available.\r\n    if (typeof global$1.ResizeObserver !== 'undefined') {\r\n        return global$1.ResizeObserver;\r\n    }\r\n    return ResizeObserver;\r\n})();\n\nexport default index;\n", "<script>\n\t// @ts-nocheck\n\t/* eslint-disable */\n\n\timport { onMount, onD<PERSON>roy, createEventDispatcher, tick } from \"svelte\";\n\timport { fade } from \"svelte/transition\";\n\timport { LazyBrush } from \"lazy-brush/src\";\n\timport ResizeObserver from \"resize-observer-polyfill\";\n\n\tconst dispatch = createEventDispatcher();\n\n\texport let value;\n\texport let value_img;\n\texport let mode = \"sketch\";\n\texport let brush_color = \"#0b0f19\";\n\texport let brush_radius;\n\texport let mask_opacity = 0.7;\n\texport let source;\n\n\texport let width = 400;\n\texport let height = 200;\n\texport let container_height = 200;\n\texport let shape;\n\n\t$: {\n\t\tif (shape && (width || height)) {\n\t\t\twidth = shape[0];\n\t\t\theight = shape[1];\n\t\t}\n\t}\n\n\tlet mounted;\n\n\tlet catenary_color = \"#aaa\";\n\n\tlet canvas_width = width;\n\tlet canvas_height = height;\n\n\t$: mounted && !value && clear();\n\n\tlet last_value_img;\n\n\t$: {\n\t\tif (mounted && value_img !== last_value_img) {\n\t\t\tlast_value_img = value_img;\n\n\t\t\tclear();\n\n\t\t\tsetTimeout(() => {\n\t\t\t\tif (source === \"webcam\") {\n\t\t\t\t\tctx.temp.save();\n\t\t\t\t\tctx.temp.translate(width, 0);\n\t\t\t\t\tctx.temp.scale(-1, 1);\n\t\t\t\t\tctx.temp.drawImage(value_img, 0, 0);\n\t\t\t\t\tctx.temp.restore();\n\t\t\t\t} else {\n\t\t\t\t\tdraw_cropped_image();\n\t\t\t\t}\n\n\t\t\t\tctx.drawing.drawImage(canvas.temp, 0, 0, width, height);\n\n\t\t\t\tdraw_lines({ lines: lines.slice() });\n\t\t\t\ttrigger_on_change();\n\t\t\t}, 50);\n\t\t}\n\t}\n\n\tfunction mid_point(p1, p2) {\n\t\treturn {\n\t\t\tx: p1.x + (p2.x - p1.x) / 2,\n\t\t\ty: p1.y + (p2.y - p1.y) / 2\n\t\t};\n\t}\n\n\tconst canvas_types = [\n\t\t{\n\t\t\tname: \"interface\",\n\t\t\tzIndex: 15\n\t\t},\n\t\t{\n\t\t\tname: \"mask\",\n\t\t\tzIndex: 13,\n\t\t\topacity: mask_opacity\n\t\t},\n\t\t{\n\t\t\tname: \"drawing\",\n\t\t\tzIndex: 11\n\t\t},\n\t\t{\n\t\t\tname: \"temp\",\n\t\t\tzIndex: 12\n\t\t}\n\t];\n\n\tlet canvas = {};\n\tlet ctx = {};\n\tlet points = [];\n\tlet lines = [];\n\tlet mouse_has_moved = true;\n\tlet values_changed = true;\n\tlet is_drawing = false;\n\tlet is_pressing = false;\n\tlet lazy = null;\n\tlet canvas_container = null;\n\tlet canvas_observer = null;\n\tlet line_count = 0;\n\n\tfunction draw_cropped_image() {\n\t\tif (!shape) {\n\t\t\tctx.temp.drawImage(value_img, 0, 0, width, height);\n\t\t\treturn;\n\t\t}\n\n\t\tlet _width = value_img.naturalWidth;\n\t\tlet _height = value_img.naturalHeight;\n\n\t\tconst shape_ratio = shape[0] / shape[1];\n\t\tconst image_ratio = _width / _height;\n\n\t\tlet x = 0;\n\t\tlet y = 0;\n\n\t\tif (shape_ratio < image_ratio) {\n\t\t\t_width = shape[1] * image_ratio;\n\t\t\t_height = shape[1];\n\t\t\tx = (shape[0] - _width) / 2;\n\t\t} else if (shape_ratio > image_ratio) {\n\t\t\t_width = shape[0];\n\t\t\t_height = shape[0] / image_ratio;\n\t\t\ty = (shape[1] - _height) / 2;\n\t\t} else {\n\t\t\tx = 0;\n\t\t\ty = 0;\n\t\t\t_width = shape[0];\n\t\t\t_height = shape[1];\n\t\t}\n\n\t\tctx.temp.drawImage(value_img, x, y, _width, _height);\n\t}\n\n\tonMount(async () => {\n\t\tObject.keys(canvas).forEach((key) => {\n\t\t\tctx[key] = canvas[key].getContext(\"2d\");\n\t\t});\n\n\t\tawait tick();\n\n\t\tif (value_img) {\n\t\t\tvalue_img.addEventListener(\"load\", (_) => {\n\t\t\t\tif (source === \"webcam\") {\n\t\t\t\t\tctx.temp.save();\n\t\t\t\t\tctx.temp.translate(width, 0);\n\t\t\t\t\tctx.temp.scale(-1, 1);\n\t\t\t\t\tctx.temp.drawImage(value_img, 0, 0);\n\t\t\t\t\tctx.temp.restore();\n\t\t\t\t} else {\n\t\t\t\t\tdraw_cropped_image();\n\t\t\t\t}\n\t\t\t\tctx.drawing.drawImage(canvas.temp, 0, 0, width, height);\n\n\t\t\t\ttrigger_on_change();\n\t\t\t});\n\n\t\t\tsetTimeout(() => {\n\t\t\t\tif (source === \"webcam\") {\n\t\t\t\t\tctx.temp.save();\n\t\t\t\t\tctx.temp.translate(width, 0);\n\t\t\t\t\tctx.temp.scale(-1, 1);\n\t\t\t\t\tctx.temp.drawImage(value_img, 0, 0);\n\t\t\t\t\tctx.temp.restore();\n\t\t\t\t} else {\n\t\t\t\t\tdraw_cropped_image();\n\t\t\t\t}\n\n\t\t\t\tctx.drawing.drawImage(canvas.temp, 0, 0, width, height);\n\n\t\t\t\tdraw_lines({ lines: lines.slice() });\n\t\t\t\ttrigger_on_change();\n\t\t\t}, 100);\n\t\t}\n\n\t\tlazy = new LazyBrush({\n\t\t\tradius: brush_radius * 0.05,\n\t\t\tenabled: true,\n\t\t\tinitialPoint: {\n\t\t\t\tx: width / 2,\n\t\t\t\ty: height / 2\n\t\t\t}\n\t\t});\n\n\t\tcanvas_observer = new ResizeObserver((entries, observer, ...rest) => {\n\t\t\thandle_canvas_resize(entries, observer);\n\t\t});\n\t\tcanvas_observer.observe(canvas_container);\n\n\t\tloop();\n\t\tmounted = true;\n\n\t\trequestAnimationFrame(() => {\n\t\t\tinit();\n\t\t\trequestAnimationFrame(() => {\n\t\t\t\tclear();\n\t\t\t});\n\t\t});\n\t});\n\n\tfunction init() {\n\t\tconst initX = width / 2;\n\t\tconst initY = height / 2;\n\t\tlazy.update({ x: initX, y: initY }, { both: true });\n\t\tlazy.update({ x: initX, y: initY }, { both: false });\n\t\tmouse_has_moved = true;\n\t\tvalues_changed = true;\n\t}\n\n\tonDestroy(() => {\n\t\tmounted = false;\n\t\tcanvas_observer.unobserve(canvas_container);\n\t});\n\n\tfunction redraw_image(_lines) {\n\t\tclear_canvas();\n\n\t\tif (value_img) {\n\t\t\tif (source === \"webcam\") {\n\t\t\t\tctx.temp.save();\n\t\t\t\tctx.temp.translate(width, 0);\n\t\t\t\tctx.temp.scale(-1, 1);\n\t\t\t\tctx.temp.drawImage(value_img, 0, 0);\n\t\t\t\tctx.temp.restore();\n\t\t\t} else {\n\t\t\t\tdraw_cropped_image();\n\t\t\t}\n\n\t\t\tif (!lines || !lines.length) {\n\t\t\t\tctx.drawing.drawImage(canvas.temp, 0, 0, width, height);\n\t\t\t}\n\t\t}\n\n\t\tdraw_lines({ lines: _lines });\n\t\tline_count = _lines.length;\n\n\t\tlines = _lines;\n\t\tctx.drawing.drawImage(canvas.temp, 0, 0, width, height);\n\n\t\tif (lines.length == 0) {\n\t\t\tdispatch(\"clear\");\n\t\t}\n\t}\n\n\texport function clear_mask() {\n\t\tconst _lines = [];\n\n\t\tredraw_image(_lines);\n\t\ttrigger_on_change();\n\t}\n\n\texport function undo() {\n\t\tconst _lines = lines.slice(0, -1);\n\n\t\tredraw_image(_lines);\n\t\ttrigger_on_change();\n\t}\n\n\tlet get_save_data = () => {\n\t\treturn JSON.stringify({\n\t\t\tlines: lines,\n\t\t\twidth: canvas_width,\n\t\t\theight: canvas_height\n\t\t});\n\t};\n\n\tlet draw_lines = ({ lines }) => {\n\t\tlines.forEach((line) => {\n\t\t\tconst { points: _points, brush_color, brush_radius } = line;\n\t\t\tdraw_points({\n\t\t\t\tpoints: _points,\n\t\t\t\tbrush_color,\n\t\t\t\tbrush_radius,\n\t\t\t\tmask: mode === \"mask\"\n\t\t\t});\n\t\t});\n\n\t\tsaveLine({ brush_color, brush_radius });\n\t\tif (mode === \"mask\") {\n\t\t\tsave_mask_line();\n\t\t}\n\t};\n\n\tlet handle_draw_start = (e) => {\n\t\te.preventDefault();\n\t\tis_pressing = true;\n\t\tconst { x, y } = get_pointer_pos(e);\n\t\tif (e.touches && e.touches.length > 0) {\n\t\t\tlazy.update({ x, y }, { both: true });\n\t\t}\n\t\thandle_pointer_move(x, y);\n\t\tline_count += 1;\n\t};\n\n\tlet handle_draw_move = (e) => {\n\t\te.preventDefault();\n\t\tconst { x, y } = get_pointer_pos(e);\n\t\thandle_pointer_move(x, y);\n\t};\n\n\tlet handle_draw_end = (e) => {\n\t\te.preventDefault();\n\t\thandle_draw_move(e);\n\t\tis_drawing = false;\n\t\tis_pressing = false;\n\t\tsaveLine();\n\n\t\tif (mode === \"mask\") {\n\t\t\tsave_mask_line();\n\t\t}\n\t};\n\n\tlet old_width = 0;\n\tlet old_height = 0;\n\tlet old_container_height = 0;\n\tlet add_lr_border = false;\n\n\tlet handle_canvas_resize = async () => {\n\t\tif (shape && canvas_container) {\n\t\t\tconst x = canvas_container?.getBoundingClientRect();\n\t\t\tconst shape_ratio = shape[0] / shape[1];\n\t\t\tconst container_ratio = x.width / x.height;\n\t\t\tadd_lr_border = shape_ratio < container_ratio;\n\t\t}\n\n\t\tif (\n\t\t\twidth === old_width &&\n\t\t\theight === old_height &&\n\t\t\told_container_height === container_height\n\t\t) {\n\t\t\treturn;\n\t\t}\n\t\tconst dimensions = { width: width, height: height };\n\n\t\tconst container_dimensions = {\n\t\t\theight: container_height,\n\t\t\twidth: container_height * (dimensions.width / dimensions.height)\n\t\t};\n\n\t\tawait Promise.all([\n\t\t\tset_canvas_size(canvas.interface, dimensions, container_dimensions),\n\t\t\tset_canvas_size(canvas.drawing, dimensions, container_dimensions),\n\t\t\tset_canvas_size(canvas.temp, dimensions, container_dimensions),\n\t\t\tset_canvas_size(canvas.mask, dimensions, container_dimensions, false)\n\t\t]);\n\n\t\tif (!brush_radius) {\n\t\t\tbrush_radius = 20 * (dimensions.width / container_dimensions.width);\n\t\t}\n\n\t\tloop({ once: true });\n\n\t\tsetTimeout(() => {\n\t\t\told_height = height;\n\t\t\told_width = width;\n\t\t\told_container_height = container_height;\n\t\t}, 10);\n\t\tawait tick();\n\n\t\tclear();\n\t};\n\n\t$: {\n\t\tif (lazy) {\n\t\t\tinit();\n\t\t\tlazy.setRadius(brush_radius * 0.05);\n\t\t}\n\t}\n\n\t$: {\n\t\tif (width || height) {\n\t\t\thandle_canvas_resize();\n\t\t}\n\t}\n\n\tlet set_canvas_size = async (canvas, dimensions, container, scale = true) => {\n\t\tif (!mounted) return;\n\t\tawait tick();\n\n\t\tconst dpr = window.devicePixelRatio || 1;\n\t\tcanvas.width = dimensions.width * (scale ? dpr : 1);\n\t\tcanvas.height = dimensions.height * (scale ? dpr : 1);\n\n\t\tconst ctx = canvas.getContext(\"2d\");\n\t\tscale && ctx.scale(dpr, dpr);\n\n\t\tcanvas.style.width = `${container.width}px`;\n\t\tcanvas.style.height = `${container.height}px`;\n\t};\n\n\tlet get_pointer_pos = (e) => {\n\t\tconst rect = canvas.interface.getBoundingClientRect();\n\n\t\tlet clientX = e.clientX;\n\t\tlet clientY = e.clientY;\n\t\tif (e.changedTouches && e.changedTouches.length > 0) {\n\t\t\tclientX = e.changedTouches[0].clientX;\n\t\t\tclientY = e.changedTouches[0].clientY;\n\t\t}\n\n\t\treturn {\n\t\t\tx: ((clientX - rect.left) / rect.width) * width,\n\t\t\ty: ((clientY - rect.top) / rect.height) * height\n\t\t};\n\t};\n\n\tlet handle_pointer_move = (x, y) => {\n\t\tlazy.update({ x: x, y: y });\n\t\tconst is_disabled = !lazy.isEnabled();\n\t\tif ((is_pressing && !is_drawing) || (is_disabled && is_pressing)) {\n\t\t\tis_drawing = true;\n\t\t\tpoints.push(lazy.brush.toObject());\n\t\t}\n\t\tif (is_drawing) {\n\t\t\tpoints.push(lazy.brush.toObject());\n\t\t\tdraw_points({\n\t\t\t\tpoints: points,\n\t\t\t\tbrush_color,\n\t\t\t\tbrush_radius,\n\t\t\t\tmask: mode === \"mask\"\n\t\t\t});\n\t\t}\n\t\tmouse_has_moved = true;\n\t};\n\n\tlet draw_points = ({ points, brush_color, brush_radius, mask }) => {\n\t\tif (!points || points.length < 2) return;\n\t\tlet target_ctx = mask ? ctx.mask : ctx.temp;\n\t\ttarget_ctx.lineJoin = \"round\";\n\t\ttarget_ctx.lineCap = \"round\";\n\n\t\ttarget_ctx.strokeStyle = brush_color;\n\t\ttarget_ctx.lineWidth = brush_radius;\n\t\tlet p1 = points[0];\n\t\tlet p2 = points[1];\n\t\ttarget_ctx.moveTo(p2.x, p2.y);\n\t\ttarget_ctx.beginPath();\n\t\tfor (var i = 1, len = points.length; i < len; i++) {\n\t\t\tvar midPoint = mid_point(p1, p2);\n\t\t\ttarget_ctx.quadraticCurveTo(p1.x, p1.y, midPoint.x, midPoint.y);\n\t\t\tp1 = points[i];\n\t\t\tp2 = points[i + 1];\n\t\t}\n\n\t\ttarget_ctx.lineTo(p1.x, p1.y);\n\t\ttarget_ctx.stroke();\n\t};\n\n\tlet save_mask_line = () => {\n\t\tif (points.length < 1) return;\n\t\tpoints.length = 0;\n\n\t\ttrigger_on_change();\n\t};\n\n\tlet saveLine = () => {\n\t\tif (points.length < 1) return;\n\n\t\tlines.push({\n\t\t\tpoints: points.slice(),\n\t\t\tbrush_color: brush_color,\n\t\t\tbrush_radius\n\t\t});\n\n\t\tif (mode !== \"mask\") {\n\t\t\tpoints.length = 0;\n\t\t}\n\n\t\tctx.drawing.drawImage(canvas.temp, 0, 0, width, height);\n\n\t\ttrigger_on_change();\n\t};\n\n\tlet trigger_on_change = () => {\n\t\tconst x = get_image_data();\n\t\tdispatch(\"change\", x);\n\t};\n\n\texport function clear() {\n\t\tlines = [];\n\t\tclear_canvas();\n\t\tline_count = 0;\n\n\t\treturn true;\n\t}\n\n\tfunction clear_canvas() {\n\t\tvalues_changed = true;\n\t\tctx.temp.clearRect(0, 0, width, height);\n\n\t\tctx.temp.fillStyle = mode === \"mask\" ? \"transparent\" : \"#FFFFFF\";\n\t\tctx.temp.fillRect(0, 0, width, height);\n\n\t\tif (mode === \"mask\") {\n\t\t\tctx.mask.clearRect(0, 0, canvas.mask.width, canvas.mask.height);\n\t\t}\n\t}\n\n\tlet loop = ({ once = false } = {}) => {\n\t\tif (mouse_has_moved || values_changed) {\n\t\t\tconst pointer = lazy.getPointerCoordinates();\n\t\t\tconst brush = lazy.getBrushCoordinates();\n\t\t\tdraw_interface(ctx.interface, pointer, brush);\n\t\t\tmouse_has_moved = false;\n\t\t\tvalues_changed = false;\n\t\t}\n\t\tif (!once) {\n\t\t\twindow.requestAnimationFrame(() => {\n\t\t\t\tloop();\n\t\t\t});\n\t\t}\n\t};\n\n\t$: brush_dot = brush_radius * 0.075;\n\n\tlet draw_interface = (ctx, pointer, brush) => {\n\t\tctx.clearRect(0, 0, width, height);\n\n\t\t// brush preview\n\t\tctx.beginPath();\n\t\tctx.fillStyle = brush_color;\n\t\tctx.arc(brush.x, brush.y, brush_radius / 2, 0, Math.PI * 2, true);\n\t\tctx.fill();\n\n\t\t// tiny brush point dot\n\t\tctx.beginPath();\n\t\tctx.fillStyle = catenary_color;\n\t\tctx.arc(brush.x, brush.y, brush_dot, 0, Math.PI * 2, true);\n\t\tctx.fill();\n\t};\n\n\texport function get_image_data() {\n\t\treturn mode === \"mask\"\n\t\t\t? canvas.mask.toDataURL(\"image/png\")\n\t\t\t: canvas.drawing.toDataURL(\"image/jpg\");\n\t}\n</script>\n\n<div\n\tclass=\"wrap\"\n\tbind:this={canvas_container}\n\tbind:offsetWidth={canvas_width}\n\tbind:offsetHeight={canvas_height}\n>\n\t{#if line_count === 0}\n\t\t<div transition:fade={{ duration: 50 }} class=\"start-prompt\">\n\t\t\tStart drawing\n\t\t</div>\n\t{/if}\n\t{#each canvas_types as { name, zIndex, opacity }}\n\t\t<canvas\n\t\t\tkey={name}\n\t\t\tstyle=\" z-index:{zIndex};\"\n\t\t\tstyle:opacity\n\t\t\tclass:lr={add_lr_border}\n\t\t\tclass:tb={!add_lr_border}\n\t\t\tbind:this={canvas[name]}\n\t\t\ton:mousedown={name === \"interface\" ? handle_draw_start : undefined}\n\t\t\ton:mousemove={name === \"interface\" ? handle_draw_move : undefined}\n\t\t\ton:mouseup={name === \"interface\" ? handle_draw_end : undefined}\n\t\t\ton:mouseout={name === \"interface\" ? handle_draw_end : undefined}\n\t\t\ton:blur={name === \"interface\" ? handle_draw_end : undefined}\n\t\t\ton:touchstart={name === \"interface\" ? handle_draw_start : undefined}\n\t\t\ton:touchmove={name === \"interface\" ? handle_draw_move : undefined}\n\t\t\ton:touchend={name === \"interface\" ? handle_draw_end : undefined}\n\t\t\ton:touchcancel={name === \"interface\" ? handle_draw_end : undefined}\n\t\t\ton:click|stopPropagation\n\t\t/>\n\t{/each}\n</div>\n\n<style>\n\tcanvas {\n\t\tdisplay: block;\n\t\tposition: absolute;\n\t\ttop: 0px;\n\t\tright: 0px;\n\t\tbottom: 0px;\n\t\tleft: 0px;\n\t\tmargin: auto;\n\t}\n\n\t.lr {\n\t\tborder-right: 1px solid var(--border-color-primary);\n\t\tborder-left: 1px solid var(--border-color-primary);\n\t}\n\n\t.tb {\n\t\tborder-top: 1px solid var(--border-color-primary);\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t}\n\n\tcanvas:hover {\n\t\tcursor: none;\n\t}\n\n\t.wrap {\n\t\tposition: relative;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\ttouch-action: none;\n\t}\n\n\t.start-prompt {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: 0px;\n\t\tright: 0px;\n\t\tbottom: 0px;\n\t\tleft: 0px;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tz-index: var(--layer-4);\n\t\ttouch-action: none;\n\t\tpointer-events: none;\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { IconButton } from \"@gradio/atoms\";\n\timport { Undo, Clear, Erase } from \"@gradio/icons\";\n\n\tconst dispatch = createEventDispatcher();\n\n\texport let show_eraser = false;\n</script>\n\n<div>\n\t<IconButton Icon={Undo} label=\"Undo\" on:click={() => dispatch(\"undo\")} />\n\n\t{#if show_eraser}\n\t\t<IconButton\n\t\t\tIcon={Erase}\n\t\t\tlabel=\"Clear\"\n\t\t\ton:click={(event) => {\n\t\t\t\tdispatch(\"clear_mask\");\n\t\t\t\tevent.stopPropagation();\n\t\t\t}}\n\t\t/>\n\t{/if}\n\n\t<IconButton\n\t\tIcon={Clear}\n\t\tlabel=\"Remove Image\"\n\t\ton:click={(event) => {\n\t\t\tdispatch(\"remove_image\");\n\t\t\tevent.stopPropagation();\n\t\t}}\n\t/>\n</div>\n\n<style>\n\tdiv {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: var(--size-2);\n\t\tright: var(--size-2);\n\t\tjustify-content: flex-end;\n\t\tgap: var(--spacing-sm);\n\t\tz-index: var(--layer-5);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { IconButton } from \"@gradio/atoms\";\n\timport { Brush, Color } from \"@gradio/icons\";\n\n\tlet show_size = false;\n\tlet show_col = false;\n\n\texport let brush_radius = 20;\n\texport let brush_color = \"#000\";\n\texport let container_height: number;\n\texport let img_width: number;\n\texport let img_height: number;\n\texport let mode: \"mask\" | \"other\" = \"other\";\n\n\t$: width = container_height * (img_width / img_height);\n</script>\n\n<div class=\"wrap\">\n\t<span class=\"brush\">\n\t\t<IconButton\n\t\t\tIcon={Brush}\n\t\t\tlabel=\"Use brush\"\n\t\t\ton:click={() => (show_size = !show_size)}\n\t\t/>\n\t\t{#if show_size}\n\t\t\t<input\n\t\t\t\taria-label=\"Brush radius\"\n\t\t\t\tbind:value={brush_radius}\n\t\t\t\ttype=\"range\"\n\t\t\t\tmin={0.5 * (img_width / width)}\n\t\t\t\tmax={75 * (img_width / width)}\n\t\t\t/>\n\t\t{/if}\n\t</span>\n\n\t{#if mode !== \"mask\"}\n\t\t<span class=\"col\">\n\t\t\t<IconButton\n\t\t\t\tIcon={Color}\n\t\t\t\tlabel=\"Select brush color\"\n\t\t\t\ton:click={() => (show_col = !show_col)}\n\t\t\t/>\n\t\t\t{#if show_col}\n\t\t\t\t<input aria-label=\"Brush color\" bind:value={brush_color} type=\"color\" />\n\t\t\t{/if}\n\t\t</span>\n\t{/if}\n</div>\n\n<style>\n\t.wrap {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: var(--size-10);\n\t\tright: var(--size-2);\n\t\tflex-direction: column;\n\t\tjustify-content: flex-end;\n\t\tgap: var(--spacing-sm);\n\t\tz-index: var(--layer-5);\n\t}\n\t.brush {\n\t\ttop: 0;\n\t\tright: 0;\n\t}\n\n\t.brush input {\n\t\tposition: absolute;\n\t\ttop: 3px;\n\t\tright: calc(100% + 5px);\n\t}\n\n\t.col input {\n\t\tposition: absolute;\n\t\tright: calc(100% + 5px);\n\t\tbottom: -4px;\n\t}\n</style>\n", "<script lang=\"ts\">\n\t// @ts-nocheck\n\timport { createEventDispatcher, tick, onMount } from \"svelte\";\n\timport { BlockLabel } from \"@gradio/atoms\";\n\timport { Image, Sketch as SketchIcon } from \"@gradio/icons\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { get_coordinates_of_clicked_image } from \"../shared/utils\";\n\n\timport Cropper from \"./Cropper.svelte\";\n\timport Sketch from \"./Sketch.svelte\";\n\timport Webcam from \"./Webcam.svelte\";\n\timport ModifySketch from \"./ModifySketch.svelte\";\n\timport SketchSettings from \"./SketchSettings.svelte\";\n\n\timport { Upload, ModifyUpload } from \"@gradio/upload\";\n\n\texport let value:\n\t\t| null\n\t\t| string\n\t\t| { image: string | null; mask: string | null };\n\texport let label: string | undefined = undefined;\n\texport let show_label: boolean;\n\n\texport let source: \"canvas\" | \"webcam\" | \"upload\" = \"upload\";\n\texport let tool: \"editor\" | \"select\" | \"sketch\" | \"color-sketch\" = \"editor\";\n\texport let shape: [number, number];\n\texport let streaming = false;\n\texport let pending = false;\n\texport let mirror_webcam: boolean;\n\texport let brush_radius: number;\n\texport let brush_color = \"#000000\";\n\texport let mask_opacity;\n\texport let selectable = false;\n\n\tlet sketch: Sketch;\n\tlet cropper: Cropper;\n\n\tif (\n\t\tvalue &&\n\t\t(source === \"upload\" || source === \"webcam\") &&\n\t\ttool === \"sketch\"\n\t) {\n\t\tvalue = { image: value as string, mask: null };\n\t}\n\n\tfunction handle_upload({ detail }: CustomEvent<string>): void {\n\t\tif (tool === \"color-sketch\") {\n\t\t\tstatic_image = detail;\n\t\t} else {\n\t\t\tvalue =\n\t\t\t\t(source === \"upload\" || source === \"webcam\") && tool === \"sketch\"\n\t\t\t\t\t? { image: detail, mask: null }\n\t\t\t\t\t: detail;\n\t\t}\n\t\tdispatch(\"upload\", detail);\n\t}\n\n\tfunction handle_clear({ detail }: CustomEvent<null>): void {\n\t\tvalue = null;\n\t\tstatic_image = undefined;\n\t\tdispatch(\"clear\");\n\t}\n\n\tasync function handle_save(\n\t\t{ detail }: { detail: string },\n\t\tinitial\n\t): Promise<void> {\n\t\tif (mode === \"mask\") {\n\t\t\tif (source === \"webcam\" && initial) {\n\t\t\t\tvalue = {\n\t\t\t\t\timage: detail,\n\t\t\t\t\tmask: null\n\t\t\t\t};\n\t\t\t} else {\n\t\t\t\tvalue = {\n\t\t\t\t\timage: typeof value === \"string\" ? value : value?.image || null,\n\t\t\t\t\tmask: detail\n\t\t\t\t};\n\t\t\t}\n\t\t} else if (\n\t\t\t(source === \"upload\" || source === \"webcam\") &&\n\t\t\ttool === \"sketch\"\n\t\t) {\n\t\t\tvalue = { image: detail, mask: null };\n\t\t} else {\n\t\t\tvalue = detail;\n\t\t}\n\n\t\tawait tick();\n\n\t\tdispatch(streaming ? \"stream\" : \"edit\");\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: string | null;\n\t\tstream: string | null;\n\t\tedit: undefined;\n\t\tclear: undefined;\n\t\tdrag: boolean;\n\t\tupload: FileData;\n\t\tselect: SelectData;\n\t}>();\n\n\tlet dragging = false;\n\n\t$: dispatch(\"drag\", dragging);\n\n\tfunction handle_image_load(event: Event): void {\n\t\tconst element = event.currentTarget as HTMLImageElement;\n\t\timg_width = element.naturalWidth;\n\t\timg_height = element.naturalHeight;\n\t\tcontainer_height = element.getBoundingClientRect().height;\n\t}\n\n\tasync function handle_sketch_clear(): Promise<void> {\n\t\tsketch.clear();\n\t\tawait tick();\n\t\tvalue = null;\n\t\tstatic_image = undefined;\n\t}\n\n\tasync function handle_mask_clear(): Promise<void> {\n\t\tsketch.clear_mask();\n\t\tawait tick();\n\t}\n\n\tlet img_height = 0;\n\tlet img_width = 0;\n\tlet container_height = 0;\n\n\tlet mode;\n\n\t$: {\n\t\tif (source === \"canvas\" && tool === \"sketch\") {\n\t\t\tmode = \"bw-sketch\";\n\t\t} else if (tool === \"color-sketch\") {\n\t\t\tmode = \"color-sketch\";\n\t\t} else if (\n\t\t\t(source === \"upload\" || source === \"webcam\") &&\n\t\t\ttool === \"sketch\"\n\t\t) {\n\t\t\tmode = \"mask\";\n\t\t} else {\n\t\t\tmode = \"editor\";\n\t\t}\n\t}\n\tlet value_img;\n\tlet max_height;\n\tlet max_width;\n\n\tlet static_image = undefined;\n\n\t$: {\n\t\tif (value === null || (value.image === null && value.mask === null)) {\n\t\t\tstatic_image = undefined;\n\t\t}\n\t}\n\n\t$: {\n\t\tif (cropper) {\n\t\t\tif (value) {\n\t\t\t\tcropper.image = value;\n\t\t\t\tcropper.create();\n\t\t\t} else {\n\t\t\t\tcropper.destroy();\n\t\t\t}\n\t\t}\n\t}\n\n\tonMount(async () => {\n\t\tif (tool === \"color-sketch\" && value && typeof value === \"string\") {\n\t\t\tstatic_image = value;\n\t\t\tawait tick();\n\t\t\thandle_image_load({ currentTarget: value_img });\n\t\t}\n\t});\n\n\tfunction handle_click(evt: MouseEvent): void {\n\t\tlet coordinates = get_coordinates_of_clicked_image(evt);\n\t\tif (coordinates) {\n\t\t\tdispatch(\"select\", { index: coordinates, value: null });\n\t\t}\n\t}\n</script>\n\n<BlockLabel\n\t{show_label}\n\tIcon={source === \"canvas\" ? SketchIcon : Image}\n\tlabel={label || (source === \"canvas\" ? \"Sketch\" : \"Image\")}\n/>\n\n<div\n\tdata-testid=\"image\"\n\tclass=\"image-container\"\n\tbind:offsetHeight={max_height}\n\tbind:offsetWidth={max_width}\n>\n\t{#if source === \"upload\"}\n\t\t<Upload\n\t\t\tbind:dragging\n\t\t\tfiletype=\"image/*\"\n\t\t\ton:load={handle_upload}\n\t\t\tinclude_file_metadata={false}\n\t\t\tdisable_click={!!value}\n\t\t>\n\t\t\t{#if (value === null && !static_image) || streaming}\n\t\t\t\t<slot />\n\t\t\t{:else if tool === \"select\"}\n\t\t\t\t<Cropper bind:this={cropper} image={value} on:crop={handle_save} />\n\t\t\t\t<ModifyUpload on:clear={(e) => (handle_clear(e), (tool = \"editor\"))} />\n\t\t\t{:else if tool === \"editor\"}\n\t\t\t\t<ModifyUpload\n\t\t\t\t\ton:edit={() => (tool = \"select\")}\n\t\t\t\t\ton:clear={handle_clear}\n\t\t\t\t\teditable\n\t\t\t\t/>\n\n\t\t\t\t<!-- TODO: fix-->\n\t\t\t\t<!-- svelte-ignore a11y-click-events-have-key-events -->\n\t\t\t\t<!-- svelte-ignore a11y-no-noninteractive-element-interactions-->\n\t\t\t\t<img\n\t\t\t\t\tsrc={value}\n\t\t\t\t\talt=\"\"\n\t\t\t\t\tclass:scale-x-[-1]={source === \"webcam\" && mirror_webcam}\n\t\t\t\t\tclass:selectable\n\t\t\t\t\ton:click={handle_click}\n\t\t\t\t/>\n\t\t\t{:else if (tool === \"sketch\" || tool === \"color-sketch\") && (value !== null || static_image)}\n\t\t\t\t{#key static_image}\n\t\t\t\t\t<img\n\t\t\t\t\t\tbind:this={value_img}\n\t\t\t\t\t\tclass=\"absolute-img\"\n\t\t\t\t\t\tsrc={static_image || value?.image || value}\n\t\t\t\t\t\talt=\"\"\n\t\t\t\t\t\ton:load={handle_image_load}\n\t\t\t\t\t\tclass:webcam={source === \"webcam\" && mirror_webcam}\n\t\t\t\t\t/>\n\t\t\t\t{/key}\n\t\t\t\t{#if img_width > 0}\n\t\t\t\t\t<Sketch\n\t\t\t\t\t\t{value}\n\t\t\t\t\t\tbind:this={sketch}\n\t\t\t\t\t\tbind:brush_radius\n\t\t\t\t\t\tbind:brush_color\n\t\t\t\t\t\t{mask_opacity}\n\t\t\t\t\t\ton:change={handle_save}\n\t\t\t\t\t\t{mode}\n\t\t\t\t\t\twidth={img_width || max_width}\n\t\t\t\t\t\theight={img_height || max_height}\n\t\t\t\t\t\tcontainer_height={container_height || max_height}\n\t\t\t\t\t\t{value_img}\n\t\t\t\t\t\t{source}\n\t\t\t\t\t\t{shape}\n\t\t\t\t\t/>\n\t\t\t\t\t<ModifySketch\n\t\t\t\t\t\tshow_eraser={value_img}\n\t\t\t\t\t\ton:undo={() => sketch.undo()}\n\t\t\t\t\t\ton:clear_mask={handle_mask_clear}\n\t\t\t\t\t\ton:remove_image={handle_sketch_clear}\n\t\t\t\t\t/>\n\t\t\t\t\t{#if tool === \"color-sketch\" || tool === \"sketch\"}\n\t\t\t\t\t\t<SketchSettings\n\t\t\t\t\t\t\tbind:brush_radius\n\t\t\t\t\t\t\tbind:brush_color\n\t\t\t\t\t\t\tcontainer_height={container_height || max_height}\n\t\t\t\t\t\t\timg_width={img_width || max_width}\n\t\t\t\t\t\t\timg_height={img_height || max_height}\n\t\t\t\t\t\t\t{mode}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{/if}\n\t\t\t\t{/if}\n\t\t\t{:else}\n\t\t\t\t<!-- TODO: fix-->\n\t\t\t\t<!-- svelte-ignore a11y-click-events-have-key-events -->\n\t\t\t\t<!-- svelte-ignore a11y-no-noninteractive-element-interactions-->\n\t\t\t\t<img\n\t\t\t\t\tsrc={value.image || value}\n\t\t\t\t\talt=\"hello\"\n\t\t\t\t\tclass:webcam={source === \"webcam\" && mirror_webcam}\n\t\t\t\t\tclass:selectable\n\t\t\t\t\ton:click={handle_click}\n\t\t\t\t/>\n\t\t\t{/if}\n\t\t</Upload>\n\t{:else if source === \"canvas\"}\n\t\t<ModifySketch\n\t\t\ton:undo={() => sketch.undo()}\n\t\t\ton:remove_image={handle_sketch_clear}\n\t\t/>\n\t\t{#if tool === \"color-sketch\"}\n\t\t\t<SketchSettings\n\t\t\t\tbind:brush_radius\n\t\t\t\tbind:brush_color\n\t\t\t\tcontainer_height={container_height || max_height}\n\t\t\t\timg_width={img_width || max_width}\n\t\t\t\timg_height={img_height || max_height}\n\t\t\t/>\n\t\t{/if}\n\t\t<Sketch\n\t\t\t{value}\n\t\t\tbind:brush_radius\n\t\t\tbind:brush_color\n\t\t\tbind:this={sketch}\n\t\t\ton:change={handle_save}\n\t\t\ton:clear={handle_sketch_clear}\n\t\t\t{mode}\n\t\t\twidth={img_width || max_width}\n\t\t\theight={img_height || max_height}\n\t\t\tcontainer_height={container_height || max_height}\n\t\t\t{shape}\n\t\t/>\n\t{:else if (value === null && !static_image) || streaming}\n\t\t{#if source === \"webcam\" && !static_image}\n\t\t\t<Webcam\n\t\t\t\ton:capture={(e) =>\n\t\t\t\t\ttool === \"color-sketch\" ? handle_upload(e) : handle_save(e, true)}\n\t\t\t\ton:stream={handle_save}\n\t\t\t\ton:error\n\t\t\t\t{streaming}\n\t\t\t\t{pending}\n\t\t\t\t{mirror_webcam}\n\t\t\t/>\n\t\t{/if}\n\t{:else if tool === \"select\"}\n\t\t<Cropper bind:this={cropper} image={value} on:crop={handle_save} />\n\t\t<ModifyUpload on:clear={(e) => (handle_clear(e), (tool = \"editor\"))} />\n\t{:else if tool === \"editor\"}\n\t\t<ModifyUpload\n\t\t\ton:edit={() => (tool = \"select\")}\n\t\t\ton:clear={handle_clear}\n\t\t\teditable\n\t\t/>\n\n\t\t<!-- TODO: fix -->\n\t\t<!-- svelte-ignore a11y-click-events-have-key-events -->\n\t\t<!-- svelte-ignore a11y-no-noninteractive-element-interactions-->\n\n\t\t<img\n\t\t\tsrc={value}\n\t\t\talt=\"\"\n\t\t\tclass:selectable\n\t\t\tclass:webcam={source === \"webcam\" && mirror_webcam}\n\t\t\ton:click={handle_click}\n\t\t/>\n\t{:else if (tool === \"sketch\" || tool === \"color-sketch\") && (value !== null || static_image)}\n\t\t{#key static_image}\n\t\t\t<img\n\t\t\t\tbind:this={value_img}\n\t\t\t\tclass=\"absolute-img\"\n\t\t\t\tsrc={static_image || value?.image || value}\n\t\t\t\talt=\"\"\n\t\t\t\ton:load={handle_image_load}\n\t\t\t\tclass:webcam={source === \"webcam\" && mirror_webcam}\n\t\t\t/>\n\t\t{/key}\n\t\t{#if img_width > 0}\n\t\t\t<Sketch\n\t\t\t\t{value}\n\t\t\t\tbind:this={sketch}\n\t\t\t\tbind:brush_radius\n\t\t\t\tbind:brush_color\n\t\t\t\ton:change={handle_save}\n\t\t\t\t{mode}\n\t\t\t\twidth={img_width || max_width}\n\t\t\t\theight={img_height || max_height}\n\t\t\t\tcontainer_height={container_height || max_height}\n\t\t\t\t{value_img}\n\t\t\t\t{source}\n\t\t\t/>\n\t\t\t<ModifySketch\n\t\t\t\ton:undo={() => sketch.undo()}\n\t\t\t\ton:remove_image={handle_sketch_clear}\n\t\t\t/>\n\t\t\t{#if tool === \"color-sketch\" || tool === \"sketch\"}\n\t\t\t\t<SketchSettings\n\t\t\t\t\tbind:brush_radius\n\t\t\t\t\tbind:brush_color\n\t\t\t\t\tcontainer_height={container_height || max_height}\n\t\t\t\t\timg_width={img_width || max_width}\n\t\t\t\t\timg_height={img_height || max_height}\n\t\t\t\t\t{mode}\n\t\t\t\t/>\n\t\t\t{/if}\n\t\t{/if}\n\t{:else}\n\t\t<!-- TODO: fix -->\n\t\t<!-- svelte-ignore a11y-click-events-have-key-events -->\n\t\t<!-- svelte-ignore a11y-no-noninteractive-element-interactions-->\n\n\t\t<img\n\t\t\tsrc={value.image || value}\n\t\t\talt=\"\"\n\t\t\tclass:webcam={source === \"webcam\" && mirror_webcam}\n\t\t\tclass:selectable\n\t\t\ton:click={handle_click}\n\t\t/>\n\t{/if}\n</div>\n\n<style>\n\t.image-container,\n\timg {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\timg {\n\t\tobject-fit: contain;\n\t}\n\n\t.selectable {\n\t\tcursor: crosshair;\n\t}\n\n\t.absolute-img {\n\t\tposition: absolute;\n\t\topacity: 0;\n\t}\n\n\t.webcam {\n\t\ttransform: scaleX(-1);\n\t}\n</style>\n", "<svelte:options accessors={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData, ShareData } from \"@gradio/utils\";\n\timport Image from \"./Image.svelte\";\n\n\timport { Block } from \"@gradio/atoms\";\n\timport { _ } from \"svelte-i18n\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { UploadText } from \"@gradio/atoms\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: null | string = null;\n\texport let source: \"canvas\" | \"webcam\" | \"upload\" = \"upload\";\n\texport let tool: \"editor\" | \"select\" | \"sketch\" | \"color-sketch\" = \"editor\";\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let streaming: boolean;\n\texport let pending: boolean;\n\texport let height: number | undefined;\n\texport let width: number | undefined;\n\texport let mirror_webcam: boolean;\n\texport let shape: [number, number];\n\texport let brush_radius: number;\n\texport let brush_color: string;\n\texport let mask_opacity: number;\n\texport let selectable = false;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\terror: string;\n\t\tedit: never;\n\t\tstream: never;\n\t\tdrag: never;\n\t\tupload: never;\n\t\tclear: never;\n\t\tselect: SelectData;\n\t\tshare: ShareData;\n\t}>;\n\n\t$: value, gradio.dispatch(\"change\");\n\tlet dragging: boolean;\n\tconst FIXED_HEIGHT = 240;\n\n\t$: value = !value ? null : value;\n</script>\n\n<Block\n\t{visible}\n\tvariant={value === null && source === \"upload\" ? \"dashed\" : \"solid\"}\n\tborder_mode={dragging ? \"focus\" : \"base\"}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\theight={height || (source === \"webcam\" ? undefined : FIXED_HEIGHT)}\n\t{width}\n\tallow_overflow={false}\n\t{container}\n\t{scale}\n\t{min_width}\n>\n\t<StatusTracker {...loading_status} />\n\n\t<Image\n\t\t{brush_radius}\n\t\t{brush_color}\n\t\t{shape}\n\t\tbind:value\n\t\t{source}\n\t\t{tool}\n\t\t{selectable}\n\t\t{mask_opacity}\n\t\ton:edit={() => gradio.dispatch(\"edit\")}\n\t\ton:clear={() => gradio.dispatch(\"clear\")}\n\t\ton:stream={() => gradio.dispatch(\"stream\")}\n\t\ton:drag={({ detail }) => (dragging = detail)}\n\t\ton:upload={() => gradio.dispatch(\"upload\")}\n\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\ton:share={({ detail }) => gradio.dispatch(\"share\", detail)}\n\t\ton:error={({ detail }) => {\n\t\t\tloading_status = loading_status || {};\n\t\t\tloading_status.status = \"error\";\n\t\t\tgradio.dispatch(\"error\", detail);\n\t\t}}\n\t\t{label}\n\t\t{show_label}\n\t\t{pending}\n\t\t{streaming}\n\t\t{mirror_webcam}\n\t>\n\t\t<UploadText type=\"image\" />\n\t</Image>\n</Block>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path0", "path1", "path", "circle", "circle0", "circle1", "circle2", "circle3", "circle4", "rect", "ctx", "button", "is_function", "div", "create_if_block", "video", "video_source", "canvas", "streaming", "$$props", "pending", "mode", "mirror_webcam", "include_audio", "dispatch", "createEventDispatcher", "onMount", "access_webcam", "stream", "err", "$_", "take_picture", "context", "data", "recording", "recorded_blobs", "mimeType", "media_recorder", "take_recording", "video_blob", "ReaderObj", "e", "validMimeTypes", "validMimeType", "$$invalidate", "$$value", "ownKeys", "object", "enumerableOnly", "keys", "symbols", "sym", "_objectSpread2", "i", "source", "key", "_defineProperty", "_typeof", "obj", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "props", "descriptor", "_createClass", "protoProps", "staticProps", "value", "_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "_arrayLikeToArray", "iter", "o", "minLen", "n", "len", "arr2", "IS_BROWSER", "WINDOW", "IS_TOUCH_DEVICE", "HAS_POINTER_EVENT", "NAMESPACE", "ACTION_ALL", "ACTION_CROP", "ACTION_MOVE", "ACTION_ZOOM", "ACTION_EAST", "ACTION_WEST", "ACTION_SOUTH", "ACTION_NORTH", "ACTION_NORTH_EAST", "ACTION_NORTH_WEST", "ACTION_SOUTH_EAST", "ACTION_SOUTH_WEST", "CLASS_CROP", "CLASS_DISABLED", "CLASS_HIDDEN", "CLASS_HIDE", "CLASS_INVISIBLE", "CLASS_MODAL", "CLASS_MOVE", "DATA_ACTION", "DATA_PREVIEW", "DRAG_MODE_CROP", "DRAG_MODE_MOVE", "DRAG_MODE_NONE", "EVENT_CROP", "EVENT_CROP_END", "EVENT_CROP_MOVE", "EVENT_CROP_START", "EVENT_DBLCLICK", "EVENT_TOUCH_START", "EVENT_TOUCH_MOVE", "EVENT_TOUCH_END", "EVENT_POINTER_DOWN", "EVENT_POINTER_MOVE", "EVENT_POINTER_UP", "EVENT_READY", "EVENT_RESIZE", "EVENT_WHEEL", "EVENT_ZOOM", "MIME_TYPE_JPEG", "REGEXP_ACTIONS", "REGEXP_DATA_URL", "REGEXP_DATA_URL_JPEG", "REGEXP_TAG_NAME", "MIN_CONTAINER_WIDTH", "MIN_CONTAINER_HEIGHT", "DEFAULTS", "TEMPLATE", "isNaN", "isNumber", "isPositiveNumber", "isUndefined", "isObject", "hasOwnProperty", "isPlainObject", "_constructor", "prototype", "isFunction", "slice", "toArray", "for<PERSON>ach", "callback", "assign", "_len", "args", "_key", "arg", "REGEXP_DECIMALS", "normalizeDecimalNumber", "times", "REGEXP_SUFFIX", "setStyle", "element", "styles", "style", "property", "hasClass", "addClass", "elem", "className", "removeClass", "toggleClass", "added", "REGEXP_CAMEL_CASE", "toParamCase", "getData", "name", "setData", "removeData", "REGEXP_SPACES", "onceSupported", "supported", "once", "listener", "options", "removeListener", "type", "handler", "event", "listeners", "addListener", "_handler", "_element$listeners", "_len2", "_key2", "dispatchEvent", "getOffset", "box", "location", "REGEXP_ORIGINS", "isCrossOriginURL", "url", "parts", "addTimestamp", "timestamp", "getTransforms", "_ref", "rotate", "scaleX", "scaleY", "translateX", "translateY", "values", "transform", "getMaxZoomRatio", "pointers", "pointers2", "maxRatio", "pointer", "pointerId", "pointer2", "x1", "y1", "x2", "y2", "z1", "z2", "ratio", "getPointer", "_ref2", "endOnly", "pageX", "pageY", "end", "getPointersCenter", "count", "_ref3", "startX", "startY", "getAdjustedSizes", "_ref4", "aspectRatio", "height", "width", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isValidHeight", "adjustedWidth", "getRotatedSizes", "_ref5", "degree", "arc", "sinArc", "cosArc", "newWidth", "newHeight", "getSourceCanvas", "image", "_ref6", "_ref7", "_ref8", "imageAspectRatio", "imageNaturalWidth", "imageNaturalHeight", "_ref6$rotate", "_ref6$scaleX", "_ref6$scaleY", "naturalWidth", "naturalHeight", "_ref8$fillColor", "fillColor", "_ref8$imageSmoothingE", "imageSmoothingEnabled", "_ref8$imageSmoothingQ", "imageSmoothingQuality", "_ref8$maxWidth", "max<PERSON><PERSON><PERSON>", "_ref8$maxHeight", "maxHeight", "_ref8$minWidth", "min<PERSON><PERSON><PERSON>", "_ref8$minHeight", "minHeight", "maxSizes", "minSizes", "destMaxSizes", "destMinSizes", "destWidth", "destHeight", "params", "param", "fromCharCode", "getStringFromCharCode", "dataView", "start", "length", "str", "REGEXP_DATA_URL_HEAD", "dataURLToArrayBuffer", "dataURL", "base64", "binary", "arrayBuffer", "uint8", "arrayBufferToDataURL", "chunks", "chunkSize", "resetAndGetOrientation", "orientation", "littleEndian", "app1Start", "ifdStart", "offset", "exifIDCode", "tiffOffset", "endianness", "firstIFDOffset", "_length", "_offset", "parseOrientation", "render", "container", "cropper", "containerData", "imageData", "viewMode", "rotated", "canvasWidth", "canvasHeight", "canvasData", "sizeLimited", "positionLimited", "cropBoxData", "cropped", "minCanvasWidth", "minCanvasHeight", "_getAdjustedSizes", "newCanvasLeft", "newCanvasTop", "changed", "transformed", "_getRotatedSizes", "autoCropArea", "limited", "minCropBoxWidth", "minCropBoxHeight", "maxCropBox<PERSON>idth", "maxCropBoxHeight", "preview", "crossOrigin", "alt", "previews", "el", "img", "cropBoxWidth", "cropBoxHeight", "left", "top", "originalWidth", "originalHeight", "events", "handlers", "ratioX", "ratioY", "_this", "delta", "buttons", "action", "touch", "change", "right", "bottom", "minLeft", "minTop", "renderable", "range", "check", "side", "p", "methods", "hasSameSize", "offsetX", "offsetY", "_this$canvasData", "x", "y", "_originalEvent", "pivot", "center", "_scaleX", "_scaleY", "rounded", "widthChanged", "heightChanged", "_this$getData", "initialX", "initialY", "initialWidth", "initialHeight", "_options$imageSmoothi", "sourceWidth", "sourceHeight", "srcX", "srcY", "srcWidth", "srcHeight", "dstX", "dstY", "dstWidth", "dstHeight", "scale", "dragBox", "face", "croppable", "movable", "AnotherCropper", "C<PERSON>per", "tagName", "xhr", "clone", "_parseOrientation", "crossOriginUrl", "_this2", "isIOSWebKit", "done", "sizingImage", "body", "template", "cropBox", "attr", "img_src_value", "destroy", "create", "image_data", "Point", "LazyPoint", "point", "angle", "distance", "angleRotated", "diff", "RADIUS_DEFAULT", "LazyBrush", "radius", "enabled", "initialPoint", "newPointerPoint", "both", "MapShim", "getIndex", "result", "entry", "index", "class_1", "entries", "_i", "_a", "<PERSON><PERSON><PERSON><PERSON>", "global$1", "requestAnimationFrame$1", "trailingTimeout", "throttle", "delay", "leadingCall", "trailingCall", "lastCallTime", "resolvePending", "proxy", "timeout<PERSON><PERSON><PERSON>", "timeStamp", "REFRESH_DELAY", "<PERSON><PERSON><PERSON><PERSON>", "mutationObserverSupported", "ResizeObserverController", "observer", "observers", "changesDetected", "activeObservers", "_b", "propertyName", "isReflowProperty", "defineConfigurable", "getWindowOf", "ownerGlobal", "emptyRect", "createRectInit", "toFloat", "getBordersSize", "positions", "size", "position", "getPaddings", "paddings", "positions_1", "getSVGContentRect", "bbox", "getHTMLElementContentRect", "clientWidth", "clientHeight", "horizPad", "vertPad", "isDocumentElement", "vertScrollbar", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isSVGGraphicsElement", "getContentRect", "createReadOnlyRect", "Constr", "ResizeObservation", "ResizeObserverEntry", "rectInit", "contentRect", "ResizeObserverSPI", "controller", "callbackCtx", "observations", "observation", "ResizeObserver", "method", "div_transition", "create_bidirectional_transition", "fade", "canvas_1", "listen", "if_block", "catenary_color", "mid_point", "p1", "p2", "value_img", "brush_color", "brush_radius", "mask_opacity", "container_height", "shape", "mounted", "canvas_width", "canvas_height", "last_value_img", "canvas_types", "points", "lines", "mouse_has_moved", "values_changed", "is_drawing", "is_pressing", "lazy", "canvas_container", "canvas_observer", "line_count", "draw_cropped_image", "_width", "_height", "shape_ratio", "image_ratio", "tick", "_", "trigger_on_change", "draw_lines", "rest", "handle_canvas_resize", "loop", "init", "clear", "initX", "initY", "onDestroy", "redraw_image", "_lines", "clear_canvas", "clear_mask", "undo", "line", "_points", "draw_points", "saveLine", "save_mask_line", "handle_draw_start", "get_pointer_pos", "handle_pointer_move", "handle_draw_move", "handle_draw_end", "old_width", "old_height", "old_container_height", "add_lr_border", "container_ratio", "dimensions", "container_dimensions", "set_canvas_size", "dpr", "clientX", "clientY", "is_disabled", "mask", "target_ctx", "midPoint", "get_image_data", "brush", "draw_interface", "brush_dot", "Erase", "Undo", "Clear", "show_eraser", "click_handler", "input", "Color", "create_if_block_1", "span", "Brush", "create_if_block_2", "if_block1", "show_size", "show_col", "img_width", "img_height", "to_number", "click_handler_1", "create_if_block_14", "create_if_block_10", "create_if_block_8", "dirty", "sketch_1_changes", "src_url_equal", "create_if_block_15", "sketchsettings_changes", "create_if_block_5", "create_if_block_6", "SketchIcon", "Image", "blocklabel_changes", "label", "show_label", "tool", "selectable", "sketch", "handle_upload", "detail", "static_image", "handle_clear", "handle_save", "initial", "dragging", "handle_image_load", "handle_sketch_clear", "handle_mask_clear", "max_height", "max_width", "handle_click", "evt", "coordinates", "get_coordinates_of_clicked_image", "clear_handler", "edit_handler", "undo_handler", "undo_handler_1", "clear_handler_1", "edit_handler_1", "undo_handler_2", "FIXED_HEIGHT", "block_changes", "elem_id", "elem_classes", "visible", "min_width", "loading_status", "gradio", "select_handler", "share_handler"], "mappings": "ivDAAAA,EASKC,EAAAC,EAAAC,CAAA,EARJC,EAGCF,EAAAG,CAAA,EACDD,EAGCF,EAAAI,CAAA,ulBCRFN,EAgBKC,EAAAC,EAAAC,CAAA,EAJJC,EAECF,EAAAK,CAAA,EACDH,EAA+BF,EAAAM,CAAA,meCfhCR,EAaKC,EAAAC,EAAAC,CAAA,EADJC,EAAgCF,EAAAM,CAAA,mhCCZjCR,EAUKC,EAAAC,EAAAC,CAAA,EATJC,EAAmDF,EAAAO,CAAA,EACnDL,EAAkDF,EAAAQ,CAAA,EAClDN,EAAmDF,EAAAS,CAAA,EACnDP,EAAmDF,EAAAU,CAAA,EACnDR,EAAmDF,EAAAW,CAAA,EACnDT,EAGCF,EAAAK,CAAA,wfCTFP,EASAC,EAAAC,EAAAC,CAAA,EAJEC,EAGCF,EAAAK,CAAA,kgBCRHP,EAaKC,EAAAC,EAAAC,CAAA,EADJC,EAAmEF,EAAAK,CAAA,qhBCZpEP,EAaKC,EAAAC,EAAAC,CAAA,EADJC,EAAwDF,EAAAY,CAAA,uKCsHjD,OAAAC,OAAS,QAAO,+FADtBf,EAgBQC,EAAAe,EAAAb,CAAA,oDAhBUc,GAAAF,OAAS,QAAUA,EAAY,CAAA,EAAGA,EAAc,CAAA,CAAA,IAAhDA,EAAI,CAAA,IAAK,QAAUA,EAAY,CAAA,EAAGA,EAAc,CAAA,GAAA,MAAA,KAAA,SAAA,0VAYhEf,EAEKC,EAAAiB,EAAAf,CAAA,oLAZAY,EAAS,CAAA,EAAA,8YAKbf,EAEKC,EAAAiB,EAAAf,CAAA,iOANLH,EAEKC,EAAAiB,EAAAf,CAAA,uIANHY,EAAS,CAAA,GAAAI,GAAAJ,CAAA,4FAD6BA,EAAa,CAAA,CAAA,4CAF1Df,EAsBKC,EAAAiB,EAAAf,CAAA,EApBJC,EAA4Dc,EAAAE,CAAA,qEAAhBL,EAAa,CAAA,CAAA,EACnDA,EAAS,CAAA,+NA3HXM,EACAC,EACO,CAAA,UAAAC,EAAY,EAAK,EAAAC,EACjB,CAAA,QAAAC,EAAU,EAAK,EAAAD,EAEf,CAAA,KAAAE,EAA0B,OAAO,EAAAF,GACjC,cAAAG,CAAsB,EAAAH,GACtB,cAAAI,CAAsB,EAAAJ,EAE3B,MAAAK,EAAWC,KAcjBC,OAAeT,EAAS,SAAS,cAAc,QAAQ,CAAA,iBAExCU,GAAa,KAE1BC,EAAe,MAAA,UAAU,aAAa,aACrC,CAAA,MAAO,GACP,MAAOL,CAAA,CAAA,MAERP,EAAa,UAAYY,EAAMZ,CAAA,MAC/BA,EAAa,MAAQ,GAAIA,CAAA,EACzBA,EAAa,KAAI,QACTa,GACJ,GAAAA,aAAe,cAAgBA,EAAI,MAAQ,kBAC9CL,EAAS,QAASM,EAAG,2BAA2B,CAAA,aAE1CD,YAKAE,GAAY,CAChB,IAAAC,EAAUf,EAAO,WAAW,IAAI,EAEhC,GAAAD,EAAa,YAAcA,EAAa,YAAW,CACtDC,EAAO,MAAQD,EAAa,WAC5BC,EAAO,OAASD,EAAa,YAC7BgB,EAAQ,UACPhB,EACA,EACA,EACAA,EAAa,WACbA,EAAa,WAAW,EAGrB,IAAAiB,EAAOhB,EAAO,UAAU,WAAW,EACvCO,EAASN,EAAY,SAAW,UAAWe,CAAI,GAI7C,IAAAC,EAAY,GACZC,EAAc,CAAA,EACdP,EACAQ,EACAC,WAEKC,GAAc,IAClBJ,EAAS,CACZG,EAAe,KAAI,EACf,IAAAE,MAAiB,KAAKJ,EAAkB,CAAA,KAAMC,CAAQ,CAAA,EACtDI,MAAgB,WACpBA,EAAU,OAAM,SAAaC,EAAC,CACzBA,EAAE,SACLjB,EAAS,UAAS,CACjB,KAAMiB,EAAE,OAAO,OACf,KAAM,UAAYL,EAAS,UAAU,CAAC,EACtC,WAAY,KAEbZ,EAAS,gBAAgB,IAG3BgB,EAAU,cAAcD,CAAU,OAElCf,EAAS,iBAAiB,EAC1BW,EAAc,CAAA,MACVO,EAAc,CAAI,aAAc,WAAW,EACtC,QAAAC,KAAiBD,KACrB,cAAc,gBAAgBC,CAAa,EAAA,CAC9CP,EAAWO,QAIT,GAAAP,IAAa,KAAI,CACpB,QAAQ,MAAM,qCAAqC,SAGpDC,EAAqB,IAAA,cAAcT,EACxB,CAAA,SAAAQ,CAAA,CAAA,EAEXC,EAAe,iBAAiB,yBAA2BI,EAAC,CAC3DN,EAAe,KAAKM,EAAE,IAAI,IAE3BJ,EAAe,MAAM,GAAG,EAEzBO,EAAA,EAAAV,GAAaA,CAAS,EAGvBP,IAEIT,GAAaG,IAAS,SACzB,OAAO,iBACFL,IAAiBI,GACpBW,KAEC,+CAMcf,EAAY6B,uXC/H/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAUA,SAASC,GAAQC,EAAQC,EAAgB,CACvC,IAAIC,EAAO,OAAO,KAAKF,CAAM,EAE7B,GAAI,OAAO,sBAAuB,CAChC,IAAIG,EAAU,OAAO,sBAAsBH,CAAM,EAE7CC,IACFE,EAAUA,EAAQ,OAAO,SAAUC,EAAK,CACtC,OAAO,OAAO,yBAAyBJ,EAAQI,CAAG,EAAE,UAC5D,CAAO,GAGHF,EAAK,KAAK,MAAMA,EAAMC,CAAO,EAG/B,OAAOD,CACT,CAEA,SAASG,GAAexD,EAAQ,CAC9B,QAASyD,EAAI,EAAGA,EAAI,UAAU,OAAQA,IAAK,CACzC,IAAIC,EAAS,UAAUD,CAAC,GAAK,KAAO,UAAUA,CAAC,EAAI,GAE/CA,EAAI,EACNP,GAAQ,OAAOQ,CAAM,EAAG,EAAI,EAAE,QAAQ,SAAUC,EAAK,CACnDC,GAAgB5D,EAAQ2D,EAAKD,EAAOC,CAAG,CAAC,CAChD,CAAO,EACQ,OAAO,0BAChB,OAAO,iBAAiB3D,EAAQ,OAAO,0BAA0B0D,CAAM,CAAC,EAExER,GAAQ,OAAOQ,CAAM,CAAC,EAAE,QAAQ,SAAUC,EAAK,CAC7C,OAAO,eAAe3D,EAAQ2D,EAAK,OAAO,yBAAyBD,EAAQC,CAAG,CAAC,CACvF,CAAO,EAIL,OAAO3D,CACT,CAEA,SAAS6D,GAAQC,EAAK,CAGpB,OAAI,OAAO,QAAW,YAAc,OAAO,OAAO,UAAa,SAC7DD,GAAU,SAAUC,EAAK,CACvB,OAAO,OAAOA,CACpB,EAEID,GAAU,SAAUC,EAAK,CACvB,OAAOA,GAAO,OAAO,QAAW,YAAcA,EAAI,cAAgB,QAAUA,IAAQ,OAAO,UAAY,SAAW,OAAOA,CAC/H,EAGSD,GAAQC,CAAG,CACpB,CAEA,SAASC,GAAgBC,EAAUC,EAAa,CAC9C,GAAI,EAAED,aAAoBC,GACxB,MAAM,IAAI,UAAU,mCAAmC,CAE3D,CAEA,SAASC,GAAkBlE,EAAQmE,EAAO,CACxC,QAASV,EAAI,EAAGA,EAAIU,EAAM,OAAQV,IAAK,CACrC,IAAIW,EAAaD,EAAMV,CAAC,EACxBW,EAAW,WAAaA,EAAW,YAAc,GACjDA,EAAW,aAAe,GACtB,UAAWA,IAAYA,EAAW,SAAW,IACjD,OAAO,eAAepE,EAAQoE,EAAW,IAAKA,CAAU,EAE5D,CAEA,SAASC,GAAaJ,EAAaK,EAAYC,EAAa,CAC1D,OAAID,GAAYJ,GAAkBD,EAAY,UAAWK,CAAU,EAC/DC,GAAaL,GAAkBD,EAAaM,CAAW,EACpDN,CACT,CAEA,SAASL,GAAgBE,EAAKH,EAAKa,EAAO,CACxC,OAAIb,KAAOG,EACT,OAAO,eAAeA,EAAKH,EAAK,CAC9B,MAAOa,EACP,WAAY,GACZ,aAAc,GACd,SAAU,EAChB,CAAK,EAEDV,EAAIH,CAAG,EAAIa,EAGNV,CACT,CAEA,SAASW,GAAmBC,EAAK,CAC/B,OAAOC,GAAmBD,CAAG,GAAKE,GAAiBF,CAAG,GAAKG,GAA4BH,CAAG,GAAKI,IACjG,CAEA,SAASH,GAAmBD,EAAK,CAC/B,GAAI,MAAM,QAAQA,CAAG,EAAG,OAAOK,GAAkBL,CAAG,CACtD,CAEA,SAASE,GAAiBI,EAAM,CAC9B,GAAI,OAAO,OAAW,KAAeA,EAAK,OAAO,QAAQ,GAAK,MAAQA,EAAK,YAAY,GAAK,KAAM,OAAO,MAAM,KAAKA,CAAI,CAC1H,CAEA,SAASH,GAA4BI,EAAGC,EAAQ,CAC9C,GAAKD,EACL,IAAI,OAAOA,GAAM,SAAU,OAAOF,GAAkBE,EAAGC,CAAM,EAC7D,IAAIC,EAAI,OAAO,UAAU,SAAS,KAAKF,CAAC,EAAE,MAAM,EAAG,EAAE,EAErD,GADIE,IAAM,UAAYF,EAAE,cAAaE,EAAIF,EAAE,YAAY,MACnDE,IAAM,OAASA,IAAM,MAAO,OAAO,MAAM,KAAKF,CAAC,EACnD,GAAIE,IAAM,aAAe,2CAA2C,KAAKA,CAAC,EAAG,OAAOJ,GAAkBE,EAAGC,CAAM,EACjH,CAEA,SAASH,GAAkBL,EAAKU,EAAK,EAC/BA,GAAO,MAAQA,EAAMV,EAAI,UAAQU,EAAMV,EAAI,QAE/C,QAASjB,EAAI,EAAG4B,EAAO,IAAI,MAAMD,CAAG,EAAG3B,EAAI2B,EAAK3B,IAAK4B,EAAK5B,CAAC,EAAIiB,EAAIjB,CAAC,EAEpE,OAAO4B,CACT,CAEA,SAASP,IAAqB,CAC5B,MAAM,IAAI,UAAU;AAAA,mFAAsI,CAC5J,CAEA,IAAIQ,GAAa,OAAO,OAAW,KAAe,OAAO,OAAO,SAAa,IACzEC,GAASD,GAAa,OAAS,GAC/BE,GAAkBF,IAAcC,GAAO,SAAS,gBAAkB,iBAAkBA,GAAO,SAAS,gBAAkB,GACtHE,GAAoBH,GAAa,iBAAkBC,GAAS,GAC5DG,EAAY,UAEZC,GAAa,MACbC,GAAc,OACdC,GAAc,OACdC,GAAc,OACdC,GAAc,IACdC,GAAc,IACdC,GAAe,IACfC,GAAe,IACfC,GAAoB,KACpBC,GAAoB,KACpBC,GAAoB,KACpBC,GAAoB,KAEpBC,GAAa,GAAG,OAAOb,EAAW,OAAO,EACzCc,GAAiB,GAAG,OAAOd,EAAW,WAAW,EACjDe,GAAe,GAAG,OAAOf,EAAW,SAAS,EAC7CgB,GAAa,GAAG,OAAOhB,EAAW,OAAO,EACzCiB,GAAkB,GAAG,OAAOjB,EAAW,YAAY,EACnDkB,GAAc,GAAG,OAAOlB,EAAW,QAAQ,EAC3CmB,GAAa,GAAG,OAAOnB,EAAW,OAAO,EAEzCoB,GAAc,GAAG,OAAOpB,EAAW,QAAQ,EAC3CqB,GAAe,GAAG,OAAOrB,EAAW,SAAS,EAE7CsB,GAAiB,OACjBC,GAAiB,OACjBC,GAAiB,OAEjBC,GAAa,OACbC,GAAiB,UACjBC,GAAkB,WAClBC,GAAmB,YACnBC,GAAiB,WACjBC,GAAoBhC,GAAkB,aAAe,YACrDiC,GAAmBjC,GAAkB,YAAc,YACnDkC,GAAkBlC,GAAkB,uBAAyB,UAC7DmC,GAAqBlC,GAAoB,cAAgB+B,GACzDI,GAAqBnC,GAAoB,cAAgBgC,GACzDI,GAAmBpC,GAAoB,0BAA4BiC,GACnEI,GAAc,QACdC,GAAe,SACfC,GAAc,QACdC,GAAa,OAEbC,GAAiB,aAEjBC,GAAiB,2CACjBC,GAAkB,SAClBC,GAAuB,4BACvBC,GAAkB,gBAGlBC,GAAsB,IACtBC,GAAuB,IAEvBC,GAAW,CAEb,SAAU,EAGV,SAAUzB,GAGV,mBAAoB,IAEpB,YAAa,IAEb,KAAM,KAEN,QAAS,GAET,WAAY,GAEZ,QAAS,GAET,iBAAkB,GAElB,iBAAkB,GAElB,MAAO,GAEP,OAAQ,GAER,OAAQ,GAER,UAAW,GAEX,WAAY,GAEZ,SAAU,GAEV,aAAc,GAEd,QAAS,GAET,UAAW,GAEX,SAAU,GAEV,SAAU,GAEV,YAAa,GAEb,YAAa,GAEb,eAAgB,GAEhB,eAAgB,GAEhB,iBAAkB,GAElB,yBAA0B,GAE1B,eAAgB,EAChB,gBAAiB,EACjB,gBAAiB,EACjB,iBAAkB,EAClB,kBAAmBuB,GACnB,mBAAoBC,GAEpB,MAAO,KACP,UAAW,KACX,SAAU,KACV,QAAS,KACT,KAAM,KACN,KAAM,IACR,EAEIE,GAAW,orCAMXC,GAAQ,OAAO,OAASpD,GAAO,MAOnC,SAASqD,EAASpE,EAAO,CACvB,OAAO,OAAOA,GAAU,UAAY,CAACmE,GAAMnE,CAAK,CAClD,CAOA,IAAIqE,GAAmB,SAA0BrE,EAAO,CACtD,OAAOA,EAAQ,GAAKA,EAAQ,GAC9B,EAOA,SAASsE,GAAYtE,EAAO,CAC1B,OAAO,OAAOA,EAAU,GAC1B,CAOA,SAASuE,GAASvE,EAAO,CACvB,OAAOX,GAAQW,CAAK,IAAM,UAAYA,IAAU,IAClD,CACA,IAAIwE,GAAiB,OAAO,UAAU,eAOtC,SAASC,GAAczE,EAAO,CAC5B,GAAI,CAACuE,GAASvE,CAAK,EACjB,MAAO,GAGT,GAAI,CACF,IAAI0E,EAAe1E,EAAM,YACrB2E,EAAYD,EAAa,UAC7B,OAAOA,GAAgBC,GAAaH,GAAe,KAAKG,EAAW,eAAe,CACnF,MAAC,CACA,MAAO,EACR,CACH,CAOA,SAASC,GAAW5E,EAAO,CACzB,OAAO,OAAOA,GAAU,UAC1B,CACA,IAAI6E,GAAQ,MAAM,UAAU,MAO5B,SAASC,GAAQ9E,EAAO,CACtB,OAAO,MAAM,KAAO,MAAM,KAAKA,CAAK,EAAI6E,GAAM,KAAK7E,CAAK,CAC1D,CAQA,SAAS+E,GAAQlH,EAAMmH,EAAU,CAC/B,OAAInH,GAAQ+G,GAAWI,CAAQ,IACzB,MAAM,QAAQnH,CAAI,GAAKuG,EAASvG,EAAK,MAAM,EAG3CiH,GAAQjH,CAAI,EAAE,QAAQ,SAAUmC,EAAOb,EAAK,CAC1C6F,EAAS,KAAKnH,EAAMmC,EAAOb,EAAKtB,CAAI,CAC9C,CAAS,EACQ0G,GAAS1G,CAAI,GACxB,OAAO,KAAKA,CAAI,EAAE,QAAQ,SAAUsB,EAAK,CACvC6F,EAAS,KAAKnH,EAAMA,EAAKsB,CAAG,EAAGA,EAAKtB,CAAI,CAChD,CAAO,GAIEA,CACT,CAQA,IAAIoH,EAAS,OAAO,QAAU,SAAgBzJ,EAAQ,CACpD,QAAS0J,EAAO,UAAU,OAAQC,EAAO,IAAI,MAAMD,EAAO,EAAIA,EAAO,EAAI,CAAC,EAAGE,EAAO,EAAGA,EAAOF,EAAME,IAClGD,EAAKC,EAAO,CAAC,EAAI,UAAUA,CAAI,EAGjC,OAAIb,GAAS/I,CAAM,GAAK2J,EAAK,OAAS,GACpCA,EAAK,QAAQ,SAAUE,EAAK,CACtBd,GAASc,CAAG,GACd,OAAO,KAAKA,CAAG,EAAE,QAAQ,SAAUlG,EAAK,CACtC3D,EAAO2D,CAAG,EAAIkG,EAAIlG,CAAG,CAC/B,CAAS,CAET,CAAK,EAGI3D,CACT,EACI8J,GAAkB,uBAStB,SAASC,GAAuBvF,EAAO,CACrC,IAAIwF,EAAQ,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,KAChF,OAAOF,GAAgB,KAAKtF,CAAK,EAAI,KAAK,MAAMA,EAAQwF,CAAK,EAAIA,EAAQxF,CAC3E,CACA,IAAIyF,GAAgB,+CAOpB,SAASC,GAASC,EAASC,EAAQ,CACjC,IAAIC,EAAQF,EAAQ,MACpBZ,GAAQa,EAAQ,SAAU5F,EAAO8F,EAAU,CACrCL,GAAc,KAAKK,CAAQ,GAAK1B,EAASpE,CAAK,IAChDA,EAAQ,GAAG,OAAOA,EAAO,IAAI,GAG/B6F,EAAMC,CAAQ,EAAI9F,CACtB,CAAG,CACH,CAQA,SAAS+F,GAASJ,EAAS3F,EAAO,CAChC,OAAO2F,EAAQ,UAAYA,EAAQ,UAAU,SAAS3F,CAAK,EAAI2F,EAAQ,UAAU,QAAQ3F,CAAK,EAAI,EACpG,CAOA,SAASgG,GAASL,EAAS3F,EAAO,CAChC,GAAKA,EAIL,IAAIoE,EAASuB,EAAQ,MAAM,EAAG,CAC5BZ,GAAQY,EAAS,SAAUM,EAAM,CAC/BD,GAASC,EAAMjG,CAAK,CAC1B,CAAK,EACD,OAGF,GAAI2F,EAAQ,UAAW,CACrBA,EAAQ,UAAU,IAAI3F,CAAK,EAC3B,OAGF,IAAIkG,EAAYP,EAAQ,UAAU,KAAI,EAEjCO,EAEMA,EAAU,QAAQlG,CAAK,EAAI,IACpC2F,EAAQ,UAAY,GAAG,OAAOO,EAAW,GAAG,EAAE,OAAOlG,CAAK,GAF1D2F,EAAQ,UAAY3F,EAIxB,CAOA,SAASmG,GAAYR,EAAS3F,EAAO,CACnC,GAAKA,EAIL,IAAIoE,EAASuB,EAAQ,MAAM,EAAG,CAC5BZ,GAAQY,EAAS,SAAUM,EAAM,CAC/BE,GAAYF,EAAMjG,CAAK,CAC7B,CAAK,EACD,OAGF,GAAI2F,EAAQ,UAAW,CACrBA,EAAQ,UAAU,OAAO3F,CAAK,EAC9B,OAGE2F,EAAQ,UAAU,QAAQ3F,CAAK,GAAK,IACtC2F,EAAQ,UAAYA,EAAQ,UAAU,QAAQ3F,EAAO,EAAE,GAE3D,CAQA,SAASoG,GAAYT,EAAS3F,EAAOqG,EAAO,CAC1C,GAAKrG,EAIL,IAAIoE,EAASuB,EAAQ,MAAM,EAAG,CAC5BZ,GAAQY,EAAS,SAAUM,EAAM,CAC/BG,GAAYH,EAAMjG,EAAOqG,CAAK,CACpC,CAAK,EACD,OAIEA,EACFL,GAASL,EAAS3F,CAAK,EAEvBmG,GAAYR,EAAS3F,CAAK,EAE9B,CACA,IAAIsG,GAAoB,oBAOxB,SAASC,GAAYvG,EAAO,CAC1B,OAAOA,EAAM,QAAQsG,GAAmB,OAAO,EAAE,YAAW,CAC9D,CAQA,SAASE,GAAQb,EAASc,EAAM,CAC9B,OAAIlC,GAASoB,EAAQc,CAAI,CAAC,EACjBd,EAAQc,CAAI,EAGjBd,EAAQ,QACHA,EAAQ,QAAQc,CAAI,EAGtBd,EAAQ,aAAa,QAAQ,OAAOY,GAAYE,CAAI,CAAC,CAAC,CAC/D,CAQA,SAASC,GAAQf,EAASc,EAAM5I,EAAM,CAChC0G,GAAS1G,CAAI,EACf8H,EAAQc,CAAI,EAAI5I,EACP8H,EAAQ,QACjBA,EAAQ,QAAQc,CAAI,EAAI5I,EAExB8H,EAAQ,aAAa,QAAQ,OAAOY,GAAYE,CAAI,CAAC,EAAG5I,CAAI,CAEhE,CAOA,SAAS8I,GAAWhB,EAASc,EAAM,CACjC,GAAIlC,GAASoB,EAAQc,CAAI,CAAC,EACxB,GAAI,CACF,OAAOd,EAAQc,CAAI,CACpB,MAAC,CACAd,EAAQc,CAAI,EAAI,MACjB,SACQd,EAAQ,QAEjB,GAAI,CACF,OAAOA,EAAQ,QAAQc,CAAI,CAC5B,MAAC,CACAd,EAAQ,QAAQc,CAAI,EAAI,MACzB,MAEDd,EAAQ,gBAAgB,QAAQ,OAAOY,GAAYE,CAAI,CAAC,CAAC,CAE7D,CACA,IAAIG,GAAgB,QAEhBC,GAAgB,UAAY,CAC9B,IAAIC,EAAY,GAEhB,GAAIhG,GAAY,CACd,IAAIiG,EAAO,GAEPC,EAAW,UAAoB,GAE/BC,EAAU,OAAO,eAAe,CAAA,EAAI,OAAQ,CAC9C,IAAK,UAAe,CAClB,OAAAH,EAAY,GACLC,CACR,EAOD,IAAK,SAAa/G,EAAO,CACvB+G,EAAO/G,CACR,CACP,CAAK,EACDe,GAAO,iBAAiB,OAAQiG,EAAUC,CAAO,EACjDlG,GAAO,oBAAoB,OAAQiG,EAAUC,CAAO,EAGtD,OAAOH,CACT,IAUA,SAASI,GAAevB,EAASwB,EAAMH,EAAU,CAC/C,IAAIC,EAAU,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAA,EAC9EG,EAAUJ,EACdG,EAAK,KAAI,EAAG,MAAMP,EAAa,EAAE,QAAQ,SAAUS,EAAO,CACxD,GAAI,CAACR,GAAe,CAClB,IAAIS,EAAY3B,EAAQ,UAEpB2B,GAAaA,EAAUD,CAAK,GAAKC,EAAUD,CAAK,EAAEL,CAAQ,IAC5DI,EAAUE,EAAUD,CAAK,EAAEL,CAAQ,EACnC,OAAOM,EAAUD,CAAK,EAAEL,CAAQ,EAE5B,OAAO,KAAKM,EAAUD,CAAK,CAAC,EAAE,SAAW,GAC3C,OAAOC,EAAUD,CAAK,EAGpB,OAAO,KAAKC,CAAS,EAAE,SAAW,GACpC,OAAO3B,EAAQ,WAKrBA,EAAQ,oBAAoB0B,EAAOD,EAASH,CAAO,CACvD,CAAG,CACH,CASA,SAASM,GAAY5B,EAASwB,EAAMH,EAAU,CAC5C,IAAIC,EAAU,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAA,EAC9EO,EAAWR,EACfG,EAAK,KAAI,EAAG,MAAMP,EAAa,EAAE,QAAQ,SAAUS,EAAO,CACxD,GAAIJ,EAAQ,MAAQ,CAACJ,GAAe,CAClC,IAAIY,EAAqB9B,EAAQ,UAC7B2B,EAAYG,IAAuB,OAAS,CAAA,EAAKA,EAErDD,EAAW,UAAmB,CAC5B,OAAOF,EAAUD,CAAK,EAAEL,CAAQ,EAChCrB,EAAQ,oBAAoB0B,EAAOG,EAAUP,CAAO,EAEpD,QAASS,EAAQ,UAAU,OAAQvC,EAAO,IAAI,MAAMuC,CAAK,EAAGC,EAAQ,EAAGA,EAAQD,EAAOC,IACpFxC,EAAKwC,CAAK,EAAI,UAAUA,CAAK,EAG/BX,EAAS,MAAMrB,EAASR,CAAI,CACpC,EAEWmC,EAAUD,CAAK,IAClBC,EAAUD,CAAK,EAAI,IAGjBC,EAAUD,CAAK,EAAEL,CAAQ,GAC3BrB,EAAQ,oBAAoB0B,EAAOC,EAAUD,CAAK,EAAEL,CAAQ,EAAGC,CAAO,EAGxEK,EAAUD,CAAK,EAAEL,CAAQ,EAAIQ,EAC7B7B,EAAQ,UAAY2B,EAGtB3B,EAAQ,iBAAiB0B,EAAOG,EAAUP,CAAO,CACrD,CAAG,CACH,CASA,SAASW,GAAcjC,EAASwB,EAAMtJ,EAAM,CAC1C,IAAIwJ,EAEJ,OAAIzC,GAAW,KAAK,GAAKA,GAAW,WAAW,EAC7CyC,EAAQ,IAAI,YAAYF,EAAM,CAC5B,OAAQtJ,EACR,QAAS,GACT,WAAY,EAClB,CAAK,GAEDwJ,EAAQ,SAAS,YAAY,aAAa,EAC1CA,EAAM,gBAAgBF,EAAM,GAAM,GAAMtJ,CAAI,GAGvC8H,EAAQ,cAAc0B,CAAK,CACpC,CAOA,SAASQ,GAAUlC,EAAS,CAC1B,IAAImC,EAAMnC,EAAQ,wBAClB,MAAO,CACL,KAAMmC,EAAI,MAAQ,OAAO,YAAc,SAAS,gBAAgB,YAChE,IAAKA,EAAI,KAAO,OAAO,YAAc,SAAS,gBAAgB,UAClE,CACA,CACA,IAAIC,GAAWhH,GAAO,SAClBiH,GAAiB,gCAOrB,SAASC,GAAiBC,EAAK,CAC7B,IAAIC,EAAQD,EAAI,MAAMF,EAAc,EACpC,OAAOG,IAAU,OAASA,EAAM,CAAC,IAAMJ,GAAS,UAAYI,EAAM,CAAC,IAAMJ,GAAS,UAAYI,EAAM,CAAC,IAAMJ,GAAS,KACtH,CAOA,SAASK,GAAaF,EAAK,CACzB,IAAIG,EAAY,aAAa,OAAO,IAAI,KAAM,EAAC,QAAO,CAAE,EACxD,OAAOH,GAAOA,EAAI,QAAQ,GAAG,IAAM,GAAK,IAAM,KAAOG,CACvD,CAOA,SAASC,GAAcC,EAAM,CAC3B,IAAIC,EAASD,EAAK,OACdE,EAASF,EAAK,OACdG,EAASH,EAAK,OACdI,EAAaJ,EAAK,WAClBK,EAAaL,EAAK,WAClBM,EAAS,CAAA,EAETzE,EAASuE,CAAU,GAAKA,IAAe,GACzCE,EAAO,KAAK,cAAc,OAAOF,EAAY,KAAK,CAAC,EAGjDvE,EAASwE,CAAU,GAAKA,IAAe,GACzCC,EAAO,KAAK,cAAc,OAAOD,EAAY,KAAK,CAAC,EAIjDxE,EAASoE,CAAM,GAAKA,IAAW,GACjCK,EAAO,KAAK,UAAU,OAAOL,EAAQ,MAAM,CAAC,EAG1CpE,EAASqE,CAAM,GAAKA,IAAW,GACjCI,EAAO,KAAK,UAAU,OAAOJ,EAAQ,GAAG,CAAC,EAGvCrE,EAASsE,CAAM,GAAKA,IAAW,GACjCG,EAAO,KAAK,UAAU,OAAOH,EAAQ,GAAG,CAAC,EAG3C,IAAII,EAAYD,EAAO,OAASA,EAAO,KAAK,GAAG,EAAI,OACnD,MAAO,CACL,gBAAiBC,EACjB,YAAaA,EACb,UAAWA,CACf,CACA,CAOA,SAASC,GAAgBC,EAAU,CACjC,IAAIC,EAAYjK,GAAe,CAAE,EAAEgK,CAAQ,EAEvCE,EAAW,EACf,OAAAnE,GAAQiE,EAAU,SAAUG,EAASC,EAAW,CAC9C,OAAOH,EAAUG,CAAS,EAC1BrE,GAAQkE,EAAW,SAAUI,EAAU,CACrC,IAAIC,EAAK,KAAK,IAAIH,EAAQ,OAASE,EAAS,MAAM,EAC9CE,EAAK,KAAK,IAAIJ,EAAQ,OAASE,EAAS,MAAM,EAC9CG,EAAK,KAAK,IAAIL,EAAQ,KAAOE,EAAS,IAAI,EAC1CI,EAAK,KAAK,IAAIN,EAAQ,KAAOE,EAAS,IAAI,EAC1CK,EAAK,KAAK,KAAKJ,EAAKA,EAAKC,EAAKA,CAAE,EAChCI,EAAK,KAAK,KAAKH,EAAKA,EAAKC,EAAKA,CAAE,EAChCG,GAASD,EAAKD,GAAMA,EAEpB,KAAK,IAAIE,CAAK,EAAI,KAAK,IAAIV,CAAQ,IACrCA,EAAWU,EAEnB,CAAK,CACL,CAAG,EACMV,CACT,CAQA,SAASW,GAAWC,EAAOC,EAAS,CAClC,IAAIC,EAAQF,EAAM,MACdG,EAAQH,EAAM,MACdI,EAAM,CACR,KAAMF,EACN,KAAMC,CACV,EACE,OAAOF,EAAUG,EAAMlL,GAAe,CACpC,OAAQgL,EACR,OAAQC,CACT,EAAEC,CAAG,CACR,CAOA,SAASC,GAAkBnB,EAAU,CACnC,IAAIgB,EAAQ,EACRC,EAAQ,EACRG,EAAQ,EACZ,OAAArF,GAAQiE,EAAU,SAAUqB,EAAO,CACjC,IAAIC,EAASD,EAAM,OACfE,EAASF,EAAM,OACnBL,GAASM,EACTL,GAASM,EACTH,GAAS,CACb,CAAG,EACDJ,GAASI,EACTH,GAASG,EACF,CACL,MAAOJ,EACP,MAAOC,CACX,CACA,CAQA,SAASO,GAAiBC,EAC1B,CACE,IAAIC,EAAcD,EAAM,YACpBE,EAASF,EAAM,OACfG,EAAQH,EAAM,MACdtD,EAAO,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,UAC3E0D,EAAexG,GAAiBuG,CAAK,EACrCE,EAAgBzG,GAAiBsG,CAAM,EAE3C,GAAIE,GAAgBC,EAAe,CACjC,IAAIC,EAAgBJ,EAASD,EAEzBvD,IAAS,WAAa4D,EAAgBH,GAASzD,IAAS,SAAW4D,EAAgBH,EACrFD,EAASC,EAAQF,EAEjBE,EAAQD,EAASD,OAEVG,EACTF,EAASC,EAAQF,EACRI,IACTF,EAAQD,EAASD,GAGnB,MAAO,CACL,MAAOE,EACP,OAAQD,CACZ,CACA,CAOA,SAASK,GAAgBC,EAAO,CAC9B,IAAIL,EAAQK,EAAM,MACdN,EAASM,EAAM,OACfC,EAASD,EAAM,OAGnB,GAFAC,EAAS,KAAK,IAAIA,CAAM,EAAI,IAExBA,IAAW,GACb,MAAO,CACL,MAAOP,EACP,OAAQC,CACd,EAGE,IAAIO,EAAMD,EAAS,GAAK,KAAK,GAAK,IAC9BE,EAAS,KAAK,IAAID,CAAG,EACrBE,EAAS,KAAK,IAAIF,CAAG,EACrBG,EAAWV,EAAQS,EAASV,EAASS,EACrCG,EAAYX,EAAQQ,EAAST,EAASU,EAC1C,OAAOH,EAAS,GAAK,CACnB,MAAOK,EACP,OAAQD,CACZ,EAAM,CACF,MAAOA,EACP,OAAQC,CACZ,CACA,CAUA,SAASC,GAAgBC,EAAOC,EAAOC,EAAOC,EAAO,CACnD,IAAIC,EAAmBH,EAAM,YACzBI,EAAoBJ,EAAM,aAC1BK,EAAqBL,EAAM,cAC3BM,EAAeN,EAAM,OACrBlD,EAASwD,IAAiB,OAAS,EAAIA,EACvCC,EAAeP,EAAM,OACrBjD,EAASwD,IAAiB,OAAS,EAAIA,EACvCC,EAAeR,EAAM,OACrBhD,EAASwD,IAAiB,OAAS,EAAIA,EACvCxB,EAAciB,EAAM,YACpBQ,EAAeR,EAAM,aACrBS,EAAgBT,EAAM,cACtBU,EAAkBT,EAAM,UACxBU,EAAYD,IAAoB,OAAS,cAAgBA,EACzDE,EAAwBX,EAAM,sBAC9BY,EAAwBD,IAA0B,OAAS,GAAOA,EAClEE,GAAwBb,EAAM,sBAC9Bc,EAAwBD,KAA0B,OAAS,MAAQA,GACnEE,EAAiBf,EAAM,SACvBgB,EAAWD,IAAmB,OAAS,IAAWA,EAClDE,EAAkBjB,EAAM,UACxBkB,EAAYD,IAAoB,OAAS,IAAWA,EACpDE,GAAiBnB,EAAM,SACvBoB,GAAWD,KAAmB,OAAS,EAAIA,GAC3CE,GAAkBrB,EAAM,UACxBsB,EAAYD,KAAoB,OAAS,EAAIA,GAC7CpQ,EAAS,SAAS,cAAc,QAAQ,EACxCe,EAAUf,EAAO,WAAW,IAAI,EAChCsQ,EAAW3C,GAAiB,CAC9B,YAAaE,EACb,MAAOkC,EACP,OAAQE,CACZ,CAAG,EACGM,GAAW5C,GAAiB,CAC9B,YAAaE,EACb,MAAOsC,GACP,OAAQE,CACT,EAAE,OAAO,EACNtC,GAAQ,KAAK,IAAIuC,EAAS,MAAO,KAAK,IAAIC,GAAS,MAAOjB,CAAY,CAAC,EACvExB,GAAS,KAAK,IAAIwC,EAAS,OAAQ,KAAK,IAAIC,GAAS,OAAQhB,CAAa,CAAC,EAG3EiB,EAAe7C,GAAiB,CAClC,YAAaqB,EACb,MAAOe,EACP,OAAQE,CACZ,CAAG,EACGQ,GAAe9C,GAAiB,CAClC,YAAaqB,EACb,MAAOmB,GACP,OAAQE,CACT,EAAE,OAAO,EACNK,GAAY,KAAK,IAAIF,EAAa,MAAO,KAAK,IAAIC,GAAa,MAAOxB,CAAiB,CAAC,EACxF0B,GAAa,KAAK,IAAIH,EAAa,OAAQ,KAAK,IAAIC,GAAa,OAAQvB,CAAkB,CAAC,EAC5F0B,GAAS,CAAC,CAACF,GAAY,EAAG,CAACC,GAAa,EAAGD,GAAWC,EAAU,EACpE,OAAA3Q,EAAO,MAAQ0I,GAAuBqF,EAAK,EAC3C/N,EAAO,OAAS0I,GAAuBoF,EAAM,EAC7C/M,EAAQ,UAAY0O,EACpB1O,EAAQ,SAAS,EAAG,EAAGgN,GAAOD,EAAM,EACpC/M,EAAQ,KAAI,EACZA,EAAQ,UAAUgN,GAAQ,EAAGD,GAAS,CAAC,EACvC/M,EAAQ,OAAO4K,EAAS,KAAK,GAAK,GAAG,EACrC5K,EAAQ,MAAM6K,EAAQC,CAAM,EAC5B9K,EAAQ,sBAAwB4O,EAChC5O,EAAQ,sBAAwB8O,EAChC9O,EAAQ,UAAU,MAAMA,EAAS,CAAC6N,CAAK,EAAE,OAAOxL,GAAmBwN,GAAO,IAAI,SAAUC,GAAO,CAC7F,OAAO,KAAK,MAAMnI,GAAuBmI,EAAK,CAAC,CACnD,CAAG,CAAC,CAAC,CAAC,EACJ9P,EAAQ,QAAO,EACRf,CACT,CACA,IAAI8Q,GAAe,OAAO,aAS1B,SAASC,GAAsBC,EAAUC,EAAOC,EAAQ,CACtD,IAAIC,EAAM,GACVD,GAAUD,EAEV,QAAS7O,EAAI6O,EAAO7O,EAAI8O,EAAQ9O,GAAK,EACnC+O,GAAOL,GAAaE,EAAS,SAAS5O,CAAC,CAAC,EAG1C,OAAO+O,CACT,CACA,IAAIC,GAAuB,YAO3B,SAASC,GAAqBC,EAAS,CACrC,IAAIC,EAASD,EAAQ,QAAQF,GAAsB,EAAE,EACjDI,EAAS,KAAKD,CAAM,EACpBE,EAAc,IAAI,YAAYD,EAAO,MAAM,EAC3CE,EAAQ,IAAI,WAAWD,CAAW,EACtC,OAAAvJ,GAAQwJ,EAAO,SAAUvO,EAAOf,EAAG,CACjCsP,EAAMtP,CAAC,EAAIoP,EAAO,WAAWpP,CAAC,CAClC,CAAG,EACMqP,CACT,CAQA,SAASE,GAAqBF,EAAatQ,EAAU,CAMnD,QALIyQ,EAAS,CAAA,EAETC,EAAY,KACZH,EAAQ,IAAI,WAAWD,CAAW,EAE/BC,EAAM,OAAS,GAGpBE,EAAO,KAAKd,GAAa,MAAM,KAAM7I,GAAQyJ,EAAM,SAAS,EAAGG,CAAS,CAAC,CAAC,CAAC,EAC3EH,EAAQA,EAAM,SAASG,CAAS,EAGlC,MAAO,QAAQ,OAAO1Q,EAAU,UAAU,EAAE,OAAO,KAAKyQ,EAAO,KAAK,EAAE,CAAC,CAAC,CAC1E,CAOA,SAASE,GAAuBL,EAAa,CAC3C,IAAIT,EAAW,IAAI,SAASS,CAAW,EACnCM,EAEJ,GAAI,CACF,IAAIC,EACAC,EACAC,EAEJ,GAAIlB,EAAS,SAAS,CAAC,IAAM,KAAQA,EAAS,SAAS,CAAC,IAAM,IAI5D,QAHIE,EAASF,EAAS,WAClBmB,EAAS,EAENA,EAAS,EAAIjB,GAAQ,CAC1B,GAAIF,EAAS,SAASmB,CAAM,IAAM,KAAQnB,EAAS,SAASmB,EAAS,CAAC,IAAM,IAAM,CAChFF,EAAYE,EACZ,MAGFA,GAAU,EAId,GAAIF,EAAW,CACb,IAAIG,EAAaH,EAAY,EACzBI,EAAaJ,EAAY,GAE7B,GAAIlB,GAAsBC,EAAUoB,EAAY,CAAC,IAAM,OAAQ,CAC7D,IAAIE,EAAatB,EAAS,UAAUqB,CAAU,EAG9C,GAFAL,EAAeM,IAAe,OAE1BN,GAAgBM,IAAe,QAG3BtB,EAAS,UAAUqB,EAAa,EAAGL,CAAY,IAAM,GAAQ,CAC/D,IAAIO,EAAiBvB,EAAS,UAAUqB,EAAa,EAAGL,CAAY,EAEhEO,GAAkB,IACpBL,EAAWG,EAAaE,KAOpC,GAAIL,EAAU,CACZ,IAAIM,EAAUxB,EAAS,UAAUkB,EAAUF,CAAY,EAEnDS,EAEArQ,EAEJ,IAAKA,EAAI,EAAGA,EAAIoQ,EAASpQ,GAAK,EAG5B,GAFAqQ,EAAUP,EAAW9P,EAAI,GAAK,EAE1B4O,EAAS,UAAUyB,EAAST,CAAY,IAAM,IAEhD,CAEES,GAAW,EAEXV,EAAcf,EAAS,UAAUyB,EAAST,CAAY,EAEtDhB,EAAS,UAAUyB,EAAS,EAAGT,CAAY,EAC3C,OAIT,MAAC,CACAD,EAAc,CACf,CAED,OAAOA,CACT,CAOA,SAASW,GAAiBX,EAAa,CACrC,IAAIpG,EAAS,EACTC,EAAS,EACTC,EAAS,EAEb,OAAQkG,EAAW,CAEjB,IAAK,GACHnG,EAAS,GACT,MAGF,IAAK,GACHD,EAAS,KACT,MAGF,IAAK,GACHE,EAAS,GACT,MAGF,IAAK,GACHF,EAAS,GACTE,EAAS,GACT,MAGF,IAAK,GACHF,EAAS,GACT,MAGF,IAAK,GACHA,EAAS,GACTC,EAAS,GACT,MAGF,IAAK,GACHD,EAAS,IACT,KACH,CAED,MAAO,CACL,OAAQA,EACR,OAAQC,EACR,OAAQC,CACZ,CACA,CAEA,IAAI8G,GAAS,CACX,OAAQ,UAAkB,CACxB,KAAK,cAAa,EAClB,KAAK,WAAU,EACf,KAAK,YAAW,EAChB,KAAK,aAAY,EAEb,KAAK,SACP,KAAK,cAAa,CAErB,EACD,cAAe,UAAyB,CACtC,IAAI7J,EAAU,KAAK,QACfsB,EAAU,KAAK,QACfwI,EAAY,KAAK,UACjBC,EAAU,KAAK,QACf1C,EAAW,OAAO/F,EAAQ,iBAAiB,EAC3CiG,EAAY,OAAOjG,EAAQ,kBAAkB,EACjDjB,GAAS0J,EAASzN,EAAY,EAC9BkE,GAAYR,EAAS1D,EAAY,EACjC,IAAI0N,EAAgB,CAClB,MAAO,KAAK,IAAIF,EAAU,YAAazC,GAAY,EAAIA,EAAWjJ,EAAmB,EACrF,OAAQ,KAAK,IAAI0L,EAAU,aAAcvC,GAAa,EAAIA,EAAYlJ,EAAoB,CAChG,EACI,KAAK,cAAgB2L,EACrBjK,GAASgK,EAAS,CAChB,MAAOC,EAAc,MACrB,OAAQA,EAAc,MAC5B,CAAK,EACD3J,GAASL,EAAS1D,EAAY,EAC9BkE,GAAYuJ,EAASzN,EAAY,CAClC,EAED,WAAY,UAAsB,CAChC,IAAI0N,EAAgB,KAAK,cACrBC,EAAY,KAAK,UACjBC,EAAW,KAAK,QAAQ,SACxBC,EAAU,KAAK,IAAIF,EAAU,MAAM,EAAI,MAAQ,GAC/CzD,EAAe2D,EAAUF,EAAU,cAAgBA,EAAU,aAC7DxD,EAAgB0D,EAAUF,EAAU,aAAeA,EAAU,cAC7DlF,EAAcyB,EAAeC,EAC7B2D,EAAcJ,EAAc,MAC5BK,EAAeL,EAAc,OAE7BA,EAAc,OAASjF,EAAciF,EAAc,MACjDE,IAAa,EACfE,EAAcJ,EAAc,OAASjF,EAErCsF,EAAeL,EAAc,MAAQjF,EAE9BmF,IAAa,EACtBG,EAAeL,EAAc,MAAQjF,EAErCqF,EAAcJ,EAAc,OAASjF,EAGvC,IAAIuF,EAAa,CACf,YAAavF,EACb,aAAcyB,EACd,cAAeC,EACf,MAAO2D,EACP,OAAQC,CACd,EACI,KAAK,WAAaC,EAClB,KAAK,QAAUJ,IAAa,GAAKA,IAAa,EAC9C,KAAK,YAAY,GAAM,EAAI,EAC3BI,EAAW,MAAQ,KAAK,IAAI,KAAK,IAAIA,EAAW,MAAOA,EAAW,QAAQ,EAAGA,EAAW,QAAQ,EAChGA,EAAW,OAAS,KAAK,IAAI,KAAK,IAAIA,EAAW,OAAQA,EAAW,SAAS,EAAGA,EAAW,SAAS,EACpGA,EAAW,MAAQN,EAAc,MAAQM,EAAW,OAAS,EAC7DA,EAAW,KAAON,EAAc,OAASM,EAAW,QAAU,EAC9DA,EAAW,QAAUA,EAAW,KAChCA,EAAW,OAASA,EAAW,IAC/B,KAAK,kBAAoBhL,EAAO,CAAE,EAAEgL,CAAU,CAC/C,EACD,YAAa,SAAqBC,EAAaC,EAAiB,CAC9D,IAAIlJ,EAAU,KAAK,QACf0I,EAAgB,KAAK,cACrBM,EAAa,KAAK,WAClBG,EAAc,KAAK,YACnBP,EAAW5I,EAAQ,SACnByD,EAAcuF,EAAW,YACzBI,EAAU,KAAK,SAAWD,EAE9B,GAAIF,EAAa,CACf,IAAII,EAAiB,OAAOrJ,EAAQ,cAAc,GAAK,EACnDsJ,EAAkB,OAAOtJ,EAAQ,eAAe,GAAK,EAErD4I,EAAW,GACbS,EAAiB,KAAK,IAAIA,EAAgBX,EAAc,KAAK,EAC7DY,EAAkB,KAAK,IAAIA,EAAiBZ,EAAc,MAAM,EAE5DE,IAAa,IACXU,EAAkB7F,EAAc4F,EAClCA,EAAiBC,EAAkB7F,EAEnC6F,EAAkBD,EAAiB5F,IAG9BmF,EAAW,IAChBS,EACFA,EAAiB,KAAK,IAAIA,EAAgBD,EAAUD,EAAY,MAAQ,CAAC,EAChEG,EACTA,EAAkB,KAAK,IAAIA,EAAiBF,EAAUD,EAAY,OAAS,CAAC,EACnEC,IACTC,EAAiBF,EAAY,MAC7BG,EAAkBH,EAAY,OAE1BG,EAAkB7F,EAAc4F,EAClCA,EAAiBC,EAAkB7F,EAEnC6F,EAAkBD,EAAiB5F,IAKzC,IAAI8F,EAAoBhG,GAAiB,CACvC,YAAaE,EACb,MAAO4F,EACP,OAAQC,CAChB,CAAO,EAEDD,EAAiBE,EAAkB,MACnCD,EAAkBC,EAAkB,OACpCP,EAAW,SAAWK,EACtBL,EAAW,UAAYM,EACvBN,EAAW,SAAW,IACtBA,EAAW,UAAY,IAGzB,GAAIE,EACF,GAAIN,GAAYQ,EAAU,EAAI,GAAI,CAChC,IAAII,EAAgBd,EAAc,MAAQM,EAAW,MACjDS,EAAef,EAAc,OAASM,EAAW,OACrDA,EAAW,QAAU,KAAK,IAAI,EAAGQ,CAAa,EAC9CR,EAAW,OAAS,KAAK,IAAI,EAAGS,CAAY,EAC5CT,EAAW,QAAU,KAAK,IAAI,EAAGQ,CAAa,EAC9CR,EAAW,OAAS,KAAK,IAAI,EAAGS,CAAY,EAExCL,GAAW,KAAK,UAClBJ,EAAW,QAAU,KAAK,IAAIG,EAAY,KAAMA,EAAY,MAAQA,EAAY,MAAQH,EAAW,MAAM,EACzGA,EAAW,OAAS,KAAK,IAAIG,EAAY,IAAKA,EAAY,KAAOA,EAAY,OAASH,EAAW,OAAO,EACxGA,EAAW,QAAUG,EAAY,KACjCH,EAAW,OAASG,EAAY,IAE5BP,IAAa,IACXI,EAAW,OAASN,EAAc,QACpCM,EAAW,QAAU,KAAK,IAAI,EAAGQ,CAAa,EAC9CR,EAAW,QAAU,KAAK,IAAI,EAAGQ,CAAa,GAG5CR,EAAW,QAAUN,EAAc,SACrCM,EAAW,OAAS,KAAK,IAAI,EAAGS,CAAY,EAC5CT,EAAW,OAAS,KAAK,IAAI,EAAGS,CAAY,UAKlDT,EAAW,QAAU,CAACA,EAAW,MACjCA,EAAW,OAAS,CAACA,EAAW,OAChCA,EAAW,QAAUN,EAAc,MACnCM,EAAW,OAASN,EAAc,MAGvC,EACD,aAAc,SAAsBgB,EAASC,EAAa,CACxD,IAAIX,EAAa,KAAK,WAClBL,EAAY,KAAK,UAErB,GAAIgB,EAAa,CACf,IAAIC,EAAmB7F,GAAgB,CACrC,MAAO4E,EAAU,aAAe,KAAK,IAAIA,EAAU,QAAU,CAAC,EAC9D,OAAQA,EAAU,cAAgB,KAAK,IAAIA,EAAU,QAAU,CAAC,EAChE,OAAQA,EAAU,QAAU,CACpC,CAAO,EACGzD,EAAe0E,EAAiB,MAChCzE,EAAgByE,EAAiB,OAEjCjG,EAAQqF,EAAW,OAAS9D,EAAe8D,EAAW,cACtDtF,EAASsF,EAAW,QAAU7D,EAAgB6D,EAAW,eAC7DA,EAAW,OAASrF,EAAQqF,EAAW,OAAS,EAChDA,EAAW,MAAQtF,EAASsF,EAAW,QAAU,EACjDA,EAAW,MAAQrF,EACnBqF,EAAW,OAAStF,EACpBsF,EAAW,YAAc9D,EAAeC,EACxC6D,EAAW,aAAe9D,EAC1B8D,EAAW,cAAgB7D,EAC3B,KAAK,YAAY,GAAM,EAAK,GAG1B6D,EAAW,MAAQA,EAAW,UAAYA,EAAW,MAAQA,EAAW,YAC1EA,EAAW,KAAOA,EAAW,UAG3BA,EAAW,OAASA,EAAW,WAAaA,EAAW,OAASA,EAAW,aAC7EA,EAAW,IAAMA,EAAW,QAG9BA,EAAW,MAAQ,KAAK,IAAI,KAAK,IAAIA,EAAW,MAAOA,EAAW,QAAQ,EAAGA,EAAW,QAAQ,EAChGA,EAAW,OAAS,KAAK,IAAI,KAAK,IAAIA,EAAW,OAAQA,EAAW,SAAS,EAAGA,EAAW,SAAS,EACpG,KAAK,YAAY,GAAO,EAAI,EAC5BA,EAAW,KAAO,KAAK,IAAI,KAAK,IAAIA,EAAW,KAAMA,EAAW,OAAO,EAAGA,EAAW,OAAO,EAC5FA,EAAW,IAAM,KAAK,IAAI,KAAK,IAAIA,EAAW,IAAKA,EAAW,MAAM,EAAGA,EAAW,MAAM,EACxFA,EAAW,QAAUA,EAAW,KAChCA,EAAW,OAASA,EAAW,IAC/BvK,GAAS,KAAK,OAAQT,EAAO,CAC3B,MAAOgL,EAAW,MAClB,OAAQA,EAAW,MACpB,EAAE3H,GAAc,CACf,WAAY2H,EAAW,KACvB,WAAYA,EAAW,GACxB,CAAA,CAAC,CAAC,EACH,KAAK,YAAYU,CAAO,EAEpB,KAAK,SAAW,KAAK,SACvB,KAAK,aAAa,GAAM,EAAI,CAE/B,EACD,YAAa,SAAqBA,EAAS,CACzC,IAAIV,EAAa,KAAK,WAClBL,EAAY,KAAK,UACjBhF,EAAQgF,EAAU,cAAgBK,EAAW,MAAQA,EAAW,cAChEtF,EAASiF,EAAU,eAAiBK,EAAW,OAASA,EAAW,eACvEhL,EAAO2K,EAAW,CAChB,MAAOhF,EACP,OAAQD,EACR,MAAOsF,EAAW,MAAQrF,GAAS,EACnC,KAAMqF,EAAW,OAAStF,GAAU,CAC1C,CAAK,EACDjF,GAAS,KAAK,MAAOT,EAAO,CAC1B,MAAO2K,EAAU,MACjB,OAAQA,EAAU,MACxB,EAAOtH,GAAcrD,EAAO,CACtB,WAAY2K,EAAU,KACtB,WAAYA,EAAU,GAC5B,EAAOA,CAAS,CAAC,CAAC,CAAC,EAEXe,GACF,KAAK,OAAM,CAEd,EACD,YAAa,UAAuB,CAClC,IAAI1J,EAAU,KAAK,QACfgJ,EAAa,KAAK,WAClBvF,EAAczD,EAAQ,aAAeA,EAAQ,mBAC7C6J,EAAe,OAAO7J,EAAQ,YAAY,GAAK,GAC/CmJ,EAAc,CAChB,MAAOH,EAAW,MAClB,OAAQA,EAAW,MACzB,EAEQvF,IACEuF,EAAW,OAASvF,EAAcuF,EAAW,MAC/CG,EAAY,OAASA,EAAY,MAAQ1F,EAEzC0F,EAAY,MAAQA,EAAY,OAAS1F,GAI7C,KAAK,YAAc0F,EACnB,KAAK,aAAa,GAAM,EAAI,EAE5BA,EAAY,MAAQ,KAAK,IAAI,KAAK,IAAIA,EAAY,MAAOA,EAAY,QAAQ,EAAGA,EAAY,QAAQ,EACpGA,EAAY,OAAS,KAAK,IAAI,KAAK,IAAIA,EAAY,OAAQA,EAAY,SAAS,EAAGA,EAAY,SAAS,EAExGA,EAAY,MAAQ,KAAK,IAAIA,EAAY,SAAUA,EAAY,MAAQU,CAAY,EACnFV,EAAY,OAAS,KAAK,IAAIA,EAAY,UAAWA,EAAY,OAASU,CAAY,EACtFV,EAAY,KAAOH,EAAW,MAAQA,EAAW,MAAQG,EAAY,OAAS,EAC9EA,EAAY,IAAMH,EAAW,KAAOA,EAAW,OAASG,EAAY,QAAU,EAC9EA,EAAY,QAAUA,EAAY,KAClCA,EAAY,OAASA,EAAY,IACjC,KAAK,mBAAqBnL,EAAO,CAAE,EAAEmL,CAAW,CACjD,EACD,aAAc,SAAsBF,EAAaC,EAAiB,CAChE,IAAIlJ,EAAU,KAAK,QACf0I,EAAgB,KAAK,cACrBM,EAAa,KAAK,WAClBG,EAAc,KAAK,YACnBW,EAAU,KAAK,QACfrG,EAAczD,EAAQ,YAE1B,GAAIiJ,EAAa,CACf,IAAIc,EAAkB,OAAO/J,EAAQ,eAAe,GAAK,EACrDgK,EAAmB,OAAOhK,EAAQ,gBAAgB,GAAK,EACvDiK,EAAkBH,EAAU,KAAK,IAAIpB,EAAc,MAAOM,EAAW,MAAOA,EAAW,MAAQA,EAAW,KAAMN,EAAc,MAAQM,EAAW,IAAI,EAAIN,EAAc,MACvKwB,EAAmBJ,EAAU,KAAK,IAAIpB,EAAc,OAAQM,EAAW,OAAQA,EAAW,OAASA,EAAW,IAAKN,EAAc,OAASM,EAAW,GAAG,EAAIN,EAAc,OAE9KqB,EAAkB,KAAK,IAAIA,EAAiBrB,EAAc,KAAK,EAC/DsB,EAAmB,KAAK,IAAIA,EAAkBtB,EAAc,MAAM,EAE9DjF,IACEsG,GAAmBC,EACjBA,EAAmBvG,EAAcsG,EACnCC,EAAmBD,EAAkBtG,EAErCsG,EAAkBC,EAAmBvG,EAE9BsG,EACTC,EAAmBD,EAAkBtG,EAC5BuG,IACTD,EAAkBC,EAAmBvG,GAGnCyG,EAAmBzG,EAAcwG,EACnCC,EAAmBD,EAAkBxG,EAErCwG,EAAkBC,EAAmBzG,GAKzC0F,EAAY,SAAW,KAAK,IAAIY,EAAiBE,CAAe,EAChEd,EAAY,UAAY,KAAK,IAAIa,EAAkBE,CAAgB,EACnEf,EAAY,SAAWc,EACvBd,EAAY,UAAYe,EAGtBhB,IACEY,GACFX,EAAY,QAAU,KAAK,IAAI,EAAGH,EAAW,IAAI,EACjDG,EAAY,OAAS,KAAK,IAAI,EAAGH,EAAW,GAAG,EAC/CG,EAAY,QAAU,KAAK,IAAIT,EAAc,MAAOM,EAAW,KAAOA,EAAW,KAAK,EAAIG,EAAY,MACtGA,EAAY,OAAS,KAAK,IAAIT,EAAc,OAAQM,EAAW,IAAMA,EAAW,MAAM,EAAIG,EAAY,SAEtGA,EAAY,QAAU,EACtBA,EAAY,OAAS,EACrBA,EAAY,QAAUT,EAAc,MAAQS,EAAY,MACxDA,EAAY,OAAST,EAAc,OAASS,EAAY,QAG7D,EACD,cAAe,UAAyB,CACtC,IAAInJ,EAAU,KAAK,QACf0I,EAAgB,KAAK,cACrBS,EAAc,KAAK,aAEnBA,EAAY,MAAQA,EAAY,UAAYA,EAAY,MAAQA,EAAY,YAC9EA,EAAY,KAAOA,EAAY,UAG7BA,EAAY,OAASA,EAAY,WAAaA,EAAY,OAASA,EAAY,aACjFA,EAAY,IAAMA,EAAY,QAGhCA,EAAY,MAAQ,KAAK,IAAI,KAAK,IAAIA,EAAY,MAAOA,EAAY,QAAQ,EAAGA,EAAY,QAAQ,EACpGA,EAAY,OAAS,KAAK,IAAI,KAAK,IAAIA,EAAY,OAAQA,EAAY,SAAS,EAAGA,EAAY,SAAS,EACxG,KAAK,aAAa,GAAO,EAAI,EAC7BA,EAAY,KAAO,KAAK,IAAI,KAAK,IAAIA,EAAY,KAAMA,EAAY,OAAO,EAAGA,EAAY,OAAO,EAChGA,EAAY,IAAM,KAAK,IAAI,KAAK,IAAIA,EAAY,IAAKA,EAAY,MAAM,EAAGA,EAAY,MAAM,EAC5FA,EAAY,QAAUA,EAAY,KAClCA,EAAY,OAASA,EAAY,IAE7BnJ,EAAQ,SAAWA,EAAQ,gBAE7BP,GAAQ,KAAK,KAAMpE,GAAa8N,EAAY,OAAST,EAAc,OAASS,EAAY,QAAUT,EAAc,OAAStO,GAAcF,EAAU,EAGnJuE,GAAS,KAAK,QAAST,EAAO,CAC5B,MAAOmL,EAAY,MACnB,OAAQA,EAAY,MACrB,EAAE9H,GAAc,CACf,WAAY8H,EAAY,KACxB,WAAYA,EAAY,GACzB,CAAA,CAAC,CAAC,EAEC,KAAK,SAAW,KAAK,SACvB,KAAK,YAAY,GAAM,EAAI,EAGxB,KAAK,UACR,KAAK,OAAM,CAEd,EACD,OAAQ,UAAkB,CACxB,KAAK,QAAO,EACZxI,GAAc,KAAK,QAASjF,GAAY,KAAK,QAAO,CAAE,CACvD,CACH,EAEIyO,GAAU,CACZ,YAAa,UAAuB,CAClC,IAAIzL,EAAU,KAAK,QACf0L,EAAc,KAAK,YACnBD,EAAU,KAAK,QAAQ,QACvBlJ,EAAMmJ,EAAc,KAAK,eAAiB,KAAK,IAC/CC,EAAM3L,EAAQ,KAAO,uBACrB8F,EAAQ,SAAS,cAAc,KAAK,EAWxC,GATI4F,IACF5F,EAAM,YAAc4F,GAGtB5F,EAAM,IAAMvD,EACZuD,EAAM,IAAM6F,EACZ,KAAK,QAAQ,YAAY7F,CAAK,EAC9B,KAAK,aAAeA,EAEhB,EAAC2F,EAIL,KAAIG,EAAWH,EAEX,OAAOA,GAAY,SACrBG,EAAW5L,EAAQ,cAAc,iBAAiByL,CAAO,EAChDA,EAAQ,gBACjBG,EAAW,CAACH,CAAO,GAGrB,KAAK,SAAWG,EAChBxM,GAAQwM,EAAU,SAAUC,EAAI,CAC9B,IAAIC,EAAM,SAAS,cAAc,KAAK,EAEtC/K,GAAQ8K,EAAIjP,GAAc,CACxB,MAAOiP,EAAG,YACV,OAAQA,EAAG,aACX,KAAMA,EAAG,SACjB,CAAO,EAEGH,IACFI,EAAI,YAAcJ,GAGpBI,EAAI,IAAMvJ,EACVuJ,EAAI,IAAMH,EAQVG,EAAI,MAAM,QAAU,0KACpBD,EAAG,UAAY,GACfA,EAAG,YAAYC,CAAG,CACxB,CAAK,EACF,EACD,aAAc,UAAwB,CACpC1M,GAAQ,KAAK,SAAU,SAAUY,EAAS,CACxC,IAAI9H,EAAO2I,GAAQb,EAASpD,EAAY,EACxCmD,GAASC,EAAS,CAChB,MAAO9H,EAAK,MACZ,OAAQA,EAAK,MACrB,CAAO,EACD8H,EAAQ,UAAY9H,EAAK,KACzB8I,GAAWhB,EAASpD,EAAY,CACtC,CAAK,CACF,EACD,QAAS,UAAmB,CAC1B,IAAIqN,EAAY,KAAK,UACjBK,EAAa,KAAK,WAClBG,EAAc,KAAK,YACnBsB,EAAetB,EAAY,MAC3BuB,EAAgBvB,EAAY,OAC5BxF,EAAQgF,EAAU,MAClBjF,EAASiF,EAAU,OACnBgC,EAAOxB,EAAY,KAAOH,EAAW,KAAOL,EAAU,KACtDiC,EAAMzB,EAAY,IAAMH,EAAW,IAAML,EAAU,IAEnD,CAAC,KAAK,SAAW,KAAK,WAI1BlK,GAAS,KAAK,aAAcT,EAAO,CACjC,MAAO2F,EACP,OAAQD,CACd,EAAOrC,GAAcrD,EAAO,CACtB,WAAY,CAAC2M,EACb,WAAY,CAACC,CACnB,EAAOjC,CAAS,CAAC,CAAC,CAAC,EACf7K,GAAQ,KAAK,SAAU,SAAUY,EAAS,CACxC,IAAI9H,EAAO2I,GAAQb,EAASpD,EAAY,EACpCuP,EAAgBjU,EAAK,MACrBkU,EAAiBlU,EAAK,OACtByN,EAAWwG,EACXvG,EAAYwG,EACZnI,EAAQ,EAER8H,IACF9H,EAAQkI,EAAgBJ,EACxBnG,EAAYoG,EAAgB/H,GAG1B+H,GAAiBpG,EAAYwG,IAC/BnI,EAAQmI,EAAiBJ,EACzBrG,EAAWoG,EAAe9H,EAC1B2B,EAAYwG,GAGdrM,GAASC,EAAS,CAChB,MAAO2F,EACP,OAAQC,CAChB,CAAO,EACD7F,GAASC,EAAQ,qBAAqB,KAAK,EAAE,CAAC,EAAGV,EAAO,CACtD,MAAO2F,EAAQhB,EACf,OAAQe,EAASf,CACzB,EAAStB,GAAcrD,EAAO,CACtB,WAAY,CAAC2M,EAAOhI,EACpB,WAAY,CAACiI,EAAMjI,CAC3B,EAASgG,CAAS,CAAC,CAAC,CAAC,CACrB,CAAK,EACF,CACH,EAEIoC,GAAS,CACX,KAAM,UAAgB,CACpB,IAAIrM,EAAU,KAAK,QACfsB,EAAU,KAAK,QACfyI,EAAU,KAAK,QAEf9K,GAAWqC,EAAQ,SAAS,GAC9BM,GAAY5B,EAAS7C,GAAkBmE,EAAQ,SAAS,EAGtDrC,GAAWqC,EAAQ,QAAQ,GAC7BM,GAAY5B,EAAS9C,GAAiBoE,EAAQ,QAAQ,EAGpDrC,GAAWqC,EAAQ,OAAO,GAC5BM,GAAY5B,EAAS/C,GAAgBqE,EAAQ,OAAO,EAGlDrC,GAAWqC,EAAQ,IAAI,GACzBM,GAAY5B,EAAShD,GAAYsE,EAAQ,IAAI,EAG3CrC,GAAWqC,EAAQ,IAAI,GACzBM,GAAY5B,EAASlC,GAAYwD,EAAQ,IAAI,EAG/CM,GAAYmI,EAASvM,GAAoB,KAAK,YAAc,KAAK,UAAU,KAAK,IAAI,CAAC,EAEjF8D,EAAQ,UAAYA,EAAQ,aAC9BM,GAAYmI,EAASlM,GAAa,KAAK,QAAU,KAAK,MAAM,KAAK,IAAI,EAAG,CACtE,QAAS,GACT,QAAS,EACjB,CAAO,EAGCyD,EAAQ,0BACVM,GAAYmI,EAAS3M,GAAgB,KAAK,WAAa,KAAK,SAAS,KAAK,IAAI,CAAC,EAGjFwE,GAAY5B,EAAQ,cAAevC,GAAoB,KAAK,WAAa,KAAK,SAAS,KAAK,IAAI,CAAC,EACjGmE,GAAY5B,EAAQ,cAAetC,GAAkB,KAAK,UAAY,KAAK,QAAQ,KAAK,IAAI,CAAC,EAEzF4D,EAAQ,YACVM,GAAY,OAAQhE,GAAc,KAAK,SAAW,KAAK,OAAO,KAAK,IAAI,CAAC,CAE3E,EACD,OAAQ,UAAkB,CACxB,IAAIoC,EAAU,KAAK,QACfsB,EAAU,KAAK,QACfyI,EAAU,KAAK,QAEf9K,GAAWqC,EAAQ,SAAS,GAC9BC,GAAevB,EAAS7C,GAAkBmE,EAAQ,SAAS,EAGzDrC,GAAWqC,EAAQ,QAAQ,GAC7BC,GAAevB,EAAS9C,GAAiBoE,EAAQ,QAAQ,EAGvDrC,GAAWqC,EAAQ,OAAO,GAC5BC,GAAevB,EAAS/C,GAAgBqE,EAAQ,OAAO,EAGrDrC,GAAWqC,EAAQ,IAAI,GACzBC,GAAevB,EAAShD,GAAYsE,EAAQ,IAAI,EAG9CrC,GAAWqC,EAAQ,IAAI,GACzBC,GAAevB,EAASlC,GAAYwD,EAAQ,IAAI,EAGlDC,GAAewI,EAASvM,GAAoB,KAAK,WAAW,EAExD8D,EAAQ,UAAYA,EAAQ,aAC9BC,GAAewI,EAASlM,GAAa,KAAK,QAAS,CACjD,QAAS,GACT,QAAS,EACjB,CAAO,EAGCyD,EAAQ,0BACVC,GAAewI,EAAS3M,GAAgB,KAAK,UAAU,EAGzDmE,GAAevB,EAAQ,cAAevC,GAAoB,KAAK,UAAU,EACzE8D,GAAevB,EAAQ,cAAetC,GAAkB,KAAK,SAAS,EAElE4D,EAAQ,YACVC,GAAe,OAAQ3D,GAAc,KAAK,QAAQ,CAErD,CACH,EAEI0O,GAAW,CACb,OAAQ,UAAkB,CACxB,GAAI,MAAK,SAIT,KAAIhL,EAAU,KAAK,QACfwI,EAAY,KAAK,UACjBE,EAAgB,KAAK,cACrBuC,EAASzC,EAAU,YAAcE,EAAc,MAC/CwC,EAAS1C,EAAU,aAAeE,EAAc,OAChD/F,EAAQ,KAAK,IAAIsI,EAAS,CAAC,EAAI,KAAK,IAAIC,EAAS,CAAC,EAAID,EAASC,EAEnE,GAAIvI,IAAU,EAAG,CACf,IAAIqG,EACAG,EAEAnJ,EAAQ,UACVgJ,EAAa,KAAK,gBAClBG,EAAc,KAAK,kBAGrB,KAAK,OAAM,EAEPnJ,EAAQ,UACV,KAAK,cAAclC,GAAQkL,EAAY,SAAUtP,EAAG1B,EAAG,CACrDgR,EAAWhR,CAAC,EAAI0B,EAAIiJ,CACrB,CAAA,CAAC,EACF,KAAK,eAAe7E,GAAQqL,EAAa,SAAUzP,EAAG1B,EAAG,CACvDmR,EAAYnR,CAAC,EAAI0B,EAAIiJ,CACtB,CAAA,CAAC,IAGP,EACD,SAAU,UAAoB,CACxB,KAAK,UAAY,KAAK,QAAQ,WAAalH,IAI/C,KAAK,YAAYqD,GAAS,KAAK,QAAShE,EAAU,EAAIU,GAAiBD,EAAc,CACtF,EACD,MAAO,SAAe6E,EAAO,CAC3B,IAAI+K,EAAQ,KAERxI,EAAQ,OAAO,KAAK,QAAQ,cAAc,GAAK,GAC/CyI,EAAQ,EAER,KAAK,WAIThL,EAAM,eAAc,EAEhB,MAAK,WAIT,KAAK,SAAW,GAChB,WAAW,UAAY,CACrB+K,EAAM,SAAW,EAClB,EAAE,EAAE,EAED/K,EAAM,OACRgL,EAAQhL,EAAM,OAAS,EAAI,EAAI,GACtBA,EAAM,WACfgL,EAAQ,CAAChL,EAAM,WAAa,IACnBA,EAAM,SACfgL,EAAQhL,EAAM,OAAS,EAAI,EAAI,IAGjC,KAAK,KAAK,CAACgL,EAAQzI,EAAOvC,CAAK,GAChC,EACD,UAAW,SAAmBA,EAAO,CACnC,IAAIiL,EAAUjL,EAAM,QAChB9K,EAAS8K,EAAM,OAEnB,GAAI,OAAK,WACLA,EAAM,OAAS,aAAeA,EAAM,OAAS,eAAiBA,EAAM,cAAgB,WACxFjD,EAASkO,CAAO,GAAKA,IAAY,GAAKlO,EAAS7H,CAAM,GAAKA,IAAW,GAClE8K,EAAM,UAIT,KAAIJ,EAAU,KAAK,QACf+B,EAAW,KAAK,SAChBuJ,EAEAlL,EAAM,eAERtC,GAAQsC,EAAM,eAAgB,SAAUmL,EAAO,CAC7CxJ,EAASwJ,EAAM,UAAU,EAAI3I,GAAW2I,CAAK,CACrD,CAAO,EAGDxJ,EAAS3B,EAAM,WAAa,CAAC,EAAIwC,GAAWxC,CAAK,EAG/C,OAAO,KAAK2B,CAAQ,EAAE,OAAS,GAAK/B,EAAQ,UAAYA,EAAQ,YAClEsL,EAASjR,GAETiR,EAAS/L,GAAQa,EAAM,OAAQ/E,EAAW,EAGvCqB,GAAe,KAAK4O,CAAM,GAI3B3K,GAAc,KAAK,QAAS9E,GAAkB,CAChD,cAAeuE,EACf,OAAQkL,CACT,CAAA,IAAM,KAKPlL,EAAM,eAAc,EACpB,KAAK,OAASkL,EACd,KAAK,SAAW,GAEZA,IAAWnR,KACb,KAAK,SAAW,GAChB4E,GAAS,KAAK,QAAS5D,EAAW,IAErC,EACD,SAAU,SAAkBiF,EAAO,CACjC,IAAIkL,EAAS,KAAK,OAElB,GAAI,OAAK,UAAY,CAACA,GAItB,KAAIvJ,EAAW,KAAK,SACpB3B,EAAM,eAAc,EAEhBO,GAAc,KAAK,QAAS/E,GAAiB,CAC/C,cAAewE,EACf,OAAQkL,CACT,CAAA,IAAM,KAIHlL,EAAM,eACRtC,GAAQsC,EAAM,eAAgB,SAAUmL,EAAO,CAE7CvN,EAAO+D,EAASwJ,EAAM,UAAU,GAAK,CAAE,EAAE3I,GAAW2I,EAAO,EAAI,CAAC,CACxE,CAAO,EAEDvN,EAAO+D,EAAS3B,EAAM,WAAa,CAAC,GAAK,CAAE,EAAEwC,GAAWxC,EAAO,EAAI,CAAC,EAGtE,KAAK,OAAOA,CAAK,GAClB,EACD,QAAS,SAAiBA,EAAO,CAC/B,GAAI,MAAK,SAIT,KAAIkL,EAAS,KAAK,OACdvJ,EAAW,KAAK,SAEhB3B,EAAM,eACRtC,GAAQsC,EAAM,eAAgB,SAAUmL,EAAO,CAC7C,OAAOxJ,EAASwJ,EAAM,UAAU,CACxC,CAAO,EAED,OAAOxJ,EAAS3B,EAAM,WAAa,CAAC,EAGjCkL,IAILlL,EAAM,eAAc,EAEf,OAAO,KAAK2B,CAAQ,EAAE,SACzB,KAAK,OAAS,IAGZ,KAAK,WACP,KAAK,SAAW,GAChB5C,GAAY,KAAK,QAAShE,GAAa,KAAK,SAAW,KAAK,QAAQ,KAAK,GAG3EwF,GAAc,KAAK,QAAShF,GAAgB,CAC1C,cAAeyE,EACf,OAAQkL,CACd,CAAK,GACF,CACH,EAEIE,GAAS,CACX,OAAQ,SAAgBpL,EAAO,CAC7B,IAAIJ,EAAU,KAAK,QACfgJ,EAAa,KAAK,WAClBN,EAAgB,KAAK,cACrBS,EAAc,KAAK,YACnBpH,EAAW,KAAK,SAChBuJ,EAAS,KAAK,OACd7H,EAAczD,EAAQ,YACtB2K,EAAOxB,EAAY,KACnByB,EAAMzB,EAAY,IAClBxF,EAAQwF,EAAY,MACpBzF,EAASyF,EAAY,OACrBsC,EAAQd,EAAOhH,EACf+H,EAASd,EAAMlH,EACfiI,EAAU,EACVC,EAAS,EACTjG,EAAW+C,EAAc,MACzB7C,EAAY6C,EAAc,OAC1BmD,EAAa,GACb9D,GAEA,CAACtE,GAAerD,EAAM,WACxBqD,EAAcE,GAASD,EAASC,EAAQD,EAAS,GAG/C,KAAK,UACPiI,EAAUxC,EAAY,QACtByC,EAASzC,EAAY,OACrBxD,EAAWgG,EAAU,KAAK,IAAIjD,EAAc,MAAOM,EAAW,MAAOA,EAAW,KAAOA,EAAW,KAAK,EACvGnD,EAAY+F,EAAS,KAAK,IAAIlD,EAAc,OAAQM,EAAW,OAAQA,EAAW,IAAMA,EAAW,MAAM,GAG3G,IAAI9G,EAAUH,EAAS,OAAO,KAAKA,CAAQ,EAAE,CAAC,CAAC,EAC3C+J,EAAQ,CACV,EAAG5J,EAAQ,KAAOA,EAAQ,OAC1B,EAAGA,EAAQ,KAAOA,EAAQ,MAChC,EAEQ6J,EAAQ,SAAeC,EAAM,CAC/B,OAAQA,EAAI,CACV,KAAK1R,GACCmR,EAAQK,EAAM,EAAInG,IACpBmG,EAAM,EAAInG,EAAW8F,GAGvB,MAEF,KAAKlR,GACCoQ,EAAOmB,EAAM,EAAIH,IACnBG,EAAM,EAAIH,EAAUhB,GAGtB,MAEF,KAAKlQ,GACCmQ,EAAMkB,EAAM,EAAIF,IAClBE,EAAM,EAAIF,EAAShB,GAGrB,MAEF,KAAKpQ,GACCkR,EAASI,EAAM,EAAIjG,IACrBiG,EAAM,EAAIjG,EAAY6F,GAGxB,KACH,CACP,EAEI,OAAQJ,EAAM,CAEZ,KAAKpR,GACHyQ,GAAQmB,EAAM,EACdlB,GAAOkB,EAAM,EACb,MAGF,KAAKxR,GACH,GAAIwR,EAAM,GAAK,IAAML,GAAS9F,GAAYlC,IAAgBmH,GAAOgB,GAAUF,GAAU7F,IAAa,CAChGgG,EAAa,GACb,MAGFE,EAAMzR,EAAW,EACjBqJ,GAASmI,EAAM,EAEXnI,EAAQ,IACV2H,EAAS/Q,GACToJ,EAAQ,CAACA,EACTgH,GAAQhH,GAGNF,IACFC,EAASC,EAAQF,EACjBmH,IAAQzB,EAAY,OAASzF,GAAU,GAGzC,MAEF,KAAKjJ,GACH,GAAIqR,EAAM,GAAK,IAAMlB,GAAOgB,GAAUnI,IAAgBkH,GAAQgB,GAAWF,GAAS9F,IAAY,CAC5FkG,EAAa,GACb,MAGFE,EAAMtR,EAAY,EAClBiJ,GAAUoI,EAAM,EAChBlB,GAAOkB,EAAM,EAETpI,EAAS,IACX4H,EAAS9Q,GACTkJ,EAAS,CAACA,EACVkH,GAAOlH,GAGLD,IACFE,EAAQD,EAASD,EACjBkH,IAASxB,EAAY,MAAQxF,GAAS,GAGxC,MAEF,KAAKpJ,GACH,GAAIuR,EAAM,GAAK,IAAMnB,GAAQgB,GAAWlI,IAAgBmH,GAAOgB,GAAUF,GAAU7F,IAAa,CAC9FgG,EAAa,GACb,MAGFE,EAAMxR,EAAW,EACjBoJ,GAASmI,EAAM,EACfnB,GAAQmB,EAAM,EAEVnI,EAAQ,IACV2H,EAAShR,GACTqJ,EAAQ,CAACA,EACTgH,GAAQhH,GAGNF,IACFC,EAASC,EAAQF,EACjBmH,IAAQzB,EAAY,OAASzF,GAAU,GAGzC,MAEF,KAAKlJ,GACH,GAAIsR,EAAM,GAAK,IAAMJ,GAAU7F,GAAapC,IAAgBkH,GAAQgB,GAAWF,GAAS9F,IAAY,CAClGkG,EAAa,GACb,MAGFE,EAAMvR,EAAY,EAClBkJ,GAAUoI,EAAM,EAEZpI,EAAS,IACX4H,EAAS7Q,GACTiJ,EAAS,CAACA,EACVkH,GAAOlH,GAGLD,IACFE,EAAQD,EAASD,EACjBkH,IAASxB,EAAY,MAAQxF,GAAS,GAGxC,MAEF,KAAKjJ,GACH,GAAI+I,EAAa,CACf,GAAIqI,EAAM,GAAK,IAAMlB,GAAOgB,GAAUH,GAAS9F,GAAW,CACxDkG,EAAa,GACb,MAGFE,EAAMtR,EAAY,EAClBiJ,GAAUoI,EAAM,EAChBlB,GAAOkB,EAAM,EACbnI,EAAQD,EAASD,OAEjBsI,EAAMtR,EAAY,EAClBsR,EAAMzR,EAAW,EAEbwR,EAAM,GAAK,EACTL,EAAQ9F,EACVhC,GAASmI,EAAM,EACNA,EAAM,GAAK,GAAKlB,GAAOgB,IAChCC,EAAa,IAGflI,GAASmI,EAAM,EAGbA,EAAM,GAAK,EACTlB,EAAMgB,IACRlI,GAAUoI,EAAM,EAChBlB,GAAOkB,EAAM,IAGfpI,GAAUoI,EAAM,EAChBlB,GAAOkB,EAAM,GAIbnI,EAAQ,GAAKD,EAAS,GACxB4H,EAASzQ,GACT6I,EAAS,CAACA,EACVC,EAAQ,CAACA,EACTiH,GAAOlH,EACPiH,GAAQhH,GACCA,EAAQ,GACjB2H,EAAS3Q,GACTgJ,EAAQ,CAACA,EACTgH,GAAQhH,GACCD,EAAS,IAClB4H,EAAS1Q,GACT8I,EAAS,CAACA,EACVkH,GAAOlH,GAGT,MAEF,KAAK/I,GACH,GAAI8I,EAAa,CACf,GAAIqI,EAAM,GAAK,IAAMlB,GAAOgB,GAAUjB,GAAQgB,GAAU,CACtDE,EAAa,GACb,MAGFE,EAAMtR,EAAY,EAClBiJ,GAAUoI,EAAM,EAChBlB,GAAOkB,EAAM,EACbnI,EAAQD,EAASD,EACjBkH,GAAQxB,EAAY,MAAQxF,OAE5BoI,EAAMtR,EAAY,EAClBsR,EAAMxR,EAAW,EAEbuR,EAAM,GAAK,EACTnB,EAAOgB,GACThI,GAASmI,EAAM,EACfnB,GAAQmB,EAAM,GACLA,EAAM,GAAK,GAAKlB,GAAOgB,IAChCC,EAAa,KAGflI,GAASmI,EAAM,EACfnB,GAAQmB,EAAM,GAGZA,EAAM,GAAK,EACTlB,EAAMgB,IACRlI,GAAUoI,EAAM,EAChBlB,GAAOkB,EAAM,IAGfpI,GAAUoI,EAAM,EAChBlB,GAAOkB,EAAM,GAIbnI,EAAQ,GAAKD,EAAS,GACxB4H,EAAS1Q,GACT8I,EAAS,CAACA,EACVC,EAAQ,CAACA,EACTiH,GAAOlH,EACPiH,GAAQhH,GACCA,EAAQ,GACjB2H,EAAS5Q,GACTiJ,EAAQ,CAACA,EACTgH,GAAQhH,GACCD,EAAS,IAClB4H,EAASzQ,GACT6I,EAAS,CAACA,EACVkH,GAAOlH,GAGT,MAEF,KAAK7I,GACH,GAAI4I,EAAa,CACf,GAAIqI,EAAM,GAAK,IAAMnB,GAAQgB,GAAWD,GAAU7F,GAAY,CAC5DgG,EAAa,GACb,MAGFE,EAAMxR,EAAW,EACjBoJ,GAASmI,EAAM,EACfnB,GAAQmB,EAAM,EACdpI,EAASC,EAAQF,OAEjBsI,EAAMvR,EAAY,EAClBuR,EAAMxR,EAAW,EAEbuR,EAAM,GAAK,EACTnB,EAAOgB,GACThI,GAASmI,EAAM,EACfnB,GAAQmB,EAAM,GACLA,EAAM,GAAK,GAAKJ,GAAU7F,IACnCgG,EAAa,KAGflI,GAASmI,EAAM,EACfnB,GAAQmB,EAAM,GAGZA,EAAM,GAAK,EACTJ,EAAS7F,IACXnC,GAAUoI,EAAM,GAGlBpI,GAAUoI,EAAM,EAIhBnI,EAAQ,GAAKD,EAAS,GACxB4H,EAAS5Q,GACTgJ,EAAS,CAACA,EACVC,EAAQ,CAACA,EACTiH,GAAOlH,EACPiH,GAAQhH,GACCA,EAAQ,GACjB2H,EAAS1Q,GACT+I,EAAQ,CAACA,EACTgH,GAAQhH,GACCD,EAAS,IAClB4H,EAAS3Q,GACT+I,EAAS,CAACA,EACVkH,GAAOlH,GAGT,MAEF,KAAK9I,GACH,GAAI6I,EAAa,CACf,GAAIqI,EAAM,GAAK,IAAML,GAAS9F,GAAY+F,GAAU7F,GAAY,CAC9DgG,EAAa,GACb,MAGFE,EAAMzR,EAAW,EACjBqJ,GAASmI,EAAM,EACfpI,EAASC,EAAQF,OAEjBsI,EAAMvR,EAAY,EAClBuR,EAAMzR,EAAW,EAEbwR,EAAM,GAAK,EACTL,EAAQ9F,EACVhC,GAASmI,EAAM,EACNA,EAAM,GAAK,GAAKJ,GAAU7F,IACnCgG,EAAa,IAGflI,GAASmI,EAAM,EAGbA,EAAM,GAAK,EACTJ,EAAS7F,IACXnC,GAAUoI,EAAM,GAGlBpI,GAAUoI,EAAM,EAIhBnI,EAAQ,GAAKD,EAAS,GACxB4H,EAAS3Q,GACT+I,EAAS,CAACA,EACVC,EAAQ,CAACA,EACTiH,GAAOlH,EACPiH,GAAQhH,GACCA,EAAQ,GACjB2H,EAASzQ,GACT8I,EAAQ,CAACA,EACTgH,GAAQhH,GACCD,EAAS,IAClB4H,EAAS5Q,GACTgJ,EAAS,CAACA,EACVkH,GAAOlH,GAGT,MAGF,KAAKtJ,GACH,KAAK,KAAK0R,EAAM,EAAGA,EAAM,CAAC,EAC1BD,EAAa,GACb,MAGF,KAAKxR,GACH,KAAK,KAAKyH,GAAgBC,CAAQ,EAAG3B,CAAK,EAC1CyL,EAAa,GACb,MAGF,KAAK1R,GACH,GAAI,CAAC2R,EAAM,GAAK,CAACA,EAAM,EAAG,CACxBD,EAAa,GACb,MAGF9D,GAASnH,GAAU,KAAK,OAAO,EAC/B+J,EAAOzI,EAAQ,OAAS6F,GAAO,KAC/B6C,EAAM1I,EAAQ,OAAS6F,GAAO,IAC9BpE,EAAQwF,EAAY,SACpBzF,EAASyF,EAAY,UAEjB2C,EAAM,EAAI,EACZR,EAASQ,EAAM,EAAI,EAAIlR,GAAoBF,GAClCoR,EAAM,EAAI,IACnBnB,GAAQhH,EACR2H,EAASQ,EAAM,EAAI,EAAIjR,GAAoBF,IAGzCmR,EAAM,EAAI,IACZlB,GAAOlH,GAIJ,KAAK,UACRxE,GAAY,KAAK,QAASlE,EAAY,EACtC,KAAK,QAAU,GAEX,KAAK,SACP,KAAK,aAAa,GAAM,EAAI,GAIhC,KACH,CAEG6Q,IACF1C,EAAY,MAAQxF,EACpBwF,EAAY,OAASzF,EACrByF,EAAY,KAAOwB,EACnBxB,EAAY,IAAMyB,EAClB,KAAK,OAASU,EACd,KAAK,cAAa,GAIpBxN,GAAQiE,EAAU,SAAUkK,EAAG,CAC7BA,EAAE,OAASA,EAAE,KACbA,EAAE,OAASA,EAAE,IACnB,CAAK,CACF,CACH,EAEIC,GAAU,CAEZ,KAAM,UAAgB,CACpB,OAAI,KAAK,OAAS,CAAC,KAAK,SAAW,CAAC,KAAK,WACvC,KAAK,QAAU,GACf,KAAK,aAAa,GAAM,EAAI,EAExB,KAAK,QAAQ,OACfnN,GAAS,KAAK,QAAS5D,EAAW,EAGpC+D,GAAY,KAAK,QAASlE,EAAY,EACtC,KAAK,eAAe,KAAK,kBAAkB,GAGtC,IACR,EAED,MAAO,UAAiB,CACtB,OAAI,KAAK,OAAS,CAAC,KAAK,WACtB,KAAK,UAAYgD,EAAO,CAAE,EAAE,KAAK,gBAAgB,EACjD,KAAK,WAAaA,EAAO,CAAE,EAAE,KAAK,iBAAiB,EACnD,KAAK,YAAcA,EAAO,CAAE,EAAE,KAAK,kBAAkB,EACrD,KAAK,aAAY,EAEb,KAAK,SACP,KAAK,cAAa,GAIf,IACR,EAED,MAAO,UAAiB,CACtB,OAAI,KAAK,SAAW,CAAC,KAAK,WACxBA,EAAO,KAAK,YAAa,CACvB,KAAM,EACN,IAAK,EACL,MAAO,EACP,OAAQ,CAChB,CAAO,EACD,KAAK,QAAU,GACf,KAAK,cAAa,EAClB,KAAK,YAAY,GAAM,EAAI,EAE3B,KAAK,aAAY,EACjBkB,GAAY,KAAK,QAAS/D,EAAW,EACrC4D,GAAS,KAAK,QAAS/D,EAAY,GAG9B,IACR,EAQD,QAAS,SAAiBiG,EAAK,CAC7B,IAAIkL,EAAc,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,GAEtF,MAAI,CAAC,KAAK,UAAYlL,IAChB,KAAK,QACP,KAAK,QAAQ,IAAMA,GAGjBkL,GACF,KAAK,IAAMlL,EACX,KAAK,MAAM,IAAMA,EAEb,KAAK,QACP,KAAK,aAAa,IAAMA,EACxBnD,GAAQ,KAAK,SAAU,SAAUY,EAAS,CACxCA,EAAQ,qBAAqB,KAAK,EAAE,CAAC,EAAE,IAAMuC,CACzD,CAAW,KAGC,KAAK,QACP,KAAK,SAAW,IAGlB,KAAK,QAAQ,KAAO,KACpB,KAAK,SAAQ,EACb,KAAK,KAAKA,CAAG,IAIV,IACR,EAED,OAAQ,UAAkB,CACxB,OAAI,KAAK,OAAS,KAAK,WACrB,KAAK,SAAW,GAChB/B,GAAY,KAAK,QAASnE,EAAc,GAGnC,IACR,EAED,QAAS,UAAmB,CAC1B,OAAI,KAAK,OAAS,CAAC,KAAK,WACtB,KAAK,SAAW,GAChBgE,GAAS,KAAK,QAAShE,EAAc,GAGhC,IACR,EAMD,QAAS,UAAmB,CAC1B,IAAI2D,EAAU,KAAK,QAEnB,OAAKA,EAAQzE,CAAS,GAItByE,EAAQzE,CAAS,EAAI,OAEjB,KAAK,OAAS,KAAK,WACrByE,EAAQ,IAAM,KAAK,aAGrB,KAAK,SAAQ,EACN,MAVE,IAWV,EAQD,KAAM,SAAc0N,EAAS,CAC3B,IAAIC,EAAU,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAID,EAC9EE,EAAmB,KAAK,WACxB3B,EAAO2B,EAAiB,KACxB1B,EAAM0B,EAAiB,IAC3B,OAAO,KAAK,OAAOjP,GAAY+O,CAAO,EAAIA,EAAUzB,EAAO,OAAOyB,CAAO,EAAG/O,GAAYgP,CAAO,EAAIA,EAAUzB,EAAM,OAAOyB,CAAO,CAAC,CACnI,EAQD,OAAQ,SAAgBE,EAAG,CACzB,IAAIC,EAAI,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAID,EACxEvD,EAAa,KAAK,WAClBU,EAAU,GACd,OAAA6C,EAAI,OAAOA,CAAC,EACZC,EAAI,OAAOA,CAAC,EAER,KAAK,OAAS,CAAC,KAAK,UAAY,KAAK,QAAQ,UAC3CrP,EAASoP,CAAC,IACZvD,EAAW,KAAOuD,EAClB7C,EAAU,IAGRvM,EAASqP,CAAC,IACZxD,EAAW,IAAMwD,EACjB9C,EAAU,IAGRA,GACF,KAAK,aAAa,EAAI,GAInB,IACR,EAQD,KAAM,SAAc/G,EAAO8J,EAAgB,CACzC,IAAIzD,EAAa,KAAK,WACtB,OAAArG,EAAQ,OAAOA,CAAK,EAEhBA,EAAQ,EACVA,EAAQ,GAAK,EAAIA,GAEjBA,EAAQ,EAAIA,EAGP,KAAK,OAAOqG,EAAW,MAAQrG,EAAQqG,EAAW,aAAc,KAAMyD,CAAc,CAC5F,EASD,OAAQ,SAAgB9J,EAAO+J,EAAOD,EAAgB,CACpD,IAAIzM,EAAU,KAAK,QACfgJ,EAAa,KAAK,WAClBrF,EAAQqF,EAAW,MACnBtF,EAASsF,EAAW,OACpB9D,EAAe8D,EAAW,aAC1B7D,EAAgB6D,EAAW,cAG/B,GAFArG,EAAQ,OAAOA,CAAK,EAEhBA,GAAS,GAAK,KAAK,OAAS,CAAC,KAAK,UAAY3C,EAAQ,SAAU,CAClE,IAAIqE,EAAWa,EAAevC,EAC1B2B,EAAYa,EAAgBxC,EAEhC,GAAIhC,GAAc,KAAK,QAASnE,GAAY,CAC1C,MAAOmG,EACP,SAAUgB,EAAQuB,EAClB,cAAeuH,CAChB,CAAA,IAAM,GACL,OAAO,KAGT,GAAIA,EAAgB,CAClB,IAAI1K,EAAW,KAAK,SAChBgG,EAASnH,GAAU,KAAK,OAAO,EAC/B+L,EAAS5K,GAAY,OAAO,KAAKA,CAAQ,EAAE,OAASmB,GAAkBnB,CAAQ,EAAI,CACpF,MAAO0K,EAAe,MACtB,MAAOA,EAAe,KAChC,EAEQzD,EAAW,OAAS3E,EAAWV,KAAWgJ,EAAO,MAAQ5E,EAAO,KAAOiB,EAAW,MAAQrF,GAC1FqF,EAAW,MAAQ1E,EAAYZ,KAAYiJ,EAAO,MAAQ5E,EAAO,IAAMiB,EAAW,KAAOtF,QAChFlG,GAAckP,CAAK,GAAKvP,EAASuP,EAAM,CAAC,GAAKvP,EAASuP,EAAM,CAAC,GACtE1D,EAAW,OAAS3E,EAAWV,KAAW+I,EAAM,EAAI1D,EAAW,MAAQrF,GACvEqF,EAAW,MAAQ1E,EAAYZ,KAAYgJ,EAAM,EAAI1D,EAAW,KAAOtF,KAGvEsF,EAAW,OAAS3E,EAAWV,GAAS,EACxCqF,EAAW,MAAQ1E,EAAYZ,GAAU,GAG3CsF,EAAW,MAAQ3E,EACnB2E,EAAW,OAAS1E,EACpB,KAAK,aAAa,EAAI,EAGxB,OAAO,IACR,EAOD,OAAQ,SAAgBL,EAAQ,CAC9B,OAAO,KAAK,UAAU,KAAK,UAAU,QAAU,GAAK,OAAOA,CAAM,CAAC,CACnE,EAOD,SAAU,SAAkBA,EAAQ,CAClC,OAAAA,EAAS,OAAOA,CAAM,EAElB9G,EAAS8G,CAAM,GAAK,KAAK,OAAS,CAAC,KAAK,UAAY,KAAK,QAAQ,YACnE,KAAK,UAAU,OAASA,EAAS,IACjC,KAAK,aAAa,GAAM,EAAI,GAGvB,IACR,EAOD,OAAQ,SAAgB2I,EAAS,CAC/B,IAAInL,EAAS,KAAK,UAAU,OAC5B,OAAO,KAAK,MAAMmL,EAASzP,EAASsE,CAAM,EAAIA,EAAS,CAAC,CACzD,EAOD,OAAQ,SAAgBoL,EAAS,CAC/B,IAAIrL,EAAS,KAAK,UAAU,OAC5B,OAAO,KAAK,MAAMrE,EAASqE,CAAM,EAAIA,EAAS,EAAGqL,CAAO,CACzD,EAQD,MAAO,SAAerL,EAAQ,CAC5B,IAAIC,EAAS,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAID,EAC7EmH,EAAY,KAAK,UACjBgB,EAAc,GAClB,OAAAnI,EAAS,OAAOA,CAAM,EACtBC,EAAS,OAAOA,CAAM,EAElB,KAAK,OAAS,CAAC,KAAK,UAAY,KAAK,QAAQ,WAC3CtE,EAASqE,CAAM,IACjBmH,EAAU,OAASnH,EACnBmI,EAAc,IAGZxM,EAASsE,CAAM,IACjBkH,EAAU,OAASlH,EACnBkI,EAAc,IAGZA,GACF,KAAK,aAAa,GAAM,EAAI,GAIzB,IACR,EAOD,QAAS,UAAmB,CAC1B,IAAImD,EAAU,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,GAC9E9M,EAAU,KAAK,QACf2I,EAAY,KAAK,UACjBK,EAAa,KAAK,WAClBG,EAAc,KAAK,YACnBvS,EAEJ,GAAI,KAAK,OAAS,KAAK,QAAS,CAC9BA,EAAO,CACL,EAAGuS,EAAY,KAAOH,EAAW,KACjC,EAAGG,EAAY,IAAMH,EAAW,IAChC,MAAOG,EAAY,MACnB,OAAQA,EAAY,MAC5B,EACM,IAAIxG,EAAQgG,EAAU,MAAQA,EAAU,aAKxC,GAJA7K,GAAQlH,EAAM,SAAU8C,EAAG1B,EAAG,CAC5BpB,EAAKoB,CAAC,EAAI0B,EAAIiJ,CACtB,CAAO,EAEGmK,EAAS,CAGX,IAAIpB,EAAS,KAAK,MAAM9U,EAAK,EAAIA,EAAK,MAAM,EACxC6U,EAAQ,KAAK,MAAM7U,EAAK,EAAIA,EAAK,KAAK,EAC1CA,EAAK,EAAI,KAAK,MAAMA,EAAK,CAAC,EAC1BA,EAAK,EAAI,KAAK,MAAMA,EAAK,CAAC,EAC1BA,EAAK,MAAQ6U,EAAQ7U,EAAK,EAC1BA,EAAK,OAAS8U,EAAS9U,EAAK,QAG9BA,EAAO,CACL,EAAG,EACH,EAAG,EACH,MAAO,EACP,OAAQ,CAChB,EAGI,OAAIoJ,EAAQ,YACVpJ,EAAK,OAAS+R,EAAU,QAAU,GAGhC3I,EAAQ,WACVpJ,EAAK,OAAS+R,EAAU,QAAU,EAClC/R,EAAK,OAAS+R,EAAU,QAAU,GAG7B/R,CACR,EAOD,QAAS,SAAiBA,EAAM,CAC9B,IAAIoJ,EAAU,KAAK,QACf2I,EAAY,KAAK,UACjBK,EAAa,KAAK,WAClBG,EAAc,CAAA,EAElB,GAAI,KAAK,OAAS,CAAC,KAAK,UAAY3L,GAAc5G,CAAI,EAAG,CACvD,IAAI+S,EAAc,GAEd3J,EAAQ,WACN7C,EAASvG,EAAK,MAAM,GAAKA,EAAK,SAAW+R,EAAU,SACrDA,EAAU,OAAS/R,EAAK,OACxB+S,EAAc,IAId3J,EAAQ,WACN7C,EAASvG,EAAK,MAAM,GAAKA,EAAK,SAAW+R,EAAU,SACrDA,EAAU,OAAS/R,EAAK,OACxB+S,EAAc,IAGZxM,EAASvG,EAAK,MAAM,GAAKA,EAAK,SAAW+R,EAAU,SACrDA,EAAU,OAAS/R,EAAK,OACxB+S,EAAc,KAIdA,GACF,KAAK,aAAa,GAAM,EAAI,EAG9B,IAAIhH,EAAQgG,EAAU,MAAQA,EAAU,aAEpCxL,EAASvG,EAAK,CAAC,IACjBuS,EAAY,KAAOvS,EAAK,EAAI+L,EAAQqG,EAAW,MAG7C7L,EAASvG,EAAK,CAAC,IACjBuS,EAAY,IAAMvS,EAAK,EAAI+L,EAAQqG,EAAW,KAG5C7L,EAASvG,EAAK,KAAK,IACrBuS,EAAY,MAAQvS,EAAK,MAAQ+L,GAG/BxF,EAASvG,EAAK,MAAM,IACtBuS,EAAY,OAASvS,EAAK,OAAS+L,GAGrC,KAAK,eAAewG,CAAW,EAGjC,OAAO,IACR,EAMD,iBAAkB,UAA4B,CAC5C,OAAO,KAAK,MAAQnL,EAAO,CAAE,EAAE,KAAK,aAAa,EAAI,EACtD,EAMD,aAAc,UAAwB,CACpC,OAAO,KAAK,MAAQA,EAAO,CAAE,EAAE,KAAK,SAAS,EAAI,EAClD,EAMD,cAAe,UAAyB,CACtC,IAAIgL,EAAa,KAAK,WAClBpS,EAAO,CAAA,EAEX,OAAI,KAAK,OACPkH,GAAQ,CAAC,OAAQ,MAAO,QAAS,SAAU,eAAgB,eAAe,EAAG,SAAUpE,EAAG,CACxF9C,EAAK8C,CAAC,EAAIsP,EAAWtP,CAAC,CAC9B,CAAO,EAGI9C,CACR,EAOD,cAAe,SAAuBA,EAAM,CAC1C,IAAIoS,EAAa,KAAK,WAClBvF,EAAcuF,EAAW,YAE7B,OAAI,KAAK,OAAS,CAAC,KAAK,UAAYxL,GAAc5G,CAAI,IAChDuG,EAASvG,EAAK,IAAI,IACpBoS,EAAW,KAAOpS,EAAK,MAGrBuG,EAASvG,EAAK,GAAG,IACnBoS,EAAW,IAAMpS,EAAK,KAGpBuG,EAASvG,EAAK,KAAK,GACrBoS,EAAW,MAAQpS,EAAK,MACxBoS,EAAW,OAASpS,EAAK,MAAQ6M,GACxBtG,EAASvG,EAAK,MAAM,IAC7BoS,EAAW,OAASpS,EAAK,OACzBoS,EAAW,MAAQpS,EAAK,OAAS6M,GAGnC,KAAK,aAAa,EAAI,GAGjB,IACR,EAMD,eAAgB,UAA0B,CACxC,IAAI0F,EAAc,KAAK,YACnBvS,EAEJ,OAAI,KAAK,OAAS,KAAK,UACrBA,EAAO,CACL,KAAMuS,EAAY,KAClB,IAAKA,EAAY,IACjB,MAAOA,EAAY,MACnB,OAAQA,EAAY,MAC5B,GAGWvS,GAAQ,CAAA,CAChB,EAOD,eAAgB,SAAwBA,EAAM,CAC5C,IAAIuS,EAAc,KAAK,YACnB1F,EAAc,KAAK,QAAQ,YAC3BsJ,EACAC,EAEJ,OAAI,KAAK,OAAS,KAAK,SAAW,CAAC,KAAK,UAAYxP,GAAc5G,CAAI,IAChEuG,EAASvG,EAAK,IAAI,IACpBuS,EAAY,KAAOvS,EAAK,MAGtBuG,EAASvG,EAAK,GAAG,IACnBuS,EAAY,IAAMvS,EAAK,KAGrBuG,EAASvG,EAAK,KAAK,GAAKA,EAAK,QAAUuS,EAAY,QACrD4D,EAAe,GACf5D,EAAY,MAAQvS,EAAK,OAGvBuG,EAASvG,EAAK,MAAM,GAAKA,EAAK,SAAWuS,EAAY,SACvD6D,EAAgB,GAChB7D,EAAY,OAASvS,EAAK,QAGxB6M,IACEsJ,EACF5D,EAAY,OAASA,EAAY,MAAQ1F,EAChCuJ,IACT7D,EAAY,MAAQA,EAAY,OAAS1F,IAI7C,KAAK,cAAa,GAGb,IACR,EAOD,iBAAkB,UAA4B,CAC5C,IAAIzD,EAAU,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAA,EAElF,GAAI,CAAC,KAAK,OAAS,CAAC,OAAO,kBACzB,OAAO,KAGT,IAAIgJ,EAAa,KAAK,WAClB/Q,EAASsM,GAAgB,KAAK,MAAO,KAAK,UAAWyE,EAAYhJ,CAAO,EAE5E,GAAI,CAAC,KAAK,QACR,OAAO/H,EAGT,IAAIgV,EAAgB,KAAK,QAAS,EAC9BC,EAAWD,EAAc,EACzBE,EAAWF,EAAc,EACzBG,EAAeH,EAAc,MAC7BI,EAAgBJ,EAAc,OAE9BtK,EAAQ1K,EAAO,MAAQ,KAAK,MAAM+Q,EAAW,YAAY,EAEzDrG,IAAU,IACZuK,GAAYvK,EACZwK,GAAYxK,EACZyK,GAAgBzK,EAChB0K,GAAiB1K,GAGnB,IAAIc,EAAc2J,EAAeC,EAC7BnH,EAAW3C,GAAiB,CAC9B,YAAaE,EACb,MAAOzD,EAAQ,UAAY,IAC3B,OAAQA,EAAQ,WAAa,GACnC,CAAK,EACGmG,EAAW5C,GAAiB,CAC9B,YAAaE,EACb,MAAOzD,EAAQ,UAAY,EAC3B,OAAQA,EAAQ,WAAa,CAC9B,EAAE,OAAO,EAENuJ,EAAoBhG,GAAiB,CACvC,YAAaE,EACb,MAAOzD,EAAQ,QAAU2C,IAAU,EAAI1K,EAAO,MAAQmV,GACtD,OAAQpN,EAAQ,SAAW2C,IAAU,EAAI1K,EAAO,OAASoV,EAC/D,CAAK,EACG1J,EAAQ4F,EAAkB,MAC1B7F,EAAS6F,EAAkB,OAE/B5F,EAAQ,KAAK,IAAIuC,EAAS,MAAO,KAAK,IAAIC,EAAS,MAAOxC,CAAK,CAAC,EAChED,EAAS,KAAK,IAAIwC,EAAS,OAAQ,KAAK,IAAIC,EAAS,OAAQzC,CAAM,CAAC,EACpE,IAAI9N,EAAS,SAAS,cAAc,QAAQ,EACxCe,EAAUf,EAAO,WAAW,IAAI,EACpCA,EAAO,MAAQ0I,GAAuBqF,CAAK,EAC3C/N,EAAO,OAAS0I,GAAuBoF,CAAM,EAC7C/M,EAAQ,UAAYqJ,EAAQ,WAAa,cACzCrJ,EAAQ,SAAS,EAAG,EAAGgN,EAAOD,CAAM,EACpC,IAAI4J,EAAwBtN,EAAQ,sBAChCuF,EAAwB+H,IAA0B,OAAS,GAAOA,EAClE7H,GAAwBzF,EAAQ,sBACpCrJ,EAAQ,sBAAwB4O,EAE5BE,KACF9O,EAAQ,sBAAwB8O,IAIlC,IAAI8H,EAActV,EAAO,MACrBuV,EAAevV,EAAO,OAEtBwV,EAAOP,EACPQ,EAAOP,EACPQ,EACAC,GAEAC,GACAC,GACAC,EACAC,EAEAP,GAAQ,CAACL,GAAgBK,EAAOF,GAClCE,EAAO,EACPE,EAAW,EACXE,GAAO,EACPE,EAAW,GACFN,GAAQ,GACjBI,GAAO,CAACJ,EACRA,EAAO,EACPE,EAAW,KAAK,IAAIJ,EAAaH,EAAeK,CAAI,EACpDM,EAAWJ,GACFF,GAAQF,IACjBM,GAAO,EACPF,EAAW,KAAK,IAAIP,EAAcG,EAAcE,CAAI,EACpDM,EAAWJ,GAGTA,GAAY,GAAKD,GAAQ,CAACL,GAAiBK,EAAOF,GACpDE,EAAO,EACPE,GAAY,EACZE,GAAO,EACPE,EAAY,GACHN,GAAQ,GACjBI,GAAO,CAACJ,EACRA,EAAO,EACPE,GAAY,KAAK,IAAIJ,EAAcH,EAAgBK,CAAI,EACvDM,EAAYJ,IACHF,GAAQF,IACjBM,GAAO,EACPF,GAAY,KAAK,IAAIP,EAAeG,EAAeE,CAAI,EACvDM,EAAYJ,IAGd,IAAIpH,EAAS,CAACiH,EAAMC,EAAMC,EAAUC,EAAS,EAE7C,GAAIG,EAAW,GAAKC,EAAY,EAAG,CACjC,IAAIC,EAAQtK,EAAQyJ,EACpB5G,EAAO,KAAKqH,GAAOI,EAAOH,GAAOG,EAAOF,EAAWE,EAAOD,EAAYC,CAAK,EAK7E,OAAAtX,EAAQ,UAAU,MAAMA,EAAS,CAACsB,CAAM,EAAE,OAAOe,GAAmBwN,EAAO,IAAI,SAAUC,GAAO,CAC9F,OAAO,KAAK,MAAMnI,GAAuBmI,EAAK,CAAC,CACrD,CAAK,CAAC,CAAC,CAAC,EACG7Q,CACR,EAOD,eAAgB,SAAwB6N,EAAa,CACnD,IAAIzD,EAAU,KAAK,QAEnB,MAAI,CAAC,KAAK,UAAY,CAAC3C,GAAYoG,CAAW,IAE5CzD,EAAQ,YAAc,KAAK,IAAI,EAAGyD,CAAW,GAAK,IAE9C,KAAK,QACP,KAAK,YAAW,EAEZ,KAAK,SACP,KAAK,cAAa,IAKjB,IACR,EAOD,YAAa,SAAqBzN,EAAM,CACtC,IAAIgK,EAAU,KAAK,QACfkO,EAAU,KAAK,QACfC,EAAO,KAAK,KAEhB,GAAI,KAAK,OAAS,CAAC,KAAK,SAAU,CAChC,IAAIC,EAAYpY,IAASuF,GACrB8S,EAAUrO,EAAQ,SAAWhK,IAASwF,GAC1CxF,EAAOoY,GAAaC,EAAUrY,EAAOyF,GACrCuE,EAAQ,SAAWhK,EACnByJ,GAAQyO,EAAS7S,GAAarF,CAAI,EAClCmJ,GAAY+O,EAASpT,GAAYsT,CAAS,EAC1CjP,GAAY+O,EAAS9S,GAAYiT,CAAO,EAEnCrO,EAAQ,iBAEXP,GAAQ0O,EAAM9S,GAAarF,CAAI,EAC/BmJ,GAAYgP,EAAMrT,GAAYsT,CAAS,EACvCjP,GAAYgP,EAAM/S,GAAYiT,CAAO,GAIzC,OAAO,IACR,CACH,EAEIC,GAAiBxU,GAAO,QAExByU,GAAuB,UAAY,CAMrC,SAASA,EAAQ7P,EAAS,CACxB,IAAIsB,EAAU,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAA,EAIlF,GAFA1H,GAAgB,KAAMiW,CAAO,EAEzB,CAAC7P,GAAW,CAAC7B,GAAgB,KAAK6B,EAAQ,OAAO,EACnD,MAAM,IAAI,MAAM,0EAA0E,EAG5F,KAAK,QAAUA,EACf,KAAK,QAAUV,EAAO,CAAE,EAAEhB,GAAUQ,GAAcwC,CAAO,GAAKA,CAAO,EACrE,KAAK,QAAU,GACf,KAAK,SAAW,GAChB,KAAK,SAAW,GAChB,KAAK,MAAQ,GACb,KAAK,UAAY,GACjB,KAAK,SAAW,GAChB,KAAK,MAAQ,GACb,KAAK,OAAS,GACd,KAAK,KAAI,CACV,CAED,OAAApH,GAAa2V,EAAS,CAAC,CACrB,IAAK,OACL,MAAO,UAAgB,CACrB,IAAI7P,EAAU,KAAK,QACf8P,EAAU9P,EAAQ,QAAQ,YAAW,EACrCuC,EAEJ,GAAI,CAAAvC,EAAQzE,CAAS,EAMrB,IAFAyE,EAAQzE,CAAS,EAAI,KAEjBuU,IAAY,MAAO,CAMrB,GALA,KAAK,MAAQ,GAEbvN,EAAMvC,EAAQ,aAAa,KAAK,GAAK,GACrC,KAAK,YAAcuC,EAEf,CAACA,EACH,OAIFA,EAAMvC,EAAQ,SACL8P,IAAY,UAAY,OAAO,oBACxCvN,EAAMvC,EAAQ,aAGhB,KAAK,KAAKuC,CAAG,EACd,CACL,EAAK,CACD,IAAK,OACL,MAAO,SAAcA,EAAK,CACxB,IAAIkK,EAAQ,KAEZ,GAAKlK,EAIL,MAAK,IAAMA,EACX,KAAK,UAAY,GACjB,IAAIvC,EAAU,KAAK,QACfsB,EAAU,KAAK,QAOnB,GALI,CAACA,EAAQ,WAAa,CAACA,EAAQ,WACjCA,EAAQ,iBAAmB,IAIzB,CAACA,EAAQ,kBAAoB,CAAC,OAAO,YAAa,CACpD,KAAK,MAAK,EACV,OAIF,GAAIrD,GAAgB,KAAKsE,CAAG,EAAG,CAEzBrE,GAAqB,KAAKqE,CAAG,EAC/B,KAAK,KAAKgG,GAAqBhG,CAAG,CAAC,EAInC,KAAK,MAAK,EAGZ,OAKF,IAAIwN,EAAM,IAAI,eACVC,EAAQ,KAAK,MAAM,KAAK,IAAI,EAChC,KAAK,UAAY,GACjB,KAAK,IAAMD,EAKXA,EAAI,QAAUC,EACdD,EAAI,QAAUC,EACdD,EAAI,UAAYC,EAEhBD,EAAI,WAAa,UAAY,CAEvBA,EAAI,kBAAkB,cAAc,IAAMhS,IAC5CgS,EAAI,MAAK,CAEnB,EAEMA,EAAI,OAAS,UAAY,CACvBtD,EAAM,KAAKsD,EAAI,QAAQ,CAC/B,EAEMA,EAAI,UAAY,UAAY,CAC1BtD,EAAM,UAAY,GAClBA,EAAM,IAAM,IACpB,EAGUnL,EAAQ,kBAAoBgB,GAAiBC,CAAG,GAAKvC,EAAQ,cAC/DuC,EAAME,GAAaF,CAAG,GAIxBwN,EAAI,KAAK,MAAOxN,EAAK,EAAI,EACzBwN,EAAI,aAAe,cACnBA,EAAI,gBAAkB/P,EAAQ,cAAgB,kBAC9C+P,EAAI,KAAI,EACT,CACL,EAAK,CACD,IAAK,OACL,MAAO,SAAcpH,EAAa,CAChC,IAAIrH,EAAU,KAAK,QACf2I,EAAY,KAAK,UAGjBhB,EAAcD,GAAuBL,CAAW,EAChD9F,EAAS,EACTC,EAAS,EACTC,EAAS,EAEb,GAAIkG,EAAc,EAAG,CAEnB,KAAK,IAAMJ,GAAqBF,EAAa5K,EAAc,EAE3D,IAAIkS,EAAoBrG,GAAiBX,CAAW,EAEpDpG,EAASoN,EAAkB,OAC3BnN,EAASmN,EAAkB,OAC3BlN,EAASkN,EAAkB,OAGzB3O,EAAQ,YACV2I,EAAU,OAASpH,GAGjBvB,EAAQ,WACV2I,EAAU,OAASnH,EACnBmH,EAAU,OAASlH,GAGrB,KAAK,MAAK,CACX,CACL,EAAK,CACD,IAAK,QACL,MAAO,UAAiB,CACtB,IAAI/C,EAAU,KAAK,QACfuC,EAAM,KAAK,IACXmJ,EAAc1L,EAAQ,YACtBkQ,EAAiB3N,EAEjB,KAAK,QAAQ,kBAAoBD,GAAiBC,CAAG,IAClDmJ,IACHA,EAAc,aAIhBwE,EAAiBzN,GAAaF,CAAG,GAGnC,KAAK,YAAcmJ,EACnB,KAAK,eAAiBwE,EACtB,IAAIpK,EAAQ,SAAS,cAAc,KAAK,EAEpC4F,IACF5F,EAAM,YAAc4F,GAGtB5F,EAAM,IAAMoK,GAAkB3N,EAC9BuD,EAAM,IAAM9F,EAAQ,KAAO,oBAC3B,KAAK,MAAQ8F,EACbA,EAAM,OAAS,KAAK,MAAM,KAAK,IAAI,EACnCA,EAAM,QAAU,KAAK,KAAK,KAAK,IAAI,EACnCzF,GAASyF,EAAOvJ,EAAU,EAC1ByD,EAAQ,WAAW,aAAa8F,EAAO9F,EAAQ,WAAW,CAC3D,CACL,EAAK,CACD,IAAK,QACL,MAAO,UAAiB,CACtB,IAAImQ,EAAS,KAETrK,EAAQ,KAAK,MACjBA,EAAM,OAAS,KACfA,EAAM,QAAU,KAChB,KAAK,OAAS,GAGd,IAAIsK,EAAchV,GAAO,WAAa,sCAAsC,KAAKA,GAAO,UAAU,SAAS,EAEvGiV,EAAO,SAAc7J,EAAcC,EAAe,CACpDnH,EAAO6Q,EAAO,UAAW,CACvB,aAAc3J,EACd,cAAeC,EACf,YAAaD,EAAeC,CACtC,CAAS,EACD0J,EAAO,iBAAmB7Q,EAAO,CAAE,EAAE6Q,EAAO,SAAS,EACrDA,EAAO,OAAS,GAChBA,EAAO,MAAQ,GAEfA,EAAO,MAAK,CACpB,EAGM,GAAIrK,EAAM,cAAgB,CAACsK,EAAa,CACtCC,EAAKvK,EAAM,aAAcA,EAAM,aAAa,EAC5C,OAGF,IAAIwK,EAAc,SAAS,cAAc,KAAK,EAC1CC,EAAO,SAAS,MAAQ,SAAS,gBACrC,KAAK,YAAcD,EAEnBA,EAAY,OAAS,UAAY,CAC/BD,EAAKC,EAAY,MAAOA,EAAY,MAAM,EAErCF,GACHG,EAAK,YAAYD,CAAW,CAEtC,EAEMA,EAAY,IAAMxK,EAAM,IAGnBsK,IACHE,EAAY,MAAM,QAAU,uJAC5BC,EAAK,YAAYD,CAAW,EAE/B,CACL,EAAK,CACD,IAAK,OACL,MAAO,UAAgB,CACrB,IAAIxK,EAAQ,KAAK,MACjBA,EAAM,OAAS,KACfA,EAAM,QAAU,KAChBA,EAAM,WAAW,YAAYA,CAAK,EAClC,KAAK,MAAQ,IACd,CACL,EAAK,CACD,IAAK,QACL,MAAO,UAAiB,CACtB,GAAI,GAAC,KAAK,OAAS,KAAK,OAIxB,KAAI9F,EAAU,KAAK,QACfsB,EAAU,KAAK,QACfwE,EAAQ,KAAK,MAEbgE,EAAY9J,EAAQ,WACpBwQ,EAAW,SAAS,cAAc,KAAK,EAC3CA,EAAS,UAAYjS,GACrB,IAAIwL,EAAUyG,EAAS,cAAc,IAAI,OAAOjV,EAAW,YAAY,CAAC,EACpErE,EAAS6S,EAAQ,cAAc,IAAI,OAAOxO,EAAW,SAAS,CAAC,EAC/DiU,EAAUzF,EAAQ,cAAc,IAAI,OAAOxO,EAAW,WAAW,CAAC,EAClEkV,EAAU1G,EAAQ,cAAc,IAAI,OAAOxO,EAAW,WAAW,CAAC,EAClEkU,EAAOgB,EAAQ,cAAc,IAAI,OAAOlV,EAAW,OAAO,CAAC,EAC/D,KAAK,UAAYuO,EACjB,KAAK,QAAUC,EACf,KAAK,OAAS7S,EACd,KAAK,QAAUsY,EACf,KAAK,QAAUiB,EACf,KAAK,QAAU1G,EAAQ,cAAc,IAAI,OAAOxO,EAAW,WAAW,CAAC,EACvE,KAAK,KAAOkU,EACZvY,EAAO,YAAY4O,CAAK,EAExBzF,GAASL,EAAS1D,EAAY,EAE9BwN,EAAU,aAAaC,EAAS/J,EAAQ,WAAW,EAE9C,KAAK,OACRQ,GAAYsF,EAAOvJ,EAAU,EAG/B,KAAK,YAAW,EAChB,KAAK,KAAI,EACT+E,EAAQ,mBAAqB,KAAK,IAAI,EAAGA,EAAQ,kBAAkB,GAAK,IACxEA,EAAQ,YAAc,KAAK,IAAI,EAAGA,EAAQ,WAAW,GAAK,IAC1DA,EAAQ,SAAW,KAAK,IAAI,EAAG,KAAK,IAAI,EAAG,KAAK,MAAMA,EAAQ,QAAQ,CAAC,CAAC,GAAK,EAC7EjB,GAASoQ,EAASnU,EAAY,EAEzBgF,EAAQ,QACXjB,GAASoQ,EAAQ,uBAAuB,GAAG,OAAOlV,EAAW,SAAS,CAAC,EAAGe,EAAY,EAGnFgF,EAAQ,QACXjB,GAASoQ,EAAQ,uBAAuB,GAAG,OAAOlV,EAAW,SAAS,CAAC,EAAGe,EAAY,EAGpFgF,EAAQ,YACVjB,GAAS0J,EAAS,GAAG,OAAOxO,EAAW,KAAK,CAAC,EAG1C+F,EAAQ,WACXjB,GAASoP,EAAMjT,EAAe,EAG5B8E,EAAQ,iBACVjB,GAASoP,EAAM/S,EAAU,EACzBqE,GAAQ0O,EAAM9S,GAAanB,EAAU,GAGlC8F,EAAQ,mBACXjB,GAASoQ,EAAQ,uBAAuB,GAAG,OAAOlV,EAAW,OAAO,CAAC,EAAGe,EAAY,EACpF+D,GAASoQ,EAAQ,uBAAuB,GAAG,OAAOlV,EAAW,QAAQ,CAAC,EAAGe,EAAY,GAGvF,KAAK,OAAM,EACX,KAAK,MAAQ,GACb,KAAK,YAAYgF,EAAQ,QAAQ,EAE7BA,EAAQ,UACV,KAAK,KAAI,EAGX,KAAK,QAAQA,EAAQ,IAAI,EAErBrC,GAAWqC,EAAQ,KAAK,GAC1BM,GAAY5B,EAASrC,GAAa2D,EAAQ,MAAO,CAC/C,KAAM,EAChB,CAAS,EAGHW,GAAcjC,EAASrC,EAAW,EACnC,CACL,EAAK,CACD,IAAK,UACL,MAAO,UAAmB,CACnB,KAAK,QAIV,KAAK,MAAQ,GACb,KAAK,OAAM,EACX,KAAK,aAAY,EACjB,KAAK,QAAQ,WAAW,YAAY,KAAK,OAAO,EAChD6C,GAAY,KAAK,QAASlE,EAAY,EACvC,CACL,EAAK,CACD,IAAK,WACL,MAAO,UAAoB,CACrB,KAAK,OACP,KAAK,QAAO,EACZ,KAAK,MAAQ,GACb,KAAK,QAAU,IACN,KAAK,QACd,KAAK,YAAY,OAAS,KAC1B,KAAK,OAAS,GACd,KAAK,MAAQ,IACJ,KAAK,WACd,KAAK,IAAI,QAAU,KACnB,KAAK,IAAI,SACA,KAAK,OACd,KAAK,KAAI,CAEZ,CAMF,CAAA,EAAG,CAAC,CACH,IAAK,aACL,MAAO,UAAsB,CAC3B,cAAO,QAAUsT,GACVC,CACR,CAML,EAAK,CACD,IAAK,cACL,MAAO,SAAqBvO,EAAS,CACnChC,EAAOhB,GAAUQ,GAAcwC,CAAO,GAAKA,CAAO,CACnD,CACF,CAAA,CAAC,EAEKuO,CACT,IAEAvQ,EAAOuQ,GAAQ,UAAWhG,GAAQ4B,GAASY,GAAQC,GAAUQ,GAAQU,EAAO,0DCpgHlE7W,EAAK,CAAA,CAAA,GAAA+Z,EAAA5E,EAAA,MAAA6E,CAAA,wBAAf/a,EAAwCC,EAAAiW,EAAA/V,CAAA,qCAA9BY,EAAK,CAAA,CAAA,0EA1BH,MAAAmP,CAAa,EAAA1O,EACpByU,EAEE,MAAApU,EAAWC,SACbqS,WAEY6G,GAAO,CACtB7G,EAAQ,QAAO,WAGA8G,GAAM,CACjB9G,GACH6G,IAED7G,EAAO,IAAO8F,GAAQhE,GACrB,aAAc,EACd,SAAO,CACA,MAAAiF,EAAa/G,EAAQ,mBAAmB,UAAS,EACvDtS,EAAS,OAAQqZ,CAAU,KAI7BrZ,EAAS,OAAQqO,CAAK,2CAII+F,EAAE/S,oTChC9B,MAAMiY,EAAM,CAMV,YAAYlD,EAAGC,EAAG,CAChB,KAAK,EAAID,EACT,KAAK,EAAIC,CACV,CACH,CCRA,MAAMkD,WAAkBD,EAAM,CAM5B,OAAQE,EAAO,CACb,KAAK,EAAIA,EAAM,EACf,KAAK,EAAIA,EAAM,CAChB,CAQD,YAAaC,EAAOC,EAAU,CAE5B,MAAMC,EAAeF,EAAS,KAAK,GAAK,EAExC,KAAK,EAAI,KAAK,EAAK,KAAK,IAAIE,CAAY,EAAID,EAC5C,KAAK,EAAI,KAAK,EAAK,KAAK,IAAIC,CAAY,EAAID,CAC7C,CAQD,SAAUF,EAAO,CACf,OAAO,KAAK,IAAMA,EAAM,GAAK,KAAK,IAAMA,EAAM,CAC/C,CAQD,gBAAiBA,EAAO,CACtB,OAAO,IAAIF,GAAM,KAAK,EAAIE,EAAM,EAAG,KAAK,EAAIA,EAAM,CAAC,CACpD,CAQD,cAAeA,EAAO,CACpB,MAAMI,EAAO,KAAK,gBAAgBJ,CAAK,EAEvC,OAAO,KAAK,KAAK,KAAK,IAAII,EAAK,EAAG,CAAC,EAAI,KAAK,IAAIA,EAAK,EAAG,CAAC,CAAC,CAC3D,CAQD,WAAYJ,EAAO,CACjB,MAAMI,EAAO,KAAK,gBAAgBJ,CAAK,EAEvC,OAAO,KAAK,MAAMI,EAAK,EAAGA,EAAK,CAAC,CACjC,CAOD,UAAY,CACV,MAAO,CACL,EAAG,KAAK,EACR,EAAG,KAAK,CACT,CACF,CACH,CCjFA,MAAMC,GAAiB,GAEvB,MAAMC,EAAU,CAQd,YAAa,CAAE,OAAAC,EAASF,GAAgB,QAAAG,EAAU,GAAM,aAAAC,EAAe,CAAE,EAAG,EAAG,EAAG,CAAG,CAAA,EAAI,CAAA,EAAI,CAC3F,KAAK,OAASF,EACd,KAAK,WAAaC,EAElB,KAAK,QAAU,IAAIT,GAAUU,EAAa,EAAGA,EAAa,CAAC,EAC3D,KAAK,MAAQ,IAAIV,GAAUU,EAAa,EAAGA,EAAa,CAAC,EAEzD,KAAK,MAAQ,EACb,KAAK,SAAW,EAChB,KAAK,UAAY,EAClB,CAMD,QAAU,CACR,KAAK,WAAa,EACnB,CAMD,SAAW,CACT,KAAK,WAAa,EACnB,CAKD,WAAa,CACX,OAAO,KAAK,UACb,CAOD,UAAWF,EAAQ,CACjB,KAAK,OAASA,CACf,CAOD,WAAa,CACX,OAAO,KAAK,MACb,CAOD,qBAAuB,CACrB,OAAO,KAAK,MAAM,SAAU,CAC7B,CAOD,uBAAyB,CACvB,OAAO,KAAK,QAAQ,SAAU,CAC/B,CAOD,UAAY,CACV,OAAO,KAAK,KACb,CAOD,YAAc,CACZ,OAAO,KAAK,OACb,CAOD,UAAY,CACV,OAAO,KAAK,KACb,CAOD,aAAe,CACb,OAAO,KAAK,QACb,CAOD,eAAiB,CACf,OAAO,KAAK,SACb,CAUD,OAAQG,EAAiB,CAAE,KAAAC,EAAO,EAAK,EAAK,CAAA,EAAI,CAE9C,OADA,KAAK,UAAY,GACb,KAAK,QAAQ,SAASD,CAAe,GAAK,CAACC,EACtC,IAGT,KAAK,QAAQ,OAAOD,CAAe,EAE/BC,GACF,KAAK,UAAY,GACjB,KAAK,MAAM,OAAOD,CAAe,EAC1B,KAGL,KAAK,YACP,KAAK,SAAW,KAAK,QAAQ,cAAc,KAAK,KAAK,EACrD,KAAK,MAAQ,KAAK,QAAQ,WAAW,KAAK,KAAK,EAE3C,KAAK,SAAW,KAAK,SACvB,KAAK,MAAM,YAAY,KAAK,MAAO,KAAK,SAAW,KAAK,MAAM,EAC9D,KAAK,UAAY,MAGnB,KAAK,SAAW,EAChB,KAAK,MAAQ,EACb,KAAK,MAAM,OAAOA,CAAe,EACjC,KAAK,UAAY,IAGZ,IACR,CACH,CC/JA,IAAIE,GAAW,UAAY,CACvB,GAAI,OAAO,IAAQ,IACf,OAAO,IASX,SAASC,EAASvX,EAAKf,EAAK,CACxB,IAAIuY,EAAS,GACb,OAAAxX,EAAI,KAAK,SAAUyX,EAAOC,EAAO,CAC7B,OAAID,EAAM,CAAC,IAAMxY,GACbuY,EAASE,EACF,IAEJ,EACnB,CAAS,EACMF,CACV,CACD,OAAsB,UAAY,CAC9B,SAASG,GAAU,CACf,KAAK,YAAc,EACtB,CACD,cAAO,eAAeA,EAAQ,UAAW,OAAQ,CAI7C,IAAK,UAAY,CACb,OAAO,KAAK,YAAY,MAC3B,EACD,WAAY,GACZ,aAAc,EAC1B,CAAS,EAKDA,EAAQ,UAAU,IAAM,SAAU1Y,EAAK,CACnC,IAAIyY,EAAQH,EAAS,KAAK,YAAatY,CAAG,EACtCwY,EAAQ,KAAK,YAAYC,CAAK,EAClC,OAAOD,GAASA,EAAM,CAAC,CACnC,EAMQE,EAAQ,UAAU,IAAM,SAAU1Y,EAAKa,EAAO,CAC1C,IAAI4X,EAAQH,EAAS,KAAK,YAAatY,CAAG,EACtC,CAACyY,EACD,KAAK,YAAYA,CAAK,EAAE,CAAC,EAAI5X,EAG7B,KAAK,YAAY,KAAK,CAACb,EAAKa,CAAK,CAAC,CAElD,EAKQ6X,EAAQ,UAAU,OAAS,SAAU1Y,EAAK,CACtC,IAAI2Y,EAAU,KAAK,YACfF,EAAQH,EAASK,EAAS3Y,CAAG,EAC7B,CAACyY,GACDE,EAAQ,OAAOF,EAAO,CAAC,CAEvC,EAKQC,EAAQ,UAAU,IAAM,SAAU1Y,EAAK,CACnC,MAAO,CAAC,CAAC,CAACsY,EAAS,KAAK,YAAatY,CAAG,CACpD,EAIQ0Y,EAAQ,UAAU,MAAQ,UAAY,CAClC,KAAK,YAAY,OAAO,CAAC,CACrC,EAMQA,EAAQ,UAAU,QAAU,SAAU7S,EAAU1I,EAAK,CAC7CA,IAAQ,SAAUA,EAAM,MAC5B,QAASyb,EAAK,EAAGC,EAAK,KAAK,YAAaD,EAAKC,EAAG,OAAQD,IAAM,CAC1D,IAAIJ,EAAQK,EAAGD,CAAE,EACjB/S,EAAS,KAAK1I,EAAKqb,EAAM,CAAC,EAAGA,EAAM,CAAC,CAAC,EAErD,EACeE,CACf,GACA,IAKII,GAAY,OAAO,OAAW,KAAe,OAAO,SAAa,KAAe,OAAO,WAAa,SAGpGC,GAAY,UAAY,CACxB,OAAI,OAAO,OAAW,KAAe,OAAO,OAAS,KAC1C,OAEP,OAAO,KAAS,KAAe,KAAK,OAAS,KACtC,KAEP,OAAO,OAAW,KAAe,OAAO,OAAS,KAC1C,OAGJ,SAAS,aAAa,GACjC,IAQIC,GAA2B,UAAY,CACvC,OAAI,OAAO,uBAA0B,WAI1B,sBAAsB,KAAKD,EAAQ,EAEvC,SAAUlT,EAAU,CAAE,OAAO,WAAW,UAAY,CAAE,OAAOA,EAAS,KAAK,IAAG,CAAE,CAAI,EAAE,IAAO,EAAE,CAAE,CAC5G,IAGIoT,GAAkB,EAStB,SAASC,GAAUrT,EAAUsT,EAAO,CAChC,IAAIC,EAAc,GAAOC,EAAe,GAAOC,EAAe,EAO9D,SAASC,GAAiB,CAClBH,IACAA,EAAc,GACdvT,KAEAwT,GACAG,GAEP,CAQD,SAASC,GAAkB,CACvBT,GAAwBO,CAAc,CACzC,CAMD,SAASC,GAAQ,CACb,IAAIE,EAAY,KAAK,MACrB,GAAIN,EAAa,CAEb,GAAIM,EAAYJ,EAAeL,GAC3B,OAMJI,EAAe,QAGfD,EAAc,GACdC,EAAe,GACf,WAAWI,EAAiBN,CAAK,EAErCG,EAAeI,CAClB,CACD,OAAOF,CACX,CAGA,IAAIG,GAAgB,GAGhBC,GAAiB,CAAC,MAAO,QAAS,SAAU,OAAQ,QAAS,SAAU,OAAQ,QAAQ,EAEvFC,GAA4B,OAAO,iBAAqB,IAIxDC,GAA0C,UAAY,CAMtD,SAASA,GAA2B,CAMhC,KAAK,WAAa,GAMlB,KAAK,qBAAuB,GAM5B,KAAK,mBAAqB,KAM1B,KAAK,WAAa,GAClB,KAAK,iBAAmB,KAAK,iBAAiB,KAAK,IAAI,EACvD,KAAK,QAAUZ,GAAS,KAAK,QAAQ,KAAK,IAAI,EAAGS,EAAa,CACjE,CAOD,OAAAG,EAAyB,UAAU,YAAc,SAAUC,EAAU,CAC5D,CAAC,KAAK,WAAW,QAAQA,CAAQ,GAClC,KAAK,WAAW,KAAKA,CAAQ,EAG5B,KAAK,YACN,KAAK,SAAQ,CAEzB,EAOID,EAAyB,UAAU,eAAiB,SAAUC,EAAU,CACpE,IAAIC,EAAY,KAAK,WACjBvB,EAAQuB,EAAU,QAAQD,CAAQ,EAElC,CAACtB,GACDuB,EAAU,OAAOvB,EAAO,CAAC,EAGzB,CAACuB,EAAU,QAAU,KAAK,YAC1B,KAAK,YAAW,CAE5B,EAOIF,EAAyB,UAAU,QAAU,UAAY,CACrD,IAAIG,EAAkB,KAAK,mBAGvBA,GACA,KAAK,QAAO,CAExB,EASIH,EAAyB,UAAU,iBAAmB,UAAY,CAE9D,IAAII,EAAkB,KAAK,WAAW,OAAO,SAAUH,EAAU,CAC7D,OAAOA,EAAS,aAAY,EAAIA,EAAS,UAAS,CAC9D,CAAS,EAMD,OAAAG,EAAgB,QAAQ,SAAUH,EAAU,CAAE,OAAOA,EAAS,gBAAe,CAAG,CAAE,EAC3EG,EAAgB,OAAS,CACxC,EAOIJ,EAAyB,UAAU,SAAW,UAAY,CAGlD,CAAChB,IAAa,KAAK,aAMvB,SAAS,iBAAiB,gBAAiB,KAAK,gBAAgB,EAChE,OAAO,iBAAiB,SAAU,KAAK,OAAO,EAC1Ce,IACA,KAAK,mBAAqB,IAAI,iBAAiB,KAAK,OAAO,EAC3D,KAAK,mBAAmB,QAAQ,SAAU,CACtC,WAAY,GACZ,UAAW,GACX,cAAe,GACf,QAAS,EACzB,CAAa,IAGD,SAAS,iBAAiB,qBAAsB,KAAK,OAAO,EAC5D,KAAK,qBAAuB,IAEhC,KAAK,WAAa,GAC1B,EAOIC,EAAyB,UAAU,YAAc,UAAY,CAGrD,CAAChB,IAAa,CAAC,KAAK,aAGxB,SAAS,oBAAoB,gBAAiB,KAAK,gBAAgB,EACnE,OAAO,oBAAoB,SAAU,KAAK,OAAO,EAC7C,KAAK,oBACL,KAAK,mBAAmB,aAExB,KAAK,sBACL,SAAS,oBAAoB,qBAAsB,KAAK,OAAO,EAEnE,KAAK,mBAAqB,KAC1B,KAAK,qBAAuB,GAC5B,KAAK,WAAa,GAC1B,EAQIgB,EAAyB,UAAU,iBAAmB,SAAUjB,EAAI,CAChE,IAAIsB,EAAKtB,EAAG,aAAcuB,EAAeD,IAAO,OAAS,GAAKA,EAE1DE,EAAmBT,GAAe,KAAK,SAAU5Z,EAAK,CACtD,MAAO,CAAC,CAAC,CAACoa,EAAa,QAAQpa,CAAG,CAC9C,CAAS,EACGqa,GACA,KAAK,QAAO,CAExB,EAMIP,EAAyB,YAAc,UAAY,CAC/C,OAAK,KAAK,YACN,KAAK,UAAY,IAAIA,GAElB,KAAK,SACpB,EAMIA,EAAyB,UAAY,KAC9BA,CACX,EAAC,EASGQ,GAAsB,SAAUje,EAAQmE,EAAO,CAC/C,QAASoY,EAAK,EAAGC,EAAK,OAAO,KAAKrY,CAAK,EAAGoY,EAAKC,EAAG,OAAQD,IAAM,CAC5D,IAAI5Y,EAAM6Y,EAAGD,CAAE,EACf,OAAO,eAAevc,EAAQ2D,EAAK,CAC/B,MAAOQ,EAAMR,CAAG,EAChB,WAAY,GACZ,SAAU,GACV,aAAc,EAC1B,CAAS,EAEL,OAAO3D,CACX,EAQIke,GAAe,SAAUle,EAAQ,CAIjC,IAAIme,EAAcne,GAAUA,EAAO,eAAiBA,EAAO,cAAc,YAGzE,OAAOme,GAAezB,EAC1B,EAGI0B,GAAYC,GAAe,EAAG,EAAG,EAAG,CAAC,EAOzC,SAASC,GAAQ9Z,EAAO,CACpB,OAAO,WAAWA,CAAK,GAAK,CAChC,CAQA,SAAS+Z,GAAenU,EAAQ,CAE5B,QADIoU,EAAY,CAAA,EACPjC,EAAK,EAAGA,EAAK,UAAU,OAAQA,IACpCiC,EAAUjC,EAAK,CAAC,EAAI,UAAUA,CAAE,EAEpC,OAAOiC,EAAU,OAAO,SAAUC,EAAMC,EAAU,CAC9C,IAAIla,EAAQ4F,EAAO,UAAYsU,EAAW,QAAQ,EAClD,OAAOD,EAAOH,GAAQ9Z,CAAK,CAC9B,EAAE,CAAC,CACR,CAOA,SAASma,GAAYvU,EAAQ,CAGzB,QAFIoU,EAAY,CAAC,MAAO,QAAS,SAAU,MAAM,EAC7CI,EAAW,CAAA,EACNrC,EAAK,EAAGsC,EAAcL,EAAWjC,EAAKsC,EAAY,OAAQtC,IAAM,CACrE,IAAImC,EAAWG,EAAYtC,CAAE,EACzB/X,EAAQ4F,EAAO,WAAasU,CAAQ,EACxCE,EAASF,CAAQ,EAAIJ,GAAQ9Z,CAAK,EAEtC,OAAOoa,CACX,CAQA,SAASE,GAAkB9e,EAAQ,CAC/B,IAAI+e,EAAO/e,EAAO,UAClB,OAAOqe,GAAe,EAAG,EAAGU,EAAK,MAAOA,EAAK,MAAM,CACvD,CAOA,SAASC,GAA0Bhf,EAAQ,CAGvC,IAAIif,EAAcjf,EAAO,YAAakf,EAAelf,EAAO,aAS5D,GAAI,CAACif,GAAe,CAACC,EACjB,OAAOd,GAEX,IAAIhU,EAAS8T,GAAYle,CAAM,EAAE,iBAAiBA,CAAM,EACpD4e,EAAWD,GAAYvU,CAAM,EAC7B+U,EAAWP,EAAS,KAAOA,EAAS,MACpCQ,EAAUR,EAAS,IAAMA,EAAS,OAKlCxP,EAAQkP,GAAQlU,EAAO,KAAK,EAAG+E,EAASmP,GAAQlU,EAAO,MAAM,EAqBjE,GAlBIA,EAAO,YAAc,eAOjB,KAAK,MAAMgF,EAAQ+P,CAAQ,IAAMF,IACjC7P,GAASmP,GAAenU,EAAQ,OAAQ,OAAO,EAAI+U,GAEnD,KAAK,MAAMhQ,EAASiQ,CAAO,IAAMF,IACjC/P,GAAUoP,GAAenU,EAAQ,MAAO,QAAQ,EAAIgV,IAOxD,CAACC,GAAkBrf,CAAM,EAAG,CAK5B,IAAIsf,EAAgB,KAAK,MAAMlQ,EAAQ+P,CAAQ,EAAIF,EAC/CM,EAAiB,KAAK,MAAMpQ,EAASiQ,CAAO,EAAIF,EAMhD,KAAK,IAAII,CAAa,IAAM,IAC5BlQ,GAASkQ,GAET,KAAK,IAAIC,CAAc,IAAM,IAC7BpQ,GAAUoQ,GAGlB,OAAOlB,GAAeO,EAAS,KAAMA,EAAS,IAAKxP,EAAOD,CAAM,CACpE,CAOA,IAAIqQ,GAAwB,UAAY,CAGpC,OAAI,OAAO,mBAAuB,IACvB,SAAUxf,EAAQ,CAAE,OAAOA,aAAkBke,GAAYle,CAAM,EAAE,oBAKrE,SAAUA,EAAQ,CAAE,OAAQA,aAAkBke,GAAYle,CAAM,EAAE,YACrE,OAAOA,EAAO,SAAY,WAClC,IAOA,SAASqf,GAAkBrf,EAAQ,CAC/B,OAAOA,IAAWke,GAAYle,CAAM,EAAE,SAAS,eACnD,CAOA,SAASyf,GAAezf,EAAQ,CAC5B,OAAKyc,GAGD+C,GAAqBxf,CAAM,EACpB8e,GAAkB9e,CAAM,EAE5Bgf,GAA0Bhf,CAAM,EAL5Boe,EAMf,CAQA,SAASsB,GAAmBlD,EAAI,CAC5B,IAAIxE,EAAIwE,EAAG,EAAGvE,EAAIuE,EAAG,EAAGpN,EAAQoN,EAAG,MAAOrN,EAASqN,EAAG,OAElDmD,EAAS,OAAO,gBAAoB,IAAc,gBAAkB,OACpE9e,EAAO,OAAO,OAAO8e,EAAO,SAAS,EAEzC,OAAA1B,GAAmBpd,EAAM,CACrB,EAAGmX,EAAG,EAAGC,EAAG,MAAO7I,EAAO,OAAQD,EAClC,IAAK8I,EACL,MAAOD,EAAI5I,EACX,OAAQD,EAAS8I,EACjB,KAAMD,CACd,CAAK,EACMnX,CACX,CAWA,SAASwd,GAAerG,EAAGC,EAAG7I,EAAOD,EAAQ,CACzC,MAAO,CAAE,EAAG6I,EAAG,EAAGC,EAAG,MAAO7I,EAAO,OAAQD,EAC/C,CAMA,IAAIyQ,GAAmC,UAAY,CAM/C,SAASA,EAAkB5f,EAAQ,CAM/B,KAAK,eAAiB,EAMtB,KAAK,gBAAkB,EAMvB,KAAK,aAAeqe,GAAe,EAAG,EAAG,EAAG,CAAC,EAC7C,KAAK,OAASre,CACjB,CAOD,OAAA4f,EAAkB,UAAU,SAAW,UAAY,CAC/C,IAAI/e,EAAO4e,GAAe,KAAK,MAAM,EACrC,YAAK,aAAe5e,EACZA,EAAK,QAAU,KAAK,gBACxBA,EAAK,SAAW,KAAK,eACjC,EAOI+e,EAAkB,UAAU,cAAgB,UAAY,CACpD,IAAI/e,EAAO,KAAK,aAChB,YAAK,eAAiBA,EAAK,MAC3B,KAAK,gBAAkBA,EAAK,OACrBA,CACf,EACW+e,CACX,EAAC,EAEGC,GAAqC,UAAY,CAOjD,SAASA,EAAoB7f,EAAQ8f,EAAU,CAC3C,IAAIC,EAAcL,GAAmBI,CAAQ,EAO7C7B,GAAmB,KAAM,CAAE,OAAQje,EAAQ,YAAa+f,CAAW,CAAE,CACxE,CACD,OAAOF,CACX,EAAC,EAEGG,GAAmC,UAAY,CAW/C,SAASA,EAAkBxW,EAAUyW,EAAYC,EAAa,CAc1D,GAPA,KAAK,oBAAsB,GAM3B,KAAK,cAAgB,IAAIlE,GACrB,OAAOxS,GAAa,WACpB,MAAM,IAAI,UAAU,yDAAyD,EAEjF,KAAK,UAAYA,EACjB,KAAK,YAAcyW,EACnB,KAAK,aAAeC,CACvB,CAOD,OAAAF,EAAkB,UAAU,QAAU,SAAUhgB,EAAQ,CACpD,GAAI,CAAC,UAAU,OACX,MAAM,IAAI,UAAU,0CAA0C,EAGlE,GAAI,SAAO,QAAY,KAAe,EAAE,mBAAmB,SAG3D,IAAI,EAAEA,aAAkBke,GAAYle,CAAM,EAAE,SACxC,MAAM,IAAI,UAAU,uCAAuC,EAE/D,IAAImgB,EAAe,KAAK,cAEpBA,EAAa,IAAIngB,CAAM,IAG3BmgB,EAAa,IAAIngB,EAAQ,IAAI4f,GAAkB5f,CAAM,CAAC,EACtD,KAAK,YAAY,YAAY,IAAI,EAEjC,KAAK,YAAY,WACzB,EAOIggB,EAAkB,UAAU,UAAY,SAAUhgB,EAAQ,CACtD,GAAI,CAAC,UAAU,OACX,MAAM,IAAI,UAAU,0CAA0C,EAGlE,GAAI,SAAO,QAAY,KAAe,EAAE,mBAAmB,SAG3D,IAAI,EAAEA,aAAkBke,GAAYle,CAAM,EAAE,SACxC,MAAM,IAAI,UAAU,uCAAuC,EAE/D,IAAImgB,EAAe,KAAK,cAEnBA,EAAa,IAAIngB,CAAM,IAG5BmgB,EAAa,OAAOngB,CAAM,EACrBmgB,EAAa,MACd,KAAK,YAAY,eAAe,IAAI,GAEhD,EAMIH,EAAkB,UAAU,WAAa,UAAY,CACjD,KAAK,YAAW,EAChB,KAAK,cAAc,QACnB,KAAK,YAAY,eAAe,IAAI,CAC5C,EAOIA,EAAkB,UAAU,aAAe,UAAY,CACnD,IAAIpJ,EAAQ,KACZ,KAAK,YAAW,EAChB,KAAK,cAAc,QAAQ,SAAUwJ,EAAa,CAC1CA,EAAY,YACZxJ,EAAM,oBAAoB,KAAKwJ,CAAW,CAE1D,CAAS,CACT,EAOIJ,EAAkB,UAAU,gBAAkB,UAAY,CAEtD,GAAK,KAAK,YAGV,KAAIlf,EAAM,KAAK,aAEXwb,EAAU,KAAK,oBAAoB,IAAI,SAAU8D,EAAa,CAC9D,OAAO,IAAIP,GAAoBO,EAAY,OAAQA,EAAY,cAAa,CAAE,CAC1F,CAAS,EACD,KAAK,UAAU,KAAKtf,EAAKwb,EAASxb,CAAG,EACrC,KAAK,YAAW,EACxB,EAMIkf,EAAkB,UAAU,YAAc,UAAY,CAClD,KAAK,oBAAoB,OAAO,CAAC,CACzC,EAMIA,EAAkB,UAAU,UAAY,UAAY,CAChD,OAAO,KAAK,oBAAoB,OAAS,CACjD,EACWA,CACX,EAAC,EAKGrC,GAAY,OAAO,QAAY,IAAc,IAAI,QAAY,IAAI3B,GAKjEqE,GAAgC,UAAY,CAO5C,SAASA,EAAe7W,EAAU,CAC9B,GAAI,EAAE,gBAAgB6W,GAClB,MAAM,IAAI,UAAU,oCAAoC,EAE5D,GAAI,CAAC,UAAU,OACX,MAAM,IAAI,UAAU,0CAA0C,EAElE,IAAIJ,EAAaxC,GAAyB,cACtCC,EAAW,IAAIsC,GAAkBxW,EAAUyW,EAAY,IAAI,EAC/DtC,GAAU,IAAI,KAAMD,CAAQ,CAC/B,CACD,OAAO2C,CACX,EAAC,EAED,CACI,UACA,YACA,YACJ,EAAE,QAAQ,SAAUC,EAAQ,CACxBD,GAAe,UAAUC,CAAM,EAAI,UAAY,CAC3C,IAAI9D,EACJ,OAAQA,EAAKmB,GAAU,IAAI,IAAI,GAAG2C,CAAM,EAAE,MAAM9D,EAAI,SAAS,CACrE,CACA,CAAC,EAED,IAAIJ,GAAS,UAAY,CAErB,OAAI,OAAOM,GAAS,eAAmB,IAC5BA,GAAS,eAEb2D,EACX,EAAI,oPCtXFtgB,EAEKC,EAAAiB,EAAAf,CAAA,gCAFmBqgB,IAAAA,EAAAC,GAAAvf,EAAAwf,GAAA,CAAA,SAAU,IAAE,EAAA,+BAAZF,IAAAA,EAAAC,GAAAvf,EAAAwf,GAAA,CAAA,SAAU,IAAE,EAAA,+JAM9B3f,EAAI,EAAA,CAAA,iBACQA,EAAM,EAAA,CAAA,wCAEbA,EAAa,CAAA,CAAA,YACZA,EAAa,CAAA,CAAA,gCALzBf,EAiBCC,EAAA0gB,EAAAxgB,CAAA,aAVcygB,GAAAD,EAAA,YAAA5f,QAAS,YAAcA,EAAiB,CAAA,EAAG,MAAS,EACpD6f,GAAAD,EAAA,YAAA5f,QAAS,YAAcA,EAAgB,CAAA,EAAG,MAAS,EACrD6f,GAAAD,EAAA,UAAA5f,QAAS,YAAcA,EAAe,CAAA,EAAG,MAAS,EACjD6f,GAAAD,EAAA,WAAA5f,QAAS,YAAcA,EAAe,CAAA,EAAG,MAAS,EACtD6f,GAAAD,EAAA,OAAA5f,QAAS,YAAcA,EAAe,CAAA,EAAG,MAAS,EAC5C6f,GAAAD,EAAA,aAAA5f,QAAS,YAAcA,EAAiB,CAAA,EAAG,MAAS,EACrD6f,GAAAD,EAAA,YAAA5f,QAAS,YAAcA,EAAgB,CAAA,EAAG,MAAS,EACpD6f,GAAAD,EAAA,WAAA5f,QAAS,YAAcA,EAAe,CAAA,EAAG,MAAS,EAC/C6f,GAAAD,EAAA,cAAA5f,QAAS,YAAcA,EAAe,CAAA,EAAG,MAAS,4FAXxDA,EAAa,CAAA,CAAA,qBACZA,EAAa,CAAA,CAAA,0DAXrB8f,EAAA9f,OAAe,GAACI,GAAA,OAKdJ,EAAY,CAAA,CAAA,uBAAjB,OAAI2C,GAAA,+JAXP1D,EA+BKC,EAAAiB,EAAAf,CAAA,mHAzBCY,OAAe,uGAKbA,EAAY,CAAA,CAAA,oBAAjB,OAAI2C,GAAA,EAAA,mHAAJ,+EA1gBE,IAAAod,GAAiB,gBAkCZC,GAAUC,EAAIC,EAAE,QAEvB,EAAGD,EAAG,GAAKC,EAAG,EAAID,EAAG,GAAK,EAC1B,EAAGA,EAAG,GAAKC,EAAG,EAAID,EAAG,GAAK,4BA7DtB,MAAAnf,EAAWC,SAEN,MAAA2C,CAAK,EAAAjD,GACL,UAAA0f,CAAS,EAAA1f,EACT,CAAA,KAAAE,EAAO,QAAQ,EAAAF,EACf,CAAA,YAAA2f,EAAc,SAAS,EAAA3f,GACvB,aAAA4f,CAAY,EAAA5f,EACZ,CAAA,aAAA6f,EAAe,EAAG,EAAA7f,GAClB,OAAAmC,CAAM,EAAAnC,EAEN,CAAA,MAAA6N,EAAQ,GAAG,EAAA7N,EACX,CAAA,OAAA4N,EAAS,GAAG,EAAA5N,EACZ,CAAA,iBAAA8f,EAAmB,GAAG,EAAA9f,GACtB,MAAA+f,CAAK,EAAA/f,EASZggB,EAIAC,EAAepS,EACfqS,EAAgBtS,EAIhBuS,QAkCEC,GAAY,CAEhB,CAAA,KAAM,YACN,OAAQ,EAAA,GAGR,KAAM,OACN,OAAQ,GACR,QAASP,GAGT,CAAA,KAAM,UACN,OAAQ,EAAA,EAGR,CAAA,KAAM,OACN,OAAQ,EAAA,OAIN/f,EAAM,CAAA,EACNP,EAAG,CAAA,EACH8gB,EAAM,CAAA,EACNC,EAAK,CAAA,EACLC,EAAkB,GAClBC,GAAiB,GACjBC,GAAa,GACbC,GAAc,GACdC,EAAO,KACPC,EAAmB,KACnBC,EAAkB,KAClBC,EAAa,WAERC,IAAkB,KACrBhB,EAAK,CACTxgB,EAAI,KAAK,UAAUmgB,EAAW,EAAG,EAAG7R,EAAOD,CAAM,aAI9CoT,EAAStB,EAAU,aACnBuB,EAAUvB,EAAU,cAElB,MAAAwB,EAAcnB,EAAM,CAAC,EAAIA,EAAM,CAAC,EAChCoB,GAAcH,EAASC,EAEzB,IAAAxK,EAAI,EACJC,EAAI,EAEJwK,EAAcC,IACjBH,EAASjB,EAAM,CAAC,EAAIoB,GACpBF,EAAUlB,EAAM,CAAC,EACjBtJ,GAAKsJ,EAAM,CAAC,EAAIiB,GAAU,GAChBE,EAAcC,IACxBH,EAASjB,EAAM,CAAC,EAChBkB,EAAUlB,EAAM,CAAC,EAAIoB,GACrBzK,GAAKqJ,EAAM,CAAC,EAAIkB,GAAW,IAE3BxK,EAAI,EACJC,EAAI,EACJsK,EAASjB,EAAM,CAAC,EAChBkB,EAAUlB,EAAM,CAAC,GAGlBxgB,EAAI,KAAK,UAAUmgB,EAAWjJ,EAAGC,EAAGsK,EAAQC,CAAO,EAGpD1gB,GAAO,SAAA,CACN,OAAO,KAAKT,CAAM,EAAE,QAASsC,GAAG,MAC/B7C,EAAI6C,CAAG,EAAItC,EAAOsC,CAAG,EAAE,WAAW,IAAI,EAAA7C,CAAA,UAGjC6hB,GAAI,EAEN1B,IACHA,EAAU,iBAAiB,OAAS2B,GAAC,CAChClf,IAAW,UACd5C,EAAI,KAAK,OACTA,EAAI,KAAK,UAAUsO,EAAO,CAAC,EAC3BtO,EAAI,KAAK,MAAO,GAAG,CAAC,EACpBA,EAAI,KAAK,UAAUmgB,EAAW,EAAG,CAAC,EAClCngB,EAAI,KAAK,WAETwhB,KAEDxhB,EAAI,QAAQ,UAAUO,EAAO,KAAM,EAAG,EAAG+N,EAAOD,CAAM,EAEtD0T,OAGD,gBACKnf,IAAW,UACd5C,EAAI,KAAK,OACTA,EAAI,KAAK,UAAUsO,EAAO,CAAC,EAC3BtO,EAAI,KAAK,MAAO,GAAG,CAAC,EACpBA,EAAI,KAAK,UAAUmgB,EAAW,EAAG,CAAC,EAClCngB,EAAI,KAAK,WAETwhB,KAGDxhB,EAAI,QAAQ,UAAUO,EAAO,KAAM,EAAG,EAAG+N,EAAOD,CAAM,EAEtD2T,GAAa,CAAA,MAAOjB,EAAM,MAAK,CAAA,CAAA,EAC/BgB,MACE,MAGJ7f,EAAA,GAAAkf,MAAWxG,GAAS,CACnB,OAAQyF,EAAe,IACvB,QAAS,GACT,aAAY,CACX,EAAG/R,EAAQ,EACX,EAAGD,EAAS,CAAA,KAIdiT,MAAsB/B,GAAc,CAAE/D,EAASoB,KAAaqF,IAAI,CAC/DC,GAAsC,IAEvCZ,EAAgB,QAAQD,CAAgB,EAExCc,KACAjgB,EAAA,GAAAue,EAAU,EAAI,EAEd,sBAAqB,IAAA,CACpB2B,KACA,sBAAqB,IAAA,CACpBC,oBAKMD,IAAI,OACNE,EAAQhU,EAAQ,EAChBiU,EAAQlU,EAAS,EACvB+S,EAAK,OAAS,CAAA,EAAGkB,EAAO,EAAGC,CAAK,EAAM,CAAA,KAAM,EAAI,CAAA,EAChDnB,EAAK,OAAS,CAAA,EAAGkB,EAAO,EAAGC,CAAK,EAAM,CAAA,KAAM,EAAK,CAAA,EACjDvB,EAAkB,GAClBC,GAAiB,GAGlBuB,GAAS,IAAA,CACRtgB,EAAA,GAAAue,EAAU,EAAK,EACfa,EAAgB,UAAUD,CAAgB,IAGlC,SAAAoB,GAAaC,EAAM,CAC3BC,KAEIxC,IACCvd,IAAW,UACd5C,EAAI,KAAK,OACTA,EAAI,KAAK,UAAUsO,EAAO,CAAC,EAC3BtO,EAAI,KAAK,MAAO,GAAG,CAAC,EACpBA,EAAI,KAAK,UAAUmgB,EAAW,EAAG,CAAC,EAClCngB,EAAI,KAAK,WAETwhB,OAGIT,GAAK,CAAKA,EAAM,SACpB/gB,EAAI,QAAQ,UAAUO,EAAO,KAAM,EAAG,EAAG+N,EAAOD,CAAM,GAIxD2T,GAAU,CAAG,MAAOU,CAAM,CAAA,MAC1BnB,EAAamB,EAAO,MAAM,EAE1BxgB,EAAA,GAAA6e,EAAQ2B,CAAM,EACd1iB,EAAI,QAAQ,UAAUO,EAAO,KAAM,EAAG,EAAG+N,EAAOD,CAAM,EAElD0S,EAAM,QAAU,GACnBjgB,EAAS,OAAO,WAIF8hB,GAAU,CAGzBH,GAFY,CAAA,CAEO,EACnBV,cAGec,IAAI,CACb,MAAAH,EAAS3B,EAAM,MAAM,IAAK,EAEhC0B,GAAaC,CAAM,EACnBX,KAWG,IAAAC,KAAgB,MAAAjB,KAAK,CACxBA,EAAM,QAAS+B,GAAI,CACV,KAAA,CAAA,OAAQC,EAAS,YAAA3C,GAAa,aAAAC,CAAY,EAAKyC,EACvDE,GAAW,CACV,OAAQD,EACR,YAAA3C,GACA,aAAAC,EACA,KAAM1f,IAAS,WAIjBsiB,GAAoC,EAChCtiB,IAAS,QACZuiB,MAIEC,GAAqBphB,GAAC,CACzBA,EAAE,eAAc,EAChBof,GAAc,GACN,KAAA,CAAA,EAAAjK,EAAG,EAAAC,CAAM,EAAAiM,GAAgBrhB,CAAC,EAC9BA,EAAE,SAAWA,EAAE,QAAQ,OAAS,GACnCqf,EAAK,OAAS,CAAA,EAAAlK,EAAG,EAAAC,CAAC,EAAA,CAAM,KAAM,EAAI,CAAA,EAEnCkM,GAAoBnM,EAAGC,CAAC,EACxBjV,EAAA,EAAAqf,GAAc,CAAC,GAGZ+B,GAAoBvhB,GAAC,CACxBA,EAAE,eAAc,EACR,KAAA,CAAA,EAAAmV,EAAG,EAAAC,CAAM,EAAAiM,GAAgBrhB,CAAC,EAClCshB,GAAoBnM,EAAGC,CAAC,GAGrBoM,GAAmBxhB,GAAC,CACvBA,EAAE,eAAc,EAChBuhB,GAAiBvhB,CAAC,EAClBmf,GAAa,GACbC,GAAc,GACd8B,KAEItiB,IAAS,QACZuiB,MAIEM,GAAY,EACZC,GAAa,EACbC,GAAuB,EACvBC,GAAgB,GAEhBzB,GAAoB,SAAA,CACnB,GAAA1B,GAASa,EAAgB,OACtBnK,EAAImK,GAAkB,wBACtBM,GAAcnB,EAAM,CAAC,EAAIA,EAAM,CAAC,EAChCoD,EAAkB1M,EAAE,MAAQA,EAAE,WACpCyM,GAAgBhC,GAAciC,CAAe,KAI7CtV,IAAUkV,IACVnV,IAAWoV,IACXC,KAAyBnD,eAIpBsD,EAAU,CAAY,MAAAvV,EAAe,OAAAD,GAErCyV,EAAoB,CACzB,OAAQvD,EACR,MAAOA,GAAoBsD,EAAW,MAAQA,EAAW,SAGpD,MAAA,QAAQ,IAAG,CAChBE,GAAgBxjB,EAAO,UAAWsjB,EAAYC,CAAoB,EAClEC,GAAgBxjB,EAAO,QAASsjB,EAAYC,CAAoB,EAChEC,GAAgBxjB,EAAO,KAAMsjB,EAAYC,CAAoB,EAC7DC,GAAgBxjB,EAAO,KAAMsjB,EAAYC,EAAsB,EAAK,IAGhEzD,QACJA,EAAe,IAAMwD,EAAW,MAAQC,EAAqB,MAAK,EAGnE3B,GAAI,CAAG,KAAM,EAAI,CAAA,EAEjB,gBACCsB,GAAapV,EACbmV,GAAYlV,EACZoV,GAAuBnD,GACrB,UACGsB,GAAI,EAEVQ,MAgBG0B,GAAe,MAAUxjB,EAAQsjB,EAAY1Q,EAAWyF,GAAQ,KAAI,KAClE6H,EAAO,aACNoB,GAAI,EAEJ,MAAAmC,EAAM,OAAO,kBAAoB,EACvCzjB,EAAO,MAAQsjB,EAAW,OAASjL,GAAQoL,EAAM,GACjDzjB,EAAO,OAASsjB,EAAW,QAAUjL,GAAQoL,EAAM,GAE7C,MAAAhkB,EAAMO,EAAO,WAAW,IAAI,EAClCqY,IAAS5Y,EAAI,MAAMgkB,EAAKA,CAAG,EAE3BzjB,EAAO,MAAM,MAAW,GAAA4S,EAAU,UAClC5S,EAAO,MAAM,OAAY,GAAA4S,EAAU,YAGhCiQ,GAAmBrhB,GAAC,CACjB,MAAAhC,EAAOQ,EAAO,UAAU,sBAAqB,MAE/C0jB,EAAUliB,EAAE,QACZmiB,GAAUniB,EAAE,eACZA,EAAE,gBAAkBA,EAAE,eAAe,OAAS,IACjDkiB,EAAUliB,EAAE,eAAe,CAAC,EAAE,QAC9BmiB,GAAUniB,EAAE,eAAe,CAAC,EAAE,UAI9B,GAAKkiB,EAAUlkB,EAAK,MAAQA,EAAK,MAASuO,EAC1C,GAAK4V,GAAUnkB,EAAK,KAAOA,EAAK,OAAUsO,IAIxCgV,GAAmB,CAAInM,EAAGC,IAAC,CAC9BiK,EAAK,OAAY,CAAA,EAAAlK,EAAM,EAAAC,CAAC,CAAA,QAClBgN,EAAW,CAAI/C,EAAK,aACrBD,IAAgB,CAAAD,IAAgBiD,GAAehD,MACnDD,GAAa,GACbJ,EAAO,KAAKM,EAAK,MAAM,SAAQ,CAAA,GAE5BF,KACHJ,EAAO,KAAKM,EAAK,MAAM,SAAQ,CAAA,EAC/B4B,GAAW,CACF,OAAAlC,EACR,YAAAV,EACA,aAAAC,EACA,KAAM1f,IAAS,UAGjBqgB,EAAkB,IAGfgC,KAAiB,OAAAlC,EAAQ,YAAAV,EAAa,aAAAC,EAAc,KAAA+D,MAAI,CACtD,GAAA,CAAAtD,GAAUA,EAAO,OAAS,EAAC,WAC5BuD,EAAaD,GAAOpkB,EAAI,KAAOA,EAAI,KACvCqkB,EAAW,SAAW,QACtBA,EAAW,QAAU,QAErBA,EAAW,YAAcjE,EACzBiE,EAAW,UAAYhE,MACnBJ,EAAKa,EAAO,CAAC,EACbZ,GAAKY,EAAO,CAAC,EACjBuD,EAAW,OAAOnE,GAAG,EAAGA,GAAG,CAAC,EAC5BmE,EAAW,UAAS,EACX,QAAA1hB,GAAI,EAAG2B,GAAMwc,EAAO,OAAQne,GAAI2B,GAAK3B,KAAC,CAC1C,IAAA2hB,GAAWtE,GAAUC,EAAIC,EAAE,EAC/BmE,EAAW,iBAAiBpE,EAAG,EAAGA,EAAG,EAAGqE,GAAS,EAAGA,GAAS,CAAC,EAC9DrE,EAAKa,EAAOne,EAAC,EACbud,GAAKY,EAAOne,GAAI,CAAC,EAGlB0hB,EAAW,OAAOpE,EAAG,EAAGA,EAAG,CAAC,EAC5BoE,EAAW,OAAM,GAGdnB,GAAc,IAAA,CACbpC,EAAO,OAAS,IACpBA,EAAO,OAAS,EAEhBiB,OAGGkB,GAAQ,IAAA,CACPnC,EAAO,OAAS,IAEpBC,EAAM,KAAI,CACT,OAAQD,EAAO,MAAK,EACP,YAAAV,EACb,aAAAC,IAGG1f,IAAS,SACZmgB,EAAO,OAAS,GAGjB9gB,EAAI,QAAQ,UAAUO,EAAO,KAAM,EAAG,EAAG+N,EAAOD,CAAM,EAEtD0T,OAGGA,GAAiB,IAAA,CACd,MAAA7K,EAAIqN,KACVzjB,EAAS,SAAUoW,CAAC,YAGLmL,IAAK,aACpBtB,EAAK,CAAA,CAAA,EACL4B,KACAzgB,EAAA,EAAAqf,EAAa,CAAC,EAEP,YAGCoB,IAAY,CACpB1B,GAAiB,GACjBjhB,EAAI,KAAK,UAAU,EAAG,EAAGsO,EAAOD,CAAM,OAEtCrO,EAAI,KAAK,UAAYW,IAAS,OAAS,cAAgB,UAASX,CAAA,EAChEA,EAAI,KAAK,SAAS,EAAG,EAAGsO,EAAOD,CAAM,EAEjC1N,IAAS,QACZX,EAAI,KAAK,UAAU,EAAG,EAAGO,EAAO,KAAK,MAAOA,EAAO,KAAK,MAAM,MAI5D4hB,GAAI,CAAA,CAAM,KAAA1X,EAAO,EAAK,EAAA,CAAA,IAAA,CACrB,GAAAuW,GAAmBC,GAAc,OAC9BpU,EAAUuU,EAAK,wBACfoD,EAAQpD,EAAK,sBACnBqD,GAAezkB,EAAI,UAAW6M,EAAS2X,CAAK,EAC5CxD,EAAkB,GAClBC,GAAiB,GAEbxW,GACJ,OAAO,sBAAqB,IAAA,CAC3B0X,QAOCsC,GAAkB,CAAAzkB,EAAK6M,EAAS2X,IAAK,CACxCxkB,EAAI,UAAU,EAAG,EAAGsO,EAAOD,CAAM,EAGjCrO,EAAI,UAAS,EACbA,EAAI,UAAYogB,EAChBpgB,EAAI,IAAIwkB,EAAM,EAAGA,EAAM,EAAGnE,EAAe,EAAG,EAAG,KAAK,GAAK,EAAG,EAAI,EAChErgB,EAAI,KAAI,EAGRA,EAAI,UAAS,EACbA,EAAI,UAAY+f,GAChB/f,EAAI,IAAIwkB,EAAM,EAAGA,EAAM,EAAGE,EAAW,EAAG,KAAK,GAAK,EAAG,EAAI,EACzD1kB,EAAI,KAAI,YAGOukB,IAAc,CACtB,OAAA5jB,IAAS,OACbJ,EAAO,KAAK,UAAU,WAAW,EACjCA,EAAO,QAAQ,UAAU,WAAW,+EAsB3BA,EAAO4J,CAAI,EAAAhI,qDAhBbkf,EAAgBlf,yBACTue,EAAY,KAAA,YACXC,EAAa,KAAA,8dA3gB3BH,IAAUlS,GAASD,UACtBC,EAAQkS,EAAM,CAAC,CAAA,OACfnS,EAASmS,EAAM,CAAC,CAAA,2BAWfC,GAAY,CAAA/c,GAAS2e,8BAKnB5B,GAAWN,IAAcS,IAC5B1e,EAAA,GAAA0e,EAAiBT,CAAS,EAE1BkC,KAEA,gBACKzf,IAAW,UACd5C,EAAI,KAAK,OACTA,EAAI,KAAK,UAAUsO,EAAO,CAAC,EAC3BtO,EAAI,KAAK,MAAO,GAAG,CAAC,EACpBA,EAAI,KAAK,UAAUmgB,EAAW,EAAG,CAAC,EAClCngB,EAAI,KAAK,WAETwhB,KAGDxhB,EAAI,QAAQ,UAAUO,EAAO,KAAM,EAAG,EAAG+N,EAAOD,CAAM,EAEtD2T,GAAa,CAAA,MAAOjB,EAAM,MAAK,CAAA,CAAA,EAC/BgB,MACE,8BAkTAX,IACHgB,KACAhB,EAAK,UAAUf,EAAe,GAAI,wBAK/B/R,GAASD,IACZ6T,0BA8ICwC,EAAYrE,EAAe,gjBCxftBsE,GAAK,MAAA,OAAA,CAAA,CAAA,2MAJKC,GAAI,MAAA,MAAA,CAAA,CAAA,4BAEjB5kB,EAAW,CAAA,GAAAI,GAAAJ,CAAA,+BAYT6kB,GAAK,MAAA,cAAA,oIAfb5lB,EAsBKC,EAAAiB,EAAAf,CAAA,sEAnBCY,EAAW,CAAA,uQARV,MAAAc,EAAWC,KAEN,GAAA,CAAA,YAAA+jB,EAAc,EAAK,EAAArkB,EAIuB,MAAAskB,EAAA,IAAAjkB,EAAS,MAAM,IAMvDiK,GAAK,CACfjK,EAAS,YAAY,EACrBiK,EAAM,gBAAe,KAQZA,GAAK,CACfjK,EAAS,cAAc,EACvBiK,EAAM,gBAAe,iRCAf,IAAO/K,EAAS,CAAA,EAAGA,EAAK,CAAA,EAAA,cACxB,IAAMA,EAAS,CAAA,EAAGA,EAAK,CAAA,EAAA,uCAL7Bf,EAMCC,EAAA8lB,EAAA5lB,CAAA,OAJYY,EAAY,CAAA,CAAA,8EAEnB,IAAOA,EAAS,CAAA,EAAGA,EAAK,CAAA,+BACxB,IAAMA,EAAS,CAAA,EAAGA,EAAK,CAAA,4BAHhBA,EAAY,CAAA,CAAA,8EAWlBilB,GAAK,MAAA,oBAAA,+BAIPjlB,EAAQ,CAAA,GAAAklB,GAAAllB,CAAA,kGANdf,EASMC,EAAAimB,EAAA/lB,CAAA,iDAHAY,EAAQ,CAAA,qSACZf,EAAuEC,EAAA8lB,EAAA5lB,CAAA,OAA3BY,EAAW,CAAA,CAAA,oDAAXA,EAAW,CAAA,CAAA,gFAvBlDolB,GAAK,MAAA,WAAA,8BAIPplB,EAAS,CAAA,GAAAqlB,GAAArlB,CAAA,EAWVslB,EAAAtlB,OAAS,QAAMI,GAAAJ,CAAA,gKAlBrBf,EA8BKC,EAAAiB,EAAAf,CAAA,EA7BJC,EAeMc,EAAAglB,CAAA,yEATAnlB,EAAS,CAAA,4DAWVA,OAAS,mPA/BVulB,EAAY,GACZC,EAAW,GAEJ,CAAA,aAAAnF,EAAe,EAAE,EAAA5f,EACjB,CAAA,YAAA2f,EAAc,MAAM,EAAA3f,GACpB,iBAAA8f,CAAwB,EAAA9f,GACxB,UAAAglB,CAAiB,EAAAhlB,GACjB,WAAAilB,CAAkB,EAAAjlB,EAClB,CAAA,KAAAE,EAAyB,OAAO,EAAAF,EAUxB,MAAAskB,EAAA,IAAA7iB,EAAA,EAAAqjB,GAAaA,CAAS,eAK1BlF,EAAYsF,GAAA,KAAA,KAAA,SAaP,MAAAC,EAAA,IAAA1jB,EAAA,EAAAsjB,GAAYA,CAAQ,eAGOpF,EAAW,KAAA,8SA7BvD9R,EAAQiS,GAAoBkF,EAAYC,EAAU,6OCwX9C1lB,EAAK,CAAA,EAAC,OAASA,EAAK,CAAA,CAAA,GAAA+Z,EAAA5E,EAAA,MAAA6E,CAAA,0DAEXha,EAAM,CAAA,IAAK,UAAYA,EAAa,EAAA,CAAA,kCAHnDf,EAMCC,EAAAiW,EAAA/V,CAAA,qBADUY,EAAY,EAAA,CAAA,oCAJjBA,EAAK,CAAA,EAAC,OAASA,EAAK,CAAA,CAAA,wCAEXA,EAAM,CAAA,IAAK,UAAYA,EAAa,EAAA,CAAA,2FA/C7CA,EAAY,EAAA,gBAUb8f,EAAA9f,MAAY,GAAC6lB,GAAA7lB,CAAA,0HAVZA,EAAY,EAAA,CAAA,sDAUbA,MAAY,0SA1BNA,EAAY,EAAA,CAAA,oDASjBA,EAAK,CAAA,CAAA,GAAA+Z,EAAA5E,EAAA,MAAA6E,CAAA,kFAGIha,EAAM,CAAA,IAAK,UAAYA,EAAa,EAAA,CAAA,4BAJnDf,EAMCC,EAAAiW,EAAA/V,CAAA,0BADUY,EAAY,EAAA,CAAA,yCAJjBA,EAAK,CAAA,CAAA,wFAGIA,EAAM,CAAA,IAAK,UAAYA,EAAa,EAAA,CAAA,8IAjBfA,EAAK,CAAA,CAAA,mDAAWA,EAAW,EAAA,CAAA,6JAA3BA,EAAK,CAAA,uLAZpCA,EAAM,CAAA,IAAK,UAAQ,CAAKA,EAAY,EAAA,GAAA8lB,GAAA9lB,CAAA,uEAApCA,EAAM,CAAA,IAAK,UAAQ,CAAKA,EAAY,EAAA,0QAzBvBA,EAAmB,EAAA,CAAA,EAEhC,IAAA8f,EAAA9f,OAAS,gBAAc+lB,GAAA/lB,CAAA,6EAiBpB,MAAAA,OAAaA,EAAS,EAAA,EACrB,OAAAA,OAAcA,EAAU,EAAA,EACd,iBAAAA,OAAoBA,EAAU,EAAA,+MALrCA,EAAW,EAAA,CAAA,gBACZA,EAAmB,EAAA,CAAA,mIAfzBA,OAAS,yLAiBNgmB,EAAA,CAAA,EAAA,UAAAC,EAAA,MAAAjmB,OAAaA,EAAS,EAAA,GACrBgmB,EAAA,CAAA,EAAA,UAAAC,EAAA,OAAAjmB,OAAcA,EAAU,EAAA,GACdgmB,EAAA,CAAA,EAAA,UAAAC,EAAA,iBAAAjmB,OAAoBA,EAAU,EAAA,yZA1GzB,mBACNA,EAAK,CAAA,oJAFbA,EAAa,EAAA,CAAA,4FAELA,EAAK,CAAA,+RAkJhBkmB,GAAA/Q,EAAA,IAAA6E,EAAAha,EAAgB,EAAA,GAAAA,EAAO,CAAA,GAAA,OAASA,EAAK,CAAA,CAAA,GAAA+Z,EAAA5E,EAAA,MAAA6E,CAAA,6BAG5Bha,EAAM,CAAA,IAAK,UAAYA,EAAa,EAAA,CAAA,UANnDf,EAOCC,EAAAiW,EAAA/V,CAAA,6BAFSY,EAAiB,EAAA,CAAA,gBAFrBgmB,EAAA,CAAA,EAAA,SAAA,CAAAE,GAAA/Q,EAAA,IAAA6E,EAAAha,EAAgB,EAAA,GAAAA,EAAO,CAAA,GAAA,OAASA,EAAK,CAAA,CAAA,wCAG5BA,EAAM,CAAA,IAAK,UAAYA,EAAa,EAAA,CAAA,qJAW3C,MAAAA,OAAaA,EAAS,EAAA,EACrB,OAAAA,OAAcA,EAAU,EAAA,EACd,iBAAAA,OAAoBA,EAAU,EAAA,yNAJrCA,EAAW,EAAA,CAAA,wDAULA,EAAmB,EAAA,CAAA,EAEhC,IAAA8f,GAAA9f,EAAS,CAAA,IAAA,gBAAkBA,OAAS,WAAQmmB,GAAAnmB,CAAA,uNAVzCgmB,EAAA,CAAA,EAAA,UAAAC,EAAA,MAAAjmB,OAAaA,EAAS,EAAA,GACrBgmB,EAAA,CAAA,EAAA,UAAAC,EAAA,OAAAjmB,OAAcA,EAAU,EAAA,GACdgmB,EAAA,CAAA,EAAA,UAAAC,EAAA,iBAAAjmB,OAAoBA,EAAU,EAAA,+KAQ5CA,EAAS,CAAA,IAAA,gBAAkBA,OAAS,sXAIrB,iBAAAA,OAAoBA,EAAU,EAAA,EACrC,UAAAA,OAAaA,EAAS,EAAA,EACrB,WAAAA,OAAcA,EAAU,EAAA,sPAFlBgmB,EAAA,CAAA,EAAA,UAAAI,EAAA,iBAAApmB,OAAoBA,EAAU,EAAA,GACrCgmB,EAAA,CAAA,EAAA,UAAAI,EAAA,UAAApmB,OAAaA,EAAS,EAAA,GACrBgmB,EAAA,CAAA,EAAA,UAAAI,EAAA,WAAApmB,OAAcA,EAAU,EAAA,wWA/D1BA,EAAW,EAAA,CAAA,2VAvBJ,iBAAAA,OAAoBA,EAAU,EAAA,EACrC,UAAAA,OAAaA,EAAS,EAAA,EACrB,WAAAA,OAAcA,EAAU,EAAA,2OAFlBgmB,EAAA,CAAA,EAAA,UAAAI,EAAA,iBAAApmB,OAAoBA,EAAU,EAAA,GACrCgmB,EAAA,CAAA,EAAA,UAAAI,EAAA,UAAApmB,OAAaA,EAAS,EAAA,GACrBgmB,EAAA,CAAA,EAAA,UAAAI,EAAA,WAAApmB,OAAcA,EAAU,EAAA,gQAnB9BA,EAAK,CAAA,EAAC,OAASA,EAAK,CAAA,CAAA,GAAA+Z,EAAA5E,EAAA,MAAA6E,CAAA,+DAEXha,EAAM,CAAA,IAAK,UAAYA,EAAa,EAAA,CAAA,kCAHnDf,EAMCC,EAAAiW,EAAA/V,CAAA,qBADUY,EAAY,EAAA,CAAA,oCAJjBA,EAAK,CAAA,EAAC,OAASA,EAAK,CAAA,CAAA,wCAEXA,EAAM,CAAA,IAAK,UAAYA,EAAa,EAAA,CAAA,2FAlD7CA,EAAY,EAAA,gBAUb8f,EAAA9f,MAAY,GAACqmB,GAAArmB,CAAA,0HAVZA,EAAY,EAAA,CAAA,sDAUbA,MAAY,0SAzBNA,EAAY,EAAA,CAAA,oDAQjBA,EAAK,CAAA,CAAA,GAAA+Z,EAAA5E,EAAA,MAAA6E,CAAA,gEAEUha,EAAM,CAAA,IAAK,UAAYA,EAAa,EAAA,CAAA,oDAHzDf,EAMCC,EAAAiW,EAAA/V,CAAA,0BADUY,EAAY,EAAA,CAAA,yCAJjBA,EAAK,CAAA,CAAA,qDAEUA,EAAM,CAAA,IAAK,UAAYA,EAAa,EAAA,CAAA,uLAfrBA,EAAK,CAAA,CAAA,mDAAWA,EAAW,EAAA,CAAA,6JAA3BA,EAAK,CAAA,mfAwBlCkmB,GAAA/Q,EAAA,IAAA6E,EAAAha,EAAgB,EAAA,GAAAA,EAAO,CAAA,GAAA,OAASA,EAAK,CAAA,CAAA,GAAA+Z,EAAA5E,EAAA,MAAA6E,CAAA,6BAG5Bha,EAAM,CAAA,IAAK,UAAYA,EAAa,EAAA,CAAA,UANnDf,EAOCC,EAAAiW,EAAA/V,CAAA,6BAFSY,EAAiB,EAAA,CAAA,gBAFrBgmB,EAAA,CAAA,EAAA,SAAA,CAAAE,GAAA/Q,EAAA,IAAA6E,EAAAha,EAAgB,EAAA,GAAAA,EAAO,CAAA,GAAA,OAASA,EAAK,CAAA,CAAA,wCAG5BA,EAAM,CAAA,IAAK,UAAYA,EAAa,EAAA,CAAA,wKAY3C,MAAAA,OAAaA,EAAS,EAAA,EACrB,OAAAA,OAAcA,EAAU,EAAA,EACd,iBAAAA,OAAoBA,EAAU,EAAA,oOAJrCA,EAAW,EAAA,CAAA,+BAUTA,EAAS,EAAA,CAAA,2CAEPA,EAAiB,EAAA,CAAA,uBACfA,EAAmB,EAAA,CAAA,EAEhC,IAAA8f,GAAA9f,EAAS,CAAA,IAAA,gBAAkBA,OAAS,WAAQsmB,GAAAtmB,CAAA,yPAbzCgmB,EAAA,CAAA,EAAA,UAAAC,EAAA,MAAAjmB,OAAaA,EAAS,EAAA,GACrBgmB,EAAA,CAAA,EAAA,UAAAC,EAAA,OAAAjmB,OAAcA,EAAU,EAAA,GACdgmB,EAAA,CAAA,EAAA,UAAAC,EAAA,iBAAAjmB,OAAoBA,EAAU,EAAA,gPAMnCA,EAAS,EAAA,aAKlBA,EAAS,CAAA,IAAA,gBAAkBA,OAAS,sXAIrB,iBAAAA,OAAoBA,EAAU,EAAA,EACrC,UAAAA,OAAaA,EAAS,EAAA,EACrB,WAAAA,OAAcA,EAAU,EAAA,sPAFlBgmB,EAAA,CAAA,EAAA,UAAAI,EAAA,iBAAApmB,OAAoBA,EAAU,EAAA,GACrCgmB,EAAA,CAAA,EAAA,UAAAI,EAAA,UAAApmB,OAAaA,EAAS,EAAA,GACrBgmB,EAAA,CAAA,EAAA,UAAAI,EAAA,WAAApmB,OAAcA,EAAU,EAAA,ySA7DlC,OAAAA,EAAU,CAAA,IAAA,MAAS,CAAAA,OAAiBA,EAAS,CAAA,EAAA,EAEzCA,OAAS,SAAQ,EAGjBA,OAAS,SAAQ,GAiBhBA,EAAI,CAAA,IAAK,UAAYA,EAAI,CAAA,IAAK,kBAAoBA,EAAK,CAAA,IAAK,MAAQA,EAAY,EAAA,GAAA,2WAxCvF,KAAAA,EAAW,CAAA,IAAA,SAAWumB,GAAaC,GAClC,MAAAxmB,OAAUA,EAAM,CAAA,IAAK,SAAW,SAAW,gEAS7C,OAAAA,OAAW,SAAQ,EAuFdA,OAAW,SAAQ,EA2BlBA,EAAU,CAAA,IAAA,MAAS,CAAAA,OAAiBA,EAAS,CAAA,EAAA,EAY9CA,OAAS,SAAQ,EAGjBA,OAAS,SAAQ,GAkBhBA,EAAI,CAAA,IAAK,UAAYA,EAAI,CAAA,IAAK,kBAAoBA,EAAK,CAAA,IAAK,MAAQA,EAAY,EAAA,GAAA,sMAzJ5Ff,EA8MKC,EAAAiB,EAAAf,CAAA,4FAlNE4mB,EAAA,CAAA,EAAA,KAAAS,EAAA,KAAAzmB,EAAW,CAAA,IAAA,SAAWumB,GAAaC,IAClCR,EAAA,CAAA,EAAA,KAAAS,EAAA,MAAAzmB,OAAUA,EAAM,CAAA,IAAK,SAAW,SAAW,iUA5KvC,MAAA0D,CAGqC,EAAAjD,EACrC,CAAA,MAAAimB,EAA4B,MAAS,EAAAjmB,GACrC,WAAAkmB,CAAmB,EAAAlmB,EAEnB,CAAA,OAAAmC,EAAyC,QAAQ,EAAAnC,EACjD,CAAA,KAAAmmB,EAAwD,QAAQ,EAAAnmB,GAChE,MAAA+f,CAAuB,EAAA/f,EACvB,CAAA,UAAAD,EAAY,EAAK,EAAAC,EACjB,CAAA,QAAAC,EAAU,EAAK,EAAAD,GACf,cAAAG,CAAsB,EAAAH,GACtB,aAAA4f,CAAoB,EAAA5f,EACpB,CAAA,YAAA2f,EAAc,SAAS,EAAA3f,GACvB,aAAA6f,CAAY,EAAA7f,EACZ,CAAA,WAAAomB,EAAa,EAAK,EAAApmB,EAEzBqmB,EACA1T,EAGH1P,IACCd,IAAW,UAAYA,IAAW,WACnCgkB,IAAS,WAETljB,GAAU,MAAOA,EAAiB,KAAM,IAAI,GAGpC,SAAAqjB,IAAgB,OAAAC,GAAM,CAC1BJ,IAAS,eACZ1kB,EAAA,GAAA+kB,GAAeD,CAAM,MAErBtjB,GACEd,IAAW,UAAYA,IAAW,WAAagkB,IAAS,SACpD,CAAA,MAAOI,EAAQ,KAAM,IAAI,EAC3BA,CAAM,EAEXlmB,EAAS,SAAUkmB,CAAM,EAGjB,SAAAE,GAAe,OAAAF,GAAM,CAC7B9kB,EAAA,EAAAwB,EAAQ,IAAI,EACZxB,EAAA,GAAA+kB,GAAe,MAAS,EACxBnmB,EAAS,OAAO,iBAGFqmB,EAAW,CACvB,OAAAH,CAAM,EACRI,EAAO,CAEHzmB,IAAS,OACRiC,IAAW,UAAYwkB,EAC1BllB,EAAA,EAAAwB,GACC,MAAOsjB,EACP,KAAM,IAAA,CAAA,MAGPtjB,EAAK,CACJ,MAAK,OAASA,GAAU,SAAWA,EAAQA,GAAO,OAAS,KAC3D,KAAMsjB,KAIPpkB,IAAW,UAAYA,IAAW,WACnCgkB,IAAS,SAET1kB,EAAA,EAAAwB,GAAU,MAAOsjB,EAAQ,KAAM,IAAI,CAAA,EAEnC9kB,EAAA,EAAAwB,EAAQsjB,CAAM,QAGTnF,GAAI,EAEV/gB,EAASN,EAAY,SAAW,MAAM,EAGjC,MAAAM,EAAWC,KAUb,IAAAsmB,EAAW,GAIN,SAAAC,EAAkBvc,EAAY,OAChC1B,EAAU0B,EAAM,mBACtB0a,EAAYpc,EAAQ,YAAY,OAChCqc,GAAarc,EAAQ,aAAa,EAClCnH,EAAA,GAAAqe,EAAmBlX,EAAQ,sBAAqB,EAAG,MAAM,iBAG3Cke,IAAmB,CACjCT,EAAO,MAAK,QACNjF,GAAI,EACV3f,EAAA,EAAAwB,EAAQ,IAAI,EACZxB,EAAA,GAAA+kB,GAAe,MAAS,iBAGVO,IAAiB,CAC/BV,EAAO,WAAU,QACXjF,GAAI,EAGP,IAAA6D,GAAa,EACbD,EAAY,EACZlF,EAAmB,EAEnB5f,EAgBAwf,EACAsH,GACAC,GAEAT,GAmBJjmB,GAAO,SAAA,CACF4lB,IAAS,gBAAkBljB,GAAgB,OAAAA,GAAU,WACxDxB,EAAA,GAAA+kB,GAAevjB,CAAK,QACdme,GAAI,EACVyF,EAAiB,CAAG,cAAenH,CAAS,CAAA,KAIrC,SAAAwH,EAAaC,EAAe,KAChCC,EAAcC,GAAiCF,CAAG,EAClDC,GACH/mB,EAAS,SAAY,CAAA,MAAO+mB,EAAa,MAAO,IAAI,CAAA,4CA4B/BzU,EAAOjR,mBACF,MAAA4lB,GAAAhmB,IAAOmlB,EAAanlB,CAAC,EAAIG,EAAA,EAAA0kB,EAAO,QAAQ,GAGhDoB,GAAA,IAAA9lB,EAAA,EAAA0kB,EAAO,QAAQ,4CAkBnBzG,EAAShe,sDAWT2kB,EAAM3kB,gEAeF,MAAA8lB,GAAA,IAAAnB,EAAO,sFA8BV,MAAAoB,GAAA,IAAApB,EAAO,yJAgBXA,EAAM3kB,qBAYHJ,GACZ6kB,IAAS,eAAiBG,GAAchlB,CAAC,EAAIolB,EAAYplB,EAAG,EAAI,6EAS/CqR,EAAOjR,mBACF,MAAAgmB,GAAApmB,IAAOmlB,EAAanlB,CAAC,EAAIG,EAAA,EAAA0kB,EAAO,QAAQ,GAGhDwB,GAAA,IAAAlmB,EAAA,EAAA0kB,EAAO,QAAQ,4CAmBnBzG,EAAShe,sDAWT2kB,EAAM3kB,gEAYF,MAAAkmB,EAAA,IAAAvB,EAAO,uEAhLNW,GAAU,KAAA,aACXC,GAAS,KAAA,gkBA1FxB5mB,EAAS,OAAQumB,CAAQ,qBA4BvBzkB,IAAW,UAAYgkB,IAAS,SACnC1kB,EAAA,GAAAvB,EAAO,WAAW,EACRimB,IAAS,eACnB1kB,EAAA,GAAAvB,EAAO,cAAc,GAEpBiC,IAAW,UAAYA,IAAW,WACnCgkB,IAAS,SAET1kB,EAAA,GAAAvB,EAAO,MAAM,EAEbuB,EAAA,GAAAvB,EAAO,QAAQ,qBAUZ+C,IAAU,MAASA,EAAM,QAAU,MAAQA,EAAM,OAAS,OAC7DxB,EAAA,GAAA+kB,GAAe,MAAS,sBAKrB7T,IACC1P,QACH0P,EAAQ,MAAQ1P,EAAK0P,CAAA,EACrBA,EAAQ,OAAM,GAEdA,EAAQ,QAAO,onBCjGCpT,EAAc,CAAA,CAAA,wrBAAdA,EAAc,CAAA,CAAA,CAAA,CAAA,iqBAZxB,QAAAA,EAAU,CAAA,IAAA,MAAQA,OAAW,SAAW,SAAW,oBAC/CA,EAAQ,EAAA,EAAG,QAAU,eACzB,yCAGDA,EAAM,EAAA,IAAKA,EAAM,CAAA,IAAK,SAAW,OAAYsoB,+BAErC,kLAPPtC,EAAA,CAAA,EAAA,KAAAuC,EAAA,QAAAvoB,EAAU,CAAA,IAAA,MAAQA,OAAW,SAAW,SAAW,sCAC/CA,EAAQ,EAAA,EAAG,QAAU,oFAI1BA,EAAM,EAAA,IAAKA,EAAM,CAAA,IAAK,SAAW,OAAYsoB,6QAZ/C,MAAAA,GAAe,uBApCV,GAAA,CAAA,QAAAE,EAAU,EAAE,EAAA/nB,GACZ,aAAAgoB,EAAY,EAAA,EAAAhoB,EACZ,CAAA,QAAAioB,EAAU,EAAI,EAAAjoB,EACd,CAAA,MAAAiD,EAAuB,IAAI,EAAAjD,EAC3B,CAAA,OAAAmC,EAAyC,QAAQ,EAAAnC,EACjD,CAAA,KAAAmmB,EAAwD,QAAQ,EAAAnmB,GAChE,MAAAimB,CAAa,EAAAjmB,GACb,WAAAkmB,CAAmB,EAAAlmB,GACnB,UAAAD,CAAkB,EAAAC,GAClB,QAAAC,CAAgB,EAAAD,GAChB,OAAA4N,CAA0B,EAAA5N,GAC1B,MAAA6N,CAAyB,EAAA7N,GACzB,cAAAG,CAAsB,EAAAH,GACtB,MAAA+f,CAAuB,EAAA/f,GACvB,aAAA4f,CAAoB,EAAA5f,GACpB,YAAA2f,CAAmB,EAAA3f,GACnB,aAAA6f,CAAoB,EAAA7f,EACpB,CAAA,WAAAomB,GAAa,EAAK,EAAApmB,EAClB,CAAA,UAAA0S,EAAY,EAAI,EAAA1S,EAChB,CAAA,MAAAmY,EAAuB,IAAI,EAAAnY,EAC3B,CAAA,UAAAkoB,EAAgC,MAAS,EAAAloB,GACzC,eAAAmoB,CAA6B,EAAAnoB,GAC7B,OAAAooB,CAUT,EAAApoB,EAGE4mB,0CA+BYwB,EAAO,SAAS,MAAM,QACrBA,EAAO,SAAS,OAAO,QACtBA,EAAO,SAAS,QAAQ,MAC7B,OAAA7B,CAAM,IAAA9kB,EAAA,GAAQmlB,GAAWL,CAAM,QAC1B6B,EAAO,SAAS,QAAQ,EAC3BC,GAAA,CAAA,CAAA,OAAA9B,KAAa6B,EAAO,SAAS,SAAU7B,CAAM,EAC9C+B,GAAA,CAAA,CAAA,OAAA/B,KAAa6B,EAAO,SAAS,QAAS7B,CAAM,OAC5C,OAAAA,KAAM,CAClB9kB,EAAA,EAAA0mB,EAAiBA,GAAc,CAAA,CAAA,MAC/BA,EAAe,OAAS,QAAOA,CAAA,EAC/BC,EAAO,SAAS,QAAS7B,CAAM,m2BAtC9BtjB,EAASA,GAAQ,IAAY,yBAJtBmlB,EAAO,SAAS,QAAQ", "x_google_ignoreList": [8, 10, 11, 12, 13]}