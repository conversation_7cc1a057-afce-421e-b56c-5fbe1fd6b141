`ImageBlur`ノードは、画像にガウスぼかしを適用し、エッジを柔らかくし、ディテールやノイズを低減します。パラメータを通じて、ぼかしの強度と広がりを制御できます。

## 入力

| フィールド          | Data Type | 説明                                                                   |
|----------------|-------------|-------------------------------------------------------------------------------|
| `image`        | `IMAGE`     | ぼかしを適用する入力画像です。これはぼかし効果の主な対象です。 |
| `blur_radius`  | `INT`       | ぼかし効果の半径を決定します。半径が大きいほど、より顕著なぼかしになります。 |
| `sigma`        | `FLOAT`     | ぼかしの広がりを制御します。シグマ値が高いほど、各ピクセル周辺の広い範囲にぼかしが影響します。 |

## 出力

| フィールド | Data Type | 説明                                                              |
|-------|-------------|--------------------------------------------------------------------------|
| `image`| `IMAGE`     | 出力は、入力パラメータによって決定されるぼかしの度合いを持つ入力画像のぼかしバージョンです。 |
