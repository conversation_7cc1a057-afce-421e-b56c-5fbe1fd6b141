{"version": 3, "file": "index-d5b24dc8.js", "sources": ["../../../../node_modules/.pnpm/d3-dsv@3.0.1/node_modules/d3-dsv/src/tsv.js", "../../../../js/number/example/Number.svelte", "../../../../js/dropdown/example/Dropdown.svelte", "../../../../js/checkbox/example/Checkbox.svelte", "../../../../js/checkboxgroup/example/Checkboxgroup.svelte", "../../../../js/slider/example/Slider.svelte", "../../../../js/radio/example/Radio.svelte", "../../../../js/image/example/Image.svelte", "../../../../js/textbox/example/Textbox.svelte", "../../../../js/audio/example/Audio.svelte", "../../../../js/video/example/Video.svelte", "../../../../js/file/example/File.svelte", "../../../../js/dataframe/example/Dataframe.svelte", "../../../../js/model3D/example/Model3d.svelte", "../../../../js/colorpicker/example/Colorpicker.svelte", "../../../../js/timeseries/example/Timeseries.svelte", "../../../../js/markdown/example/Markdown.svelte", "../../../../js/html/example/Html.svelte", "../../../../js/code/example/Code.svelte", "../../../../js/app/src/components/Dataset/directory.ts", "../../../../js/app/src/components/Dataset/Dataset.svelte"], "sourcesContent": ["import dsv from \"./dsv.js\";\n\nvar tsv = dsv(\"\\t\");\n\nexport var tsvParse = tsv.parse;\nexport var tsvParseRows = tsv.parseRows;\nexport var tsvFormat = tsv.format;\nexport var tsvFormatBody = tsv.formatBody;\nexport var tsvFormatRows = tsv.formatRows;\nexport var tsvFormatRow = tsv.formatRow;\nexport var tsvFormatValue = tsv.formatValue;\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: boolean;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value.toLocaleString()}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string[];\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{#each value as check, i}{check.toLocaleString()}{#if i !== value.length - 1},&nbsp;{/if}{/each}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let samples_dir: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<!-- TODO: fix -->\n<!-- svelte-ignore a11y-missing-attribute -->\n<img\n\tsrc={samples_dir + value}\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n/>\n\n<style>\n\timg {\n\t\tborder-radius: var(--radius-lg);\n\t\tmax-width: none;\n\t}\n\n\timg.selected {\n\t\tborder-color: var(--border-color-accent);\n\t}\n\n\t.table {\n\t\tmargin: 0 auto;\n\t\tborder: 2px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-lg);\n\t\twidth: var(--size-20);\n\t\theight: var(--size-20);\n\t\tobject-fit: cover;\n\t}\n\n\t.gallery {\n\t\tborder: 2px solid var(--border-color-primary);\n\t\tmax-height: var(--size-20);\n\t\tobject-fit: cover;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n\n\tlet size: number;\n\tlet el: HTMLDivElement;\n\n\tfunction set_styles(element: HTMLElement, el_width: number): void {\n\t\tif (!element || !el_width) return;\n\t\tel.style.setProperty(\n\t\t\t\"--local-text-width\",\n\t\t\t`${el_width < 150 ? el_width : 200}px`\n\t\t);\n\t\tel.style.whiteSpace = \"unset\";\n\t}\n\n\tonMount(() => {\n\t\tset_styles(el, size);\n\t});\n</script>\n\n<div\n\tbind:clientWidth={size}\n\tbind:this={el}\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n\n\tdiv {\n\t\toverflow: hidden;\n\t\tmin-width: var(--local-text-width);\n\n\t\twhite-space: nowrap;\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { playable } from \"../shared\";\n\timport { onMount } from \"svelte\";\n\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n\texport let value: string;\n\texport let samples_dir: string;\n\tlet video: HTMLVideoElement;\n\n\tasync function init(): Promise<void> {\n\t\tvideo.muted = true;\n\t\tvideo.playsInline = true;\n\t\tvideo.controls = false;\n\t\tvideo.setAttribute(\"muted\", \"\");\n\n\t\tawait video.play();\n\t\tvideo.pause();\n\t}\n\n\tonMount(() => {\n\t\tinit();\n\t});\n</script>\n\n{#if playable()}\n\t<video\n\t\tmuted\n\t\tplaysinline\n\t\tbind:this={video}\n\t\tclass:table={type === \"table\"}\n\t\tclass:gallery={type === \"gallery\"}\n\t\tclass:selected\n\t\ton:mouseover={video.play}\n\t\ton:mouseout={video.pause}\n\t\tsrc={samples_dir + value}\n\t/>\n{:else}\n\t<div>{value}</div>\n{/if}\n\n<style>\n\tvideo {\n\t\tflex: none;\n\t\tborder: 2px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-lg);\n\t\tmax-width: none;\n\t}\n\n\tvideo:hover,\n\tvideo.selected {\n\t\tborder-color: var(--border-color-accent);\n\t}\n\t.table {\n\t\tmargin: 0 auto;\n\t\twidth: var(--size-20);\n\t\theight: var(--size-20);\n\t\tobject-fit: cover;\n\t}\n\n\t.gallery {\n\t\tmax-height: var(--size-20);\n\t\tobject-fit: cover;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { FileData } from \"@gradio/upload\";\n\n\texport let value: FileData;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{Array.isArray(value) ? value.join(\", \") : value}\n</div>\n\n<style>\n\tdiv {\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t}\n\t.gallery {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tcursor: pointer;\n\t\tpadding: var(--size-1) var(--size-2);\n\t\ttext-align: left;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { csvParseRows, tsvParseRows } from \"d3-dsv\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let value: (string | number)[][] | string;\n\texport let samples_dir: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n\texport let index: number;\n\n\tlet hovered = false;\n\tlet loaded_value: (string | number)[][] | string = value;\n\tlet loaded = Array.isArray(loaded_value);\n\n\t$: if (!loaded && typeof value === \"string\" && /\\.[a-zA-Z]+$/.test(value)) {\n\t\tfetch(samples_dir + value)\n\t\t\t.then((v) => v.text())\n\t\t\t.then((v) => {\n\t\t\t\ttry {\n\t\t\t\t\tif ((value as string).endsWith(\"csv\")) {\n\t\t\t\t\t\tconst small_df = v\n\t\t\t\t\t\t\t.split(\"\\n\")\n\t\t\t\t\t\t\t.slice(0, 4)\n\t\t\t\t\t\t\t.map((v) => v.split(\",\").slice(0, 4).join(\",\"))\n\t\t\t\t\t\t\t.join(\"\\n\");\n\n\t\t\t\t\t\tloaded_value = csvParseRows(small_df);\n\t\t\t\t\t} else if ((value as string).endsWith(\"tsv\")) {\n\t\t\t\t\t\tconst small_df = v\n\t\t\t\t\t\t\t.split(\"\\n\")\n\t\t\t\t\t\t\t.slice(0, 4)\n\t\t\t\t\t\t\t.map((v) => v.split(\"\\t\").slice(0, 4).join(\"\\t\"))\n\t\t\t\t\t\t\t.join(\"\\n\");\n\n\t\t\t\t\t\tloaded_value = tsvParseRows(small_df);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error($_(\"dataframe.incorrect_format\"));\n\t\t\t\t\t}\n\n\t\t\t\t\tloaded = true;\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error(e);\n\t\t\t\t}\n\t\t\t})\n\t\t\t.catch((e) => {\n\t\t\t\tloaded_value = value;\n\t\t\t\tloaded = true;\n\t\t\t});\n\t}\n</script>\n\n{#if loaded}\n\t<!-- TODO: fix-->\n\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t<div\n\t\tclass:table={type === \"table\"}\n\t\tclass:gallery={type === \"gallery\"}\n\t\tclass:selected\n\t\ton:mouseenter={() => (hovered = true)}\n\t\ton:mouseleave={() => (hovered = false)}\n\t>\n\t\t{#if typeof loaded_value === \"string\"}\n\t\t\t{loaded_value}\n\t\t{:else}\n\t\t\t<table class=\"\">\n\t\t\t\t{#each loaded_value.slice(0, 3) as row, i}\n\t\t\t\t\t<tr>\n\t\t\t\t\t\t{#each row.slice(0, 3) as cell, j}\n\t\t\t\t\t\t\t<td>{cell}</td>\n\t\t\t\t\t\t{/each}\n\t\t\t\t\t\t{#if row.length > 3}\n\t\t\t\t\t\t\t<td>…</td>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</tr>\n\t\t\t\t{/each}\n\t\t\t\t{#if value.length > 3}\n\t\t\t\t\t<div\n\t\t\t\t\t\tclass=\"overlay\"\n\t\t\t\t\t\tclass:odd={index % 2 != 0}\n\t\t\t\t\t\tclass:even={index % 2 == 0}\n\t\t\t\t\t\tclass:button={type === \"gallery\"}\n\t\t\t\t\t/>\n\t\t\t\t{/if}\n\t\t\t</table>\n\t\t{/if}\n\t</div>\n{/if}\n\n<style>\n\ttable {\n\t\tposition: relative;\n\t}\n\n\ttd {\n\t\tborder: 1px solid var(--table-border-color);\n\t\tpadding: var(--size-2);\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t}\n\n\t.selected td {\n\t\tborder-color: var(--border-color-accent);\n\t}\n\n\t.table {\n\t\tdisplay: inline-block;\n\t\tmargin: 0 auto;\n\t}\n\n\t.gallery td:first-child {\n\t\tborder-left: none;\n\t}\n\n\t.gallery tr:first-child td {\n\t\tborder-top: none;\n\t}\n\n\t.gallery td:last-child {\n\t\tborder-right: none;\n\t}\n\n\t.gallery tr:last-child td {\n\t\tborder-bottom: none;\n\t}\n\n\t.overlay {\n\t\t--gradient-to: transparent;\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tbackground: linear-gradient(to bottom, transparent, var(--gradient-to));\n\t\twidth: var(--size-full);\n\t\theight: 50%;\n\t}\n\n\t/* i dont know what i've done here but it is what it is */\n\t.odd {\n\t\t--gradient-to: var(--table-even-background-fill);\n\t}\n\n\t.even {\n\t\t--gradient-to: var(--table-odd-background-fill);\n\t}\n\n\t.button {\n\t\t--gradient-to: var(--background-fill-primary);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tstyle=\"background-color: {value}\"\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n/>\n\n<style>\n\tdiv {\n\t\twidth: var(--size-10);\n\t\theight: var(--size-10);\n\t}\n\t.table {\n\t\tmargin: 0 auto;\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n\tclass=\"prose\"\n>\n\t{@html value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n\tclass=\"prose\"\n>\n\t{@html value}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<pre\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected>{value}</pre>\n\n<style>\n\tpre {\n\t\ttext-align: left;\n\t}\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n", "import ExampleNumber from \"@gradio/number/example\";\nimport ExampleDropdown from \"@gradio/dropdown/example\";\nimport ExampleCheckbox from \"@gradio/checkbox/example\";\nimport ExampleCheckboxGroup from \"@gradio/checkboxgroup/example\";\nimport ExampleSlider from \"@gradio/slider/example\";\nimport ExampleRadio from \"@gradio/radio/example\";\nimport ExampleImage from \"@gradio/image/example\";\nimport ExampleTextbox from \"@gradio/textbox/example\";\nimport ExampleAudio from \"@gradio/audio/example\";\nimport ExampleVideo from \"@gradio/video/example\";\nimport ExampleFile from \"@gradio/file/example\";\nimport ExampleDataframe from \"@gradio/dataframe/example\";\nimport ExampleModel3D from \"@gradio/model3d/example\";\nimport ExampleColorPicker from \"@gradio/colorpicker/example\";\nimport ExampleTimeSeries from \"@gradio/timeseries/example\";\nimport ExampleMarkdown from \"@gradio/markdown/example\";\nimport ExampleHTML from \"@gradio/html/example\";\nimport ExampleCode from \"@gradio/code/example\";\n\nexport const component_map = {\n\tdropdown: ExampleDropdown,\n\tcheckbox: ExampleCheckbox,\n\tcheckboxgroup: ExampleCheckboxGroup,\n\tnumber: ExampleNumber,\n\tslider: ExampleSlider,\n\tradio: ExampleRadio,\n\timage: ExampleImage,\n\ttextbox: ExampleTextbox,\n\taudio: ExampleAudio,\n\tvideo: ExampleVideo,\n\tfile: ExampleFile,\n\tdataframe: ExampleDataframe,\n\tmodel3d: ExampleModel3D,\n\tcolorpicker: ExampleColorPicker,\n\ttimeseries: ExampleTimeSeries,\n\tmarkdown: ExampleMarkdown,\n\thtml: ExampleHTML,\n\tcode: ExampleCode\n};\n", "<script lang=\"ts\">\n\timport { Block } from \"@gradio/atoms\";\n\timport type { SvelteComponent, ComponentType } from \"svelte\";\n\timport { component_map } from \"./directory\";\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { get_fetchable_url_or_file } from \"@gradio/upload\";\n\texport let components: (keyof typeof component_map)[];\n\texport let label = \"Examples\";\n\texport let headers: string[];\n\texport let samples: any[][];\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: number | null = null;\n\texport let root: string;\n\texport let root_url: null | string;\n\texport let samples_per_page = 10;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let gradio: Gradio<{\n\t\tclick: number;\n\t\tselect: SelectData;\n\t}>;\n\n\tlet samples_dir: string = get_fetchable_url_or_file(null, root, root_url);\n\tlet page = 0;\n\t$: gallery = components.length < 2;\n\tlet paginate = samples.length > samples_per_page;\n\n\tlet selected_samples: any[][];\n\tlet page_count: number;\n\tlet visible_pages: number[] = [];\n\n\tlet current_hover = -1;\n\n\tfunction handle_mouseenter(i: number): void {\n\t\tcurrent_hover = i;\n\t}\n\tfunction handle_mouseleave(): void {\n\t\tcurrent_hover = -1;\n\t}\n\n\t$: {\n\t\tif (paginate) {\n\t\t\tvisible_pages = [];\n\t\t\tselected_samples = samples.slice(\n\t\t\t\tpage * samples_per_page,\n\t\t\t\t(page + 1) * samples_per_page\n\t\t\t);\n\t\t\tpage_count = Math.ceil(samples.length / samples_per_page);\n\t\t\t[0, page, page_count - 1].forEach((anchor) => {\n\t\t\t\tfor (let i = anchor - 2; i <= anchor + 2; i++) {\n\t\t\t\t\tif (i >= 0 && i < page_count && !visible_pages.includes(i)) {\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\tvisible_pages.length > 0 &&\n\t\t\t\t\t\t\ti - visible_pages[visible_pages.length - 1] > 1\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tvisible_pages.push(-1);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvisible_pages.push(i);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\tselected_samples = samples.slice();\n\t\t}\n\t}\n\n\t$: component_meta = selected_samples.map((sample_row) =>\n\t\tsample_row.map((sample_cell, j) => ({\n\t\t\tvalue: sample_cell,\n\t\t\tcomponent: component_map[components[j]] as ComponentType<SvelteComponent>\n\t\t}))\n\t);\n</script>\n\n<Block\n\t{visible}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n\tcontainer={false}\n>\n\t<div class=\"label\">\n\t\t<svg\n\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\t\t\taria-hidden=\"true\"\n\t\t\trole=\"img\"\n\t\t\twidth=\"1em\"\n\t\t\theight=\"1em\"\n\t\t\tpreserveAspectRatio=\"xMidYMid meet\"\n\t\t\tviewBox=\"0 0 32 32\"\n\t\t>\n\t\t\t<path\n\t\t\t\tfill=\"currentColor\"\n\t\t\t\td=\"M10 6h18v2H10zm0 18h18v2H10zm0-9h18v2H10zm-6 0h2v2H4zm0-9h2v2H4zm0 18h2v2H4z\"\n\t\t\t/>\n\t\t</svg>\n\t\t{label}\n\t</div>\n\t{#if gallery}\n\t\t<div class=\"gallery\">\n\t\t\t{#each selected_samples as sample_row, i}\n\t\t\t\t<button\n\t\t\t\t\tclass=\"gallery-item\"\n\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\tvalue = i + page * samples_per_page;\n\t\t\t\t\t\tgradio.dispatch(\"click\", value);\n\t\t\t\t\t\tgradio.dispatch(\"select\", { index: value, value: sample_row });\n\t\t\t\t\t}}\n\t\t\t\t\ton:mouseenter={() => handle_mouseenter(i)}\n\t\t\t\t\ton:mouseleave={() => handle_mouseleave()}\n\t\t\t\t>\n\t\t\t\t\t{#if Object.keys(component_map).includes(components[0]) && component_map[components[0]]}\n\t\t\t\t\t\t<svelte:component\n\t\t\t\t\t\t\tthis={component_meta[0][0].component}\n\t\t\t\t\t\t\tvalue={sample_row[0]}\n\t\t\t\t\t\t\t{samples_dir}\n\t\t\t\t\t\t\ttype=\"gallery\"\n\t\t\t\t\t\t\tselected={current_hover === i}\n\t\t\t\t\t\t\tindex={i}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{/if}\n\t\t\t\t</button>\n\t\t\t{/each}\n\t\t</div>\n\t{:else}\n\t\t<div class=\"table-wrap\">\n\t\t\t<table>\n\t\t\t\t<thead>\n\t\t\t\t\t<tr class=\"tr-head\">\n\t\t\t\t\t\t{#each headers as header}\n\t\t\t\t\t\t\t<th>\n\t\t\t\t\t\t\t\t{header}\n\t\t\t\t\t\t\t</th>\n\t\t\t\t\t\t{/each}\n\t\t\t\t\t</tr>\n\t\t\t\t</thead>\n\t\t\t\t<tbody>\n\t\t\t\t\t{#each component_meta as sample_row, i}\n\t\t\t\t\t\t<tr\n\t\t\t\t\t\t\tclass=\"tr-body\"\n\t\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\t\tvalue = i + page * samples_per_page;\n\t\t\t\t\t\t\t\tgradio.dispatch(\"click\", value);\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\ton:mouseenter={() => handle_mouseenter(i)}\n\t\t\t\t\t\t\ton:mouseleave={() => handle_mouseleave()}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{#each sample_row as { value, component }, j}\n\t\t\t\t\t\t\t\t{@const component_name = components[j]}\n\t\t\t\t\t\t\t\t{#if component_name !== undefined && component_map[component_name] !== undefined}\n\t\t\t\t\t\t\t\t\t<td\n\t\t\t\t\t\t\t\t\t\tstyle=\"max-width: {component_name === 'textbox'\n\t\t\t\t\t\t\t\t\t\t\t? '35ch'\n\t\t\t\t\t\t\t\t\t\t\t: 'auto'}\"\n\t\t\t\t\t\t\t\t\t\tclass={component_name}\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<svelte:component\n\t\t\t\t\t\t\t\t\t\t\tthis={component}\n\t\t\t\t\t\t\t\t\t\t\t{value}\n\t\t\t\t\t\t\t\t\t\t\t{samples_dir}\n\t\t\t\t\t\t\t\t\t\t\ttype=\"table\"\n\t\t\t\t\t\t\t\t\t\t\tselected={current_hover === i}\n\t\t\t\t\t\t\t\t\t\t\tindex={i}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t{/each}\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t{/each}\n\t\t\t\t</tbody>\n\t\t\t</table>\n\t\t</div>\n\t{/if}\n\t{#if paginate}\n\t\t<div class=\"paginate\">\n\t\t\tPages:\n\t\t\t{#each visible_pages as visible_page}\n\t\t\t\t{#if visible_page === -1}\n\t\t\t\t\t<div>...</div>\n\t\t\t\t{:else}\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass:current-page={page === visible_page}\n\t\t\t\t\t\ton:click={() => (page = visible_page)}\n\t\t\t\t\t>\n\t\t\t\t\t\t{visible_page + 1}\n\t\t\t\t\t</button>\n\t\t\t\t{/if}\n\t\t\t{/each}\n\t\t</div>\n\t{/if}\n</Block>\n\n<style>\n\t.wrap {\n\t\tdisplay: inline-block;\n\t\twidth: var(--size-full);\n\t\tmax-width: var(--size-full);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.hide {\n\t\tdisplay: none;\n\t}\n\n\t.label {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: var(--size-2);\n\t\tcolor: var(--block-label-text-color);\n\t\tfont-weight: var(--block-label-text-weight);\n\t\tfont-size: var(--block-label-text-size);\n\t\tline-height: var(--line-sm);\n\t}\n\n\tsvg {\n\t\tmargin-right: var(--size-1);\n\t}\n\n\t.gallery {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: var(--spacing-lg);\n\t}\n\n\t.gallery-item {\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--button-large-radius);\n\t\toverflow: hidden;\n\t}\n\n\t.gallery-item:hover {\n\t\tborder-color: var(--border-color-accent);\n\t\tbackground: var(--table-row-focus);\n\t}\n\n\t.table-wrap {\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--table-radius);\n\t\twidth: var(--size-full);\n\t\ttable-layout: auto;\n\t\toverflow-x: auto;\n\t\tline-height: var(--line-sm);\n\t}\n\ttable {\n\t\twidth: var(--size-full);\n\t}\n\n\t.tr-head {\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t}\n\n\t.tr-head > * + * {\n\t\tborder-right-width: 0px;\n\t\tborder-left-width: 1px;\n\t\tborder-color: var(--border-color-primary);\n\t}\n\n\tth {\n\t\tpadding: var(--size-2);\n\t\twhite-space: nowrap;\n\t}\n\n\t.tr-body {\n\t\tcursor: pointer;\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t\tbackground: var(--table-even-background-fill);\n\t}\n\n\t.tr-body:last-child {\n\t\tborder: none;\n\t}\n\n\t.tr-body:nth-child(odd) {\n\t\tbackground: var(--table-odd-background-fill);\n\t}\n\n\t.tr-body:hover {\n\t\tbackground: var(--table-row-focus);\n\t}\n\n\t.tr-body > * + * {\n\t\tborder-right-width: 0px;\n\t\tborder-left-width: 1px;\n\t\tborder-color: var(--border-color-primary);\n\t}\n\n\t.tr-body:hover > * + * {\n\t\tborder-color: var(--border-color-accent);\n\t}\n\n\ttd {\n\t\tpadding: var(--size-2);\n\t\ttext-align: center;\n\t}\n\n\t.paginate {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tgap: var(--spacing-sm);\n\t\tmargin-top: var(--size-2);\n\t\tcolor: var(--block-label-text-color);\n\t\tfont-size: var(--text-sm);\n\t}\n\n\tbutton.current-page {\n\t\tfont-weight: var(--weight-bold);\n\t}\n</style>\n"], "names": ["tsv", "dsv", "tsvParseRows", "ctx", "toggle_class", "div", "insert", "target", "anchor", "value", "$$props", "type", "selected", "t_value", "dirty", "set_data", "if_block", "create_if_block", "t", "i", "src_url_equal", "img", "img_src_value", "attr", "samples_dir", "size", "el", "set_styles", "element", "el_width", "$$invalidate", "onMount", "$$value", "video_1", "video_1_src_value", "is_function", "video", "init", "create_if_block_1", "each_value", "ensure_array_like", "create_if_block_2", "table", "td", "each_value_1", "create_if_block_3", "tr", "index", "hovered", "loaded_value", "loaded", "mouseenter_handler", "mouseleave_handler", "v", "small_df", "csvParseRows", "$_", "e", "pre", "component_map", "ExampleDropdown", "ExampleCheckbox", "ExampleCheckboxGroup", "ExampleNumber", "ExampleSlider", "ExampleRadio", "ExampleImage", "ExampleTextbox", "ExampleAudio", "ExampleVideo", "ExampleFile", "ExampleDataframe", "ExampleModel3D", "ExampleColorPicker", "ExampleTimeSeries", "ExampleMarkdown", "ExampleHTML", "ExampleCode", "constants_0", "child_ctx", "append", "thead", "tbody", "each_blocks", "th", "t0", "t0_value", "set_style", "switch_instance_changes", "create_if_block_4", "switch_value", "show_if", "button", "svg", "path", "if_block1", "components", "label", "headers", "samples", "elem_id", "elem_classes", "visible", "root", "root_url", "samples_per_page", "scale", "min_width", "gradio", "get_fetchable_url_or_file", "page", "paginate", "selected_samples", "page_count", "visible_pages", "current_hover", "handle_mouseenter", "handle_mouseleave", "sample_row", "mouseenter_handler_1", "click_handler_2", "visible_page", "gallery", "component_meta", "sample_cell", "j"], "mappings": "4eAEA,IAAIA,GAAMC,GAAI,GAAI,EAGPC,GAAeF,GAAI,2DCM5BG,EAAK,CAAA,CAAA,gCAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,4BADHL,EAAK,CAAA,CAAA,OAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,gQCQ1BP,EAAK,CAAA,CAAA,gCAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,4BADHL,EAAK,CAAA,CAAA,OAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,oOCQ1BG,EAAAV,KAAM,eAAc,EAAA,gEAJRC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,mBADHM,EAAA,GAAAD,KAAAA,EAAAV,KAAM,eAAc,EAAA,KAAAY,EAAA,EAAAF,CAAA,OAJRT,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAc,EAAAC,GACd,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,kTCQkD,IAAO,kDAA1D,IAAAG,EAAAV,KAAM,eAAc,EAAA,OAAQa,EAAAb,EAAM,CAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,GAACc,GAAA,iFAAlDH,EAAA,GAAAD,KAAAA,EAAAV,KAAM,eAAc,EAAA,KAAAY,EAAAG,EAAAL,CAAA,EAAQV,EAAM,CAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,qHAApEA,EAAK,CAAA,CAAA,uBAAV,OAAIgB,GAAA,8GAJOf,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,yEADGL,EAAK,CAAA,CAAA,oBAAV,OAAI,GAAA,EAAA,mHAAJ,YAJWC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,kFAPtB,MAAAM,CAAe,EAAAC,GACf,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,gQCQ1BP,EAAK,CAAA,CAAA,gCAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,4BADHL,EAAK,CAAA,CAAA,OAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,gQCQ1BP,EAAK,CAAA,CAAA,gCAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,4BADHL,EAAK,CAAA,CAAA,OAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,4PCOtBU,EAAAC,EAAA,IAAAC,EAAAnB,KAAcA,EAAK,CAAA,CAAA,GAAAoB,EAAAF,EAAA,MAAAC,CAAA,+BACXlB,EAAAiB,EAAA,QAAAlB,OAAS,OAAO,EACdC,EAAAiB,EAAA,UAAAlB,OAAS,SAAS,+BAHlCG,EAKCC,EAAAc,EAAAb,CAAA,YAJKM,EAAA,GAAA,CAAAM,EAAAC,EAAA,IAAAC,EAAAnB,KAAcA,EAAK,CAAA,CAAA,qBACXC,EAAAiB,EAAA,QAAAlB,OAAS,OAAO,OACdC,EAAAiB,EAAA,UAAAlB,OAAS,SAAS,2EAXtB,MAAAM,CAAa,EAAAC,GACb,YAAAc,CAAmB,EAAAd,GACnB,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,0TC2B1BP,EAAK,CAAA,CAAA,qDAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAJlCG,EAQKC,EAAAF,EAAAG,CAAA,yDADHL,EAAK,CAAA,CAAA,OAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,0FAzBtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,EAEvBe,EACAC,WAEKC,EAAWC,EAAsBC,EAAgB,CACpD,CAAAD,IAAYC,IACjBH,EAAG,MAAM,YACR,qBACG,GAAAG,EAAW,IAAMA,EAAW,OAAG,EAEnCC,EAAA,EAAAJ,EAAG,MAAM,WAAa,QAAOA,CAAA,GAG9BK,GAAO,IAAA,CACNJ,EAAWD,EAAID,CAAI,iBAKFA,EAAI,KAAA,6DACXC,EAAEM,iRCfZ7B,EAAK,CAAA,CAAA,gCAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,4BADHL,EAAK,CAAA,CAAA,OAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,8RCgCrBU,EAAAa,EAAA,IAAAC,EAAA/B,KAAcA,EAAK,CAAA,CAAA,GAAAoB,EAAAU,EAAA,MAAAC,CAAA,gCALX9B,EAAA6B,EAAA,QAAA9B,OAAS,OAAO,EACdC,EAAA6B,EAAA,UAAA9B,OAAS,SAAS,+BALlCG,EAUCC,EAAA0B,EAAAzB,CAAA,4CAHc2B,GAAAhC,EAAM,CAAA,EAAA,IAAI,GAAVA,KAAM,KAAI,MAAA,KAAA,SAAA,8BACXgC,GAAAhC,EAAM,CAAA,EAAA,KAAK,GAAXA,KAAM,MAAK,MAAA,KAAA,SAAA,uBACnBW,EAAA,IAAA,CAAAM,EAAAa,EAAA,IAAAC,EAAA/B,KAAcA,EAAK,CAAA,CAAA,qBALXC,EAAA6B,EAAA,QAAA9B,OAAS,OAAO,OACdC,EAAA6B,EAAA,UAAA9B,OAAS,SAAS,sGANtB,OAAAc,0IArBD,KAAAN,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,GAChB,MAAAD,CAAa,EAAAC,GACb,YAAAc,CAAmB,EAAAd,EAC1B0B,iBAEWC,GAAI,KAClBD,EAAM,MAAQ,GAAIA,CAAA,MAClBA,EAAM,YAAc,GAAIA,CAAA,MACxBA,EAAM,SAAW,GAAKA,CAAA,EACtBA,EAAM,aAAa,QAAS,EAAE,EAExB,MAAAA,EAAM,OACZA,EAAM,MAAK,EAGZL,GAAO,IAAA,CACNM,gDAQWD,EAAKJ,4SChBhB,MAAM,QAAQ7B,EAAK,CAAA,CAAA,EAAIA,EAAK,CAAA,EAAC,KAAK,IAAI,EAAIA,EAAK,CAAA,GAAA,+DAJnCC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,gCADH,MAAM,QAAQL,EAAK,CAAA,CAAA,EAAIA,EAAK,CAAA,EAAC,KAAK,IAAI,EAAIA,EAAK,CAAA,GAAA,KAAAY,EAAA,EAAAF,CAAA,OAJnCT,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAe,EAAAC,GACf,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,0XCwDd,OAAA,OAAAP,MAAiB,SAAQmC,kFANxBlC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EA+BKC,EAAAF,EAAAG,CAAA,wJA9BSJ,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,mFASxBoC,EAAAC,EAAArC,EAAa,CAAA,EAAA,MAAM,EAAG,CAAC,CAAA,uBAA5B,OAAI,GAAA,2BAUDA,EAAK,CAAA,EAAC,OAAS,GAACsC,GAAAtC,CAAA,qHAXtBG,EAmBOC,EAAAmC,EAAAlC,CAAA,0FAlBC+B,EAAAC,EAAArC,EAAa,CAAA,EAAA,MAAM,EAAG,CAAC,CAAA,oBAA5B,OAAIgB,GAAA,EAAA,gHAAJ,OAUGhB,EAAK,CAAA,EAAC,OAAS,gIAbpBA,EAAY,CAAA,CAAA,qCAAZA,EAAY,CAAA,CAAA,yCAMJA,EAAI,EAAA,EAAA,uEAATG,EAAcC,EAAAoC,EAAAnC,CAAA,8BAATL,EAAI,EAAA,EAAA,KAAAY,EAAA,EAAAF,CAAA,oHAGTP,EAASC,EAAAoC,EAAAnC,CAAA,yCAJHoC,EAAAJ,EAAArC,EAAI,EAAA,EAAA,MAAM,EAAG,CAAC,CAAA,uBAAnB,OAAI,GAAA,2BAGDA,EAAG,EAAA,EAAC,OAAS,GAAC0C,GAAA,mFAJpBvC,EAOIC,EAAAuC,EAAAtC,CAAA,0FANIoC,EAAAJ,EAAArC,EAAI,EAAA,EAAA,MAAM,EAAG,CAAC,CAAA,oBAAnB,OAAIgB,GAAA,EAAA,gHAAJ,OAGGhB,EAAG,EAAA,EAAC,OAAS,8KAQPA,EAAK,CAAA,EAAG,GAAK,CAAC,aACbA,EAAK,CAAA,EAAG,GAAK,CAAC,EACZC,EAAAC,EAAA,SAAAF,OAAS,SAAS,UAJjCG,EAKCC,EAAAF,EAAAG,CAAA,yBAHWL,EAAK,CAAA,EAAG,GAAK,CAAC,kBACbA,EAAK,CAAA,EAAG,GAAK,CAAC,OACZC,EAAAC,EAAA,SAAAF,OAAS,SAAS,yCA7BjCA,EAAM,CAAA,GAAAc,GAAAd,CAAA,mEAANA,EAAM,CAAA,wJA/CC,MAAAM,CAAqC,EAAAC,GACrC,YAAAc,CAAmB,EAAAd,GACnB,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,GAChB,MAAAqC,CAAa,EAAArC,EAEpBsC,EAAU,GACVC,EAA+CxC,EAC/CyC,EAAS,MAAM,QAAQD,CAAY,EA8ChB,MAAAE,EAAA,IAAArB,EAAA,EAAAkB,EAAU,EAAI,EACdI,EAAA,IAAAtB,EAAA,EAAAkB,EAAU,EAAK,mNA7CrC,CAAOE,GAAM,OAAWzC,GAAU,UAAY,eAAe,KAAKA,CAAK,GACvE,MAAMe,EAAcf,CAAK,EACvB,KAAM4C,GAAMA,EAAE,KACd,CAAA,EAAA,KAAMA,GAAC,QAED5C,EAAiB,SAAS,KAAK,EAAA,CAC7B,MAAA6C,EAAWD,EACf,MAAM;AAAA,CAAI,EACV,MAAM,EAAG,CAAC,EACV,IAAKA,GAAMA,EAAE,MAAM,GAAG,EAAE,MAAM,EAAG,CAAC,EAAE,KAAK,GAAG,CAC5C,EAAA,KAAK;AAAA,CAAI,MAEXJ,EAAeM,GAAaD,CAAQ,CAAA,UACzB7C,EAAiB,SAAS,KAAK,EAAA,CACpC,MAAA6C,EAAWD,EACf,MAAM;AAAA,CAAI,EACV,MAAM,EAAG,CAAC,EACV,IAAKA,GAAMA,EAAE,MAAM,GAAI,EAAE,MAAM,EAAG,CAAC,EAAE,KAAK,GAAI,CAC9C,EAAA,KAAK;AAAA,CAAI,MAEXJ,EAAe/C,GAAaoD,CAAQ,CAAA,iBAE1B,MAAME,EAAG,4BAA4B,CAAA,EAGhD1B,EAAA,EAAAoB,EAAS,EAAI,QACLO,GACR,QAAQ,MAAMA,CAAC,EAGhB,CAAA,EAAA,MAAOA,GAAC,CACR3B,EAAA,EAAAmB,EAAexC,CAAK,EACpBqB,EAAA,EAAAoB,EAAS,EAAI,6LCnCf/C,EAAK,CAAA,CAAA,gCAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,4BADHL,EAAK,CAAA,CAAA,OAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,iRCIDP,EAAK,CAAA,CAAA,+BAClBC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAHlCG,EAKCC,EAAAF,EAAAG,CAAA,wCAJ0BL,EAAK,CAAA,CAAA,OAClBC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EARtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,gQCQ1BP,EAAK,CAAA,CAAA,gCAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,4BADHL,EAAK,CAAA,CAAA,OAJOC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,8RCIdN,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAOKC,EAAAF,EAAAG,CAAA,cADGL,EAAK,CAAA,8BAALA,EAAK,CAAA,QALCC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,6RCIdN,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAOKC,EAAAF,EAAAG,CAAA,cADGL,EAAK,CAAA,8BAALA,EAAK,CAAA,QALCC,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,2EAPtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,gQCMXP,EAAK,CAAA,CAAA,+BAFRC,EAAAsD,EAAA,QAAAvD,OAAS,OAAO,EACdC,EAAAsD,EAAA,UAAAvD,OAAS,SAAS,+BAFlCG,EAG4BC,EAAAmD,EAAAlD,CAAA,4BAAXL,EAAK,CAAA,CAAA,OAFRC,EAAAsD,EAAA,QAAAvD,OAAS,OAAO,OACdC,EAAAsD,EAAA,UAAAvD,OAAS,SAAS,2EAPtB,MAAAM,CAAa,EAAAC,GACb,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,+MCgBrB,MAAMiD,EAAgB,CAC5B,SAAUC,GACV,SAAUC,GACV,cAAeC,GACf,OAAQC,GACR,OAAQC,GACR,MAAOC,GACP,MAAOC,GACP,QAASC,GACT,MAAOC,GACP,MAAOC,GACP,KAAMC,GACN,UAAWC,GACX,QAASC,GACT,YAAaC,GACb,WAAYC,GACZ,SAAUC,GACV,KAAMC,GACN,KAAMC,EACP,8MCoHiC,MAAAC,EAAAC,KAAWA,EAAC,EAAA,CAAA,kLAnBhC5E,EAAO,CAAA,CAAA,uBAAZ,OAAIgB,GAAA,6BAQAhB,EAAc,EAAA,CAAA,uBAAnB,OAAIgB,GAAA,wUAZTb,EA8CKC,EAAAF,EAAAG,CAAA,EA7CJwE,EA4CO3E,EAAAqC,CAAA,EA3CNsC,EAQOtC,EAAAuC,CAAA,EAPND,EAMIC,EAAAnC,CAAA,0DAELkC,EAiCOtC,EAAAwC,CAAA,+EAxCE/E,EAAO,CAAA,CAAA,oBAAZ,OAAIgB,GAAA,EAAA,mHAAJ,4BAQIhB,EAAc,EAAA,CAAA,oBAAnB,OAAIgB,GAAA,EAAA,2GAAJ,OAAIA,EAAAgE,EAAA,OAAAhE,GAAA,yCAAJ,OAAIA,GAAA,4IArCDhB,EAAgB,EAAA,CAAA,uBAArB,OAAI,GAAA,qKADPG,EAwBKC,EAAAF,EAAAG,CAAA,qFAvBGL,EAAgB,EAAA,CAAA,oBAArB,OAAIgB,GAAA,EAAA,2GAAJ,OAAIA,EAAAgE,EAAA,OAAAhE,GAAA,yCAAJ,OAAIA,GAAA,iIA+BAhB,EAAM,EAAA,EAAA,+EADRG,EAEIC,EAAA6E,EAAA5E,CAAA,uCADFL,EAAM,EAAA,EAAA,KAAAY,EAAAsE,EAAAC,CAAA,mDA0BEnF,EAAS,EAAA,wEAIL,SAAAA,QAAkBA,EAAC,EAAA,QACtBA,EAAC,EAAA,+DAXUoF,EAAA5C,EAAA,YAAAxC,QAAmB,UACnC,OACA,MAAM,mBACFA,EAAc,EAAA,CAAA,EAAA,iBAAA,UAJtBG,EAcIC,EAAAoC,EAAAnC,CAAA,uEAHQM,EAAA,CAAA,EAAA,QAAA0E,EAAA,SAAArF,QAAkBA,EAAC,EAAA,sBAJvBA,EAAS,EAAA,GAAA,kKANGoF,EAAA5C,EAAA,YAAAxC,QAAmB,UACnC,OACA,MAAM,yBACFA,EAAc,EAAA,CAAA,EAAA,0JALlBa,EAAAb,QAAmB,QAAawD,EAAcxD,SAAoB,QAASsF,GAAAtF,CAAA,sEAA3EA,QAAmB,QAAawD,EAAcxD,SAAoB,iNAFjEA,EAAU,EAAA,CAAA,uBAAf,OAAIgB,GAAA,4OATPb,EA6BIC,EAAAuC,EAAAtC,CAAA,wKApBIL,EAAU,EAAA,CAAA,oBAAf,OAAIgB,GAAA,EAAA,wGAAJ,OAAIA,EAAAgE,EAAA,OAAAhE,GAAA,yCAAJ,OAAIA,GAAA,6IAlCA,IAAAuE,EAAAvF,EAAe,EAAA,EAAA,CAAC,EAAE,CAAC,EAAE,sCACpB,MAAAA,MAAW,CAAC,mCAGT,SAAAA,QAAkBA,EAAC,EAAA,QACtBA,EAAC,EAAA,gHALF,GACCW,EAAA,CAAA,EAAA,OAAA0E,EAAA,MAAArF,MAAW,CAAC,GAGTW,EAAA,CAAA,EAAA,QAAA0E,EAAA,SAAArF,QAAkBA,EAAC,EAAA,GAJvBW,EAAA,CAAA,EAAA,OAAA4E,KAAAA,EAAAvF,EAAe,EAAA,EAAA,CAAC,EAAE,CAAC,EAAE,WAAS,kRAFjCwF,EAAA,OAAO,KAAKhC,CAAa,EAAE,SAASxD,EAAU,CAAA,EAAC,CAAC,CAAM,GAAAwD,EAAcxD,KAAW,CAAC,CAAA,wLAVtFG,EAoBQC,EAAAqF,EAAApF,CAAA,kHAVFM,EAAA,CAAA,EAAA,IAAA6E,EAAA,OAAO,KAAKhC,CAAa,EAAE,SAASxD,EAAU,CAAA,EAAC,CAAC,CAAM,GAAAwD,EAAcxD,KAAW,CAAC,CAAA,uMAiEhFA,EAAa,EAAA,CAAA,uBAAlB,OAAIgB,GAAA,+CAFc;AAAA,IAEpB,sFAFDb,EAcKC,EAAAF,EAAAG,CAAA,qFAZGL,EAAa,EAAA,CAAA,oBAAlB,OAAIgB,GAAA,EAAA,mHAAJ,oDAQEmE,EAAAnF,MAAe,EAAC,gHAHGC,EAAAwF,EAAA,eAAAzF,QAASA,EAAY,EAAA,CAAA,UAD1CG,EAKQC,EAAAqF,EAAApF,CAAA,uDADNM,EAAA,CAAA,EAAA,MAAAwE,KAAAA,EAAAnF,MAAe,EAAC,KAAAY,EAAAsE,EAAAC,CAAA,cAHGlF,EAAAwF,EAAA,eAAAzF,QAASA,EAAY,EAAA,CAAA,kGAH1CG,EAAaC,EAAAF,EAAAG,CAAA,2DADT,OAAAL,WAAmBmC,qQA/EtBnC,EAAO,EAAA,EAAA,gCA2EPA,EAAQ,EAAA,GAAAc,GAAAd,CAAA,2DA7EXA,EAAK,CAAA,CAAA,qdAhBPG,EAiBKC,EAAAF,EAAAG,CAAA,EAhBJwE,EAcK3E,EAAAwF,CAAA,EAJJb,EAGCa,EAAAC,CAAA,kGAED3F,EAAK,CAAA,CAAA,6IA6EFA,EAAQ,EAAA,GAAA4F,EAAA,EAAA5F,EAAAW,CAAA,gKArGJ,2EAKO,aACL,+ZA9EA,WAAAkF,CAA0C,EAAAtF,EAC1C,CAAA,MAAAuF,EAAQ,UAAU,EAAAvF,GAClB,QAAAwF,CAAiB,EAAAxF,GACjB,QAAAyF,CAAgB,EAAAzF,EAChB,CAAA,QAAA0F,EAAU,EAAE,EAAA1F,GACZ,aAAA2F,EAAY,EAAA,EAAA3F,EACZ,CAAA,QAAA4F,EAAU,EAAI,EAAA5F,EACd,CAAA,MAAAD,EAAuB,IAAI,EAAAC,GAC3B,KAAA6F,CAAY,EAAA7F,GACZ,SAAA8F,CAAuB,EAAA9F,EACvB,CAAA,iBAAA+F,EAAmB,EAAE,EAAA/F,EACrB,CAAA,MAAAgG,EAAuB,IAAI,EAAAhG,EAC3B,CAAA,UAAAiG,EAAgC,MAAS,EAAAjG,GACzC,OAAAkG,CAGT,EAAAlG,EAEEc,GAAsBqF,GAA0B,KAAMN,EAAMC,CAAQ,EACpEM,EAAO,EAEPC,GAAWZ,EAAQ,OAASM,EAE5BO,EACAC,EACAC,EAAa,CAAA,EAEbC,MAEK,SAAAC,GAAkBjG,EAAS,CACnCW,EAAA,GAAAqF,GAAgBhG,CAAC,WAETkG,IAAiB,CACzBvF,EAAA,GAAAqF,KAAkB,mBAuEdrF,EAAA,EAAArB,EAAQU,EAAI2F,EAAOL,CAAgB,EACnCG,EAAO,SAAS,QAASnG,CAAK,EAC9BmG,EAAO,SAAS,SAAQ,CAAI,MAAOnG,EAAO,MAAO6G,CAAU,CAAA,GAEvCnE,GAAAhC,GAAAiG,GAAkBjG,CAAC,SACnBkG,YAgClBvF,EAAA,EAAArB,EAAQU,EAAI2F,EAAOL,CAAgB,EACnCG,EAAO,SAAS,QAASnG,CAAK,GAEV8G,GAAApG,GAAAiG,GAAkBjG,CAAC,SACnBkG,KAqCLG,GAAAC,GAAA3F,EAAA,GAAAgF,EAAOW,CAAY,qhBAlKtCC,EAAU1B,EAAW,OAAS,CAAC,2BAiB7Be,SACHG,EAAa,CAAA,CAAA,EACbpF,EAAA,GAAAkF,EAAmBb,EAAQ,MAC1BW,EAAOL,GACNK,EAAO,GAAKL,CAAgB,CAAA,OAE9BQ,EAAa,KAAK,KAAKd,EAAQ,OAASM,CAAgB,CAAA,GACvD,EAAGK,EAAMG,EAAa,CAAC,EAAE,QAASzG,GAAM,SAC/BW,EAAIX,EAAS,EAAGW,GAAKX,EAAS,EAAGW,IACrCA,GAAK,GAAKA,EAAI8F,GAAU,CAAKC,EAAc,SAAS/F,CAAC,IAEvD+F,EAAc,OAAS,GACvB/F,EAAI+F,EAAcA,EAAc,OAAS,CAAC,EAAI,GAE9CA,EAAc,KAAI,EAAG,EAEtBA,EAAc,KAAK/F,CAAC,WAKvB6F,EAAmBb,EAAQ,MAAK,CAAA,uBAI/BrE,EAAA,GAAA6F,EAAiBX,EAAiB,IAAKM,GACzCA,EAAW,IAAK,CAAAM,EAAaC,MAAC,CAC7B,MAAOD,EACP,UAAWjE,EAAcqC,EAAW6B,EAAC,CAAA", "x_google_ignoreList": [0]}