{"version": 3, "file": "StaticForm-ed3cbc92.js", "sources": ["../../../../js/form/static/StaticForm.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let visible = true;\n\texport let scale: number | null = null;\n\texport let min_width = 0;\n</script>\n\n<div\n\tclass=\"form\"\n\tclass:hidden={!visible}\n\tstyle:flex-grow={scale}\n\tstyle:min-width={`calc(min(${min_width}px, 100%))`}\n>\n\t<slot />\n</div>\n\n<style>\n\tdiv {\n\t\tdisplay: flex;\n\t\tflex-direction: inherit;\n\t\tflex-wrap: wrap;\n\t\tgap: var(--form-gap-width);\n\t\tbox-shadow: var(--block-shadow);\n\t\tborder: var(--block-border-width) solid var(--border-color-primary);\n\t\tborder-radius: var(--block-radius);\n\t\tbackground: var(--border-color-primary);\n\t\toverflow-y: hidden;\n\t}\n\n\tdiv :global(.block) {\n\t\tbox-shadow: none !important;\n\t\tborder-width: 0px !important;\n\t\tborder-radius: 0px !important;\n\t}\n\n\t.hidden {\n\t\tdisplay: none;\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "div", "anchor", "visible", "$$props", "scale", "min_width"], "mappings": "sRAQgBA,EAAO,CAAA,CAAA,kBACLA,EAAK,CAAA,CAAA,8BACOA,EAAS,CAAA,aAAA,UAJvCC,EAOKC,EAAAC,EAAAC,CAAA,0HALWJ,EAAO,CAAA,CAAA,uBACLA,EAAK,CAAA,CAAA,mCACOA,EAAS,CAAA,aAAA,qHAT3B,CAAA,QAAAK,EAAU,EAAI,EAAAC,EACd,CAAA,MAAAC,EAAuB,IAAI,EAAAD,EAC3B,CAAA,UAAAE,EAAY,CAAC,EAAAF"}