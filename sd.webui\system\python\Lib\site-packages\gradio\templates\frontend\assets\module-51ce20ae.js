import{av as sn}from"./index-2519a27e.js";const vr=e=>t=>{const n=e(t);return t.add(n),n},_r=e=>(t,n)=>(e.set(t,n),n),Lt=Number.MAX_SAFE_INTEGER===void 0?9007199254740991:Number.MAX_SAFE_INTEGER,an=536870912,Pt=an*2,Er=(e,t)=>n=>{const r=t.get(n);let o=r===void 0?n.size:r<Pt?r+1:0;if(!n.has(o))return e(n,o);if(n.size<an){for(;n.has(o);)o=Math.floor(Math.random()*Pt);return e(n,o)}if(n.size>Lt)throw new Error("Congratulations, you created a collection of unique numbers which uses all available integers!");for(;n.has(o);)o=Math.floor(Math.random()*Lt);return e(n,o)},cn=new WeakMap,yr=_r(cn),un=Er(yr,cn),Ar=vr(un),br=e=>typeof e.start=="function",xt=new WeakMap,Cr=e=>({...e,connect:({call:t})=>async()=>{const{port1:n,port2:r}=new MessageChannel,o=await t("connect",{port:n},[n]);return xt.set(r,o),r},disconnect:({call:t})=>async n=>{const r=xt.get(n);if(r===void 0)throw new Error("The given port is not connected.");await t("disconnect",{portId:r})},isSupported:({call:t})=>()=>t("isSupported")}),Je=new WeakMap,Tr=e=>{if(Je.has(e))return Je.get(e);const t=new Map;return Je.set(e,t),t},Mr=e=>{const t=Cr(e);return n=>{const r=Tr(n);n.addEventListener("message",({data:a})=>{const{id:c}=a;if(c!==null&&r.has(c)){const{reject:u,resolve:d}=r.get(c);r.delete(c),a.error===void 0?d(a.result):u(new Error(a.error.message))}}),br(n)&&n.start();const o=(a,c=null,u=[])=>new Promise((d,l)=>{const m=un(r);r.set(m,{reject:l,resolve:d}),c===null?n.postMessage({id:m,method:a},u):n.postMessage({id:m,method:a,params:c},u)}),s=(a,c,u=[])=>{n.postMessage({id:null,method:a,params:c},u)};let i={};for(const[a,c]of Object.entries(t))i={...i,[a]:c({call:o,notify:s})};return{...i}}},Ut=new Set,Nr=Mr({encode:({call:e})=>async(t,n)=>{const r=await e("encode",{encoderId:t,timeslice:n});return Ut.delete(t),r},instantiate:({call:e})=>async(t,n)=>{const r=Ar(Ut),o=await e("instantiate",{encoderId:r,mimeType:t,sampleRate:n});return{encoderId:r,port:o}},register:({call:e})=>t=>e("register",{port:t},[t])}),Or=e=>{const t=new Worker(e);return Nr(t)},Rr=`(()=>{var e={775:function(e,t,r){!function(e,t,r,n){"use strict";var o=function(e,t){return void 0===t?e:t.reduce((function(e,t){if("capitalize"===t){var o=e.charAt(0).toUpperCase(),s=e.slice(1);return"".concat(o).concat(s)}return"dashify"===t?r(e):"prependIndefiniteArticle"===t?"".concat(n(e)," ").concat(e):e}),e)},s=function(e){var t=e.name+e.modifiers.map((function(e){return"\\\\.".concat(e,"\\\\(\\\\)")})).join("");return new RegExp("\\\\$\\\\{".concat(t,"}"),"g")},a=function(e,r){for(var n=/\\\${([^.}]+)((\\.[^(]+\\(\\))*)}/g,a=[],i=n.exec(e);null!==i;){var c={modifiers:[],name:i[1]};if(void 0!==i[3])for(var u=/\\.[^(]+\\(\\)/g,l=u.exec(i[2]);null!==l;)c.modifiers.push(l[0].slice(1,-2)),l=u.exec(i[2]);a.push(c),i=n.exec(e)}var d=a.reduce((function(e,n){return e.map((function(e){return"string"==typeof e?e.split(s(n)).reduce((function(e,s,a){return 0===a?[s]:n.name in r?[].concat(t(e),[o(r[n.name],n.modifiers),s]):[].concat(t(e),[function(e){return o(e[n.name],n.modifiers)},s])}),[]):[e]})).reduce((function(e,r){return[].concat(t(e),t(r))}),[])}),[e]);return function(e){return d.reduce((function(r,n){return[].concat(t(r),"string"==typeof n?[n]:[n(e)])}),[]).join("")}},i=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=void 0===e.code?void 0:a(e.code,t),n=void 0===e.message?void 0:a(e.message,t);function o(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=arguments.length>1?arguments[1]:void 0,s=void 0===o&&(t instanceof Error||void 0!==t.code&&"Exception"===t.code.slice(-9))?{cause:t,missingParameters:{}}:{cause:o,missingParameters:t},a=s.cause,i=s.missingParameters,c=void 0===n?new Error:new Error(n(i));return null!==a&&(c.cause=a),void 0!==r&&(c.code=r(i)),void 0!==e.status&&(c.status=e.status),c}return o};e.compile=i}(t,r(106),r(881),r(507))},881:e=>{"use strict";e.exports=(e,t)=>{if("string"!=typeof e)throw new TypeError("expected a string");return e.trim().replace(/([a-z])([A-Z])/g,"$1-$2").replace(/\\W/g,(e=>/[À-ž]/.test(e)?e:"-")).replace(/^-+|-+$/g,"").replace(/-{2,}/g,(e=>t&&t.condense?"-":e)).toLowerCase()}},107:function(e,t){!function(e){"use strict";var t=function(e){return function(t){var r=e(t);return t.add(r),r}},r=function(e){return function(t,r){return e.set(t,r),r}},n=void 0===Number.MAX_SAFE_INTEGER?9007199254740991:Number.MAX_SAFE_INTEGER,o=536870912,s=2*o,a=function(e,t){return function(r){var a=t.get(r),i=void 0===a?r.size:a<s?a+1:0;if(!r.has(i))return e(r,i);if(r.size<o){for(;r.has(i);)i=Math.floor(Math.random()*s);return e(r,i)}if(r.size>n)throw new Error("Congratulations, you created a collection of unique numbers which uses all available integers!");for(;r.has(i);)i=Math.floor(Math.random()*n);return e(r,i)}},i=new WeakMap,c=r(i),u=a(c,i),l=t(u);e.addUniqueNumber=l,e.generateUniqueNumber=u}(t)},507:e=>{var t=function(e){var t,r,n=/\\w+/.exec(e);if(!n)return"an";var o=(r=n[0]).toLowerCase(),s=["honest","hour","hono"];for(t in s)if(0==o.indexOf(s[t]))return"an";if(1==o.length)return"aedhilmnorsx".indexOf(o)>=0?"an":"a";if(r.match(/(?!FJO|[HLMNS]Y.|RY[EO]|SQU|(F[LR]?|[HL]|MN?|N|RH?|S[CHKLMNPTVW]?|X(YL)?)[AEIOU])[FHLMNRSX][A-Z]/))return"an";var a=[/^e[uw]/,/^onc?e\\b/,/^uni([^nmd]|mo)/,/^u[bcfhjkqrst][aeiou]/];for(t=0;t<a.length;t++)if(o.match(a[t]))return"a";return r.match(/^U[NK][AIEO]/)?"a":r==r.toUpperCase()?"aedhilmnorsx".indexOf(o[0])>=0?"an":"a":"aeiou".indexOf(o[0])>=0||o.match(/^y(b[lor]|cl[ea]|fere|gg|p[ios]|rou|tt)/)?"an":"a"};void 0!==e.exports?e.exports=t:window.indefiniteArticle=t},768:e=>{e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n},e.exports.__esModule=!0,e.exports.default=e.exports},907:(e,t,r)=>{var n=r(768);e.exports=function(e){if(Array.isArray(e))return n(e)},e.exports.__esModule=!0,e.exports.default=e.exports},642:e=>{e.exports=function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports},344:e=>{e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},106:(e,t,r)=>{var n=r(907),o=r(642),s=r(906),a=r(344);e.exports=function(e){return n(e)||o(e)||s(e)||a()},e.exports.__esModule=!0,e.exports.default=e.exports},906:(e,t,r)=>{var n=r(768);e.exports=function(e,t){if(e){if("string"==typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var s=t[n]={exports:{}};return e[n].call(s.exports,s,s.exports,r),s.exports}(()=>{"use strict";var e=r(775);const t=-32603,n=-32602,o=-32601,s=(0,e.compile)({message:'The requested method called "\${method}" is not supported.',status:o}),a=(0,e.compile)({message:'The handler of the method called "\${method}" returned no required result.',status:t}),i=(0,e.compile)({message:'The handler of the method called "\${method}" returned an unexpected result.',status:t}),c=(0,e.compile)({message:'The specified parameter called "portId" with the given value "\${portId}" does not identify a port connected to this worker.',status:n});var u=r(107);const l=new Map,d=(e,t,r)=>({...t,connect:r=>{let{port:n}=r;n.start();const o=e(n,t),s=(0,u.generateUniqueNumber)(l);return l.set(s,(()=>{o(),n.close(),l.delete(s)})),{result:s}},disconnect:e=>{let{portId:t}=e;const r=l.get(t);if(void 0===r)throw c({portId:t.toString()});return r(),{result:null}},isSupported:async()=>{if(await new Promise((e=>{const t=new ArrayBuffer(0),{port1:r,port2:n}=new MessageChannel;r.onmessage=t=>{let{data:r}=t;return e(null!==r)},n.postMessage(t,[t])}))){const e=r();return{result:e instanceof Promise?await e:e}}return{result:!1}}}),f=function(e,t){const r=d(f,t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>!0),n=((e,t)=>async r=>{let{data:{id:n,method:o,params:c}}=r;const u=t[o];try{if(void 0===u)throw s({method:o});const t=void 0===c?u():u(c);if(void 0===t)throw a({method:o});const r=t instanceof Promise?await t:t;if(null===n){if(void 0!==r.result)throw i({method:o})}else{if(void 0===r.result)throw i({method:o});const{result:t,transferables:s=[]}=r;e.postMessage({id:n,result:t},s)}}catch(t){const{message:r,status:o=-32603}=t;e.postMessage({error:{code:o,message:r},id:n})}})(e,r);return e.addEventListener("message",n),()=>e.removeEventListener("message",n)},p=e=>{e.onmessage=null,e.close()},m=new WeakMap,h=new WeakMap,g=(e=>{const t=(r=e,{...r,connect:e=>{let{call:t}=e;return async()=>{const{port1:e,port2:r}=new MessageChannel,n=await t("connect",{port:e},[e]);return m.set(r,n),r}},disconnect:e=>{let{call:t}=e;return async e=>{const r=m.get(e);if(void 0===r)throw new Error("The given port is not connected.");await t("disconnect",{portId:r})}},isSupported:e=>{let{call:t}=e;return()=>t("isSupported")}});var r;return e=>{const r=(e=>{if(h.has(e))return h.get(e);const t=new Map;return h.set(e,t),t})(e);e.addEventListener("message",(e=>{let{data:t}=e;const{id:n}=t;if(null!==n&&r.has(n)){const{reject:e,resolve:o}=r.get(n);r.delete(n),void 0===t.error?o(t.result):e(new Error(t.error.message))}})),(e=>"function"==typeof e.start)(e)&&e.start();const n=function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return new Promise(((s,a)=>{const i=(0,u.generateUniqueNumber)(r);r.set(i,{reject:a,resolve:s}),null===n?e.postMessage({id:i,method:t},o):e.postMessage({id:i,method:t,params:n},o)}))},o=function(t,r){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];e.postMessage({id:null,method:t,params:r},n)};let s={};for(const[e,r]of Object.entries(t))s={...s,[e]:r({call:n,notify:o})};return{...s}}})({characterize:e=>{let{call:t}=e;return()=>t("characterize")},encode:e=>{let{call:t}=e;return(e,r)=>t("encode",{recordingId:e,timeslice:r})},record:e=>{let{call:t}=e;return async(e,r,n)=>{await t("record",{recordingId:e,sampleRate:r,typedArrays:n},n.map((e=>{let{buffer:t}=e;return t})))}}}),v=async(e,t)=>{const r=g(t),n=await r.characterize(),o=n.toString();if(e.has(o))throw new Error("There is already an encoder stored which handles exactly the same mime types.");return e.set(o,[n,r]),n},w=new Map,x=(e=>t=>{const r=e.get(t);if(void 0===r)throw new Error("There was no instance of an encoder stored with the given id.");return r})(w),y=((e,t)=>r=>{const n=t(r);return e.delete(r),n})(w,x),M=new Map,b=((e,t)=>r=>{const[n,o,s,a]=t(r);return s?new Promise((t=>{o.onmessage=s=>{let{data:i}=s;0===i.length?(e(o),t(n.encode(r,null))):n.record(r,a,i)}})):n.encode(r,null)})(p,y),E=(e=>t=>{for(const[r,n]of Array.from(e.values()))if(r.test(t))return n;throw new Error("There is no encoder registered which could handle the given mimeType.")})(M),A=((e,t,r)=>(n,o,s)=>{if(t.has(n))throw new Error('There is already an encoder registered with an id called "'.concat(n,'".'));const a=r(o),{port1:i,port2:c}=new MessageChannel,u=[a,i,!0,s];return t.set(n,u),i.onmessage=t=>{let{data:r}=t;0===r.length?(e(i),u[2]=!1):a.record(n,s,r.map((e=>"number"==typeof e?new Float32Array(e):e)))},c})(p,w,E),I=(e=>(t,r)=>{const[n]=e(t);return n.encode(t,r)})(x);f(self,{encode:async e=>{let{encoderId:t,timeslice:r}=e;const n=null===r?await b(t):await I(t,r);return{result:n,transferables:n}},instantiate:e=>{let{encoderId:t,mimeType:r,sampleRate:n}=e;const o=A(t,r,n);return{result:o,transferables:[o]}},register:async e=>{let{port:t}=e;return{result:await v(M,t)}}})})()})();`,Ir=new Blob([Rr],{type:"application/javascript; charset=utf-8"}),ln=URL.createObjectURL(Ir),pt=Or(ln),Se=pt.encode,dn=pt.instantiate,Sr=pt.register;URL.revokeObjectURL(ln);const kr=e=>(t,n)=>{if(e===null)throw new Error("A native BlobEvent could not be created.");return new e(t,n)},Lr=(e,t)=>(n,r,o)=>{const s=[];let i=r,a=0;for(;a<n.byteLength;)if(i===null){const c=t(n,a);if(c===null)break;const{length:u,type:d}=c;i=d,a+=u}else{const c=e(n,a,i,o);if(c===null)break;const{content:u,length:d}=c;i=null,a+=d,u!==null&&s.push(u)}return{contents:s,currentElementType:i,offset:a}},Pr=(e,t)=>class{constructor(r=null){this._listeners=new WeakMap,this._nativeEventTarget=r===null?e():r}addEventListener(r,o,s){if(o!==null){let i=this._listeners.get(o);i===void 0&&(i=t(this,o),typeof o=="function"&&this._listeners.set(o,i)),this._nativeEventTarget.addEventListener(r,i,s)}}dispatchEvent(r){return this._nativeEventTarget.dispatchEvent(r)}removeEventListener(r,o,s){const i=o===null?void 0:this._listeners.get(o);this._nativeEventTarget.removeEventListener(r,i===void 0?null:i,s)}},xr=e=>()=>{if(e===null)throw new Error("A native EventTarget could not be created.");return e.document.createElement("p")},mt=(e="")=>{try{return new DOMException(e,"InvalidModificationError")}catch(t){return t.code=13,t.message=e,t.name="InvalidModificationError",t}},Ur=()=>{try{return new DOMException("","InvalidStateError")}catch(e){return e.code=11,e.name="InvalidStateError",e}},Wr=e=>{if(e!==null&&e.BlobEvent!==void 0&&e.MediaStream!==void 0&&(e.MediaRecorder===void 0||e.MediaRecorder.isTypeSupported!==void 0)){if(e.MediaRecorder===void 0)return Promise.resolve(!0);const t=e.document.createElement("canvas");if(t.getContext("2d"),typeof t.captureStream!="function")return Promise.resolve(!1);const n=t.captureStream();return Promise.all([new Promise(r=>{const o="audio/webm";try{const s=new e.MediaRecorder(n,{mimeType:o});s.addEventListener("dataavailable",({data:i})=>r(i.type===o)),s.start(),setTimeout(()=>s.stop(),10)}catch(s){r(s.name==="NotSupportedError")}}),new Promise(r=>{const o=new e.MediaRecorder(n);o.addEventListener("error",s=>{r("error"in s&&s.error!==null&&typeof s.error=="object"&&"name"in s.error&&s.error.name!=="UnknownError"),o.stop()}),o.start(),n.removeTrack(n.getVideoTracks()[0])})]).then(r=>r.every(o=>o))}return Promise.resolve(!1)},Br=(e,t,n,r,o,s,i)=>class extends s{constructor(c,u={}){const{mimeType:d}=u;if(i!==null&&(d===void 0||i.isTypeSupported!==void 0&&i.isTypeSupported(d))){const l=e(i,c,u);super(l),this._internalMediaRecorder=l}else if(d!==void 0&&o.some(l=>l.test(d)))super(),i!==null&&i.isTypeSupported!==void 0&&i.isTypeSupported("audio/webm;codecs=pcm")?this._internalMediaRecorder=r(this,i,c,d):this._internalMediaRecorder=n(this,c,d);else throw i!==null&&e(i,c,u),t();this._ondataavailable=null,this._onerror=null,this._onpause=null,this._onresume=null,this._onstart=null,this._onstop=null}get mimeType(){return this._internalMediaRecorder.mimeType}get ondataavailable(){return this._ondataavailable===null?this._ondataavailable:this._ondataavailable[0]}set ondataavailable(c){if(this._ondataavailable!==null&&this.removeEventListener("dataavailable",this._ondataavailable[1]),typeof c=="function"){const u=c.bind(this);this.addEventListener("dataavailable",u),this._ondataavailable=[c,u]}else this._ondataavailable=null}get onerror(){return this._onerror===null?this._onerror:this._onerror[0]}set onerror(c){if(this._onerror!==null&&this.removeEventListener("error",this._onerror[1]),typeof c=="function"){const u=c.bind(this);this.addEventListener("error",u),this._onerror=[c,u]}else this._onerror=null}get onpause(){return this._onpause===null?this._onpause:this._onpause[0]}set onpause(c){if(this._onpause!==null&&this.removeEventListener("pause",this._onpause[1]),typeof c=="function"){const u=c.bind(this);this.addEventListener("pause",u),this._onpause=[c,u]}else this._onpause=null}get onresume(){return this._onresume===null?this._onresume:this._onresume[0]}set onresume(c){if(this._onresume!==null&&this.removeEventListener("resume",this._onresume[1]),typeof c=="function"){const u=c.bind(this);this.addEventListener("resume",u),this._onresume=[c,u]}else this._onresume=null}get onstart(){return this._onstart===null?this._onstart:this._onstart[0]}set onstart(c){if(this._onstart!==null&&this.removeEventListener("start",this._onstart[1]),typeof c=="function"){const u=c.bind(this);this.addEventListener("start",u),this._onstart=[c,u]}else this._onstart=null}get onstop(){return this._onstop===null?this._onstop:this._onstop[0]}set onstop(c){if(this._onstop!==null&&this.removeEventListener("stop",this._onstop[1]),typeof c=="function"){const u=c.bind(this);this.addEventListener("stop",u),this._onstop=[c,u]}else this._onstop=null}get state(){return this._internalMediaRecorder.state}pause(){return this._internalMediaRecorder.pause()}resume(){return this._internalMediaRecorder.resume()}start(c){return this._internalMediaRecorder.start(c)}stop(){return this._internalMediaRecorder.stop()}static isTypeSupported(c){return i!==null&&i.isTypeSupported!==void 0&&i.isTypeSupported(c)||o.some(u=>u.test(c))}},Dr=e=>e!==null&&e.BlobEvent!==void 0?e.BlobEvent:null,Vr=(e,t)=>(n,r,o)=>{const s=[],i=new WeakMap,a=new WeakMap,c=new n(r,o),u=new WeakMap;let d=!0;return c.addEventListener=(l=>(m,g,w)=>{let f=g;return typeof g=="function"&&(m==="dataavailable"?(f=p=>{setTimeout(()=>{if(d&&c.state==="inactive")s.push(p.data);else{if(s.length>0){const h=p.data;Object.defineProperty(p,"data",{value:new Blob([...s,h],{type:h.type})}),s.length=0}g.call(c,p)}})},i.set(g,f)):m==="error"?(f=p=>{p.error===void 0?g.call(c,new ErrorEvent("error",{error:e()})):p instanceof ErrorEvent?g.call(c,p):g.call(c,new ErrorEvent("error",{error:p.error}))},a.set(g,f)):m==="stop"&&(f=p=>{d=!1,setTimeout(()=>g.call(c,p))},u.set(g,f))),l.call(c,m,f,w)})(c.addEventListener),c.dispatchEvent=(l=>m=>{let g;setTimeout(()=>{g=d,d=!1});const w=l.call(c,m);return setTimeout(()=>d=g),w})(c.dispatchEvent),c.removeEventListener=(l=>(m,g,w)=>{let f=g;if(typeof g=="function"){if(m==="dataavailable"){const p=i.get(g);p!==void 0&&(f=p)}else if(m==="error"){const p=a.get(g);p!==void 0&&(f=p)}else if(m==="stop"){const p=u.get(g);p!==void 0&&(f=p)}}return l.call(c,m,f,w)})(c.removeEventListener),c.start=(l=>m=>{if(o.mimeType!==void 0&&o.mimeType.startsWith("audio/")&&r.getVideoTracks().length>0)throw t();return d=m!==void 0,m===void 0?l.call(c):l.call(c,m)})(c.start),c},Fr=e=>e===null||e.MediaRecorder===void 0?null:e.MediaRecorder,De=()=>{try{return new DOMException("","NotSupportedError")}catch(e){return e.code=9,e.name="NotSupportedError",e}},jr=e=>(t,n,r,o=2)=>{const s=e(t,n);if(s===null)return s;const{length:i,value:a}=s;if(r==="master")return{content:null,length:i};if(n+i+a>t.byteLength)return null;if(r==="binary"){const c=(a/Float32Array.BYTES_PER_ELEMENT-1)/o,u=Array.from({length:o},()=>new Float32Array(c));for(let d=0;d<c;d+=1){const l=d*o+1;for(let m=0;m<o;m+=1)u[m][d]=t.getFloat32(n+i+(l+m)*Float32Array.BYTES_PER_ELEMENT,!0)}return{content:u,length:i+a}}return{content:null,length:i+a}},$r=e=>(t,n)=>{const r=e(t,n);if(r===null)return r;const{length:o,value:s}=r;return s===35?{length:o,type:"binary"}:s===46||s===97||s===88713574||s===106212971||s===139690087||s===172351395||s===256095861?{length:o,type:"master"}:{length:o,type:"unknown"}},Gr=e=>(t,n)=>{const r=e(t,n);if(r===null)return r;const o=n+Math.floor((r-1)/8);if(o+r>t.byteLength)return null;let i=t.getUint8(o)&(1<<8-r%8)-1;for(let a=1;a<r;a+=1)i=(i<<8)+t.getUint8(o+a);return{length:r,value:i}},qr=e=>(t,n)=>(e.set(t,n),n),Wt=Number.MAX_SAFE_INTEGER===void 0?9007199254740991:Number.MAX_SAFE_INTEGER,fn=536870912,Bt=fn*2,zr=(e,t)=>n=>{const r=t.get(n);let o=r===void 0?n.size:r<Bt?r+1:0;if(!n.has(o))return e(n,o);if(n.size<fn){for(;n.has(o);)o=Math.floor(Math.random()*Bt);return e(n,o)}if(n.size>Wt)throw new Error("Congratulations, you created a collection of unique numbers which uses all available integers!");for(;n.has(o);)o=Math.floor(Math.random()*Wt);return e(n,o)},hn=new WeakMap,Hr=qr(hn),Xr=zr(Hr,hn),Dt=Symbol.observable||"@@observable";function pn(e){return Symbol.observable||(typeof e=="function"&&e.prototype&&e.prototype[Symbol.observable]?(e.prototype[Dt]=e.prototype[Symbol.observable],delete e.prototype[Symbol.observable]):(e[Dt]=e[Symbol.observable],delete e[Symbol.observable])),e}const Ne=()=>{},Vt=e=>{throw e};function mn(e){return e?e.next&&e.error&&e.complete?e:{complete:(e.complete??Ne).bind(e),error:(e.error??Vt).bind(e),next:(e.next??Ne).bind(e)}:{complete:Ne,error:Vt,next:Ne}}const Yr=e=>(t,n,r)=>e(o=>{const s=i=>o.next(i);return t.addEventListener(n,s,r),()=>t.removeEventListener(n,s,r)}),Zr=(e,t)=>{const n=()=>{},r=o=>typeof o[0]=="function";return o=>{const s=(...i)=>{const a=o(r(i)?t({next:i[0]}):t(...i));return a!==void 0?a:n};return s[Symbol.observable]=()=>({subscribe:(...i)=>({unsubscribe:s(...i)})}),e(s)}},Kr=Zr(pn,mn),Qr=Yr(Kr);/*!
 * dashify <https://github.com/jonschlinkert/dashify>
 *
 * Copyright (c) 2015-2017, Jon Schlinkert.
 * Released under the MIT License.
 */var Jr=(e,t)=>{if(typeof e!="string")throw new TypeError("expected a string");return e.trim().replace(/([a-z])([A-Z])/g,"$1-$2").replace(/\W/g,n=>/[À-ž]/.test(n)?n:"-").replace(/^-+|-+$/g,"").replace(/-{2,}/g,n=>t&&t.condense?"-":n).toLowerCase()};const eo=sn(Jr);var gn={exports:{}};(function(e){var t=function(n){var r,o,s=/\w+/.exec(n);if(s)o=s[0];else return"an";var i=o.toLowerCase(),a=["honest","hour","hono"];for(r in a)if(i.indexOf(a[r])==0)return"an";if(i.length==1)return"aedhilmnorsx".indexOf(i)>=0?"an":"a";if(o.match(/(?!FJO|[HLMNS]Y.|RY[EO]|SQU|(F[LR]?|[HL]|MN?|N|RH?|S[CHKLMNPTVW]?|X(YL)?)[AEIOU])[FHLMNRSX][A-Z]/))return"an";var c=[/^e[uw]/,/^onc?e\b/,/^uni([^nmd]|mo)/,/^u[bcfhjkqrst][aeiou]/];for(r=0;r<c.length;r++)if(i.match(c[r]))return"a";return o.match(/^U[NK][AIEO]/)?"a":o==o.toUpperCase()?"aedhilmnorsx".indexOf(i[0])>=0?"an":"a":"aeiou".indexOf(i[0])>=0||i.match(/^y(b[lor]|cl[ea]|fere|gg|p[ios]|rou|tt)/)?"an":"a"};e.exports=t})(gn);var to=gn.exports;const no=sn(to),Ft=(e,t)=>t===void 0?e:t.reduce((n,r)=>{if(r==="capitalize"){const o=n.charAt(0).toUpperCase(),s=n.slice(1);return`${o}${s}`}return r==="dashify"?eo(n):r==="prependIndefiniteArticle"?`${no(n)} ${n}`:n},e),ro=e=>{const t=e.name+e.modifiers.map(n=>`\\.${n}\\(\\)`).join("");return new RegExp(`\\$\\{${t}}`,"g")},jt=(e,t)=>{const n=/\${([^.}]+)((\.[^(]+\(\))*)}/g,r=[];let o=n.exec(e);for(;o!==null;){const i={modifiers:[],name:o[1]};if(o[3]!==void 0){const a=/\.[^(]+\(\)/g;let c=a.exec(o[2]);for(;c!==null;)i.modifiers.push(c[0].slice(1,-2)),c=a.exec(o[2])}r.push(i),o=n.exec(e)}const s=r.reduce((i,a)=>i.map(c=>typeof c=="string"?c.split(ro(a)).reduce((u,d,l)=>l===0?[d]:a.name in t?[...u,Ft(t[a.name],a.modifiers),d]:[...u,m=>Ft(m[a.name],a.modifiers),d],[]):[c]).reduce((c,u)=>[...c,...u],[]),[e]);return i=>s.reduce((a,c)=>typeof c=="string"?[...a,c]:[...a,c(i)],[]).join("")},Ve=(e,t={})=>{const n=e.code===void 0?void 0:jt(e.code,t),r=e.message===void 0?void 0:jt(e.message,t);function o(s={},i){const a=i===void 0&&(s instanceof Error||s.code!==void 0&&s.code.slice(-9)==="Exception"),{cause:c,missingParameters:u}=a?{cause:s,missingParameters:{}}:{cause:i,missingParameters:s},d=r===void 0?new Error:new Error(r(u));return c!==null&&(d.cause=c),n!==void 0&&(d.code=n(u)),e.status!==void 0&&(d.status=e.status),d}return o},Fe={INTERNAL_ERROR:-32603,INVALID_PARAMS:-32602,METHOD_NOT_FOUND:-32601};Ve({message:'The requested method called "${method}" is not supported.',status:Fe.METHOD_NOT_FOUND});Ve({message:'The handler of the method called "${method}" returned no required result.',status:Fe.INTERNAL_ERROR});Ve({message:'The handler of the method called "${method}" returned an unexpected result.',status:Fe.INTERNAL_ERROR});Ve({message:'The specified parameter called "portId" with the given value "${portId}" does not identify a port connected to this worker.',status:Fe.INVALID_PARAMS});const oo=(e,t,n)=>async r=>{const o=new e([n],{type:"application/javascript; charset=utf-8"}),s=t.createObjectURL(o);try{await r(s)}finally{t.revokeObjectURL(s)}},so=e=>({data:t})=>{const{id:n}=t;if(n!==null){const r=e.get(n);if(r!==void 0){const{reject:o,resolve:s}=r;e.delete(n),t.error===void 0?s(t.result):o(new Error(t.error.message))}}},io=e=>(t,n)=>(r,o=[])=>new Promise((s,i)=>{const a=e(t);t.set(a,{reject:i,resolve:s}),n.postMessage({id:a,...r},o)}),ao=(e,t,n,r)=>(o,s,i={})=>{const a=new o(s,"recorder-audio-worklet-processor",{...i,channelCountMode:"explicit",numberOfInputs:1,numberOfOutputs:0}),c=new Map,u=t(c,a.port),d=n(a.port,"message")(e(c));a.port.start();let l="inactive";return Object.defineProperties(a,{pause:{get(){return async()=>(r(["recording"],l),l="paused",u({method:"pause"}))}},port:{get(){throw new Error("The port of a RecorderAudioWorkletNode can't be accessed.")}},record:{get(){return async m=>(r(["inactive"],l),l="recording",u({method:"record",params:{encoderPort:m}},[m]))}},resume:{get(){return async()=>(r(["paused"],l),l="recording",u({method:"resume"}))}},stop:{get(){return async()=>{r(["paused","recording"],l),l="stopped";try{await u({method:"stop"})}finally{d()}}}}}),a},co=(e,t)=>{if(!e.includes(t))throw new Error(`Expected the state to be ${e.map(n=>`"${n}"`).join(" or ")} but it was "${t}".`)},uo='(()=>{"use strict";class e extends AudioWorkletProcessor{constructor(){super(),this._encoderPort=null,this._numberOfChannels=0,this._state="inactive",this.port.onmessage=e=>{let{data:t}=e;"pause"===t.method?"active"===this._state||"recording"===this._state?(this._state="paused",this._sendAcknowledgement(t.id)):this._sendUnexpectedStateError(t.id):"record"===t.method?"inactive"===this._state?(this._encoderPort=t.params.encoderPort,this._state="active",this._sendAcknowledgement(t.id)):this._sendUnexpectedStateError(t.id):"resume"===t.method?"paused"===this._state?(this._state="active",this._sendAcknowledgement(t.id)):this._sendUnexpectedStateError(t.id):"stop"===t.method?"active"!==this._state&&"paused"!==this._state&&"recording"!==this._state||null===this._encoderPort?this._sendUnexpectedStateError(t.id):(this._stop(this._encoderPort),this._sendAcknowledgement(t.id)):"number"==typeof t.id&&this.port.postMessage({error:{code:-32601,message:"The requested method is not supported."},id:t.id})}}process(e){let[t]=e;if("inactive"===this._state||"paused"===this._state)return!0;if("active"===this._state){if(void 0===t)throw new Error("No channelData was received for the first input.");if(0===t.length)return!0;this._state="recording"}if("recording"===this._state&&null!==this._encoderPort){if(void 0===t)throw new Error("No channelData was received for the first input.");return 0===t.length?this._encoderPort.postMessage(Array.from({length:this._numberOfChannels},(()=>128))):(this._encoderPort.postMessage(t,t.map((e=>{let{buffer:t}=e;return t}))),this._numberOfChannels=t.length),!0}return!1}_sendAcknowledgement(e){this.port.postMessage({id:e,result:null})}_sendUnexpectedStateError(e){this.port.postMessage({error:{code:-32603,message:"The internal state does not allow to process the given message."},id:e})}_stop(e){e.postMessage([]),e.close(),this._encoderPort=null,this._state="stopped"}}e.parameterDescriptors=[],registerProcessor("recorder-audio-worklet-processor",e)})();',lo=oo(Blob,URL,uo),fo=ao(so,io(Xr),Qr,co),$t=(e,t,n)=>({endTime:t,insertTime:n,type:"exponentialRampToValue",value:e}),Gt=(e,t,n)=>({endTime:t,insertTime:n,type:"linearRampToValue",value:e}),nt=(e,t)=>({startTime:t,type:"setValue",value:e}),wn=(e,t,n)=>({duration:n,startTime:t,type:"setValueCurve",values:e}),vn=(e,t,{startTime:n,target:r,timeConstant:o})=>r+(t-r)*Math.exp((n-e)/o),ge=e=>e.type==="exponentialRampToValue",ke=e=>e.type==="linearRampToValue",se=e=>ge(e)||ke(e),gt=e=>e.type==="setValue",te=e=>e.type==="setValueCurve",Le=(e,t,n,r)=>{const o=e[t];return o===void 0?r:se(o)||gt(o)?o.value:te(o)?o.values[o.values.length-1]:vn(n,Le(e,t-1,o.startTime,r),o)},qt=(e,t,n,r,o)=>n===void 0?[r.insertTime,o]:se(n)?[n.endTime,n.value]:gt(n)?[n.startTime,n.value]:te(n)?[n.startTime+n.duration,n.values[n.values.length-1]]:[n.startTime,Le(e,t-1,n.startTime,o)],rt=e=>e.type==="cancelAndHold",ot=e=>e.type==="cancelScheduledValues",oe=e=>rt(e)||ot(e)?e.cancelTime:ge(e)||ke(e)?e.endTime:e.startTime,zt=(e,t,n,{endTime:r,value:o})=>n===o?o:0<n&&0<o||n<0&&o<0?n*(o/n)**((e-t)/(r-t)):0,Ht=(e,t,n,{endTime:r,value:o})=>n+(e-t)/(r-t)*(o-n),ho=(e,t)=>{const n=Math.floor(t),r=Math.ceil(t);return n===r?e[n]:(1-(t-n))*e[n]+(1-(r-t))*e[r]},po=(e,{duration:t,startTime:n,values:r})=>{const o=(e-n)/t*(r.length-1);return ho(r,o)},Oe=e=>e.type==="setTarget";class mo{constructor(t){this._automationEvents=[],this._currenTime=0,this._defaultValue=t}[Symbol.iterator](){return this._automationEvents[Symbol.iterator]()}add(t){const n=oe(t);if(rt(t)||ot(t)){const r=this._automationEvents.findIndex(s=>ot(t)&&te(s)?s.startTime+s.duration>=n:oe(s)>=n),o=this._automationEvents[r];if(r!==-1&&(this._automationEvents=this._automationEvents.slice(0,r)),rt(t)){const s=this._automationEvents[this._automationEvents.length-1];if(o!==void 0&&se(o)){if(s!==void 0&&Oe(s))throw new Error("The internal list is malformed.");const i=s===void 0?o.insertTime:te(s)?s.startTime+s.duration:oe(s),a=s===void 0?this._defaultValue:te(s)?s.values[s.values.length-1]:s.value,c=ge(o)?zt(n,i,a,o):Ht(n,i,a,o),u=ge(o)?$t(c,n,this._currenTime):Gt(c,n,this._currenTime);this._automationEvents.push(u)}if(s!==void 0&&Oe(s)&&this._automationEvents.push(nt(this.getValue(n),n)),s!==void 0&&te(s)&&s.startTime+s.duration>n){const i=n-s.startTime,a=(s.values.length-1)/s.duration,c=Math.max(2,1+Math.ceil(i*a)),u=i/(c-1)*a,d=s.values.slice(0,c);if(u<1)for(let l=1;l<c;l+=1){const m=u*l%1;d[l]=s.values[l-1]*(1-m)+s.values[l]*m}this._automationEvents[this._automationEvents.length-1]=wn(d,s.startTime,i)}}}else{const r=this._automationEvents.findIndex(i=>oe(i)>n),o=r===-1?this._automationEvents[this._automationEvents.length-1]:this._automationEvents[r-1];if(o!==void 0&&te(o)&&oe(o)+o.duration>n)return!1;const s=ge(t)?$t(t.value,t.endTime,this._currenTime):ke(t)?Gt(t.value,n,this._currenTime):t;if(r===-1)this._automationEvents.push(s);else{if(te(t)&&n+t.duration>oe(this._automationEvents[r]))return!1;this._automationEvents.splice(r,0,s)}}return!0}flush(t){const n=this._automationEvents.findIndex(r=>oe(r)>t);if(n>1){const r=this._automationEvents.slice(n-1),o=r[0];Oe(o)&&r.unshift(nt(Le(this._automationEvents,n-2,o.startTime,this._defaultValue),o.startTime)),this._automationEvents=r}}getValue(t){if(this._automationEvents.length===0)return this._defaultValue;const n=this._automationEvents.findIndex(i=>oe(i)>t),r=this._automationEvents[n],o=(n===-1?this._automationEvents.length:n)-1,s=this._automationEvents[o];if(s!==void 0&&Oe(s)&&(r===void 0||!se(r)||r.insertTime>t))return vn(t,Le(this._automationEvents,o-1,s.startTime,this._defaultValue),s);if(s!==void 0&&gt(s)&&(r===void 0||!se(r)))return s.value;if(s!==void 0&&te(s)&&(r===void 0||!se(r)||s.startTime+s.duration>t))return t<s.startTime+s.duration?po(t,s):s.values[s.values.length-1];if(s!==void 0&&se(s)&&(r===void 0||!se(r)))return s.value;if(r!==void 0&&ge(r)){const[i,a]=qt(this._automationEvents,o,s,r,this._defaultValue);return zt(t,i,a,r)}if(r!==void 0&&ke(r)){const[i,a]=qt(this._automationEvents,o,s,r,this._defaultValue);return Ht(t,i,a,r)}return this._defaultValue}}const go=e=>({cancelTime:e,type:"cancelAndHold"}),wo=e=>({cancelTime:e,type:"cancelScheduledValues"}),vo=(e,t)=>({endTime:t,type:"exponentialRampToValue",value:e}),_o=(e,t)=>({endTime:t,type:"linearRampToValue",value:e}),Eo=(e,t,n)=>({startTime:t,target:e,timeConstant:n,type:"setTarget"}),yo=()=>new DOMException("","AbortError"),Ao=e=>(t,n,[r,o,s],i)=>{e(t[o],[n,r,s],a=>a[0]===n&&a[1]===r,i)},bo=e=>(t,n,r)=>{const o=[];for(let s=0;s<r.numberOfInputs;s+=1)o.push(new Set);e.set(t,{activeInputs:o,outputs:new Set,passiveInputs:new WeakMap,renderer:n})},Co=e=>(t,n)=>{e.set(t,{activeInputs:new Set,passiveInputs:new WeakMap,renderer:n})},we=new WeakSet,_n=new WeakMap,En=new WeakMap,yn=new WeakMap,An=new WeakMap,bn=new WeakMap,Cn=new WeakMap,st=new WeakMap,it=new WeakMap,at=new WeakMap,Tn={construct(){return Tn}},To=e=>{try{const t=new Proxy(e,Tn);new t}catch{return!1}return!0},Xt=/^import(?:(?:[\s]+[\w]+|(?:[\s]+[\w]+[\s]*,)?[\s]*\{[\s]*[\w]+(?:[\s]+as[\s]+[\w]+)?(?:[\s]*,[\s]*[\w]+(?:[\s]+as[\s]+[\w]+)?)*[\s]*}|(?:[\s]+[\w]+[\s]*,)?[\s]*\*[\s]+as[\s]+[\w]+)[\s]+from)?(?:[\s]*)("([^"\\]|\\.)+"|'([^'\\]|\\.)+')(?:[\s]*);?/,Yt=(e,t)=>{const n=[];let r=e.replace(/^[\s]+/,""),o=r.match(Xt);for(;o!==null;){const s=o[1].slice(1,-1),i=o[0].replace(/([\s]+)?;?$/,"").replace(s,new URL(s,t).toString());n.push(i),r=r.slice(o[0].length).replace(/^[\s]+/,""),o=r.match(Xt)}return[n.join(";"),r]},Zt=e=>{if(e!==void 0&&!Array.isArray(e))throw new TypeError("The parameterDescriptors property of given value for processorCtor is not an array.")},Kt=e=>{if(!To(e))throw new TypeError("The given value for processorCtor should be a constructor.");if(e.prototype===null||typeof e.prototype!="object")throw new TypeError("The given value for processorCtor should have a prototype.")},Mo=(e,t,n,r,o,s,i,a,c,u,d,l,m)=>{let g=0;return(w,f,p={credentials:"omit"})=>{const h=d.get(w);if(h!==void 0&&h.has(f))return Promise.resolve();const _=u.get(w);if(_!==void 0){const A=_.get(f);if(A!==void 0)return A}const E=s(w),T=E.audioWorklet===void 0?o(f).then(([A,b])=>{const[y,v]=Yt(A,b),M=`${y};((a,b)=>{(a[b]=a[b]||[]).push((AudioWorkletProcessor,global,registerProcessor,sampleRate,self,window)=>{${v}
})})(window,'_AWGS')`;return n(M)}).then(()=>{const A=m._AWGS.pop();if(A===void 0)throw new SyntaxError;r(E.currentTime,E.sampleRate,()=>A(class{},void 0,(b,y)=>{if(b.trim()==="")throw t();const v=it.get(E);if(v!==void 0){if(v.has(b))throw t();Kt(y),Zt(y.parameterDescriptors),v.set(b,y)}else Kt(y),Zt(y.parameterDescriptors),it.set(E,new Map([[b,y]]))},E.sampleRate,void 0,void 0))}):Promise.all([o(f),Promise.resolve(e(l,l))]).then(([[A,b],y])=>{const v=g+1;g=v;const[M,k]=Yt(A,b),W=`${M};((AudioWorkletProcessor,registerProcessor)=>{${k}
})(${y?"AudioWorkletProcessor":"class extends AudioWorkletProcessor {__b=new WeakSet();constructor(){super();(p=>p.postMessage=(q=>(m,t)=>q.call(p,m,t?t.filter(u=>!this.__b.has(u)):t))(p.postMessage))(this.port)}}"},(n,p)=>registerProcessor(n,class extends p{${y?"":"__c = (a) => a.forEach(e=>this.__b.add(e.buffer));"}process(i,o,p){${y?"":"i.forEach(this.__c);o.forEach(this.__c);this.__c(Object.values(p));"}return super.process(i.map(j=>j.some(k=>k.length===0)?[]:j),o,p)}}));registerProcessor('__sac${v}',class extends AudioWorkletProcessor{process(){return !1}})`,L=new Blob([W],{type:"application/javascript; charset=utf-8"}),I=URL.createObjectURL(L);return E.audioWorklet.addModule(I,p).then(()=>{if(a(E))return E;const S=i(E);return S.audioWorklet.addModule(I,p).then(()=>S)}).then(S=>{if(c===null)throw new SyntaxError;try{new c(S,`__sac${v}`)}catch{throw new SyntaxError}}).finally(()=>URL.revokeObjectURL(I))});return _===void 0?u.set(w,new Map([[f,T]])):_.set(f,T),T.then(()=>{const A=d.get(w);A===void 0?d.set(w,new Set([f])):A.add(f)}).finally(()=>{const A=u.get(w);A!==void 0&&A.delete(f)}),T}},K=(e,t)=>{const n=e.get(t);if(n===void 0)throw new Error("A value with the given key could not be found.");return n},je=(e,t)=>{const n=Array.from(e).filter(t);if(n.length>1)throw Error("More than one element was found.");if(n.length===0)throw Error("No element was found.");const[r]=n;return e.delete(r),r},Mn=(e,t,n,r)=>{const o=K(e,t),s=je(o,i=>i[0]===n&&i[1]===r);return o.size===0&&e.delete(t),s},Ae=e=>K(Cn,e),Pe=e=>{if(we.has(e))throw new Error("The AudioNode is already stored.");we.add(e),Ae(e).forEach(t=>t(!0))},Nn=e=>"port"in e,wt=e=>{if(!we.has(e))throw new Error("The AudioNode is not stored.");we.delete(e),Ae(e).forEach(t=>t(!1))},ct=(e,t)=>{!Nn(e)&&t.every(n=>n.size===0)&&wt(e)},No=(e,t,n,r,o,s,i,a,c,u,d,l,m)=>{const g=new WeakMap;return(w,f,p,h,_)=>{const{activeInputs:E,passiveInputs:T}=s(f),{outputs:A}=s(w),b=a(w),y=v=>{const M=c(f),k=c(w);if(v){const N=Mn(T,w,p,h);e(E,w,N,!1),!_&&!l(w)&&n(k,M,p,h),m(f)&&Pe(f)}else{const N=r(E,w,p,h);t(T,h,N,!1),!_&&!l(w)&&o(k,M,p,h);const U=i(f);if(U===0)d(f)&&ct(f,E);else{const x=g.get(f);x!==void 0&&clearTimeout(x),g.set(f,setTimeout(()=>{d(f)&&ct(f,E)},U*1e3))}}};return u(A,[f,p,h],v=>v[0]===f&&v[1]===p&&v[2]===h,!0)?(b.add(y),d(w)?e(E,w,[p,h,y],!0):t(T,h,[w,p,y],!0),!0):!1}},Oo=e=>(t,n,[r,o,s],i)=>{const a=t.get(r);a===void 0?t.set(r,new Set([[o,n,s]])):e(a,[o,n,s],c=>c[0]===o&&c[1]===n,i)},Ro=e=>(t,n)=>{const r=e(t,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});n.connect(r).connect(t.destination);const o=()=>{n.removeEventListener("ended",o),n.disconnect(r),r.disconnect()};n.addEventListener("ended",o)},Io=e=>(t,n)=>{e(t).add(n)},On=(e,t)=>e.context===t,ut=e=>{try{e.copyToChannel(new Float32Array(1),0,-1)}catch{return!1}return!0},ae=()=>new DOMException("","IndexSizeError"),Rn=e=>{e.getChannelData=(t=>n=>{try{return t.call(e,n)}catch(r){throw r.code===12?ae():r}})(e.getChannelData)},So={numberOfChannels:1},ko=(e,t,n,r,o,s,i,a)=>{let c=null;return class In{constructor(d){if(o===null)throw new Error("Missing the native OfflineAudioContext constructor.");const{length:l,numberOfChannels:m,sampleRate:g}={...So,...d};c===null&&(c=new o(1,1,44100));const w=r!==null&&t(s,s)?new r({length:l,numberOfChannels:m,sampleRate:g}):c.createBuffer(m,l,g);if(w.numberOfChannels===0)throw n();return typeof w.copyFromChannel!="function"?(i(w),Rn(w)):t(ut,()=>ut(w))||a(w),e.add(w),w}static[Symbol.hasInstance](d){return d!==null&&typeof d=="object"&&Object.getPrototypeOf(d)===In.prototype||e.has(d)}}},$e=-34028234663852886e22,vt=-$e,ue=e=>we.has(e),Lo={buffer:null,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",loop:!1,loopEnd:0,loopStart:0,playbackRate:1},Po=(e,t,n,r,o,s,i,a)=>class extends e{constructor(u,d){const l=s(u),m={...Lo,...d},g=o(l,m),w=i(l),f=w?t():null;super(u,!1,g,f),this._audioBufferSourceNodeRenderer=f,this._isBufferNullified=!1,this._isBufferSet=m.buffer!==null,this._nativeAudioBufferSourceNode=g,this._onended=null,this._playbackRate=n(this,w,g.playbackRate,vt,$e)}get buffer(){return this._isBufferNullified?null:this._nativeAudioBufferSourceNode.buffer}set buffer(u){if(this._nativeAudioBufferSourceNode.buffer=u,u!==null){if(this._isBufferSet)throw r();this._isBufferSet=!0}}get loop(){return this._nativeAudioBufferSourceNode.loop}set loop(u){this._nativeAudioBufferSourceNode.loop=u}get loopEnd(){return this._nativeAudioBufferSourceNode.loopEnd}set loopEnd(u){this._nativeAudioBufferSourceNode.loopEnd=u}get loopStart(){return this._nativeAudioBufferSourceNode.loopStart}set loopStart(u){this._nativeAudioBufferSourceNode.loopStart=u}get onended(){return this._onended}set onended(u){const d=typeof u=="function"?a(this,u):null;this._nativeAudioBufferSourceNode.onended=d;const l=this._nativeAudioBufferSourceNode.onended;this._onended=l!==null&&l===d?u:l}get playbackRate(){return this._playbackRate}start(u=0,d=0,l){if(this._nativeAudioBufferSourceNode.start(u,d,l),this._audioBufferSourceNodeRenderer!==null&&(this._audioBufferSourceNodeRenderer.start=l===void 0?[u,d]:[u,d,l]),this.context.state!=="closed"){Pe(this);const m=()=>{this._nativeAudioBufferSourceNode.removeEventListener("ended",m),ue(this)&&wt(this)};this._nativeAudioBufferSourceNode.addEventListener("ended",m)}}stop(u=0){this._nativeAudioBufferSourceNode.stop(u),this._audioBufferSourceNodeRenderer!==null&&(this._audioBufferSourceNodeRenderer.stop=u)}},xo=(e,t,n,r,o)=>()=>{const s=new WeakMap;let i=null,a=null;const c=async(u,d)=>{let l=n(u);const m=On(l,d);if(!m){const g={buffer:l.buffer,channelCount:l.channelCount,channelCountMode:l.channelCountMode,channelInterpretation:l.channelInterpretation,loop:l.loop,loopEnd:l.loopEnd,loopStart:l.loopStart,playbackRate:l.playbackRate.value};l=t(d,g),i!==null&&l.start(...i),a!==null&&l.stop(a)}return s.set(d,l),m?await e(d,u.playbackRate,l.playbackRate):await r(d,u.playbackRate,l.playbackRate),await o(u,d,l),l};return{set start(u){i=u},set stop(u){a=u},render(u,d){const l=s.get(d);return l!==void 0?Promise.resolve(l):c(u,d)}}},Uo=e=>"playbackRate"in e,Wo=e=>"frequency"in e&&"gain"in e,Bo=e=>"offset"in e,Do=e=>!("frequency"in e)&&"gain"in e,Vo=e=>"detune"in e&&"frequency"in e,Fo=e=>"pan"in e,z=e=>K(_n,e),be=e=>K(yn,e),lt=(e,t)=>{const{activeInputs:n}=z(e);n.forEach(o=>o.forEach(([s])=>{t.includes(e)||lt(s,[...t,e])}));const r=Uo(e)?[e.playbackRate]:Nn(e)?Array.from(e.parameters.values()):Wo(e)?[e.Q,e.detune,e.frequency,e.gain]:Bo(e)?[e.offset]:Do(e)?[e.gain]:Vo(e)?[e.detune,e.frequency]:Fo(e)?[e.pan]:[];for(const o of r){const s=be(o);s!==void 0&&s.activeInputs.forEach(([i])=>lt(i,t))}ue(e)&&wt(e)},jo=e=>{lt(e.destination,[])},$o=e=>e===void 0||typeof e=="number"||typeof e=="string"&&(e==="balanced"||e==="interactive"||e==="playback"),Go=(e,t,n,r,o,s,i,a)=>class extends e{constructor(u,d){const l=s(u),m=i(l),g=o(l,d,m),w=m?t(a):null;super(u,!1,g,w),this._isNodeOfNativeOfflineAudioContext=m,this._nativeAudioDestinationNode=g}get channelCount(){return this._nativeAudioDestinationNode.channelCount}set channelCount(u){if(this._isNodeOfNativeOfflineAudioContext)throw r();if(u>this._nativeAudioDestinationNode.maxChannelCount)throw n();this._nativeAudioDestinationNode.channelCount=u}get channelCountMode(){return this._nativeAudioDestinationNode.channelCountMode}set channelCountMode(u){if(this._isNodeOfNativeOfflineAudioContext)throw r();this._nativeAudioDestinationNode.channelCountMode=u}get maxChannelCount(){return this._nativeAudioDestinationNode.maxChannelCount}},qo=e=>{const t=new WeakMap,n=async(r,o)=>{const s=o.destination;return t.set(o,s),await e(r,o,s),s};return{render(r,o){const s=t.get(o);return s!==void 0?Promise.resolve(s):n(r,o)}}},zo=(e,t,n,r,o,s,i,a)=>(c,u)=>{const d=u.listener,l=()=>{const A=new Float32Array(1),b=t(u,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:9}),y=i(u);let v=!1,M=[0,0,-1,0,1,0],k=[0,0,0];const N=()=>{if(v)return;v=!0;const L=r(u,256,9,0);L.onaudioprocess=({inputBuffer:I})=>{const S=[s(I,A,0),s(I,A,1),s(I,A,2),s(I,A,3),s(I,A,4),s(I,A,5)];S.some((O,P)=>O!==M[P])&&(d.setOrientation(...S),M=S);const V=[s(I,A,6),s(I,A,7),s(I,A,8)];V.some((O,P)=>O!==k[P])&&(d.setPosition(...V),k=V)},b.connect(L)},U=L=>I=>{I!==M[L]&&(M[L]=I,d.setOrientation(...M))},x=L=>I=>{I!==k[L]&&(k[L]=I,d.setPosition(...k))},W=(L,I,S)=>{const V=n(u,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",offset:I});V.connect(b,0,L),V.start(),Object.defineProperty(V.offset,"defaultValue",{get(){return I}});const O=e({context:c},y,V.offset,vt,$e);return a(O,"value",P=>()=>P.call(O),P=>B=>{try{P.call(O,B)}catch(F){if(F.code!==9)throw F}N(),y&&S(B)}),O.cancelAndHoldAtTime=(P=>y?()=>{throw o()}:(...B)=>{const F=P.apply(O,B);return N(),F})(O.cancelAndHoldAtTime),O.cancelScheduledValues=(P=>y?()=>{throw o()}:(...B)=>{const F=P.apply(O,B);return N(),F})(O.cancelScheduledValues),O.exponentialRampToValueAtTime=(P=>y?()=>{throw o()}:(...B)=>{const F=P.apply(O,B);return N(),F})(O.exponentialRampToValueAtTime),O.linearRampToValueAtTime=(P=>y?()=>{throw o()}:(...B)=>{const F=P.apply(O,B);return N(),F})(O.linearRampToValueAtTime),O.setTargetAtTime=(P=>y?()=>{throw o()}:(...B)=>{const F=P.apply(O,B);return N(),F})(O.setTargetAtTime),O.setValueAtTime=(P=>y?()=>{throw o()}:(...B)=>{const F=P.apply(O,B);return N(),F})(O.setValueAtTime),O.setValueCurveAtTime=(P=>y?()=>{throw o()}:(...B)=>{const F=P.apply(O,B);return N(),F})(O.setValueCurveAtTime),O};return{forwardX:W(0,0,U(0)),forwardY:W(1,0,U(1)),forwardZ:W(2,-1,U(2)),positionX:W(6,0,x(0)),positionY:W(7,0,x(1)),positionZ:W(8,0,x(2)),upX:W(3,0,U(3)),upY:W(4,1,U(4)),upZ:W(5,0,U(5))}},{forwardX:m,forwardY:g,forwardZ:w,positionX:f,positionY:p,positionZ:h,upX:_,upY:E,upZ:T}=d.forwardX===void 0?l():d;return{get forwardX(){return m},get forwardY(){return g},get forwardZ(){return w},get positionX(){return f},get positionY(){return p},get positionZ(){return h},get upX(){return _},get upY(){return E},get upZ(){return T}}},xe=e=>"context"in e,Ce=e=>xe(e[0]),de=(e,t,n,r)=>{for(const o of e)if(n(o)){if(r)return!1;throw Error("The set contains at least one similar element.")}return e.add(t),!0},Qt=(e,t,[n,r],o)=>{de(e,[t,n,r],s=>s[0]===t&&s[1]===n,o)},Jt=(e,[t,n,r],o)=>{const s=e.get(t);s===void 0?e.set(t,new Set([[n,r]])):de(s,[n,r],i=>i[0]===n,o)},Sn=e=>"inputs"in e,dt=(e,t,n,r)=>{if(Sn(t)){const o=t.inputs[r];return e.connect(o,n,0),[o,n,0]}return e.connect(t,n,r),[t,n,r]},kn=(e,t,n)=>{for(const r of e)if(r[0]===t&&r[1]===n)return e.delete(r),r;return null},Ho=(e,t,n)=>je(e,r=>r[0]===t&&r[1]===n),Ln=(e,t)=>{if(!Ae(e).delete(t))throw new Error("Missing the expected event listener.")},Pn=(e,t,n)=>{const r=K(e,t),o=je(r,s=>s[0]===n);return r.size===0&&e.delete(t),o},ft=(e,t,n,r)=>{Sn(t)?e.disconnect(t.inputs[r],n,0):e.disconnect(t,n,r)},Y=e=>K(En,e),Ee=e=>K(An,e),le=e=>st.has(e),Ie=e=>!we.has(e),en=(e,t)=>new Promise(n=>{if(t!==null)n(!0);else{const r=e.createScriptProcessor(256,1,1),o=e.createGain(),s=e.createBuffer(1,2,44100),i=s.getChannelData(0);i[0]=1,i[1]=1;const a=e.createBufferSource();a.buffer=s,a.loop=!0,a.connect(r).connect(e.destination),a.connect(o),a.disconnect(o),r.onaudioprocess=c=>{const u=c.inputBuffer.getChannelData(0);Array.prototype.some.call(u,d=>d===1)?n(!0):n(!1),a.stop(),r.onaudioprocess=null,a.disconnect(r),r.disconnect(e.destination)},a.start()}}),et=(e,t)=>{const n=new Map;for(const r of e)for(const o of r){const s=n.get(o);n.set(o,s===void 0?1:s+1)}n.forEach((r,o)=>t(o,r))},Ue=e=>"context"in e,Xo=e=>{const t=new Map;e.connect=(n=>(r,o=0,s=0)=>{const i=Ue(r)?n(r,o,s):n(r,o),a=t.get(r);return a===void 0?t.set(r,[{input:s,output:o}]):a.every(c=>c.input!==s||c.output!==o)&&a.push({input:s,output:o}),i})(e.connect.bind(e)),e.disconnect=(n=>(r,o,s)=>{if(n.apply(e),r===void 0)t.clear();else if(typeof r=="number")for(const[i,a]of t){const c=a.filter(u=>u.output!==r);c.length===0?t.delete(i):t.set(i,c)}else if(t.has(r))if(o===void 0)t.delete(r);else{const i=t.get(r);if(i!==void 0){const a=i.filter(c=>c.output!==o&&(c.input!==s||s===void 0));a.length===0?t.delete(r):t.set(r,a)}}for(const[i,a]of t)a.forEach(c=>{Ue(i)?e.connect(i,c.output,c.input):e.connect(i,c.output)})})(e.disconnect)},Yo=(e,t,n,r)=>{const{activeInputs:o,passiveInputs:s}=be(t),{outputs:i}=z(e),a=Ae(e),c=u=>{const d=Y(e),l=Ee(t);if(u){const m=Pn(s,e,n);Qt(o,e,m,!1),!r&&!le(e)&&d.connect(l,n)}else{const m=Ho(o,e,n);Jt(s,m,!1),!r&&!le(e)&&d.disconnect(l,n)}};return de(i,[t,n],u=>u[0]===t&&u[1]===n,!0)?(a.add(c),ue(e)?Qt(o,e,[n,c],!0):Jt(s,[e,n,c],!0),!0):!1},Zo=(e,t,n,r)=>{const{activeInputs:o,passiveInputs:s}=z(t),i=kn(o[r],e,n);return i===null?[Mn(s,e,n,r)[2],!1]:[i[2],!0]},Ko=(e,t,n)=>{const{activeInputs:r,passiveInputs:o}=be(t),s=kn(r,e,n);return s===null?[Pn(o,e,n)[1],!1]:[s[2],!0]},_t=(e,t,n,r,o)=>{const[s,i]=Zo(e,n,r,o);if(s!==null&&(Ln(e,s),i&&!t&&!le(e)&&ft(Y(e),Y(n),r,o)),ue(n)){const{activeInputs:a}=z(n);ct(n,a)}},Et=(e,t,n,r)=>{const[o,s]=Ko(e,n,r);o!==null&&(Ln(e,o),s&&!t&&!le(e)&&Y(e).disconnect(Ee(n),r))},Qo=(e,t)=>{const n=z(e),r=[];for(const o of n.outputs)Ce(o)?_t(e,t,...o):Et(e,t,...o),r.push(o[0]);return n.outputs.clear(),r},Jo=(e,t,n)=>{const r=z(e),o=[];for(const s of r.outputs)s[1]===n&&(Ce(s)?_t(e,t,...s):Et(e,t,...s),o.push(s[0]),r.outputs.delete(s));return o},es=(e,t,n,r,o)=>{const s=z(e);return Array.from(s.outputs).filter(i=>i[0]===n&&(r===void 0||i[1]===r)&&(o===void 0||i[2]===o)).map(i=>(Ce(i)?_t(e,t,...i):Et(e,t,...i),s.outputs.delete(i),i[0]))},ts=(e,t,n,r,o,s,i,a,c,u,d,l,m,g,w,f)=>class extends u{constructor(h,_,E,T){super(E),this._context=h,this._nativeAudioNode=E;const A=d(h);l(A)&&n(en,()=>en(A,f))!==!0&&Xo(E),En.set(this,E),Cn.set(this,new Set),h.state!=="closed"&&_&&Pe(this),e(this,T,E)}get channelCount(){return this._nativeAudioNode.channelCount}set channelCount(h){this._nativeAudioNode.channelCount=h}get channelCountMode(){return this._nativeAudioNode.channelCountMode}set channelCountMode(h){this._nativeAudioNode.channelCountMode=h}get channelInterpretation(){return this._nativeAudioNode.channelInterpretation}set channelInterpretation(h){this._nativeAudioNode.channelInterpretation=h}get context(){return this._context}get numberOfInputs(){return this._nativeAudioNode.numberOfInputs}get numberOfOutputs(){return this._nativeAudioNode.numberOfOutputs}connect(h,_=0,E=0){if(_<0||_>=this._nativeAudioNode.numberOfOutputs)throw o();const T=d(this._context),A=w(T);if(m(h)||g(h))throw s();if(xe(h)){const v=Y(h);try{const k=dt(this._nativeAudioNode,v,_,E),N=Ie(this);(A||N)&&this._nativeAudioNode.disconnect(...k),this.context.state!=="closed"&&!N&&Ie(h)&&Pe(h)}catch(k){throw k.code===12?s():k}if(t(this,h,_,E,A)){const k=c([this],h);et(k,r(A))}return h}const b=Ee(h);if(b.name==="playbackRate"&&b.maxValue===1024)throw i();try{this._nativeAudioNode.connect(b,_),(A||Ie(this))&&this._nativeAudioNode.disconnect(b,_)}catch(v){throw v.code===12?s():v}if(Yo(this,h,_,A)){const v=c([this],h);et(v,r(A))}}disconnect(h,_,E){let T;const A=d(this._context),b=w(A);if(h===void 0)T=Qo(this,b);else if(typeof h=="number"){if(h<0||h>=this.numberOfOutputs)throw o();T=Jo(this,b,h)}else{if(_!==void 0&&(_<0||_>=this.numberOfOutputs)||xe(h)&&E!==void 0&&(E<0||E>=h.numberOfInputs))throw o();if(T=es(this,b,h,_,E),T.length===0)throw s()}for(const y of T){const v=c([this],y);et(v,a)}}},ns=(e,t,n,r,o,s,i,a,c,u,d,l,m)=>(g,w,f,p=null,h=null)=>{const _=f.value,E=new mo(_),T=w?r(E):null,A={get defaultValue(){return _},get maxValue(){return p===null?f.maxValue:p},get minValue(){return h===null?f.minValue:h},get value(){return f.value},set value(b){f.value=b,A.setValueAtTime(b,g.context.currentTime)},cancelAndHoldAtTime(b){if(typeof f.cancelAndHoldAtTime=="function")T===null&&E.flush(g.context.currentTime),E.add(o(b)),f.cancelAndHoldAtTime(b);else{const y=Array.from(E).pop();T===null&&E.flush(g.context.currentTime),E.add(o(b));const v=Array.from(E).pop();f.cancelScheduledValues(b),y!==v&&v!==void 0&&(v.type==="exponentialRampToValue"?f.exponentialRampToValueAtTime(v.value,v.endTime):v.type==="linearRampToValue"?f.linearRampToValueAtTime(v.value,v.endTime):v.type==="setValue"?f.setValueAtTime(v.value,v.startTime):v.type==="setValueCurve"&&f.setValueCurveAtTime(v.values,v.startTime,v.duration))}return A},cancelScheduledValues(b){return T===null&&E.flush(g.context.currentTime),E.add(s(b)),f.cancelScheduledValues(b),A},exponentialRampToValueAtTime(b,y){if(b===0)throw new RangeError;if(!Number.isFinite(y)||y<0)throw new RangeError;const v=g.context.currentTime;return T===null&&E.flush(v),Array.from(E).length===0&&(E.add(u(_,v)),f.setValueAtTime(_,v)),E.add(i(b,y)),f.exponentialRampToValueAtTime(b,y),A},linearRampToValueAtTime(b,y){const v=g.context.currentTime;return T===null&&E.flush(v),Array.from(E).length===0&&(E.add(u(_,v)),f.setValueAtTime(_,v)),E.add(a(b,y)),f.linearRampToValueAtTime(b,y),A},setTargetAtTime(b,y,v){return T===null&&E.flush(g.context.currentTime),E.add(c(b,y,v)),f.setTargetAtTime(b,y,v),A},setValueAtTime(b,y){return T===null&&E.flush(g.context.currentTime),E.add(u(b,y)),f.setValueAtTime(b,y),A},setValueCurveAtTime(b,y,v){const M=b instanceof Float32Array?b:new Float32Array(b);if(l!==null&&l.name==="webkitAudioContext"){const k=y+v,N=g.context.sampleRate,U=Math.ceil(y*N),x=Math.floor(k*N),W=x-U,L=new Float32Array(W);for(let S=0;S<W;S+=1){const V=(M.length-1)/v*((U+S)/N-y),O=Math.floor(V),P=Math.ceil(V);L[S]=O===P?M[O]:(1-(V-O))*M[O]+(1-(P-V))*M[P]}T===null&&E.flush(g.context.currentTime),E.add(d(L,y,v)),f.setValueCurveAtTime(L,y,v);const I=x/N;I<k&&m(A,L[L.length-1],I),m(A,M[M.length-1],k)}else T===null&&E.flush(g.context.currentTime),E.add(d(M,y,v)),f.setValueCurveAtTime(M,y,v);return A}};return n.set(A,f),t.set(A,g),e(A,T),A},rs=e=>({replay(t){for(const n of e)if(n.type==="exponentialRampToValue"){const{endTime:r,value:o}=n;t.exponentialRampToValueAtTime(o,r)}else if(n.type==="linearRampToValue"){const{endTime:r,value:o}=n;t.linearRampToValueAtTime(o,r)}else if(n.type==="setTarget"){const{startTime:r,target:o,timeConstant:s}=n;t.setTargetAtTime(o,r,s)}else if(n.type==="setValue"){const{startTime:r,value:o}=n;t.setValueAtTime(o,r)}else if(n.type==="setValueCurve"){const{duration:r,startTime:o,values:s}=n;t.setValueCurveAtTime(s,o,r)}else throw new Error("Can't apply an unknown automation.")}});class xn{constructor(t){this._map=new Map(t)}get size(){return this._map.size}entries(){return this._map.entries()}forEach(t,n=null){return this._map.forEach((r,o)=>t.call(n,r,o,this))}get(t){return this._map.get(t)}has(t){return this._map.has(t)}keys(){return this._map.keys()}values(){return this._map.values()}}const os={channelCount:2,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:1,numberOfOutputs:1,parameterData:{},processorOptions:{}},ss=(e,t,n,r,o,s,i,a,c,u,d,l,m,g)=>class extends t{constructor(f,p,h){var _;const E=a(f),T=c(E),A=d({...os,...h});m(A);const b=it.get(E),y=b?.get(p),v=T||E.state!=="closed"?E:(_=i(E))!==null&&_!==void 0?_:E,M=o(v,T?null:f.baseLatency,u,p,y,A),k=T?r(p,A,y):null;super(f,!0,M,k);const N=[];M.parameters.forEach((x,W)=>{const L=n(this,T,x);N.push([W,L])}),this._nativeAudioWorkletNode=M,this._onprocessorerror=null,this._parameters=new xn(N),T&&e(E,this);const{activeInputs:U}=s(this);l(M,U)}get onprocessorerror(){return this._onprocessorerror}set onprocessorerror(f){const p=typeof f=="function"?g(this,f):null;this._nativeAudioWorkletNode.onprocessorerror=p;const h=this._nativeAudioWorkletNode.onprocessorerror;this._onprocessorerror=h!==null&&h===p?f:h}get parameters(){return this._parameters===null?this._nativeAudioWorkletNode.parameters:this._parameters}get port(){return this._nativeAudioWorkletNode.port}};function We(e,t,n,r,o){if(typeof e.copyFromChannel=="function")t[n].byteLength===0&&(t[n]=new Float32Array(128)),e.copyFromChannel(t[n],r,o);else{const s=e.getChannelData(r);if(t[n].byteLength===0)t[n]=s.slice(o,o+128);else{const i=new Float32Array(s.buffer,o*Float32Array.BYTES_PER_ELEMENT,128);t[n].set(i)}}}const Un=(e,t,n,r,o)=>{typeof e.copyToChannel=="function"?t[n].byteLength!==0&&e.copyToChannel(t[n],r,o):t[n].byteLength!==0&&e.getChannelData(r).set(t[n],o)},Be=(e,t)=>{const n=[];for(let r=0;r<e;r+=1){const o=[],s=typeof t=="number"?t:t[r];for(let i=0;i<s;i+=1)o.push(new Float32Array(128));n.push(o)}return n},is=(e,t)=>{const n=K(at,e),r=Y(t);return K(n,r)},as=async(e,t,n,r,o,s,i)=>{const a=t===null?Math.ceil(e.context.length/128)*128:t.length,c=r.channelCount*r.numberOfInputs,u=o.reduce((p,h)=>p+h,0),d=u===0?null:n.createBuffer(u,a,n.sampleRate);if(s===void 0)throw new Error("Missing the processor constructor.");const l=z(e),m=await is(n,e),g=Be(r.numberOfInputs,r.channelCount),w=Be(r.numberOfOutputs,o),f=Array.from(e.parameters.keys()).reduce((p,h)=>({...p,[h]:new Float32Array(128)}),{});for(let p=0;p<a;p+=128){if(r.numberOfInputs>0&&t!==null)for(let h=0;h<r.numberOfInputs;h+=1)for(let _=0;_<r.channelCount;_+=1)We(t,g[h],_,_,p);s.parameterDescriptors!==void 0&&t!==null&&s.parameterDescriptors.forEach(({name:h},_)=>{We(t,f,h,c+_,p)});for(let h=0;h<r.numberOfInputs;h+=1)for(let _=0;_<o[h];_+=1)w[h][_].byteLength===0&&(w[h][_]=new Float32Array(128));try{const h=g.map((E,T)=>l.activeInputs[T].size===0?[]:E),_=i(p/n.sampleRate,n.sampleRate,()=>m.process(h,w,f));if(d!==null)for(let E=0,T=0;E<r.numberOfOutputs;E+=1){for(let A=0;A<o[E];A+=1)Un(d,w[E],A,T+A,p);T+=o[E]}if(!_)break}catch(h){e.dispatchEvent(new ErrorEvent("processorerror",{colno:h.colno,filename:h.filename,lineno:h.lineno,message:h.message}));break}}return d},cs=(e,t,n,r,o,s,i,a,c,u,d,l,m,g,w,f)=>(p,h,_)=>{const E=new WeakMap;let T=null;const A=async(b,y)=>{let v=d(b),M=null;const k=On(v,y),N=Array.isArray(h.outputChannelCount)?h.outputChannelCount:Array.from(h.outputChannelCount);if(l===null){const U=N.reduce((I,S)=>I+S,0),x=o(y,{channelCount:Math.max(1,U),channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:Math.max(1,U)}),W=[];for(let I=0;I<b.numberOfOutputs;I+=1)W.push(r(y,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:N[I]}));const L=i(y,{channelCount:h.channelCount,channelCountMode:h.channelCountMode,channelInterpretation:h.channelInterpretation,gain:1});L.connect=t.bind(null,W),L.disconnect=c.bind(null,W),M=[x,W,L]}else k||(v=new l(y,p));if(E.set(y,M===null?v:M[2]),M!==null){if(T===null){if(_===void 0)throw new Error("Missing the processor constructor.");if(m===null)throw new Error("Missing the native OfflineAudioContext constructor.");const S=b.channelCount*b.numberOfInputs,V=_.parameterDescriptors===void 0?0:_.parameterDescriptors.length,O=S+V;T=as(b,O===0?null:await(async()=>{const B=new m(O,Math.ceil(b.context.length/128)*128,y.sampleRate),F=[],Q=[];for(let $=0;$<h.numberOfInputs;$+=1)F.push(i(B,{channelCount:h.channelCount,channelCountMode:h.channelCountMode,channelInterpretation:h.channelInterpretation,gain:1})),Q.push(o(B,{channelCount:h.channelCount,channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:h.channelCount}));const pe=await Promise.all(Array.from(b.parameters.values()).map(async $=>{const H=s(B,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",offset:$.value});return await g(B,$,H.offset),H})),me=r(B,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:Math.max(1,S+V)});for(let $=0;$<h.numberOfInputs;$+=1){F[$].connect(Q[$]);for(let H=0;H<h.channelCount;H+=1)Q[$].connect(me,H,$*h.channelCount+H)}for(const[$,H]of pe.entries())H.connect(me,0,S+$),H.start(0);return me.connect(B.destination),await Promise.all(F.map($=>w(b,B,$))),f(B)})(),y,h,N,_,u)}const U=await T,x=n(y,{buffer:null,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",loop:!1,loopEnd:0,loopStart:0,playbackRate:1}),[W,L,I]=M;U!==null&&(x.buffer=U,x.start(0)),x.connect(W);for(let S=0,V=0;S<b.numberOfOutputs;S+=1){const O=L[S];for(let P=0;P<N[S];P+=1)W.connect(O,V+P,P);V+=N[S]}return I}if(k)for(const[U,x]of b.parameters.entries())await e(y,x,v.parameters.get(U));else for(const[U,x]of b.parameters.entries())await g(y,x,v.parameters.get(U));return await w(b,y,v),v};return{render(b,y){a(y,b);const v=E.get(y);return v!==void 0?Promise.resolve(v):A(b,y)}}},us=(e,t)=>(n,r)=>{const o=t.get(n);if(o!==void 0)return o;const s=e.get(n);if(s!==void 0)return s;try{const i=r();return i instanceof Promise?(e.set(n,i),i.catch(()=>!1).then(a=>(e.delete(n),t.set(n,a),a))):(t.set(n,i),i)}catch{return t.set(n,!1),!1}},ls=e=>(t,n,r)=>e(n,t,r),ds=e=>(t,n,r=0,o=0)=>{const s=t[r];if(s===void 0)throw e();return Ue(n)?s.connect(n,0,o):s.connect(n,0)},fs=e=>t=>(e[0]=t,e[0]),hs=()=>new DOMException("","DataCloneError"),tn=e=>{const{port1:t,port2:n}=new MessageChannel;return new Promise(r=>{const o=()=>{n.onmessage=null,t.close(),n.close(),r()};n.onmessage=()=>o();try{t.postMessage(e,[e])}catch{}finally{o()}})},ps=(e,t,n,r,o,s,i,a,c,u,d)=>(l,m)=>{const g=i(l)?l:s(l);if(o.has(m)){const w=n();return Promise.reject(w)}try{o.add(m)}catch{}return t(c,()=>c(g))?g.decodeAudioData(m).then(w=>(tn(m).catch(()=>{}),t(a,()=>a(w))||d(w),e.add(w),w)):new Promise((w,f)=>{const p=async()=>{try{await tn(m)}catch{}},h=_=>{f(_),p()};try{g.decodeAudioData(m,_=>{typeof _.copyFromChannel!="function"&&(u(_),Rn(_)),e.add(_),p().then(()=>w(_))},_=>{h(_===null?r():_)})}catch(_){h(_)}})},ms=(e,t,n,r,o,s,i,a)=>(c,u)=>{const d=t.get(c);if(d===void 0)throw new Error("Missing the expected cycle count.");const l=s(c.context),m=a(l);if(d===u){if(t.delete(c),!m&&i(c)){const g=r(c),{outputs:w}=n(c);for(const f of w)if(Ce(f)){const p=r(f[0]);e(g,p,f[1],f[2])}else{const p=o(f[0]);g.connect(p,f[1])}}}else t.set(c,d-u)},gs=e=>(t,n,r,o)=>e(t[o],s=>s[0]===n&&s[1]===r),ws=e=>(t,n)=>{e(t).delete(n)},vs=e=>"delayTime"in e,_s=(e,t,n)=>function r(o,s){const i=xe(s)?s:n(e,s);if(vs(i))return[];if(o[0]===i)return[o];if(o.includes(i))return[];const{outputs:a}=t(i);return Array.from(a).map(c=>r([...o,i],c[0])).reduce((c,u)=>c.concat(u),[])},Re=(e,t,n)=>{const r=t[n];if(r===void 0)throw e();return r},Es=e=>(t,n=void 0,r=void 0,o=0)=>n===void 0?t.forEach(s=>s.disconnect()):typeof n=="number"?Re(e,t,n).disconnect():Ue(n)?r===void 0?t.forEach(s=>s.disconnect(n)):o===void 0?Re(e,t,r).disconnect(n,0):Re(e,t,r).disconnect(n,0,o):r===void 0?t.forEach(s=>s.disconnect(n)):Re(e,t,r).disconnect(n,0),ys=()=>new DOMException("","EncodingError"),As=e=>t=>new Promise((n,r)=>{if(e===null){r(new SyntaxError);return}const o=e.document.head;if(o===null)r(new SyntaxError);else{const s=e.document.createElement("script"),i=new Blob([t],{type:"application/javascript"}),a=URL.createObjectURL(i),c=e.onerror,u=()=>{e.onerror=c,URL.revokeObjectURL(a)};e.onerror=(d,l,m,g,w)=>{if(l===a||l===e.location.href&&m===1&&g===1)return u(),r(w),!1;if(c!==null)return c(d,l,m,g,w)},s.onerror=()=>{u(),r(new SyntaxError)},s.onload=()=>{u(),n()},s.src=a,s.type="module",o.appendChild(s)}}),bs=e=>class{constructor(n){this._nativeEventTarget=n,this._listeners=new WeakMap}addEventListener(n,r,o){if(r!==null){let s=this._listeners.get(r);s===void 0&&(s=e(this,r),typeof r=="function"&&this._listeners.set(r,s)),this._nativeEventTarget.addEventListener(n,s,o)}}dispatchEvent(n){return this._nativeEventTarget.dispatchEvent(n)}removeEventListener(n,r,o){const s=r===null?void 0:this._listeners.get(r);this._nativeEventTarget.removeEventListener(n,s===void 0?null:s,o)}},Cs=e=>(t,n,r)=>{Object.defineProperties(e,{currentFrame:{configurable:!0,get(){return Math.round(t*n)}},currentTime:{configurable:!0,get(){return t}}});try{return r()}finally{e!==null&&(delete e.currentFrame,delete e.currentTime)}},Ts=e=>async t=>{try{const n=await fetch(t);if(n.ok)return[await n.text(),n.url]}catch{}throw e()},Ms=(e,t)=>n=>t(e,n),Ns=e=>t=>{const n=e(t);if(n.renderer===null)throw new Error("Missing the renderer of the given AudioNode in the audio graph.");return n.renderer},Os=e=>t=>{var n;return(n=e.get(t))!==null&&n!==void 0?n:0},Rs=e=>t=>{const n=e(t);if(n.renderer===null)throw new Error("Missing the renderer of the given AudioParam in the audio graph.");return n.renderer},Is=e=>t=>e.get(t),Z=()=>new DOMException("","InvalidStateError"),Ss=e=>t=>{const n=e.get(t);if(n===void 0)throw Z();return n},ks=(e,t)=>n=>{let r=e.get(n);if(r!==void 0)return r;if(t===null)throw new Error("Missing the native OfflineAudioContext constructor.");return r=new t(1,1,44100),e.set(n,r),r},Ls=e=>t=>{const n=e.get(t);if(n===void 0)throw new Error("The context has no set of AudioWorkletNodes.");return n},Ps=()=>new DOMException("","InvalidAccessError"),xs=(e,t,n,r,o,s)=>i=>(a,c)=>{const u=e.get(a);if(u===void 0){if(!i&&s(a)){const d=r(a),{outputs:l}=n(a);for(const m of l)if(Ce(m)){const g=r(m[0]);t(d,g,m[1],m[2])}else{const g=o(m[0]);d.disconnect(g,m[1])}}e.set(a,c)}else e.set(a,u+c)},Us=e=>t=>e!==null&&t instanceof e,Ws=e=>t=>e!==null&&typeof e.AudioNode=="function"&&t instanceof e.AudioNode,Bs=e=>t=>e!==null&&typeof e.AudioParam=="function"&&t instanceof e.AudioParam,Ds=(e,t)=>n=>e(n)||t(n),Vs=e=>t=>e!==null&&t instanceof e,Fs=e=>e!==null&&e.isSecureContext,js=(e,t,n,r)=>class extends e{constructor(s,i){const a=n(s),c=t(a,i);if(r(a))throw new TypeError;super(s,!0,c,null),this._nativeMediaStreamAudioSourceNode=c}get mediaStream(){return this._nativeMediaStreamAudioSourceNode.mediaStream}},$s=(e,t,n,r,o)=>class extends r{constructor(i={}){if(o===null)throw new Error("Missing the native AudioContext constructor.");let a;try{a=new o(i)}catch(d){throw d.code===12&&d.message==="sampleRate is not in range"?t():d}if(a===null)throw n();if(!$o(i.latencyHint))throw new TypeError(`The provided value '${i.latencyHint}' is not a valid enum value of type AudioContextLatencyCategory.`);if(i.sampleRate!==void 0&&a.sampleRate!==i.sampleRate)throw t();super(a,2);const{latencyHint:c}=i,{sampleRate:u}=a;if(this._baseLatency=typeof a.baseLatency=="number"?a.baseLatency:c==="balanced"?512/u:c==="interactive"||c===void 0?256/u:c==="playback"?1024/u:Math.max(2,Math.min(128,Math.round(c*u/128)))*128/u,this._nativeAudioContext=a,o.name==="webkitAudioContext"?(this._nativeGainNode=a.createGain(),this._nativeOscillatorNode=a.createOscillator(),this._nativeGainNode.gain.value=1e-37,this._nativeOscillatorNode.connect(this._nativeGainNode).connect(a.destination),this._nativeOscillatorNode.start()):(this._nativeGainNode=null,this._nativeOscillatorNode=null),this._state=null,a.state==="running"){this._state="suspended";const d=()=>{this._state==="suspended"&&(this._state=null),a.removeEventListener("statechange",d)};a.addEventListener("statechange",d)}}get baseLatency(){return this._baseLatency}get state(){return this._state!==null?this._state:this._nativeAudioContext.state}close(){return this.state==="closed"?this._nativeAudioContext.close().then(()=>{throw e()}):(this._state==="suspended"&&(this._state=null),this._nativeAudioContext.close().then(()=>{this._nativeGainNode!==null&&this._nativeOscillatorNode!==null&&(this._nativeOscillatorNode.stop(),this._nativeGainNode.disconnect(),this._nativeOscillatorNode.disconnect()),jo(this)}))}resume(){return this._state==="suspended"?new Promise((i,a)=>{const c=()=>{this._nativeAudioContext.removeEventListener("statechange",c),this._nativeAudioContext.state==="running"?i():this.resume().then(i,a)};this._nativeAudioContext.addEventListener("statechange",c)}):this._nativeAudioContext.resume().catch(i=>{throw i===void 0||i.code===15?e():i})}suspend(){return this._nativeAudioContext.suspend().catch(i=>{throw i===void 0?e():i})}},Gs=(e,t,n,r,o,s)=>class extends n{constructor(a,c){super(a),this._nativeContext=a,bn.set(this,a),r(a)&&o.set(a,new Set),this._destination=new e(this,c),this._listener=t(this,a),this._onstatechange=null}get currentTime(){return this._nativeContext.currentTime}get destination(){return this._destination}get listener(){return this._listener}get onstatechange(){return this._onstatechange}set onstatechange(a){const c=typeof a=="function"?s(this,a):null;this._nativeContext.onstatechange=c;const u=this._nativeContext.onstatechange;this._onstatechange=u!==null&&u===c?a:u}get sampleRate(){return this._nativeContext.sampleRate}get state(){return this._nativeContext.state}},ht=e=>{const t=new Uint32Array([1179011410,40,1163280727,544501094,16,131073,44100,176400,1048580,1635017060,4,0]);try{const n=e.decodeAudioData(t.buffer,()=>{});return n===void 0?!1:(n.catch(()=>{}),!0)}catch{}return!1},qs=(e,t)=>(n,r,o)=>{const s=new Set;return n.connect=(i=>(a,c=0,u=0)=>{const d=s.size===0;if(t(a))return i.call(n,a,c,u),e(s,[a,c,u],l=>l[0]===a&&l[1]===c&&l[2]===u,!0),d&&r(),a;i.call(n,a,c),e(s,[a,c],l=>l[0]===a&&l[1]===c,!0),d&&r()})(n.connect),n.disconnect=(i=>(a,c,u)=>{const d=s.size>0;if(a===void 0)i.apply(n),s.clear();else if(typeof a=="number"){i.call(n,a);for(const m of s)m[1]===a&&s.delete(m)}else{t(a)?i.call(n,a,c,u):i.call(n,a,c);for(const m of s)m[0]===a&&(c===void 0||m[1]===c)&&(u===void 0||m[2]===u)&&s.delete(m)}const l=s.size===0;d&&l&&o()})(n.disconnect),n},ce=(e,t,n)=>{const r=t[n];r!==void 0&&r!==e[n]&&(e[n]=r)},Te=(e,t)=>{ce(e,t,"channelCount"),ce(e,t,"channelCountMode"),ce(e,t,"channelInterpretation")},zs=e=>e===null?null:e.hasOwnProperty("AudioBuffer")?e.AudioBuffer:null,yt=(e,t,n)=>{const r=t[n];r!==void 0&&r!==e[n].value&&(e[n].value=r)},Hs=e=>{e.start=(t=>{let n=!1;return(r=0,o=0,s)=>{if(n)throw Z();t.call(e,r,o,s),n=!0}})(e.start)},Wn=e=>{e.start=(t=>(n=0,r=0,o)=>{if(typeof o=="number"&&o<0||r<0||n<0)throw new RangeError("The parameters can't be negative.");t.call(e,n,r,o)})(e.start)},Bn=e=>{e.stop=(t=>(n=0)=>{if(n<0)throw new RangeError("The parameter can't be negative.");t.call(e,n)})(e.stop)},Xs=(e,t,n,r,o,s,i,a,c,u,d)=>(l,m)=>{const g=l.createBufferSource();return Te(g,m),yt(g,m,"playbackRate"),ce(g,m,"buffer"),ce(g,m,"loop"),ce(g,m,"loopEnd"),ce(g,m,"loopStart"),t(n,()=>n(l))||Hs(g),t(r,()=>r(l))||c(g),t(o,()=>o(l))||u(g,l),t(s,()=>s(l))||Wn(g),t(i,()=>i(l))||d(g,l),t(a,()=>a(l))||Bn(g),e(l,g),g},Ys=e=>e===null?null:e.hasOwnProperty("AudioContext")?e.AudioContext:e.hasOwnProperty("webkitAudioContext")?e.webkitAudioContext:null,Zs=(e,t)=>(n,r,o)=>{const s=n.destination;if(s.channelCount!==r)try{s.channelCount=r}catch{}o&&s.channelCountMode!=="explicit"&&(s.channelCountMode="explicit"),s.maxChannelCount===0&&Object.defineProperty(s,"maxChannelCount",{value:r});const i=e(n,{channelCount:r,channelCountMode:s.channelCountMode,channelInterpretation:s.channelInterpretation,gain:1});return t(i,"channelCount",a=>()=>a.call(i),a=>c=>{a.call(i,c);try{s.channelCount=c}catch(u){if(c>s.maxChannelCount)throw u}}),t(i,"channelCountMode",a=>()=>a.call(i),a=>c=>{a.call(i,c),s.channelCountMode=c}),t(i,"channelInterpretation",a=>()=>a.call(i),a=>c=>{a.call(i,c),s.channelInterpretation=c}),Object.defineProperty(i,"maxChannelCount",{get:()=>s.maxChannelCount}),i.connect(s),i},Ks=e=>e===null?null:e.hasOwnProperty("AudioWorkletNode")?e.AudioWorkletNode:null,Qs=e=>{const{port1:t}=new MessageChannel;try{t.postMessage(e)}finally{t.close()}},Js=(e,t,n,r,o)=>(s,i,a,c,u,d)=>{if(a!==null)try{const l=new a(s,c,d),m=new Map;let g=null;if(Object.defineProperties(l,{channelCount:{get:()=>d.channelCount,set:()=>{throw e()}},channelCountMode:{get:()=>"explicit",set:()=>{throw e()}},onprocessorerror:{get:()=>g,set:w=>{typeof g=="function"&&l.removeEventListener("processorerror",g),g=typeof w=="function"?w:null,typeof g=="function"&&l.addEventListener("processorerror",g)}}}),l.addEventListener=(w=>(...f)=>{if(f[0]==="processorerror"){const p=typeof f[1]=="function"?f[1]:typeof f[1]=="object"&&f[1]!==null&&typeof f[1].handleEvent=="function"?f[1].handleEvent:null;if(p!==null){const h=m.get(f[1]);h!==void 0?f[1]=h:(f[1]=_=>{_.type==="error"?(Object.defineProperties(_,{type:{value:"processorerror"}}),p(_)):p(new ErrorEvent(f[0],{..._}))},m.set(p,f[1]))}}return w.call(l,"error",f[1],f[2]),w.call(l,...f)})(l.addEventListener),l.removeEventListener=(w=>(...f)=>{if(f[0]==="processorerror"){const p=m.get(f[1]);p!==void 0&&(m.delete(f[1]),f[1]=p)}return w.call(l,"error",f[1],f[2]),w.call(l,f[0],f[1],f[2])})(l.removeEventListener),d.numberOfOutputs!==0){const w=n(s,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});return l.connect(w).connect(s.destination),o(l,()=>w.disconnect(),()=>w.connect(s.destination))}return l}catch(l){throw l.code===11?r():l}if(u===void 0)throw r();return Qs(d),t(s,i,u,d)},ei=(e,t)=>e===null?512:Math.max(512,Math.min(16384,Math.pow(2,Math.round(Math.log2(e*t))))),ti=e=>new Promise((t,n)=>{const{port1:r,port2:o}=new MessageChannel;r.onmessage=({data:s})=>{r.close(),o.close(),t(s)},r.onmessageerror=({data:s})=>{r.close(),o.close(),n(s)},o.postMessage(e)}),ni=async(e,t)=>{const n=await ti(t);return new e(n)},ri=(e,t,n,r)=>{let o=at.get(e);o===void 0&&(o=new WeakMap,at.set(e,o));const s=ni(n,r);return o.set(t,s),s},oi=(e,t,n,r,o,s,i,a,c,u,d,l,m)=>(g,w,f,p)=>{if(p.numberOfInputs===0&&p.numberOfOutputs===0)throw c();const h=Array.isArray(p.outputChannelCount)?p.outputChannelCount:Array.from(p.outputChannelCount);if(h.some(C=>C<1))throw c();if(h.length!==p.numberOfOutputs)throw t();if(p.channelCountMode!=="explicit")throw c();const _=p.channelCount*p.numberOfInputs,E=h.reduce((C,R)=>C+R,0),T=f.parameterDescriptors===void 0?0:f.parameterDescriptors.length;if(_+T>6||E>6)throw c();const A=new MessageChannel,b=[],y=[];for(let C=0;C<p.numberOfInputs;C+=1)b.push(i(g,{channelCount:p.channelCount,channelCountMode:p.channelCountMode,channelInterpretation:p.channelInterpretation,gain:1})),y.push(o(g,{channelCount:p.channelCount,channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:p.channelCount}));const v=[];if(f.parameterDescriptors!==void 0)for(const{defaultValue:C,maxValue:R,minValue:q,name:j}of f.parameterDescriptors){const D=s(g,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",offset:p.parameterData[j]!==void 0?p.parameterData[j]:C===void 0?0:C});Object.defineProperties(D.offset,{defaultValue:{get:()=>C===void 0?0:C},maxValue:{get:()=>R===void 0?vt:R},minValue:{get:()=>q===void 0?$e:q}}),v.push(D)}const M=r(g,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:Math.max(1,_+T)}),k=ei(w,g.sampleRate),N=a(g,k,_+T,Math.max(1,E)),U=o(g,{channelCount:Math.max(1,E),channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:Math.max(1,E)}),x=[];for(let C=0;C<p.numberOfOutputs;C+=1)x.push(r(g,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:h[C]}));for(let C=0;C<p.numberOfInputs;C+=1){b[C].connect(y[C]);for(let R=0;R<p.channelCount;R+=1)y[C].connect(M,R,C*p.channelCount+R)}const W=new xn(f.parameterDescriptors===void 0?[]:f.parameterDescriptors.map(({name:C},R)=>{const q=v[R];return q.connect(M,0,_+R),q.start(0),[C,q.offset]}));M.connect(N);let L=p.channelInterpretation,I=null;const S=p.numberOfOutputs===0?[N]:x,V={get bufferSize(){return k},get channelCount(){return p.channelCount},set channelCount(C){throw n()},get channelCountMode(){return p.channelCountMode},set channelCountMode(C){throw n()},get channelInterpretation(){return L},set channelInterpretation(C){for(const R of b)R.channelInterpretation=C;L=C},get context(){return N.context},get inputs(){return b},get numberOfInputs(){return p.numberOfInputs},get numberOfOutputs(){return p.numberOfOutputs},get onprocessorerror(){return I},set onprocessorerror(C){typeof I=="function"&&V.removeEventListener("processorerror",I),I=typeof C=="function"?C:null,typeof I=="function"&&V.addEventListener("processorerror",I)},get parameters(){return W},get port(){return A.port2},addEventListener(...C){return N.addEventListener(C[0],C[1],C[2])},connect:e.bind(null,S),disconnect:u.bind(null,S),dispatchEvent(...C){return N.dispatchEvent(C[0])},removeEventListener(...C){return N.removeEventListener(C[0],C[1],C[2])}},O=new Map;A.port1.addEventListener=(C=>(...R)=>{if(R[0]==="message"){const q=typeof R[1]=="function"?R[1]:typeof R[1]=="object"&&R[1]!==null&&typeof R[1].handleEvent=="function"?R[1].handleEvent:null;if(q!==null){const j=O.get(R[1]);j!==void 0?R[1]=j:(R[1]=D=>{d(g.currentTime,g.sampleRate,()=>q(D))},O.set(q,R[1]))}}return C.call(A.port1,R[0],R[1],R[2])})(A.port1.addEventListener),A.port1.removeEventListener=(C=>(...R)=>{if(R[0]==="message"){const q=O.get(R[1]);q!==void 0&&(O.delete(R[1]),R[1]=q)}return C.call(A.port1,R[0],R[1],R[2])})(A.port1.removeEventListener);let P=null;Object.defineProperty(A.port1,"onmessage",{get:()=>P,set:C=>{typeof P=="function"&&A.port1.removeEventListener("message",P),P=typeof C=="function"?C:null,typeof P=="function"&&(A.port1.addEventListener("message",P),A.port1.start())}}),f.prototype.port=A.port1;let B=null;ri(g,V,f,p).then(C=>B=C);const Q=Be(p.numberOfInputs,p.channelCount),pe=Be(p.numberOfOutputs,h),me=f.parameterDescriptors===void 0?[]:f.parameterDescriptors.reduce((C,{name:R})=>({...C,[R]:new Float32Array(128)}),{});let $=!0;const H=()=>{p.numberOfOutputs>0&&N.disconnect(U);for(let C=0,R=0;C<p.numberOfOutputs;C+=1){const q=x[C];for(let j=0;j<h[C];j+=1)U.disconnect(q,R+j,j);R+=h[C]}},Me=new Map;N.onaudioprocess=({inputBuffer:C,outputBuffer:R})=>{if(B!==null){const q=l(V);for(let j=0;j<k;j+=128){for(let D=0;D<p.numberOfInputs;D+=1)for(let G=0;G<p.channelCount;G+=1)We(C,Q[D],G,G,j);f.parameterDescriptors!==void 0&&f.parameterDescriptors.forEach(({name:D},G)=>{We(C,me,D,_+G,j)});for(let D=0;D<p.numberOfInputs;D+=1)for(let G=0;G<h[D];G+=1)pe[D][G].byteLength===0&&(pe[D][G]=new Float32Array(128));try{const D=Q.map((X,re)=>{if(q[re].size>0)return Me.set(re,k/128),X;const Qe=Me.get(re);return Qe===void 0?[]:(X.every(gr=>gr.every(wr=>wr===0))&&(Qe===1?Me.delete(re):Me.set(re,Qe-1)),X)});$=d(g.currentTime+j/g.sampleRate,g.sampleRate,()=>B.process(D,pe,me));for(let X=0,re=0;X<p.numberOfOutputs;X+=1){for(let _e=0;_e<h[X];_e+=1)Un(R,pe[X],_e,re+_e,j);re+=h[X]}}catch(D){$=!1,V.dispatchEvent(new ErrorEvent("processorerror",{colno:D.colno,filename:D.filename,lineno:D.lineno,message:D.message}))}if(!$){for(let D=0;D<p.numberOfInputs;D+=1){b[D].disconnect(y[D]);for(let G=0;G<p.channelCount;G+=1)y[j].disconnect(M,G,D*p.channelCount+G)}if(f.parameterDescriptors!==void 0){const D=f.parameterDescriptors.length;for(let G=0;G<D;G+=1){const X=v[G];X.disconnect(M,0,_+G),X.stop()}}M.disconnect(N),N.onaudioprocess=null,Ze?H():kt();break}}}};let Ze=!1;const Ke=i(g,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0}),St=()=>N.connect(Ke).connect(g.destination),kt=()=>{N.disconnect(Ke),Ke.disconnect()},pr=()=>{if($){kt(),p.numberOfOutputs>0&&N.connect(U);for(let C=0,R=0;C<p.numberOfOutputs;C+=1){const q=x[C];for(let j=0;j<h[C];j+=1)U.connect(q,R+j,j);R+=h[C]}}Ze=!0},mr=()=>{$&&(St(),H()),Ze=!1};return St(),m(V,pr,mr)},si=(e,t)=>(n,r)=>{const o=n.createChannelMerger(r.numberOfInputs);return e!==null&&e.name==="webkitAudioContext"&&t(n,o),Te(o,r),o},ii=e=>{const t=e.numberOfOutputs;Object.defineProperty(e,"channelCount",{get:()=>t,set:n=>{if(n!==t)throw Z()}}),Object.defineProperty(e,"channelCountMode",{get:()=>"explicit",set:n=>{if(n!=="explicit")throw Z()}}),Object.defineProperty(e,"channelInterpretation",{get:()=>"discrete",set:n=>{if(n!=="discrete")throw Z()}})},Dn=(e,t)=>{const n=e.createChannelSplitter(t.numberOfOutputs);return Te(n,t),ii(n),n},ai=(e,t,n,r,o)=>(s,i)=>{if(s.createConstantSource===void 0)return n(s,i);const a=s.createConstantSource();return Te(a,i),yt(a,i,"offset"),t(r,()=>r(s))||Wn(a),t(o,()=>o(s))||Bn(a),e(s,a),a},Vn=(e,t)=>(e.connect=t.connect.bind(t),e.disconnect=t.disconnect.bind(t),e),ci=(e,t,n,r)=>(o,{offset:s,...i})=>{const a=o.createBuffer(1,2,44100),c=t(o,{buffer:null,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",loop:!1,loopEnd:0,loopStart:0,playbackRate:1}),u=n(o,{...i,gain:s}),d=a.getChannelData(0);d[0]=1,d[1]=1,c.buffer=a,c.loop=!0;const l={get bufferSize(){},get channelCount(){return u.channelCount},set channelCount(w){u.channelCount=w},get channelCountMode(){return u.channelCountMode},set channelCountMode(w){u.channelCountMode=w},get channelInterpretation(){return u.channelInterpretation},set channelInterpretation(w){u.channelInterpretation=w},get context(){return u.context},get inputs(){return[]},get numberOfInputs(){return c.numberOfInputs},get numberOfOutputs(){return u.numberOfOutputs},get offset(){return u.gain},get onended(){return c.onended},set onended(w){c.onended=w},addEventListener(...w){return c.addEventListener(w[0],w[1],w[2])},dispatchEvent(...w){return c.dispatchEvent(w[0])},removeEventListener(...w){return c.removeEventListener(w[0],w[1],w[2])},start(w=0){c.start.call(c,w)},stop(w=0){c.stop.call(c,w)}},m=()=>c.connect(u),g=()=>c.disconnect(u);return e(o,c),r(Vn(l,u),m,g)},ie=(e,t)=>{const n=e.createGain();return Te(n,t),yt(n,t,"gain"),n},ui=(e,{mediaStream:t})=>{const n=t.getAudioTracks();n.sort((s,i)=>s.id<i.id?-1:s.id>i.id?1:0);const r=n.slice(0,1),o=e.createMediaStreamSource(new MediaStream(r));return Object.defineProperty(o,"mediaStream",{value:t}),o},li=e=>e===null?null:e.hasOwnProperty("OfflineAudioContext")?e.OfflineAudioContext:e.hasOwnProperty("webkitOfflineAudioContext")?e.webkitOfflineAudioContext:null,di=e=>(t,{disableNormalization:n,imag:r,real:o})=>{const s=r instanceof Float32Array?r:new Float32Array(r),i=o instanceof Float32Array?o:new Float32Array(o),a=t.createPeriodicWave(i,s,{disableNormalization:n});if(Array.from(r).length<2)throw e();return a},At=(e,t,n,r)=>e.createScriptProcessor(t,n,r),fe=()=>new DOMException("","NotSupportedError"),fi={disableNormalization:!1},hi=(e,t,n,r)=>class Fn{constructor(s,i){const a=t(s),c=r({...fi,...i}),u=e(a,c);return n.add(u),u}static[Symbol.hasInstance](s){return s!==null&&typeof s=="object"&&Object.getPrototypeOf(s)===Fn.prototype||n.has(s)}},pi=(e,t)=>(n,r,o)=>(e(r).replay(o),t(r,n,o)),mi=(e,t,n)=>async(r,o,s)=>{const i=e(r);await Promise.all(i.activeInputs.map((a,c)=>Array.from(a).map(async([u,d])=>{const m=await t(u).render(u,o),g=r.context.destination;!n(u)&&(r!==g||!n(r))&&m.connect(s,d,c)})).reduce((a,c)=>[...a,...c],[]))},gi=(e,t,n)=>async(r,o,s)=>{const i=t(r);await Promise.all(Array.from(i.activeInputs).map(async([a,c])=>{const d=await e(a).render(a,o);n(a)||d.connect(s,c)}))},wi=(e,t,n,r)=>o=>e(ht,()=>ht(o))?Promise.resolve(e(r,r)).then(s=>{if(!s){const i=n(o,512,0,1);o.oncomplete=()=>{i.onaudioprocess=null,i.disconnect()},i.onaudioprocess=()=>o.currentTime,i.connect(o.destination)}return o.startRendering()}):new Promise(s=>{const i=t(o,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});o.oncomplete=a=>{i.disconnect(),s(a.renderedBuffer)},i.connect(o.destination),o.startRendering()}),vi=e=>(t,n)=>{e.set(t,n)},_i=e=>()=>{if(e===null)return!1;try{new e({length:1,sampleRate:44100})}catch{return!1}return!0},Ei=(e,t)=>async()=>{if(e===null)return!0;if(t===null)return!1;const n=new Blob(['class A extends AudioWorkletProcessor{process(i){this.port.postMessage(i,[i[0][0].buffer])}}registerProcessor("a",A)'],{type:"application/javascript; charset=utf-8"}),r=new t(1,128,44100),o=URL.createObjectURL(n);let s=!1,i=!1;try{await r.audioWorklet.addModule(o);const a=new e(r,"a",{numberOfOutputs:0}),c=r.createOscillator();a.port.onmessage=()=>s=!0,a.onprocessorerror=()=>i=!0,c.connect(a),c.start(0),await r.startRendering()}catch{}finally{URL.revokeObjectURL(o)}return s&&!i},yi=(e,t)=>()=>{if(t===null)return Promise.resolve(!1);const n=new t(1,1,44100),r=e(n,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});return new Promise(o=>{n.oncomplete=()=>{r.disconnect(),o(n.currentTime!==0)},n.startRendering()})},Ai=()=>new DOMException("","UnknownError"),bi=()=>typeof window>"u"?null:window,Ci=(e,t)=>n=>{n.copyFromChannel=(r,o,s=0)=>{const i=e(s),a=e(o);if(a>=n.numberOfChannels)throw t();const c=n.length,u=n.getChannelData(a),d=r.length;for(let l=i<0?-i:0;l+i<c&&l<d;l+=1)r[l]=u[l+i]},n.copyToChannel=(r,o,s=0)=>{const i=e(s),a=e(o);if(a>=n.numberOfChannels)throw t();const c=n.length,u=n.getChannelData(a),d=r.length;for(let l=i<0?-i:0;l+i<c&&l<d;l+=1)u[l+i]=r[l]}},Ti=e=>t=>{t.copyFromChannel=(n=>(r,o,s=0)=>{const i=e(s),a=e(o);if(i<t.length)return n.call(t,r,a,i)})(t.copyFromChannel),t.copyToChannel=(n=>(r,o,s=0)=>{const i=e(s),a=e(o);if(i<t.length)return n.call(t,r,a,i)})(t.copyToChannel)},Mi=e=>(t,n)=>{const r=n.createBuffer(1,1,44100);t.buffer===null&&(t.buffer=r),e(t,"buffer",o=>()=>{const s=o.call(t);return s===r?null:s},o=>s=>o.call(t,s===null?r:s))},Ni=(e,t)=>(n,r)=>{r.channelCount=1,r.channelCountMode="explicit",Object.defineProperty(r,"channelCount",{get:()=>1,set:()=>{throw e()}}),Object.defineProperty(r,"channelCountMode",{get:()=>"explicit",set:()=>{throw e()}});const o=n.createBufferSource();t(r,()=>{const a=r.numberOfInputs;for(let c=0;c<a;c+=1)o.connect(r,0,c)},()=>o.disconnect(r))},Oi=(e,t,n)=>e.copyFromChannel===void 0?e.getChannelData(n)[0]:(e.copyFromChannel(t,n),t[0]),bt=(e,t,n,r)=>{let o=e;for(;!o.hasOwnProperty(t);)o=Object.getPrototypeOf(o);const{get:s,set:i}=Object.getOwnPropertyDescriptor(o,t);Object.defineProperty(e,t,{get:n(s),set:r(i)})},Ri=e=>({...e,outputChannelCount:e.outputChannelCount!==void 0?e.outputChannelCount:e.numberOfInputs===1&&e.numberOfOutputs===1?[e.channelCount]:Array.from({length:e.numberOfOutputs},()=>1)}),Ii=e=>{const{imag:t,real:n}=e;return t===void 0?n===void 0?{...e,imag:[0,0],real:[0,0]}:{...e,imag:Array.from(n,()=>0),real:n}:n===void 0?{...e,imag:t,real:Array.from(t,()=>0)}:{...e,imag:t,real:n}},jn=(e,t,n)=>{try{e.setValueAtTime(t,n)}catch(r){if(r.code!==9)throw r;jn(e,t,n+1e-7)}},Si=e=>{const t=e.createBufferSource();t.start();try{t.start()}catch{return!0}return!1},ki=e=>{const t=e.createBufferSource(),n=e.createBuffer(1,1,44100);t.buffer=n;try{t.start(0,1)}catch{return!1}return!0},Li=e=>{const t=e.createBufferSource();t.start();try{t.stop()}catch{return!1}return!0},$n=e=>{const t=e.createOscillator();try{t.start(-1)}catch(n){return n instanceof RangeError}return!1},Pi=e=>{const t=e.createBuffer(1,1,44100),n=e.createBufferSource();n.buffer=t,n.start(),n.stop();try{return n.stop(),!0}catch{return!1}},Gn=e=>{const t=e.createOscillator();try{t.stop(-1)}catch(n){return n instanceof RangeError}return!1},xi=e=>{const{port1:t,port2:n}=new MessageChannel;try{t.postMessage(e)}finally{t.close(),n.close()}},Ui=e=>{e.start=(t=>(n=0,r=0,o)=>{const s=e.buffer,i=s===null?r:Math.min(s.duration,r);s!==null&&i>s.duration-.5/e.context.sampleRate?t.call(e,n,0,0):t.call(e,n,i,o)})(e.start)},Wi=(e,t)=>{const n=t.createGain();e.connect(n);const r=(o=>()=>{o.call(e,n),e.removeEventListener("ended",r)})(e.disconnect);e.addEventListener("ended",r),Vn(e,n),e.stop=(o=>{let s=!1;return(i=0)=>{if(s)try{o.call(e,i)}catch{n.gain.setValueAtTime(0,i)}else o.call(e,i),s=!0}})(e.stop)},Ge=(e,t)=>n=>{const r={value:e};return Object.defineProperties(n,{currentTarget:r,target:r}),typeof t=="function"?t.call(e,n):t.handleEvent.call(e,n)},Bi=Ao(de),Di=Oo(de),Vi=gs(je),Fi=new WeakMap,ji=Os(Fi),he=us(new Map,new WeakMap),J=bi(),qn=Ns(z),Ct=mi(z,qn,le),ne=Ss(bn),ve=li(J),ee=Vs(ve),zn=new WeakMap,Hn=bs(Ge),qe=Ys(J),Xn=Us(qe),Yn=Ws(J),$i=Bs(J),ye=Ks(J),ze=ts(bo(_n),No(Bi,Di,dt,Vi,ft,z,ji,Ae,Y,de,ue,le,Ie),he,xs(st,ft,z,Y,Ee,ue),ae,Ps,fe,ms(dt,st,z,Y,Ee,ne,ue,ee),_s(zn,z,K),Hn,ne,Xn,Yn,$i,ee,ye),Zn=new WeakSet,nn=zs(J),Kn=fs(new Uint32Array(1)),Qn=Ci(Kn,ae),Jn=Ti(Kn),Gi=ko(Zn,he,fe,nn,ve,_i(nn),Qn,Jn),Tt=Ro(ie),er=gi(qn,be,le),tr=ls(er),He=Xs(Tt,he,Si,ki,Li,$n,Pi,Gn,Ui,Mi(bt),Wi),nr=pi(Rs(be),er),qi=xo(tr,He,Y,nr,Ct),Mt=ns(Co(yn),zn,An,rs,go,wo,vo,_o,Eo,nt,wn,qe,jn),zi=Po(ze,qi,Mt,Z,He,ne,ee,Ge),Hi=Go(ze,qo,ae,Z,Zs(ie,bt),ne,ee,Ct),Xe=qs(de,Yn),Xi=Ni(Z,Xe),Nt=si(qe,Xi),Yi=ci(Tt,He,ie,Xe),Ot=ai(Tt,he,Yi,$n,Gn),Zi=wi(he,ie,At,yi(ie,ve)),Ki=zo(Mt,Nt,Ot,At,fe,Oi,ee,bt),rr=new WeakMap,Qi=Gs(Hi,Ki,Hn,ee,rr,Ge),Ji=di(ae);hi(Ji,ne,new WeakSet,Ii);const or=Fs(J),Rt=Cs(J),sr=new WeakMap,ea=ks(sr,ve),rn=or?Mo(he,fe,As(J),Rt,Ts(yo),ne,ea,ee,ye,new WeakMap,new WeakMap,Ei(ye,ve),J):void 0,ta=Ds(Xn,ee);ps(Zn,he,hs,ys,new WeakSet,ne,ta,ut,ht,Qn,Jn);const na=js(ze,ui,ne,ee),ir=Ls(rr),ra=Io(ir),ar=ds(ae),oa=ws(ir),cr=Es(ae),ur=new WeakMap,sa=Ms(ur,K),ia=oi(ar,ae,Z,Nt,Dn,Ot,ie,At,fe,cr,Rt,sa,Xe),aa=Js(Z,ia,ie,fe,Xe),ca=cs(tr,ar,He,Nt,Dn,Ot,ie,oa,cr,Rt,Y,ye,ve,nr,Ct,Zi),ua=Is(sr),la=vi(ur),on=or?ss(ra,ze,Mt,ca,aa,z,ua,ne,ee,ye,Ri,la,xi,Ge):void 0,da=$s(Z,fe,Ai,Qi,qe),lr="Missing AudioWorklet support. Maybe this is not running in a secure context.",fa=async(e,t,n,r,o)=>{const{encoderId:s,port:i}=await dn(o,t.sampleRate);if(on===void 0)throw new Error(lr);const a=new zi(t,{buffer:e}),c=new na(t,{mediaStream:r}),u=fo(on,t,{channelCount:n});return{audioBufferSourceNode:a,encoderId:s,mediaStreamAudioSourceNode:c,port:i,recorderAudioWorkletNode:u}},ha=(e,t,n,r)=>(o,s,i)=>{var a;const c=(a=s.getAudioTracks()[0])===null||a===void 0?void 0:a.getSettings().sampleRate,u=new da({latencyHint:"playback",sampleRate:c}),d=Math.max(1024,Math.ceil(u.baseLatency*u.sampleRate)),l=new Gi({length:d,sampleRate:u.sampleRate}),m=[],g=lo(v=>{if(rn===void 0)throw new Error(lr);return rn(u,v)});let w=null,f=null,p=null,h=null,_=!0;const E=v=>{o.dispatchEvent(e("dataavailable",{data:new Blob(v,{type:i})}))},T=async(v,M)=>{const k=await Se(v,M);p===null?m.push(...k):(E(k),h=T(v,M))},A=()=>(_=!0,u.resume()),b=()=>{p!==null&&(w!==null&&(s.removeEventListener("addtrack",w),s.removeEventListener("removetrack",w)),f!==null&&clearTimeout(f),p.then(async({encoderId:v,mediaStreamAudioSourceNode:M,recorderAudioWorkletNode:k})=>{h!==null&&(h.catch(()=>{}),h=null),await k.stop(),M.disconnect(k);const N=await Se(v,null);p===null&&await y(),E([...m,...N]),m.length=0,o.dispatchEvent(new Event("stop"))}),p=null)},y=()=>(_=!1,u.suspend());return y(),{get mimeType(){return i},get state(){return p===null?"inactive":_?"recording":"paused"},pause(){if(p===null)throw n();_&&(y(),o.dispatchEvent(new Event("pause")))},resume(){if(p===null)throw n();_||(A(),o.dispatchEvent(new Event("resume")))},start(v){var M;if(p!==null)throw n();if(s.getVideoTracks().length>0)throw r();o.dispatchEvent(new Event("start"));const k=s.getAudioTracks(),N=k.length===0?2:(M=k[0].getSettings().channelCount)!==null&&M!==void 0?M:2;p=Promise.all([A(),g.then(()=>fa(l,u,N,s,i))]).then(async([,{audioBufferSourceNode:x,encoderId:W,mediaStreamAudioSourceNode:L,port:I,recorderAudioWorkletNode:S}])=>(L.connect(S),await new Promise(V=>{x.onended=V,x.connect(S),x.start(u.currentTime+d/u.sampleRate)}),x.disconnect(S),await S.record(I),v!==void 0&&(h=T(W,v)),{encoderId:W,mediaStreamAudioSourceNode:L,recorderAudioWorkletNode:S}));const U=s.getTracks();w=()=>{b(),o.dispatchEvent(new ErrorEvent("error",{error:t()}))},s.addEventListener("addtrack",w),s.addEventListener("removetrack",w),f=setInterval(()=>{const x=s.getTracks();(x.length!==U.length||x.some((W,L)=>W!==U[L]))&&w!==null&&w()},1e3)},stop:b}};class tt{constructor(t,n=0,r){if(n<0||r!==void 0&&r<0)throw new RangeError;const o=t.reduce((d,l)=>d+l.byteLength,0);if(n>o||r!==void 0&&n+r>o)throw new RangeError;const s=[],i=r===void 0?o-n:r,a=[];let c=0,u=n;for(const d of t)if(a.length===0)if(d.byteLength>u){c=d.byteLength-u;const l=c>i?i:c;s.push(new DataView(d,u,l)),a.push(d)}else u-=d.byteLength;else if(c<i){c+=d.byteLength;const l=c>i?d.byteLength-c+i:d.byteLength;s.push(new DataView(d,0,l)),a.push(d)}this._buffers=a,this._byteLength=i,this._byteOffset=u,this._dataViews=s,this._internalBuffer=new DataView(new ArrayBuffer(8))}get buffers(){return this._buffers}get byteLength(){return this._byteLength}get byteOffset(){return this._byteOffset}getFloat32(t,n){return this._internalBuffer.setUint8(0,this.getUint8(t+0)),this._internalBuffer.setUint8(1,this.getUint8(t+1)),this._internalBuffer.setUint8(2,this.getUint8(t+2)),this._internalBuffer.setUint8(3,this.getUint8(t+3)),this._internalBuffer.getFloat32(0,n)}getFloat64(t,n){return this._internalBuffer.setUint8(0,this.getUint8(t+0)),this._internalBuffer.setUint8(1,this.getUint8(t+1)),this._internalBuffer.setUint8(2,this.getUint8(t+2)),this._internalBuffer.setUint8(3,this.getUint8(t+3)),this._internalBuffer.setUint8(4,this.getUint8(t+4)),this._internalBuffer.setUint8(5,this.getUint8(t+5)),this._internalBuffer.setUint8(6,this.getUint8(t+6)),this._internalBuffer.setUint8(7,this.getUint8(t+7)),this._internalBuffer.getFloat64(0,n)}getInt16(t,n){return this._internalBuffer.setUint8(0,this.getUint8(t+0)),this._internalBuffer.setUint8(1,this.getUint8(t+1)),this._internalBuffer.getInt16(0,n)}getInt32(t,n){return this._internalBuffer.setUint8(0,this.getUint8(t+0)),this._internalBuffer.setUint8(1,this.getUint8(t+1)),this._internalBuffer.setUint8(2,this.getUint8(t+2)),this._internalBuffer.setUint8(3,this.getUint8(t+3)),this._internalBuffer.getInt32(0,n)}getInt8(t){const[n,r]=this._findDataViewWithOffset(t);return n.getInt8(t-r)}getUint16(t,n){return this._internalBuffer.setUint8(0,this.getUint8(t+0)),this._internalBuffer.setUint8(1,this.getUint8(t+1)),this._internalBuffer.getUint16(0,n)}getUint32(t,n){return this._internalBuffer.setUint8(0,this.getUint8(t+0)),this._internalBuffer.setUint8(1,this.getUint8(t+1)),this._internalBuffer.setUint8(2,this.getUint8(t+2)),this._internalBuffer.setUint8(3,this.getUint8(t+3)),this._internalBuffer.getUint32(0,n)}getUint8(t){const[n,r]=this._findDataViewWithOffset(t);return n.getUint8(t-r)}setFloat32(t,n,r){this._internalBuffer.setFloat32(0,n,r),this.setUint8(t,this._internalBuffer.getUint8(0)),this.setUint8(t+1,this._internalBuffer.getUint8(1)),this.setUint8(t+2,this._internalBuffer.getUint8(2)),this.setUint8(t+3,this._internalBuffer.getUint8(3))}setFloat64(t,n,r){this._internalBuffer.setFloat64(0,n,r),this.setUint8(t,this._internalBuffer.getUint8(0)),this.setUint8(t+1,this._internalBuffer.getUint8(1)),this.setUint8(t+2,this._internalBuffer.getUint8(2)),this.setUint8(t+3,this._internalBuffer.getUint8(3)),this.setUint8(t+4,this._internalBuffer.getUint8(4)),this.setUint8(t+5,this._internalBuffer.getUint8(5)),this.setUint8(t+6,this._internalBuffer.getUint8(6)),this.setUint8(t+7,this._internalBuffer.getUint8(7))}setInt16(t,n,r){this._internalBuffer.setInt16(0,n,r),this.setUint8(t,this._internalBuffer.getUint8(0)),this.setUint8(t+1,this._internalBuffer.getUint8(1))}setInt32(t,n,r){this._internalBuffer.setInt32(0,n,r),this.setUint8(t,this._internalBuffer.getUint8(0)),this.setUint8(t+1,this._internalBuffer.getUint8(1)),this.setUint8(t+2,this._internalBuffer.getUint8(2)),this.setUint8(t+3,this._internalBuffer.getUint8(3))}setInt8(t,n){const[r,o]=this._findDataViewWithOffset(t);r.setInt8(t-o,n)}setUint16(t,n,r){this._internalBuffer.setUint16(0,n,r),this.setUint8(t,this._internalBuffer.getUint8(0)),this.setUint8(t+1,this._internalBuffer.getUint8(1))}setUint32(t,n,r){this._internalBuffer.setUint32(0,n,r),this.setUint8(t,this._internalBuffer.getUint8(0)),this.setUint8(t+1,this._internalBuffer.getUint8(1)),this.setUint8(t+2,this._internalBuffer.getUint8(2)),this.setUint8(t+3,this._internalBuffer.getUint8(3))}setUint8(t,n){const[r,o]=this._findDataViewWithOffset(t);r.setUint8(t-o,n)}_findDataViewWithOffset(t){let n=0;for(const r of this._dataViews){const o=n+r.byteLength;if(t>=n&&t<o)return[r,n];n=o}throw new RangeError}}const pa=e=>(t,n,r)=>e(o=>{const s=i=>o.next(i);return t.addEventListener(n,s,r),()=>t.removeEventListener(n,s,r)}),ma=(e,t)=>{const n=()=>{},r=o=>typeof o[0]=="function";return o=>{const s=(...i)=>{const a=o(r(i)?t({next:i[0]}):t(...i));return a!==void 0?a:n};return s[Symbol.observable]=()=>({subscribe:(...i)=>({unsubscribe:s(...i)})}),e(s)}},ga=ma(pn,mn),wa=pa(ga),va=(e,t,n,r,o)=>(s,i,a,c)=>{const u=a.getAudioTracks(),d=[],l=new i(a,{mimeType:"audio/webm;codecs=pcm"});let m=null,g=()=>{};const w=h=>{s.dispatchEvent(e("dataavailable",{data:new Blob(h,{type:c})}))},f=async(h,_)=>{const E=await Se(h,_);l.state==="inactive"?d.push(...E):(w(E),m=f(h,_))},p=()=>{l.state!=="inactive"&&(m!==null&&(m.catch(()=>{}),m=null),g(),g=()=>{},l.stop())};return l.addEventListener("error",h=>{p(),s.dispatchEvent(new ErrorEvent("error",{error:h.error===void 0?t():h.error}))}),l.addEventListener("pause",()=>s.dispatchEvent(new Event("pause"))),l.addEventListener("resume",()=>s.dispatchEvent(new Event("resume"))),l.addEventListener("start",()=>s.dispatchEvent(new Event("start"))),{get mimeType(){return c},get state(){return l.state},pause(){return l.pause()},resume(){return l.resume()},start(h){var _,E;if(a.getVideoTracks().length>0)throw n();if(l.state==="inactive"){const T=((_=u[0])===null||_===void 0?void 0:_.getSettings()).channelCount,A=(E=u[0])===null||E===void 0?void 0:E.getSettings().sampleRate;if(T===void 0)throw new Error("The channelCount is not defined.");if(A===void 0)throw new Error("The sampleRate is not defined.");let b=!1,y=!1,v=0,M=dn(c,A);g=()=>{y=!0};const k=wa(l,"dataavailable")(({data:N})=>{v+=1,M=M.then(async({dataView:U=null,elementType:x=null,encoderId:W,port:L})=>{const I=await N.arrayBuffer();v-=1;const S=U===null?new tt([I]):new tt([...U.buffers,I],U.byteOffset);if(!b&&l.state==="recording"&&!y){const F=o(S,0);if(F===null)return{dataView:S,elementType:x,encoderId:W,port:L};const{value:Q}=F;if(Q!==172351395)return{dataView:U,elementType:x,encoderId:W,port:L};b=!0}const{currentElementType:V,offset:O,contents:P}=r(S,x,T),B=O<S.byteLength?new tt(S.buffers,S.byteOffset+O):null;return P.forEach(F=>L.postMessage(F,F.map(({buffer:Q})=>Q))),v===0&&(l.state==="inactive"||y)&&(Se(W,null).then(F=>{w([...d,...F]),d.length=0,s.dispatchEvent(new Event("stop"))}),L.postMessage([]),L.close(),k()),{dataView:B,elementType:V,encoderId:W,port:L}})});h!==void 0&&M.then(({encoderId:N})=>m=f(N,h))}l.start(100)},stop:p}},_a=()=>typeof window>"u"?null:window,dr=(e,t)=>{if(t>=e.byteLength)return null;const n=e.getUint8(t);if(n>127)return 1;if(n>63)return 2;if(n>31)return 3;if(n>15)return 4;if(n>7)return 5;if(n>3)return 6;if(n>1)return 7;if(n>0)return 8;const r=dr(e,t+1);return r===null?null:r+8},Ea=(e,t)=>n=>{const r={value:e};return Object.defineProperties(n,{currentTarget:r,target:r}),typeof t=="function"?t.call(e,n):t.handleEvent.call(e,n)},fr=[],Ye=_a(),ya=Dr(Ye),hr=kr(ya),Aa=ha(hr,mt,Ur,De),It=Gr(dr),ba=jr(It),Ca=$r(It),Ta=Lr(ba,Ca),Ma=va(hr,mt,De,Ta,It),Na=xr(Ye),Oa=Fr(Ye),Ra=Vr(mt,De),ja=Br(Ra,De,Aa,Ma,fr,Pr(Na,Ea),Oa),$a=()=>Wr(Ye),Ga=async e=>{fr.push(await Sr(e))};export{ja as MediaRecorder,$a as isSupported,Ga as register};
//# sourceMappingURL=module-51ce20ae.js.map
