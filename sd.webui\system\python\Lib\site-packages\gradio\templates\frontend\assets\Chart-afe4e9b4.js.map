{"version": 3, "file": "Chart-afe4e9b4.js", "sources": ["../../../../js/icons/src/Chart.svelte", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/constant.js", "../../../../node_modules/.pnpm/d3-path@3.1.0/node_modules/d3-path/src/path.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/path.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/array.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/curve/linear.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/point.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/line.js", "../../../../js/timeseries/shared/utils.ts", "../../../../js/tooltip/src/Tooltip.svelte", "../../../../js/tooltip/src/tooltip.ts", "../../../../js/timeseries/shared/Chart.svelte"], "sourcesContent": ["<svg width=\"1em\" height=\"1em\" viewBox=\"0 0 32 32\">\n\t<path\n\t\td=\"M28.828 3.172a4.094 4.094 0 0 0-5.656 0L4.05 22.292A6.954 6.954 0 0 0 2 27.242V30h2.756a6.952 6.952 0 0 0 4.95-2.05L28.828 8.829a3.999 3.999 0 0 0 0-5.657zM10.91 18.26l2.829 2.829l-2.122 2.121l-2.828-2.828zm-2.619 8.276A4.966 4.966 0 0 1 4.756 28H4v-.759a4.967 4.967 0 0 1 1.464-3.535l1.91-1.91l2.829 2.828zM27.415 7.414l-12.261 12.26l-2.829-2.828l12.262-12.26a2.047 2.047 0 0 1 2.828 0a2 2 0 0 1 0 2.828z\"\n\t\tfill=\"currentColor\"\n\t/>\n\t<path\n\t\td=\"M6.5 15a3.5 3.5 0 0 1-2.475-5.974l3.5-3.5a1.502 1.502 0 0 0 0-2.121a1.537 1.537 0 0 0-2.121 0L3.415 5.394L2 3.98l1.99-1.988a3.585 3.585 0 0 1 4.95 0a3.504 3.504 0 0 1 0 4.949L5.439 10.44a1.502 1.502 0 0 0 0 2.121a1.537 1.537 0 0 0 2.122 0l4.024-4.024L13 9.95l-4.025 4.024A3.475 3.475 0 0 1 6.5 15z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "export default function(x) {\n  return function constant() {\n    return x;\n  };\n}\n", "const pi = Math.PI,\n    tau = 2 * pi,\n    epsilon = 1e-6,\n    tauEpsilon = tau - epsilon;\n\nfunction append(strings) {\n  this._ += strings[0];\n  for (let i = 1, n = strings.length; i < n; ++i) {\n    this._ += arguments[i] + strings[i];\n  }\n}\n\nfunction appendRound(digits) {\n  let d = Math.floor(digits);\n  if (!(d >= 0)) throw new Error(`invalid digits: ${digits}`);\n  if (d > 15) return append;\n  const k = 10 ** d;\n  return function(strings) {\n    this._ += strings[0];\n    for (let i = 1, n = strings.length; i < n; ++i) {\n      this._ += Math.round(arguments[i] * k) / k + strings[i];\n    }\n  };\n}\n\nexport class Path {\n  constructor(digits) {\n    this._x0 = this._y0 = // start of current subpath\n    this._x1 = this._y1 = null; // end of current subpath\n    this._ = \"\";\n    this._append = digits == null ? append : appendRound(digits);\n  }\n  moveTo(x, y) {\n    this._append`M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}`;\n  }\n  closePath() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._append`Z`;\n    }\n  }\n  lineTo(x, y) {\n    this._append`L${this._x1 = +x},${this._y1 = +y}`;\n  }\n  quadraticCurveTo(x1, y1, x, y) {\n    this._append`Q${+x1},${+y1},${this._x1 = +x},${this._y1 = +y}`;\n  }\n  bezierCurveTo(x1, y1, x2, y2, x, y) {\n    this._append`C${+x1},${+y1},${+x2},${+y2},${this._x1 = +x},${this._y1 = +y}`;\n  }\n  arcTo(x1, y1, x2, y2, r) {\n    x1 = +x1, y1 = +y1, x2 = +x2, y2 = +y2, r = +r;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(`negative radius: ${r}`);\n\n    let x0 = this._x1,\n        y0 = this._y1,\n        x21 = x2 - x1,\n        y21 = y2 - y1,\n        x01 = x0 - x1,\n        y01 = y0 - y1,\n        l01_2 = x01 * x01 + y01 * y01;\n\n    // Is this path empty? Move to (x1,y1).\n    if (this._x1 === null) {\n      this._append`M${this._x1 = x1},${this._y1 = y1}`;\n    }\n\n    // Or, is (x1,y1) coincident with (x0,y0)? Do nothing.\n    else if (!(l01_2 > epsilon));\n\n    // Or, are (x0,y0), (x1,y1) and (x2,y2) collinear?\n    // Equivalently, is (x1,y1) coincident with (x2,y2)?\n    // Or, is the radius zero? Line to (x1,y1).\n    else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon) || !r) {\n      this._append`L${this._x1 = x1},${this._y1 = y1}`;\n    }\n\n    // Otherwise, draw an arc!\n    else {\n      let x20 = x2 - x0,\n          y20 = y2 - y0,\n          l21_2 = x21 * x21 + y21 * y21,\n          l20_2 = x20 * x20 + y20 * y20,\n          l21 = Math.sqrt(l21_2),\n          l01 = Math.sqrt(l01_2),\n          l = r * Math.tan((pi - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2),\n          t01 = l / l01,\n          t21 = l / l21;\n\n      // If the start tangent is not coincident with (x0,y0), line to.\n      if (Math.abs(t01 - 1) > epsilon) {\n        this._append`L${x1 + t01 * x01},${y1 + t01 * y01}`;\n      }\n\n      this._append`A${r},${r},0,0,${+(y01 * x20 > x01 * y20)},${this._x1 = x1 + t21 * x21},${this._y1 = y1 + t21 * y21}`;\n    }\n  }\n  arc(x, y, r, a0, a1, ccw) {\n    x = +x, y = +y, r = +r, ccw = !!ccw;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(`negative radius: ${r}`);\n\n    let dx = r * Math.cos(a0),\n        dy = r * Math.sin(a0),\n        x0 = x + dx,\n        y0 = y + dy,\n        cw = 1 ^ ccw,\n        da = ccw ? a0 - a1 : a1 - a0;\n\n    // Is this path empty? Move to (x0,y0).\n    if (this._x1 === null) {\n      this._append`M${x0},${y0}`;\n    }\n\n    // Or, is (x0,y0) not coincident with the previous point? Line to (x0,y0).\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) {\n      this._append`L${x0},${y0}`;\n    }\n\n    // Is this arc empty? We’re done.\n    if (!r) return;\n\n    // Does the angle go the wrong way? Flip the direction.\n    if (da < 0) da = da % tau + tau;\n\n    // Is this a complete circle? Draw two arcs to complete the circle.\n    if (da > tauEpsilon) {\n      this._append`A${r},${r},0,1,${cw},${x - dx},${y - dy}A${r},${r},0,1,${cw},${this._x1 = x0},${this._y1 = y0}`;\n    }\n\n    // Is this arc non-empty? Draw an arc!\n    else if (da > epsilon) {\n      this._append`A${r},${r},0,${+(da >= pi)},${cw},${this._x1 = x + r * Math.cos(a1)},${this._y1 = y + r * Math.sin(a1)}`;\n    }\n  }\n  rect(x, y, w, h) {\n    this._append`M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}h${w = +w}v${+h}h${-w}Z`;\n  }\n  toString() {\n    return this._;\n  }\n}\n\nexport function path() {\n  return new Path;\n}\n\n// Allow instanceof d3.path\npath.prototype = Path.prototype;\n\nexport function pathRound(digits = 3) {\n  return new Path(+digits);\n}\n", "import {Path} from \"d3-path\";\n\nexport function withPath(shape) {\n  let digits = 3;\n\n  shape.digits = function(_) {\n    if (!arguments.length) return digits;\n    if (_ == null) {\n      digits = null;\n    } else {\n      const d = Math.floor(_);\n      if (!(d >= 0)) throw new RangeError(`invalid digits: ${_}`);\n      digits = d;\n    }\n    return shape;\n  };\n\n  return () => new Path(digits);\n}\n", "export var slice = Array.prototype.slice;\n\nexport default function(x) {\n  return typeof x === \"object\" && \"length\" in x\n    ? x // Array, TypedArray, NodeList, array-like\n    : Array.from(x); // Map, Set, iterable, string, or anything else\n}\n", "function Linear(context) {\n  this._context = context;\n}\n\nLinear.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; // falls through\n      default: this._context.lineTo(x, y); break;\n    }\n  }\n};\n\nexport default function(context) {\n  return new Linear(context);\n}\n", "export function x(p) {\n  return p[0];\n}\n\nexport function y(p) {\n  return p[1];\n}\n", "import array from \"./array.js\";\nimport constant from \"./constant.js\";\nimport curveLinear from \"./curve/linear.js\";\nimport {withPath} from \"./path.js\";\nimport {x as pointX, y as pointY} from \"./point.js\";\n\nexport default function(x, y) {\n  var defined = constant(true),\n      context = null,\n      curve = curveLinear,\n      output = null,\n      path = withPath(line);\n\n  x = typeof x === \"function\" ? x : (x === undefined) ? pointX : constant(x);\n  y = typeof y === \"function\" ? y : (y === undefined) ? pointY : constant(y);\n\n  function line(data) {\n    var i,\n        n = (data = array(data)).length,\n        d,\n        defined0 = false,\n        buffer;\n\n    if (context == null) output = curve(buffer = path());\n\n    for (i = 0; i <= n; ++i) {\n      if (!(i < n && defined(d = data[i], i, data)) === defined0) {\n        if (defined0 = !defined0) output.lineStart();\n        else output.lineEnd();\n      }\n      if (defined0) output.point(+x(d, i, data), +y(d, i, data));\n    }\n\n    if (buffer) return output = null, buffer + \"\" || null;\n  }\n\n  line.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), line) : x;\n  };\n\n  line.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), line) : y;\n  };\n\n  line.defined = function(_) {\n    return arguments.length ? (defined = typeof _ === \"function\" ? _ : constant(!!_), line) : defined;\n  };\n\n  line.curve = function(_) {\n    return arguments.length ? (curve = _, context != null && (output = curve(context)), line) : curve;\n  };\n\n  line.context = function(_) {\n    return arguments.length ? (_ == null ? context = output = null : output = curve(context = _), line) : context;\n  };\n\n  return line;\n}\n", "interface XYValue {\n\tx: number;\n\ty: number;\n}\n\ninterface ObjectValue {\n\tvalues: XYValue[];\n}\n\nexport function get_domains(\n\tvalues: ObjectValue[] | { values: number[] }\n): [number, number] {\n\tlet _vs: number[];\n\tif (Array.isArray(values)) {\n\t\t_vs = values.reduce<number[]>((acc, { values }) => {\n\t\t\treturn [...acc, ...values.map(({ y }) => y)];\n\t\t}, []);\n\t} else {\n\t\t_vs = values.values;\n\t}\n\treturn [Math.min(..._vs), Math.max(..._vs)];\n}\n\ninterface Row {\n\tname: string;\n\tvalues: number[];\n}\n\ninterface RowPoint {\n\tname: string;\n\tvalues: { x: number; y: number }[];\n}\n\ninterface TransformedValues {\n\tx: Row;\n\ty: RowPoint[];\n}\n\nexport function transform_values(\n\tvalues: Record<string, string>[],\n\tx?: string,\n\ty?: string[]\n): TransformedValues {\n\tconst transformed_values = Object.entries(\n\t\tvalues[0]\n\t).reduce<TransformedValues>(\n\t\t(acc, next, i) => {\n\t\t\tif ((!x && i === 0) || (x && next[0] === x)) {\n\t\t\t\tacc.x.name = next[0];\n\t\t\t} else if (!y || (y && y.includes(next[0]))) {\n\t\t\t\tacc.y.push({ name: next[0], values: [] });\n\t\t\t}\n\t\t\treturn acc;\n\t\t},\n\t\t{ x: { name: \"\", values: [] }, y: [] }\n\t);\n\n\tfor (let i = 0; i < values.length; i++) {\n\t\tconst _a = Object.entries(values[i]);\n\t\tfor (let j = 0; j < _a.length; j++) {\n\t\t\tlet [name, x] = _a[j];\n\t\t\tif (name === transformed_values.x.name) {\n\t\t\t\ttransformed_values.x.values.push(parseFloat(x));\n\t\t\t} else {\n\t\t\t\ttransformed_values.y[j - 1].values.push({\n\t\t\t\t\ty: parseFloat(_a[j][1]),\n\t\t\t\t\tx: parseFloat(_a[0][1])\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n\n\treturn transformed_values;\n}\n", "<script lang=\"ts\">\n\texport let text: string;\n\texport let x: number;\n\texport let y: number;\n\texport let color: string;\n\n\tlet w: number;\n\tlet h: number;\n</script>\n\n<div\n\tbind:offsetWidth={w}\n\tbind:offsetHeight={h}\n\tstyle=\"\n\t\ttop: {y - h / 2}px;\n\t\tleft: {x - w - 7}px;\"\n>\n\t<span style=\"background: {color}\" />\n\t{text}\n</div>\n\n<style>\n\tdiv {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tborder-radius: var(--radius-sm);\n\t\tbackground-color: rgba(0, 0, 0, 0.8);\n\t\tpadding: var(--size-1) 0.4rem;\n\t\tcolor: white;\n\t\tfont-size: var(--text-sm);\n\t}\n\n\tspan {\n\t\tdisplay: inline-block;\n\t\tmargin-right: var(--size-1);\n\t\tborder-radius: var(--radius-xs);\n\t\twidth: var(--size-3);\n\t\theight: var(--size-3);\n\t}\n</style>\n", "import type { ActionReturn } from \"svelte/action\";\nimport Tooltip from \"./Tooltip.svelte\";\n\ninterface ActionArgs {\n\tcolor: string;\n\ttext: string;\n}\n\nexport function tooltip(\n\telement: HTMLElement | SVGElement,\n\t{ color, text }: ActionArgs\n): ActionReturn {\n\tlet tooltipComponent: Tooltip;\n\tfunction mouse_over(event: MouseEvent): MouseEvent {\n\t\ttooltipComponent = new Tooltip({\n\t\t\tprops: {\n\t\t\t\ttext,\n\t\t\t\tx: event.pageX,\n\t\t\t\ty: event.pageY,\n\t\t\t\tcolor\n\t\t\t},\n\t\t\ttarget: document.body\n\t\t});\n\n\t\treturn event;\n\t}\n\tfunction mouseMove(event: MouseEvent): void {\n\t\ttooltipComponent.$set({\n\t\t\tx: event.pageX,\n\t\t\ty: event.pageY\n\t\t});\n\t}\n\tfunction mouseLeave(): void {\n\t\ttooltipComponent.$destroy();\n\t}\n\n\tconst el = element as HTMLElement;\n\n\tel.addEventListener(\"mouseover\", mouse_over);\n\tel.addEventListener(\"mouseleave\", mouseLeave);\n\tel.addEventListener(\"mousemove\", mouseMove);\n\n\treturn {\n\t\tdestroy() {\n\t\t\tel.removeEventListener(\"mouseover\", mouse_over);\n\t\t\tel.removeEventListener(\"mouseleave\", mouseLeave);\n\t\t\tel.removeEventListener(\"mousemove\", mouseMove);\n\t\t}\n\t};\n}\n", "<script lang=\"ts\">\n\timport { createEventDispatcher, onMount } from \"svelte\";\n\timport { csvParse } from \"d3-dsv\";\n\timport { scaleLinear } from \"d3-scale\";\n\timport { line as _line, curveLinear } from \"d3-shape\";\n\n\timport { colors as color_palette, ordered_colors } from \"@gradio/theme\";\n\timport { get_next_color } from \"@gradio/utils\";\n\n\timport { get_domains, transform_values } from \"./utils\";\n\n\timport { tooltip } from \"@gradio/tooltip\";\n\n\texport let value: string | Record<string, string>[];\n\texport let x: string | undefined = undefined;\n\texport let y: string[] | undefined = undefined;\n\texport let colors: string[] = [];\n\n\tconst dispatch = createEventDispatcher();\n\n\t$: ({ x: _x, y: _y } =\n\t\ttypeof value === \"string\"\n\t\t\t? transform_values(csvParse(value) as Record<string, string>[], x, y)\n\t\t\t: transform_values(value, x, y));\n\n\t$: x_domain = get_domains(_x);\n\t$: y_domain = get_domains(_y);\n\n\t$: scale_x = scaleLinear(x_domain, [0, 600]).nice();\n\t$: scale_y = scaleLinear(y_domain, [350, 0]).nice();\n\t$: x_ticks = scale_x.ticks(8);\n\t$: y_ticks = scale_y.ticks(8);\n\n\tlet color_map: Record<string, string>;\n\t$: color_map = _y.reduce(\n\t\t(acc, next, i) => ({ ...acc, [next.name]: get_color(i) }),\n\t\t{}\n\t);\n\n\tfunction get_color(index: number): string {\n\t\tlet current_color = colors[index % colors.length];\n\n\t\tif (current_color && current_color in color_palette) {\n\t\t\treturn color_palette[current_color as keyof typeof color_palette]\n\t\t\t\t?.primary;\n\t\t} else if (!current_color) {\n\t\t\treturn color_palette[get_next_color(index) as keyof typeof color_palette]\n\t\t\t\t.primary;\n\t\t}\n\t\treturn current_color;\n\t}\n\n\tonMount(() => {\n\t\tdispatch(\"process\", { x: _x, y: _y });\n\t});\n</script>\n\n<div class=\"wrap\">\n\t<div class=\"legend\">\n\t\t{#each _y as { name }}\n\t\t\t<div class=\"legend-item\">\n\t\t\t\t<span class=\"legend-box\" style=\"background-color: {color_map[name]}\" />\n\t\t\t\t{name}\n\t\t\t</div>\n\t\t{/each}\n\t</div>\n\t<svg class=\"w-full\" viewBox=\"-70 -20 700 420\">\n\t\t<g>\n\t\t\t{#each x_ticks as tick}\n\t\t\t\t<line\n\t\t\t\t\tstroke-width=\"0.5\"\n\t\t\t\t\tx1={scale_x(tick)}\n\t\t\t\t\tx2={scale_x(tick)}\n\t\t\t\t\ty1={scale_y(y_ticks[0] < y_domain[0] ? y_ticks[0] : y_domain[0]) + 10}\n\t\t\t\t\ty2={scale_y(\n\t\t\t\t\t\ty_domain[1] > y_ticks[y_ticks.length - 1]\n\t\t\t\t\t\t\t? y_domain[1]\n\t\t\t\t\t\t\t: y_ticks[y_ticks.length - 1]\n\t\t\t\t\t)}\n\t\t\t\t\tstroke=\"#aaa\"\n\t\t\t\t/>\n\t\t\t\t<text\n\t\t\t\t\tclass=\"label-text\"\n\t\t\t\t\ttext-anchor=\"middle\"\n\t\t\t\t\tx={scale_x(tick)}\n\t\t\t\t\ty={scale_y(y_ticks[0]) + 30}\n\t\t\t\t>\n\t\t\t\t\t{tick}\n\t\t\t\t</text>\n\t\t\t{/each}\n\n\t\t\t{#each y_ticks as tick}\n\t\t\t\t<line\n\t\t\t\t\tstroke-width=\"0.5\"\n\t\t\t\t\ty1={scale_y(tick)}\n\t\t\t\t\ty2={scale_y(tick)}\n\t\t\t\t\tx1={scale_x(x_ticks[0] < x_domain[0] ? x_ticks[0] : x_domain[0]) - 10}\n\t\t\t\t\tx2={scale_x(\n\t\t\t\t\t\tx_domain[1] > x_ticks[x_ticks.length - 1]\n\t\t\t\t\t\t\t? x_domain[1]\n\t\t\t\t\t\t\t: x_ticks[x_ticks.length - 1]\n\t\t\t\t\t)}\n\t\t\t\t\tstroke=\"#aaa\"\n\t\t\t\t/>\n\n\t\t\t\t<text\n\t\t\t\t\tclass=\"label-text\"\n\t\t\t\t\ttext-anchor=\"end\"\n\t\t\t\t\ty={scale_y(tick) + 4}\n\t\t\t\t\tx={scale_x(x_ticks[0]) - 20}\n\t\t\t\t>\n\t\t\t\t\t{tick}\n\t\t\t\t</text>\n\t\t\t{/each}\n\n\t\t\t{#if y_domain[1] > y_ticks[y_ticks.length - 1]}\n\t\t\t\t<line\n\t\t\t\t\tstroke-width=\"0.5\"\n\t\t\t\t\ty1={scale_y(y_domain[1])}\n\t\t\t\t\ty2={scale_y(y_domain[1])}\n\t\t\t\t\tx1={scale_x(x_ticks[0])}\n\t\t\t\t\tx2={scale_x(x_domain[1])}\n\t\t\t\t\tstroke=\"#aaa\"\n\t\t\t\t/>\n\t\t\t\t<text\n\t\t\t\t\tclass=\"label-text\"\n\t\t\t\t\ttext-anchor=\"end\"\n\t\t\t\t\ty={scale_y(y_domain[1]) + 4}\n\t\t\t\t\tx={scale_x(x_ticks[0]) - 20}\n\t\t\t\t>\n\t\t\t\t\t{y_domain[1]}\n\t\t\t\t</text>\n\t\t\t{/if}\n\t\t</g>\n\n\t\t{#each _y as { name, values }}\n\t\t\t{@const color = color_map[name]}\n\t\t\t{#each values as { x, y }}\n\t\t\t\t<circle\n\t\t\t\t\tr=\"3.5\"\n\t\t\t\t\tcx={scale_x(x)}\n\t\t\t\t\tcy={scale_y(y)}\n\t\t\t\t\tstroke-width=\"1.5\"\n\t\t\t\t\tstroke={color}\n\t\t\t\t\tfill=\"none\"\n\t\t\t\t/>\n\t\t\t{/each}\n\t\t\t<path\n\t\t\t\td={_line().curve(curveLinear)(\n\t\t\t\t\tvalues.map(({ x, y }) => [scale_x(x), scale_y(y)])\n\t\t\t\t)}\n\t\t\t\tfill=\"none\"\n\t\t\t\tstroke={color}\n\t\t\t\tstroke-width=\"3\"\n\t\t\t/>\n\t\t{/each}\n\n\t\t{#each _y as { name, values }}\n\t\t\t{@const color = color_map[name]}\n\t\t\t{#each values as { x, y }}\n\t\t\t\t<circle\n\t\t\t\t\tuse:tooltip={{ color, text: `(${x}, ${y})` }}\n\t\t\t\t\tr=\"7\"\n\t\t\t\t\tcx={scale_x(x)}\n\t\t\t\t\tcy={scale_y(y)}\n\t\t\t\t\tstroke=\"black\"\n\t\t\t\t\tfill=\"black\"\n\t\t\t\t\tstyle=\"cursor: pointer; opacity: 0\"\n\t\t\t\t/>\n\t\t\t{/each}\n\t\t{/each}\n\t</svg>\n\n\t<div class=\"main-label\">\n\t\t{_x.name}\n\t</div>\n</div>\n\n<style>\n\t.wrap {\n\t\tmargin-top: var(--size-3);\n\t}\n\n\t.legend {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.legend-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: var(--spacing-sm);\n\t\tmargin-right: var(--size-2);\n\t\tmargin-left: var(--size-2);\n\t}\n\n\t.legend-box {\n\t\tdisplay: inline-block;\n\t\tborder-radius: var(--radius-xs);\n\t\twidth: var(--size-3);\n\t\theight: var(--size-3);\n\t}\n\n\tsvg {\n\t\twidth: var(--size-full);\n\t}\n\n\t.label-text {\n\t\tfill: var(--body-text-color);\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t}\n\n\t.main-label {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tcolor: var(--body-text-color);\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path0", "path1", "constant", "x", "pi", "tau", "epsilon", "tauEpsilon", "strings", "i", "appendRound", "digits", "d", "k", "n", "Path", "y", "x1", "y1", "x2", "y2", "r", "x0", "y0", "x21", "y21", "x01", "y01", "l01_2", "x20", "y20", "l21_2", "l20_2", "l21", "l01", "l", "t01", "t21", "a0", "a1", "ccw", "dx", "dy", "cw", "da", "w", "h", "with<PERSON><PERSON>", "shape", "_", "array", "Linear", "context", "curveLinear", "p", "_line", "defined", "curve", "output", "path", "line", "pointX", "pointY", "data", "defined0", "buffer", "get_domains", "values", "_vs", "acc", "transform_values", "transformed_values", "next", "_a", "j", "name", "ctx", "div", "span", "text", "$$props", "color", "tooltip", "element", "tooltipComponent", "mouse_over", "event", "<PERSON><PERSON><PERSON>", "mouseMove", "mouseLeave", "el", "constants_0", "child_ctx", "set_style", "set_data", "t1", "t1_value", "attr", "line_x__value", "line_x__value_1", "line_y__value", "text_1", "text_1_x_value", "text_1_y_value", "dirty", "t", "t_value", "line_y__value_1", "circle", "circle_cx_value", "circle_cy_value", "path_d_value", "t2_value", "create_if_block", "div2", "div0", "g", "div1", "t2", "value", "colors", "dispatch", "createEventDispatcher", "color_map", "get_color", "index", "current_color", "color_palette", "get_next_color", "onMount", "_x", "_y", "scale_x", "scale_y", "$$invalidate", "csvParse", "x_domain", "y_domain", "scaleLinear", "x_ticks", "y_ticks"], "mappings": "wuCAAAA,EASKC,EAAAC,EAAAC,CAAA,EARJC,EAGCF,EAAAG,CAAA,EACDD,EAGCF,EAAAI,CAAA,qGCRa,SAAQC,EAACC,EAAG,CACzB,OAAO,UAAoB,CACzB,OAAOA,CACX,CACA,CCJA,MAAMC,EAAK,KAAK,GACZC,EAAM,EAAID,EACVE,EAAU,KACVC,GAAaF,EAAMC,EAEvB,SAASP,GAAOS,EAAS,CACvB,KAAK,GAAKA,EAAQ,CAAC,EACnB,QAASC,EAAI,EAAG,EAAID,EAAQ,OAAQC,EAAI,EAAG,EAAEA,EAC3C,KAAK,GAAK,UAAUA,CAAC,EAAID,EAAQC,CAAC,CAEtC,CAEA,SAASC,GAAYC,EAAQ,CAC3B,IAAIC,EAAI,KAAK,MAAMD,CAAM,EACzB,GAAI,EAAEC,GAAK,GAAI,MAAM,IAAI,MAAM,mBAAmBD,GAAQ,EAC1D,GAAIC,EAAI,GAAI,OAAOb,GACnB,MAAMc,EAAI,IAAMD,EAChB,OAAO,SAASJ,EAAS,CACvB,KAAK,GAAKA,EAAQ,CAAC,EACnB,QAASC,EAAI,EAAGK,EAAIN,EAAQ,OAAQC,EAAIK,EAAG,EAAEL,EAC3C,KAAK,GAAK,KAAK,MAAM,UAAUA,CAAC,EAAII,CAAC,EAAIA,EAAIL,EAAQC,CAAC,CAE5D,CACA,CAEO,MAAMM,EAAK,CAChB,YAAYJ,EAAQ,CAClB,KAAK,IAAM,KAAK,IAChB,KAAK,IAAM,KAAK,IAAM,KACtB,KAAK,EAAI,GACT,KAAK,QAAUA,GAAU,KAAOZ,GAASW,GAAYC,CAAM,CAC5D,CACD,OAAOR,EAAGa,EAAG,CACX,KAAK,WAAW,KAAK,IAAM,KAAK,IAAM,CAACb,KAAK,KAAK,IAAM,KAAK,IAAM,CAACa,GACpE,CACD,WAAY,CACN,KAAK,MAAQ,OACf,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IACrC,KAAK,WAER,CACD,OAAOb,EAAGa,EAAG,CACX,KAAK,WAAW,KAAK,IAAM,CAACb,KAAK,KAAK,IAAM,CAACa,GAC9C,CACD,iBAAiBC,EAAIC,EAAIf,EAAGa,EAAG,CAC7B,KAAK,WAAW,CAACC,KAAM,CAACC,KAAM,KAAK,IAAM,CAACf,KAAK,KAAK,IAAM,CAACa,GAC5D,CACD,cAAcC,EAAIC,EAAIC,EAAIC,EAAIjB,EAAGa,EAAG,CAClC,KAAK,WAAW,CAACC,KAAM,CAACC,KAAM,CAACC,KAAM,CAACC,KAAM,KAAK,IAAM,CAACjB,KAAK,KAAK,IAAM,CAACa,GAC1E,CACD,MAAMC,EAAIC,EAAIC,EAAIC,EAAIC,EAAG,CAIvB,GAHAJ,EAAK,CAACA,EAAIC,EAAK,CAACA,EAAIC,EAAK,CAACA,EAAIC,EAAK,CAACA,EAAIC,EAAI,CAACA,EAGzCA,EAAI,EAAG,MAAM,IAAI,MAAM,oBAAoBA,GAAG,EAElD,IAAIC,EAAK,KAAK,IACVC,EAAK,KAAK,IACVC,EAAML,EAAKF,EACXQ,EAAML,EAAKF,EACXQ,EAAMJ,EAAKL,EACXU,EAAMJ,EAAKL,EACXU,EAAQF,EAAMA,EAAMC,EAAMA,EAG9B,GAAI,KAAK,MAAQ,KACf,KAAK,WAAW,KAAK,IAAMV,KAAM,KAAK,IAAMC,YAInCU,EAAQtB,EAKd,GAAI,EAAE,KAAK,IAAIqB,EAAMH,EAAMC,EAAMC,CAAG,EAAIpB,IAAY,CAACe,EACxD,KAAK,WAAW,KAAK,IAAMJ,KAAM,KAAK,IAAMC,QAIzC,CACH,IAAIW,EAAMV,EAAKG,EACXQ,EAAMV,EAAKG,EACXQ,EAAQP,EAAMA,EAAMC,EAAMA,EAC1BO,EAAQH,EAAMA,EAAMC,EAAMA,EAC1BG,EAAM,KAAK,KAAKF,CAAK,EACrBG,EAAM,KAAK,KAAKN,CAAK,EACrBO,EAAId,EAAI,KAAK,KAAKjB,EAAK,KAAK,MAAM2B,EAAQH,EAAQI,IAAU,EAAIC,EAAMC,EAAI,GAAK,CAAC,EAChFE,EAAMD,EAAID,EACVG,EAAMF,EAAIF,EAGV,KAAK,IAAIG,EAAM,CAAC,EAAI9B,GACtB,KAAK,WAAWW,EAAKmB,EAAMV,KAAOR,EAAKkB,EAAMT,IAG/C,KAAK,WAAWN,KAAKA,SAAS,EAAEM,EAAME,EAAMH,EAAMI,MAAQ,KAAK,IAAMb,EAAKoB,EAAMb,KAAO,KAAK,IAAMN,EAAKmB,EAAMZ,IAEhH,CACD,IAAItB,EAAGa,EAAGK,EAAGiB,EAAIC,EAAIC,EAAK,CAIxB,GAHArC,EAAI,CAACA,EAAGa,EAAI,CAACA,EAAGK,EAAI,CAACA,EAAGmB,EAAM,CAAC,CAACA,EAG5BnB,EAAI,EAAG,MAAM,IAAI,MAAM,oBAAoBA,GAAG,EAElD,IAAIoB,EAAKpB,EAAI,KAAK,IAAIiB,CAAE,EACpBI,EAAKrB,EAAI,KAAK,IAAIiB,CAAE,EACpBhB,EAAKnB,EAAIsC,EACTlB,EAAKP,EAAI0B,EACTC,EAAK,EAAIH,EACTI,EAAKJ,EAAMF,EAAKC,EAAKA,EAAKD,EAG1B,KAAK,MAAQ,KACf,KAAK,WAAWhB,KAAMC,KAIf,KAAK,IAAI,KAAK,IAAMD,CAAE,EAAIhB,GAAW,KAAK,IAAI,KAAK,IAAMiB,CAAE,EAAIjB,IACtE,KAAK,WAAWgB,KAAMC,IAInBF,IAGDuB,EAAK,IAAGA,EAAKA,EAAKvC,EAAMA,GAGxBuC,EAAKrC,GACP,KAAK,WAAWc,KAAKA,SAASsB,KAAMxC,EAAIsC,KAAMzB,EAAI0B,KAAMrB,KAAKA,SAASsB,KAAM,KAAK,IAAMrB,KAAM,KAAK,IAAMC,IAIjGqB,EAAKtC,GACZ,KAAK,WAAWe,KAAKA,OAAO,EAAEuB,GAAMxC,MAAOuC,KAAM,KAAK,IAAMxC,EAAIkB,EAAI,KAAK,IAAIkB,CAAE,KAAK,KAAK,IAAMvB,EAAIK,EAAI,KAAK,IAAIkB,CAAE,IAErH,CACD,KAAKpC,EAAGa,EAAG6B,EAAGC,EAAG,CACf,KAAK,WAAW,KAAK,IAAM,KAAK,IAAM,CAAC3C,KAAK,KAAK,IAAM,KAAK,IAAM,CAACa,KAAK6B,EAAI,CAACA,KAAK,CAACC,KAAK,CAACD,IAC1F,CACD,UAAW,CACT,OAAO,KAAK,CACb,CACH,CC9IO,SAASE,GAASC,EAAO,CAC9B,IAAIrC,EAAS,EAEb,OAAAqC,EAAM,OAAS,SAASC,EAAG,CACzB,GAAI,CAAC,UAAU,OAAQ,OAAOtC,EAC9B,GAAIsC,GAAK,KACPtC,EAAS,SACJ,CACL,MAAMC,EAAI,KAAK,MAAMqC,CAAC,EACtB,GAAI,EAAErC,GAAK,GAAI,MAAM,IAAI,WAAW,mBAAmBqC,GAAG,EAC1DtC,EAASC,EAEX,OAAOoC,CACX,EAES,IAAM,IAAIjC,GAAKJ,CAAM,CAC9B,CChBe,SAAQuC,GAAC/C,EAAG,CACzB,OAAO,OAAOA,GAAM,UAAY,WAAYA,EACxCA,EACA,MAAM,KAAKA,CAAC,CAClB,CCNA,SAASgD,GAAOC,EAAS,CACvB,KAAK,SAAWA,CAClB,CAEAD,GAAO,UAAY,CACjB,UAAW,UAAW,CACpB,KAAK,MAAQ,CACd,EACD,QAAS,UAAW,CAClB,KAAK,MAAQ,GACd,EACD,UAAW,UAAW,CACpB,KAAK,OAAS,CACf,EACD,QAAS,UAAW,EACd,KAAK,OAAU,KAAK,QAAU,GAAK,KAAK,SAAW,IAAI,KAAK,SAAS,UAAS,EAClF,KAAK,MAAQ,EAAI,KAAK,KACvB,EACD,MAAO,SAAShD,EAAGa,EAAG,CAEpB,OADAb,EAAI,CAACA,EAAGa,EAAI,CAACA,EACL,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,MAAQ,KAAK,SAAS,OAAOb,EAAGa,CAAC,EAAI,KAAK,SAAS,OAAOb,EAAGa,CAAC,EAAG,MAC/F,IAAK,GAAG,KAAK,OAAS,EACtB,QAAS,KAAK,SAAS,OAAOb,EAAGa,CAAC,EAAG,KACtC,CACF,CACH,EAEe,SAAQqC,EAACD,EAAS,CAC/B,OAAO,IAAID,GAAOC,CAAO,CAC3B,CC9BO,SAASjD,GAAEmD,EAAG,CACnB,OAAOA,EAAE,CAAC,CACZ,CAEO,SAAStC,GAAEsC,EAAG,CACnB,OAAOA,EAAE,CAAC,CACZ,CCAe,SAAAC,EAASpD,EAAGa,EAAG,CAC5B,IAAIwC,EAAUtD,EAAS,EAAI,EACvBkD,EAAU,KACVK,EAAQJ,EACRK,EAAS,KACTC,EAAOZ,GAASa,CAAI,EAExBzD,EAAI,OAAOA,GAAM,WAAaA,EAAKA,IAAM,OAAa0D,GAAS3D,EAASC,CAAC,EACzEa,EAAI,OAAOA,GAAM,WAAaA,EAAKA,IAAM,OAAa8C,GAAS5D,EAASc,CAAC,EAEzE,SAAS4C,EAAKG,EAAM,CAClB,IAAItD,EACAK,GAAKiD,EAAOb,GAAMa,CAAI,GAAG,OACzBnD,EACAoD,EAAW,GACXC,EAIJ,IAFIb,GAAW,OAAMM,EAASD,EAAMQ,EAASN,EAAI,CAAE,GAE9ClD,EAAI,EAAGA,GAAKK,EAAG,EAAEL,EAChB,EAAEA,EAAIK,GAAK0C,EAAQ5C,EAAImD,EAAKtD,CAAC,EAAGA,EAAGsD,CAAI,KAAOC,KAC5CA,EAAW,CAACA,GAAUN,EAAO,UAAS,EACrCA,EAAO,QAAO,GAEjBM,GAAUN,EAAO,MAAM,CAACvD,EAAES,EAAGH,EAAGsD,CAAI,EAAG,CAAC/C,EAAEJ,EAAGH,EAAGsD,CAAI,CAAC,EAG3D,GAAIE,EAAQ,OAAOP,EAAS,KAAMO,EAAS,IAAM,IAClD,CAED,OAAAL,EAAK,EAAI,SAAS,EAAG,CACnB,OAAO,UAAU,QAAUzD,EAAI,OAAO,GAAM,WAAa,EAAID,EAAS,CAAC,CAAC,EAAG0D,GAAQzD,CACvF,EAEEyD,EAAK,EAAI,SAAS,EAAG,CACnB,OAAO,UAAU,QAAU5C,EAAI,OAAO,GAAM,WAAa,EAAId,EAAS,CAAC,CAAC,EAAG0D,GAAQ5C,CACvF,EAEE4C,EAAK,QAAU,SAAS,EAAG,CACzB,OAAO,UAAU,QAAUJ,EAAU,OAAO,GAAM,WAAa,EAAItD,EAAS,CAAC,CAAC,CAAC,EAAG0D,GAAQJ,CAC9F,EAEEI,EAAK,MAAQ,SAAS,EAAG,CACvB,OAAO,UAAU,QAAUH,EAAQ,EAAGL,GAAW,OAASM,EAASD,EAAML,CAAO,GAAIQ,GAAQH,CAChG,EAEEG,EAAK,QAAU,SAAS,EAAG,CACzB,OAAO,UAAU,QAAU,GAAK,KAAOR,EAAUM,EAAS,KAAOA,EAASD,EAAML,EAAU,CAAC,EAAGQ,GAAQR,CAC1G,EAESQ,CACT,CChDO,SAASM,EACfC,EACmB,CACf,IAAAC,EACA,OAAA,MAAM,QAAQD,CAAM,EACvBC,EAAMD,EAAO,OAAiB,CAACE,EAAK,CAAE,OAAAF,KAC9B,CAAC,GAAGE,EAAK,GAAGF,EAAO,IAAI,CAAC,CAAE,EAAAnD,CAAA,IAAQA,CAAC,CAAC,EACzC,CAAE,CAAA,EAELoD,EAAMD,EAAO,OAEP,CAAC,KAAK,IAAI,GAAGC,CAAG,EAAG,KAAK,IAAI,GAAGA,CAAG,CAAC,CAC3C,CAiBgB,SAAAE,GACfH,EACAhE,EACAa,EACoB,CACpB,MAAMuD,EAAqB,OAAO,QACjCJ,EAAO,CAAC,CAAA,EACP,OACD,CAACE,EAAKG,EAAM,KACN,CAACrE,GAAK,IAAM,GAAOA,GAAKqE,EAAK,CAAC,IAAMrE,EACpCkE,EAAA,EAAE,KAAOG,EAAK,CAAC,GACT,CAACxD,GAAMA,GAAKA,EAAE,SAASwD,EAAK,CAAC,CAAC,IACpCH,EAAA,EAAE,KAAK,CAAE,KAAMG,EAAK,CAAC,EAAG,OAAQ,CAAC,CAAA,CAAG,EAElCH,GAER,CAAE,EAAG,CAAE,KAAM,GAAI,OAAQ,EAAG,EAAG,EAAG,EAAG,CAAA,EAGtC,QAAS5D,EAAI,EAAGA,EAAI0D,EAAO,OAAQ1D,IAAK,CACvC,MAAMgE,EAAK,OAAO,QAAQN,EAAO1D,CAAC,CAAC,EACnC,QAASiE,EAAI,EAAGA,EAAID,EAAG,OAAQC,IAAK,CACnC,GAAI,CAACC,EAAMxE,CAAC,EAAIsE,EAAGC,CAAC,EAChBC,IAASJ,EAAmB,EAAE,KACjCA,EAAmB,EAAE,OAAO,KAAK,WAAWpE,CAAC,CAAC,EAE9CoE,EAAmB,EAAEG,EAAI,CAAC,EAAE,OAAO,KAAK,CACvC,EAAG,WAAWD,EAAGC,CAAC,EAAE,CAAC,CAAC,EACtB,EAAG,WAAWD,EAAG,CAAC,EAAE,CAAC,CAAC,CAAA,CACtB,GAKG,OAAAF,CACR,0ECvDEK,EAAI,CAAA,CAAA,mBADqBA,EAAK,CAAA,CAAA,0CAHxBA,EAAC,CAAA,EAAGA,EAAC,CAAA,EAAG,EAAC,IAAA,aACRA,EAAC,CAAA,EAAGA,EAAC,CAAA,EAAG,EAAC,IAAA,6DALlBjF,EASKC,EAAAiF,EAAA/E,CAAA,EAFJC,EAAmC8E,EAAAC,CAAA,qEAATF,EAAK,CAAA,CAAA,WAC9BA,EAAI,CAAA,CAAA,kBAJEA,EAAC,CAAA,EAAGA,EAAC,CAAA,EAAG,EAAC,IAAA,mBACRA,EAAC,CAAA,EAAGA,EAAC,CAAA,EAAG,EAAC,IAAA,qDAdN,KAAAG,CAAY,EAAAC,GACZ,EAAA7E,CAAS,EAAA6E,GACT,EAAAhE,CAAS,EAAAgE,GACT,MAAAC,CAAa,EAAAD,EAEpBnC,EACAC,eAIcD,EAAC,KAAA,YACAC,EAAC,KAAA,mPCJd,SAASoC,GACfC,EACA,CAAE,MAAAF,EAAO,KAAAF,GACM,CACX,IAAAK,EACJ,SAASC,EAAWC,EAA+B,CAClD,OAAAF,EAAmB,IAAIG,GAAQ,CAC9B,MAAO,CACN,KAAAR,EACA,EAAGO,EAAM,MACT,EAAGA,EAAM,MACT,MAAAL,CACD,EACA,OAAQ,SAAS,IAAA,CACjB,EAEMK,CACR,CACA,SAASE,EAAUF,EAAyB,CAC3CF,EAAiB,KAAK,CACrB,EAAGE,EAAM,MACT,EAAGA,EAAM,KAAA,CACT,CACF,CACA,SAASG,GAAmB,CAC3BL,EAAiB,SAAS,CAC3B,CAEA,MAAMM,EAAKP,EAER,OAAAO,EAAA,iBAAiB,YAAaL,CAAU,EACxCK,EAAA,iBAAiB,aAAcD,CAAU,EACzCC,EAAA,iBAAiB,YAAaF,CAAS,EAEnC,CACN,SAAU,CACNE,EAAA,oBAAoB,YAAaL,CAAU,EAC3CK,EAAA,oBAAoB,aAAcD,CAAU,EAC5CC,EAAA,oBAAoB,YAAaF,CAAS,CAC9C,CAAA,CAEF,wEC6GmB,MAAAG,EAAAC,KAAUA,EAAI,EAAA,CAAA,gKAtBd,MAAAD,EAAAC,KAAUA,EAAI,EAAA,CAAA,oSA1E5BhB,EAAI,EAAA,EAAA,qGAD8CiB,EAAAf,EAAA,mBAAAF,KAAUA,EAAI,EAAA,CAAA,CAAA,oDADlEjF,EAGKC,EAAAiF,EAAA/E,CAAA,EAFJC,EAAsE8E,EAAAC,CAAA,yCAAnBe,EAAAf,EAAA,mBAAAF,KAAUA,EAAI,EAAA,CAAA,CAAA,iBAChEA,EAAI,EAAA,EAAA,KAAAkB,EAAAC,EAAAC,CAAA,mDAyBHpB,EAAI,EAAA,EAAA,6EAhBDqB,EAAArC,EAAA,KAAAsC,EAAAtB,KAAQA,EAAI,EAAA,CAAA,CAAA,EACZqB,EAAArC,EAAA,KAAAuC,EAAAvB,KAAQA,EAAI,EAAA,CAAA,CAAA,EACZqB,EAAArC,EAAA,KAAAwC,EAAAxB,KAAQA,EAAO,CAAA,EAAC,CAAC,EAAIA,KAAS,CAAC,EAAIA,KAAQ,CAAC,EAAIA,EAAQ,CAAA,EAAC,CAAC,CAAA,EAAK,EAAE,aACjEA,EAAO,CAAA,EACVA,EAAQ,CAAA,EAAC,CAAC,EAAIA,EAAO,CAAA,EAACA,EAAO,CAAA,EAAC,OAAS,CAAC,EACrCA,KAAS,CAAC,EACVA,EAAQ,CAAA,EAAAA,EAAQ,CAAA,EAAA,OAAS,CAAC,CAAA,CAAA,4FAO3BqB,EAAAI,EAAA,IAAAC,EAAA1B,KAAQA,EAAI,EAAA,CAAA,CAAA,EACZqB,EAAAI,EAAA,IAAAE,EAAA3B,EAAQ,CAAA,EAAAA,EAAQ,CAAA,EAAA,CAAC,GAAK,EAAE,UAhB5BjF,EAWCC,EAAAgE,EAAA9D,CAAA,EACDH,EAOMC,EAAAyG,EAAAvG,CAAA,iBAjBD0G,EAAA,CAAA,EAAA,MAAAN,KAAAA,EAAAtB,KAAQA,EAAI,EAAA,CAAA,gBACZ4B,EAAA,CAAA,EAAA,MAAAL,KAAAA,EAAAvB,KAAQA,EAAI,EAAA,CAAA,gBACZ4B,EAAA,CAAA,EAAA,KAAAJ,KAAAA,EAAAxB,KAAQA,EAAO,CAAA,EAAC,CAAC,EAAIA,KAAS,CAAC,EAAIA,KAAQ,CAAC,EAAIA,EAAQ,CAAA,EAAC,CAAC,CAAA,EAAK,kCAC/DA,EAAO,CAAA,EACVA,EAAQ,CAAA,EAAC,CAAC,EAAIA,EAAO,CAAA,EAACA,EAAO,CAAA,EAAC,OAAS,CAAC,EACrCA,KAAS,CAAC,EACVA,EAAQ,CAAA,EAAAA,EAAQ,CAAA,EAAA,OAAS,CAAC,CAAA,kCAU7BA,EAAI,EAAA,EAAA,KAAAkB,EAAAW,EAAAC,CAAA,EAHFF,EAAA,CAAA,EAAA,MAAAF,KAAAA,EAAA1B,KAAQA,EAAI,EAAA,CAAA,eACZ4B,EAAA,CAAA,EAAA,KAAAD,KAAAA,EAAA3B,EAAQ,CAAA,EAAAA,EAAQ,CAAA,EAAA,CAAC,GAAK,wEA0BxBA,EAAI,EAAA,EAAA,6EAjBDqB,EAAArC,EAAA,KAAAwC,EAAAxB,KAAQA,EAAI,EAAA,CAAA,CAAA,EACZqB,EAAArC,EAAA,KAAA+C,EAAA/B,KAAQA,EAAI,EAAA,CAAA,CAAA,EACZqB,EAAArC,EAAA,KAAAsC,EAAAtB,KAAQA,EAAO,EAAA,EAAC,CAAC,EAAIA,KAAS,CAAC,EAAIA,MAAQ,CAAC,EAAIA,EAAQ,CAAA,EAAC,CAAC,CAAA,EAAK,EAAE,aACjEA,EAAO,CAAA,EACVA,EAAQ,CAAA,EAAC,CAAC,EAAIA,EAAO,EAAA,EAACA,EAAO,EAAA,EAAC,OAAS,CAAC,EACrCA,KAAS,CAAC,EACVA,EAAQ,EAAA,EAAAA,EAAQ,EAAA,EAAA,OAAS,CAAC,CAAA,CAAA,mGAQ3BA,EAAO,CAAA,EAACA,EAAI,EAAA,CAAA,EAAI,CAAC,EACjBqB,EAAAI,EAAA,IAAAC,EAAA1B,EAAQ,CAAA,EAAAA,EAAQ,EAAA,EAAA,CAAC,GAAK,EAAE,UAjB5BjF,EAWCC,EAAAgE,EAAA9D,CAAA,EAEDH,EAOMC,EAAAyG,EAAAvG,CAAA,iBAlBD0G,EAAA,CAAA,EAAA,KAAAJ,KAAAA,EAAAxB,KAAQA,EAAI,EAAA,CAAA,gBACZ4B,EAAA,CAAA,EAAA,KAAAG,KAAAA,EAAA/B,KAAQA,EAAI,EAAA,CAAA,gBACZ4B,EAAA,CAAA,EAAA,MAAAN,KAAAA,EAAAtB,KAAQA,EAAO,EAAA,EAAC,CAAC,EAAIA,KAAS,CAAC,EAAIA,MAAQ,CAAC,EAAIA,EAAQ,CAAA,EAAC,CAAC,CAAA,EAAK,mCAC/DA,EAAO,CAAA,EACVA,EAAQ,CAAA,EAAC,CAAC,EAAIA,EAAO,EAAA,EAACA,EAAO,EAAA,EAAC,OAAS,CAAC,EACrCA,KAAS,CAAC,EACVA,EAAQ,EAAA,EAAAA,EAAQ,EAAA,EAAA,OAAS,CAAC,CAAA,iCAW7BA,EAAI,EAAA,EAAA,KAAAkB,EAAAW,EAAAC,CAAA,mBAHF9B,EAAO,CAAA,EAACA,EAAI,EAAA,CAAA,EAAI,eAChB4B,EAAA,CAAA,EAAA,MAAAF,KAAAA,EAAA1B,EAAQ,CAAA,EAAAA,EAAQ,EAAA,EAAA,CAAC,GAAK,sEAqBxB8B,EAAA9B,KAAS,CAAC,EAAA,wFAZPA,EAAO,CAAA,EAACA,EAAQ,CAAA,EAAC,CAAC,CAAA,CAAA,aAClBA,EAAO,CAAA,EAACA,EAAQ,CAAA,EAAC,CAAC,CAAA,CAAA,aAClBA,EAAO,CAAA,EAACA,EAAO,EAAA,EAAC,CAAC,CAAA,CAAA,aACjBA,EAAO,CAAA,EAACA,EAAQ,CAAA,EAAC,CAAC,CAAA,CAAA,yFAMnBqB,EAAAI,EAAA,IAAAE,EAAA3B,EAAQ,CAAA,EAAAA,EAAS,CAAA,EAAA,CAAC,GAAK,CAAC,EACxBqB,EAAAI,EAAA,IAAAC,EAAA1B,EAAQ,CAAA,EAAAA,EAAQ,EAAA,EAAA,CAAC,GAAK,EAAE,UAZ5BjF,EAOCC,EAAAgE,EAAA9D,CAAA,EACDH,EAOMC,EAAAyG,EAAAvG,CAAA,iCAbD8E,EAAO,CAAA,EAACA,EAAQ,CAAA,EAAC,CAAC,CAAA,gCAClBA,EAAO,CAAA,EAACA,EAAQ,CAAA,EAAC,CAAC,CAAA,kCAClBA,EAAO,CAAA,EAACA,EAAO,EAAA,EAAC,CAAC,CAAA,iCACjBA,EAAO,CAAA,EAACA,EAAQ,CAAA,EAAC,CAAC,CAAA,gBASrB4B,EAAA,CAAA,EAAA,IAAAE,KAAAA,EAAA9B,KAAS,CAAC,EAAA,KAAAkB,EAAAW,EAAAC,CAAA,EAHRF,EAAA,CAAA,EAAA,IAAAD,KAAAA,EAAA3B,EAAQ,CAAA,EAAAA,EAAS,CAAA,EAAA,CAAC,GAAK,eACvB4B,EAAA,CAAA,EAAA,MAAAF,KAAAA,EAAA1B,EAAQ,CAAA,EAAAA,EAAQ,EAAA,EAAA,CAAC,GAAK,0GAYrBqB,EAAAW,EAAA,KAAAC,EAAAjC,KAAQA,EAAC,CAAA,CAAA,CAAA,EACTqB,EAAAW,EAAA,KAAAE,EAAAlC,KAAQA,EAAC,CAAA,CAAA,CAAA,2CAELA,EAAK,EAAA,CAAA,6BALdjF,EAOCC,EAAAgH,EAAA9G,CAAA,UALI0G,EAAA,CAAA,EAAA,IAAAK,KAAAA,EAAAjC,KAAQA,EAAC,CAAA,CAAA,gBACT4B,EAAA,CAAA,EAAA,IAAAM,KAAAA,EAAAlC,KAAQA,EAAC,CAAA,CAAA,iCAELA,EAAK,EAAA,iEANRA,EAAM,EAAA,CAAA,uBAAX,OAAI,GAAA,iFAWFqB,EAAAtC,EAAA,IAAAoD,EAAAxD,EAAK,EAAG,MAAMF,CAAW,EAC3BuB,MAAO,IAAGA,EAAA,EAAA,CAAA,CAAA,CAAA,oCAGHA,EAAK,EAAA,CAAA,gFALdjF,EAOCC,EAAA+D,EAAA7D,CAAA,2BAjBM8E,EAAM,EAAA,CAAA,oBAAX,OAAInE,GAAA,EAAA,2HAAJ,OAWE+F,EAAA,CAAA,EAAA,IAAAO,KAAAA,EAAAxD,EAAK,EAAG,MAAMF,CAAW,EAC3BuB,MAAO,IAAGA,EAAA,EAAA,CAAA,CAAA,gCAGHA,EAAK,EAAA,gHAWRqB,EAAAW,EAAA,KAAAC,EAAAjC,KAAQA,EAAC,CAAA,CAAA,CAAA,EACTqB,EAAAW,EAAA,KAAAE,EAAAlC,KAAQA,EAAC,CAAA,CAAA,CAAA,+FAJdjF,EAQCC,EAAAgH,EAAA9G,CAAA,6BAPe,MAAA8E,EAAK,EAAA,EAAE,KAAI,IAAMA,EAAC,CAAA,MAAKA,EAAC,CAAA,0BAEnC4B,EAAA,CAAA,EAAA,IAAAK,KAAAA,EAAAjC,KAAQA,EAAC,CAAA,CAAA,gBACT4B,EAAA,CAAA,EAAA,IAAAM,KAAAA,EAAAlC,KAAQA,EAAC,CAAA,CAAA,+DAHE,MAAAA,EAAK,EAAA,EAAE,KAAI,IAAMA,EAAC,CAAA,MAAKA,EAAC,CAAA,yDAFlCA,EAAM,EAAA,CAAA,uBAAX,OAAInE,GAAA,mKAACmE,EAAM,EAAA,CAAA,oBAAX,OAAI,GAAA,EAAA,2HAAJ,sEAeFoC,EAAApC,KAAG,KAAI,SAnHDA,EAAE,CAAA,CAAA,uBAAP,OAAInE,GAAA,6BASEmE,EAAO,EAAA,CAAA,uBAAZ,OAAInE,GAAA,6BAuBCmE,EAAO,CAAA,CAAA,uBAAZ,OAAInE,GAAA,2BAwBDmE,EAAQ,CAAA,EAAC,CAAC,EAAIA,KAAQA,EAAO,CAAA,EAAC,OAAS,CAAC,GAAAqC,GAAArC,CAAA,MAoBvCA,EAAE,CAAA,CAAA,uBAAP,OAAInE,GAAA,6BAsBCmE,EAAE,CAAA,CAAA,uBAAP,OAAInE,GAAA,2fApGRd,EAuHKC,EAAAsH,EAAApH,CAAA,EAtHJC,EAOKmH,EAAAC,CAAA,0DACLpH,EAyGKmH,EAAArH,CAAA,EAxGJE,EAkEGF,EAAAuH,CAAA,iPAwCJrH,EAEKmH,EAAAG,CAAA,kCApHGzC,EAAE,CAAA,CAAA,oBAAP,OAAInE,GAAA,EAAA,mHAAJ,yBASMmE,EAAO,EAAA,CAAA,oBAAZ,OAAInE,GAAA,EAAA,gHAAJ,yBAuBKmE,EAAO,CAAA,CAAA,oBAAZ,OAAInE,GAAA,EAAA,gHAAJ,UAwBGmE,EAAQ,CAAA,EAAC,CAAC,EAAIA,KAAQA,EAAO,CAAA,EAAC,OAAS,CAAC,0EAoBvCA,EAAE,CAAA,CAAA,oBAAP,OAAInE,GAAA,EAAA,gHAAJ,wBAsBKmE,EAAE,CAAA,CAAA,oBAAP,OAAInE,GAAA,EAAA,mHAAJ,OAiBD+F,EAAA,CAAA,EAAA,GAAAQ,KAAAA,EAAApC,KAAG,KAAI,KAAAkB,EAAAwB,EAAAN,CAAA,8GAjKE,MAAAO,CAAwC,EAAAvC,EACxC,CAAA,EAAA7E,EAAwB,MAAS,EAAA6E,EACjC,CAAA,EAA0B,MAAS,EAAAA,UACnCwC,EAAM,EAAA,EAAAxC,EAEX,MAAAyC,EAAWC,SAebC,EAMK,SAAAC,EAAUC,EAAa,CAC3B,IAAAC,EAAgBN,EAAOK,EAAQL,EAAO,MAAM,SAE5CM,GAAiBA,KAAiBC,EAC9BA,EAAcD,CAA2C,GAC7D,QACQA,GACJC,EAAcC,GAAeH,CAAK,GACvC,QAKJI,GAAO,IAAA,CACNR,EAAS,UAAa,CAAA,EAAGS,EAAI,EAAGC,CAAE,CAAA,cAgGjB,EAAAhI,EAAG,EAAAa,CAAS,IAAA,CAAAoH,EAAQjI,CAAC,EAAGkI,EAAQrH,CAAC,CAAA,+JAjIlDsH,KAAK,EAAGJ,EAAI,EAAGC,CAAE,EAEd7D,GAFc,OACViD,GAAU,SACGgB,GAAShB,CAAK,EACdA,EAD6CpH,EAAG,CAAC,qDAGnEmI,EAAA,EAAAE,EAAWtE,EAAYgE,CAAE,CAAA,mBACzBI,EAAA,EAAAG,EAAWvE,EAAYiE,CAAE,CAAA,qBAE3BG,EAAA,EAAEF,EAAUM,EAAYF,EAAQ,CAAG,EAAG,GAAG,CAAA,EAAG,KAAI,CAAA,oBAChDF,EAAA,EAAED,EAAUK,EAAYD,EAAQ,CAAG,IAAK,CAAC,CAAA,EAAG,KAAI,CAAA,yBAC9CE,EAAUP,EAAQ,MAAM,CAAC,CAAA,wBACzBQ,EAAUP,EAAQ,MAAM,CAAC,CAAA,mBAG3BC,EAAA,EAAEX,EAAYQ,EAAG,OAAM,CACtB9D,EAAKG,EAAM/D,KAAC,CAAA,GAAW4D,EAAM,CAAAG,EAAK,IAAI,EAAGoD,EAAUnH,CAAC,IAAA,CAAA,CAAA,CAAA", "x_google_ignoreList": [1, 2, 3, 4, 5, 6, 7]}